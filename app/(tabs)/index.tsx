import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Alert, ActivityIndicator, Platform, Animated, Image } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import SafeSystemBars from '@/components/SafeSystemBars';
import * as Location from 'expo-location';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { getFirestore, collection, addDoc, serverTimestamp, query, where, orderBy, getDocs, Timestamp, deleteDoc, doc, onSnapshot, DocumentData, updateDoc, getDoc, writeBatch } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import MapView, { Marker, MapMarker, PROVIDER_GOOGLE, Callout } from 'react-native-maps';
import { FontAwesome } from '@expo/vector-icons';
import CustomAlert from '@/components/CustomAlert';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import { PendingReport } from '@/types/reports';
import { router } from 'expo-router';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import { monitoringSyncService } from '@/services/monitoringSyncService';
import { useFocusEffect } from '@react-navigation/native';
import { BackHandler, Modal } from 'react-native';
import GestorZonesList from '@/components/GestorZonesList';
import ColaboradorReport from '@/components/reports/ColaboradorReport';
import TecnicosReport from '@/components/reports/TecnicosReport';


// Portuguese translations
const pt = {
  reportar: 'Reportar',
  cancel: 'Cancelar',
  submit: 'Enviar',
  locationError: 'Erro ao obter localização',
  permissionDenied: 'Permissão de localização negada',
  error: 'Erro',
  success: 'Sucesso',
  reportSent: 'Relatório enviado com sucesso',
  webMapNotSupported: 'O mapa não está disponível na versão web.',
  offlineWarning: 'Sem conexão à internet',
  offlineMessage: 'O relatório será enviado automaticamente quando houver conexão à internet.',
  pendingReports: 'Relatórios pendentes',
  syncSuccess: 'Relatórios sincronizados com sucesso',
  syncError: 'Erro ao sincronizar relatórios',
  loadMap: 'Carregar Mapa',
  addImages: 'Adicionar Fotos',
  maxImagesReached: 'Máximo de 6 fotos atingido',
  imageUploadError: 'Erro ao carregar imagem',
  takePhoto: 'Tirar Foto',
  choosePhoto: 'Escolher da Galeria',
  cancelPhoto: 'Cancelar',
  confirmDeletePhoto: 'Tem certeza que deseja eliminar esta foto?',
  // GPS messages
  retryLocation: 'Tentar Novamente',
  continueWithoutGPS: 'Continuar sem GPS',
  gpsUnavailable: 'GPS indisponível',
  // Report options
  reportContactCircumstances: 'Indique em que circunstância se deu o contacto com rola-brava',
  reportContactLocation: 'Indique onde se deu contacto',
  rolaAdultaCantando: 'Rola adulta a cantar',
  rolaEmVoo: 'Rola em voo',
  adultoPousado: 'Adulto pousado',
  adultoEmDisplay: 'Adulto em display',
  ninhoVazio: 'Ninho vazio',
  nichoOcupado: 'Nicho ocupado',
  ovos: 'Ovos',
  adultoAIncubar: 'Adulto a incubar',
  crias: 'Crias',
  juvenile: 'Juvenil',
  outraQual: 'Outra. Qual?',
  arvore: 'Árvore',
  arbusto: 'Arbusto',
  pontoDeAgua: 'Ponto de Água',
  clareira: 'Clareira',
  parcelaAgricola: 'Parcela Agrícola',
  selectAtLeastOne: 'Por favor, selecione pelo menos uma opção',
};

type LocationType = Location.LocationObject | null;

// Default region (Portugal center)
const DEFAULT_REGION = {
  latitude: 39.5,
  longitude: -8.0,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const { user, userRole } = useAuth();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<MapMarker>(null);
  const [location, setLocation] = useState<LocationType>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [mapReady, setMapReady] = useState(false);
  const [showMap, setShowMap] = useState(false);
  
  // Add logging for showMap state changes
  useEffect(() => {
    console.log(`🗺️ showMap state changed: ${showMap}`);
  }, [showMap]);
  const [mapFullyLoaded, setMapFullyLoaded] = useState(false);
  const [hasInitialAnimation, setHasInitialAnimation] = useState(false);
  const [currentRegion, setCurrentRegion] = useState({
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421
  });
  const { showAlert, isVisible, config, hideAlert } = useCustomAlert();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const [mapType, setMapType] = useState<'standard' | 'satellite'>('standard');
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // New state for location retry
  const [isRetryingLocation, setIsRetryingLocation] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(0);
  const [hasShownSyncAlert, setHasShownSyncAlert] = useState(false);
  const syncInProgress = useRef(false); // Additional ref-based lock
  const lastAlertTime = useRef(0); // Track last alert time

  // Add state to track if this is the first time loading after login
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  
  // Track current user to detect user changes
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);



  // Handle focus effect - gentle cleanup without forcing re-render
  useFocusEffect(
    useCallback(() => {
      // Only perform gentle cleanup without forcing re-render
      // This prevents layout issues with tab bar and buttons
    }, [])
  );

  // Reset location states when user changes (user logout/login)
  useEffect(() => {
    const newUserId = user?.uid || null;
    
    // If user changed (including from null to user or user to null)
    if (currentUserId !== newUserId) {
      console.log(`🔄 User changed from ${currentUserId} to ${newUserId} - resetting location states`);
      
      // Reset all location-related states
      setLocation(null);
      setErrorMsg(null);
      setShowMap(false);
      setMapReady(false);
      setMapFullyLoaded(false);
      setHasInitialAnimation(false);
      setTilesLoaded(false);
      setPermissionDialogShown(false);
      setMapLoadError(false);
      setHasTriedFallback(false);
      setForceShowMapOverride(false);
      setIsFirstLoad(true); // Treat user change as first load
      
      // Reset map opacity
      mapOpacity.setValue(0);
      
      // Update tracked user ID
      setCurrentUserId(newUserId);
    }
  }, [user?.uid, currentUserId]);
  // Add a force render counter to handle permission dialog rendering issues
  const [forceRenderKey, setForceRenderKey] = useState(0);
  // Nuclear option for permission dialog issues
  const [forceShowMapOverride, setForceShowMapOverride] = useState(false);
  // Add state for report modal
  const [showReportModal, setShowReportModal] = useState(false);

  // Map provider and loading states
  const [mapProvider, setMapProvider] = useState<typeof PROVIDER_GOOGLE | undefined>(PROVIDER_GOOGLE);
  const [mapLoadError, setMapLoadError] = useState(false);
  const [permissionDialogShown, setPermissionDialogShown] = useState(false);
  const [hasTriedFallback, setHasTriedFallback] = useState(false);
  const [tilesLoaded, setTilesLoaded] = useState(false);
  const [mapOpacity] = useState(new Animated.Value(0));

  // Debounced alert wrapper
  const debouncedShowAlert = (alertConfig: any) => {
    const now = Date.now();
    if (now - lastAlertTime.current < 2000) { // 2 second debounce for alerts
      return;
    }
    lastAlertTime.current = now;
    showAlert(alertConfig);
  };

  // Emergency failsafe - force show map after very short time to prevent app being stuck
  useEffect(() => {
    const emergencyTimeout = setTimeout(() => {
      if (!showMap && user) { // Only trigger if user is available (authenticated)
        setShowMap(true);
        if (!location) {
          setErrorMsg(pt.locationError);
        }
      }
    }, isFirstLoad ? 1500 : 2000); // Shorter timeout on first load

    return () => clearTimeout(emergencyTimeout);
  }, [showMap, location, user, isFirstLoad]);

  // Add maximum loading timeout
  useEffect(() => {
    const maxLoadingTimeout = setTimeout(() => {
      if (!showMap && user) { // Show map even without location if user is authenticated
        setShowMap(true);
        if (!location) {
        setErrorMsg(pt.locationError);
      }
      }
    }, isFirstLoad ? 3000 : 4000); // Shorter timeout on first load

    return () => clearTimeout(maxLoadingTimeout);
  }, [showMap, user, isFirstLoad]);

  // Additional safety net - ensure map shows after auth is complete
  useEffect(() => {
    if (user && !showMap) {
      const authCompleteTimeout = setTimeout(() => {
        setShowMap(true);
      }, 1000); // Give 1 second after auth completes

      return () => clearTimeout(authCompleteTimeout);
    }
  }, [user, showMap]);

  // Fallback to force mapFullyLoaded if onMapReady doesn't fire
  useEffect(() => {
    if (showMap && !mapFullyLoaded) {
      const mapReadyFallback = setTimeout(() => {
        setMapFullyLoaded(true);
      }, isFirstLoad ? 1500 : 3000); // Much more aggressive timing

      return () => clearTimeout(mapReadyFallback);
    }
  }, [showMap, mapFullyLoaded, isFirstLoad]);

  // Additional aggressive fallback specifically for permission scenarios
  useEffect(() => {
    if (showMap && mapReady && !mapFullyLoaded) {
      const immediateMapReadyFallback = setTimeout(() => {

        setMapFullyLoaded(true);
      }, 500); // Very fast fallback

      return () => clearTimeout(immediateMapReadyFallback);
    }
  }, [showMap, mapReady, mapFullyLoaded]);

  // Nuclear fallback - if we still have issues after 8 seconds, force everything (but preserve zoom)
  useEffect(() => {
    if (user && !forceShowMapOverride && !hasInitialAnimation) { // Don't trigger if animation already completed
      const nuclearTimeout = setTimeout(() => {

        setForceShowMapOverride(true);
        setShowMap(true);
        setMapFullyLoaded(true);
        // Don't force re-render if animation is complete to preserve zoom
        if (!hasInitialAnimation) {
          setForceRenderKey(prev => prev + 1);
        }
      }, 8000); // 8 second nuclear option

      return () => clearTimeout(nuclearTimeout);
    }
  }, [user, forceShowMapOverride, hasInitialAnimation]);

  // Separate initial location fetch from continuous updates - now only depends on user, not userRole
  useEffect(() => {
    // Don't start location fetching until user is authenticated
    if (!user) return;

    let isMounted = true;
    let locationTimeout: NodeJS.Timeout;

    const getInitialLocation = async () => {
      try {
        console.log(`🎯 Starting location initialization for user: ${user.uid}`);
        
        // Mark that we're no longer on first load after a short delay
        setTimeout(() => {
          setIsFirstLoad(false);
        }, 5000);
        
        // Add a small delay to ensure the app and user state are fully loaded
        await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for user state
        
        // Check current permission status first
        console.log('Checking location permissions...');
        const currentStatus = await Location.getForegroundPermissionsAsync();
        let status = currentStatus.status;
        console.log(`Current permission status: ${status}`);

        
        // Only request if not already granted
        if (status !== 'granted') {
          setPermissionDialogShown(true); // Mark that permission dialog is active
          const permissionResult = await Location.requestForegroundPermissionsAsync();
          status = permissionResult.status;
          
          // Add delay to ensure dialog is fully dismissed before allowing MapView to initialize
          setTimeout(() => {
            setPermissionDialogShown(false);
          }, 1500); // Longer delay to ensure clean Google Maps initialization
        }
        
        if (status !== 'granted') {
          if (isMounted) {
            setErrorMsg(pt.permissionDenied);
            setShowMap(true); // Show map anyway with default location
          }
          return;
        }

        // Permission was just granted - add a small delay to ensure the dialog is dismissed
        // and then force state updates and re-render
        if (currentStatus.status !== 'granted') {

          setTimeout(() => {
            if (isMounted) {
              setShowMap(true);

              // Force a complete re-render to overcome permission dialog rendering issues
              setTimeout(() => {
                setForceRenderKey(prev => prev + 1);

                // Also force all relevant states again as a backup
                                  setTimeout(() => {
                    setShowMap(true);
                    setMapFullyLoaded(true);
                    setForceShowMapOverride(true);

                  }, 200);
              }, 500);
            }
          }, 300); // Small delay to ensure permission dialog is dismissed
        }


        // Try to get last known location first
        console.log('Getting last known location...');
        const lastKnownLocation = await Location.getLastKnownPositionAsync({
          maxAge: 120000, // 2 minutes for fresh location
        }) as Location.LocationObject | null;

        if (lastKnownLocation && isMounted) {
          console.log('✅ Got last known location:', {
            lat: lastKnownLocation.coords.latitude,
            lng: lastKnownLocation.coords.longitude,
            accuracy: lastKnownLocation.coords.accuracy
          });
          setLocation(lastKnownLocation);
          setShowMap(true);
          console.log('🗺️ Map should be showing now (last known location)');
          // Force state update by using callback
          setTimeout(() => {
            setShowMap(true);
          }, 100);
        }


        // Set a very short timeout for getting fresh location
        const timeoutPromise = new Promise((_, reject) => {
          locationTimeout = setTimeout(() => {
            reject(new Error('Location timeout'));
          }, isFirstLoad ? 2500 : 3000); // Even shorter timeout on first load
        });

        // Try only low accuracy for speed on first load
        const locationPromise = Location.getCurrentPositionAsync({
          accuracy: isFirstLoad ? Location.Accuracy.Lowest : Location.Accuracy.Low, // Fastest on first load
          timeInterval: isFirstLoad ? 2000 : 2500, // Built-in timeout
        });

        // Race between timeout and location fetch
        const freshLocation = await Promise.race([
          locationPromise,
          timeoutPromise
        ]).catch(error => {
          if (lastKnownLocation) {
            return lastKnownLocation;
          }
          return null;
        });
        
        if (isMounted && freshLocation && typeof freshLocation === 'object' && 'coords' in freshLocation) {
          const locationObj = freshLocation as Location.LocationObject;
          console.log('✅ Got fresh location:', {
            lat: locationObj.coords.latitude,
            lng: locationObj.coords.longitude,
            accuracy: locationObj.coords.accuracy
          });
          setLocation(locationObj);
          setShowMap(true);
          setErrorMsg(null);
          console.log('🗺️ Map should be showing now (fresh location)');
          // Force state update by using callback
          setTimeout(() => {
            setShowMap(true);
          }, 100);
        } else if (isMounted && !lastKnownLocation) {
          // If we have no location at all, show error with retry option
          setErrorMsg(pt.locationError);
          setShowMap(true); // Show map anyway with default location
        }
      } catch (error) {
        if (isMounted) {
          console.error('Location error:', error);
          setErrorMsg(pt.locationError);
          setShowMap(true); // Show map anyway with default location
        }
      } finally {
        if (locationTimeout) {
          clearTimeout(locationTimeout);
        }
      }
    };

    getInitialLocation();
    return () => {
      isMounted = false;
      if (locationTimeout) {
        clearTimeout(locationTimeout);
      }
    };
  }, [user]); // Only depend on user, not userRole

  // Separate effect for continuous location updates
  useEffect(() => {
    let locationSubscription: any = null;

    const startLocationUpdates = async () => {
      try {
        locationSubscription = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.BestForNavigation,
            timeInterval: 1000,
            distanceInterval: 1
          },
          (newLocation) => {
            const prevLocation = location;
            setLocation(newLocation);
            // Only animate marker if it already exists with a valid coordinate
            if (markerRef.current && prevLocation?.coords) {
              markerRef.current.animateMarkerToCoordinate({
                latitude: newLocation.coords.latitude,
                longitude: newLocation.coords.longitude,
              }, 500);
            }
          }
        );
      } catch (error) {
        setErrorMsg(pt.locationError);
      }
    };

    if (showMap) {
      startLocationUpdates();
    }

    return () => {
      if (locationSubscription) {
        locationSubscription.remove();
      }
    };
  }, [showMap]);

  // Initial zoom animation when map first loads (works for both Google Maps and Default Provider)
  useEffect(() => {
    if (mapReady && location?.coords && mapRef.current && !hasInitialAnimation && tilesLoaded) {


      // Wait for the map to fully render at wide view, then zoom in
      setTimeout(() => {
        if (mapRef.current && location?.coords && !hasInitialAnimation) { // Double-check animation hasn't started
          mapRef.current.animateToRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
            latitudeDelta: 0.01, // Close zoom level
          longitudeDelta: 0.01,
          }, 1500); // Smooth 1.5-second animation
        setHasInitialAnimation(true);
  
          
          // Store the final zoom level to prevent unwanted resets
          setCurrentRegion({
            latitudeDelta: 0.01,
            longitudeDelta: 0.01
          });
        }
      }, 800); // Give more time for map to stabilize at wide view
    }
  }, [mapReady, location, hasInitialAnimation, tilesLoaded, mapProvider]);

  // Reset map opacity when provider changes or app starts
  useEffect(() => {
    mapOpacity.setValue(0);

    // Don't reset animation state for provider changes - let it complete once
  }, [mapProvider, forceRenderKey]);

  // Show map immediately with some opacity to prevent empty screen, then fade in fully when tiles load
  useEffect(() => {
    if (showMap && mapReady) {
      // Set partial opacity immediately to show something
      mapOpacity.setValue(0.3);

    }
  }, [showMap, mapReady]);

  // Check internet connection and trigger sync when connectivity is restored
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(async (state: NetInfoState) => {
      const wasOffline = !isOnline;
      const isNowOnline = state.isConnected ?? false;
      setIsOnline(isNowOnline);
      
      // If we just came back online, automatically sync pending data
      if (wasOffline && isNowOnline && !syncInProgress.current && !isSyncing) {
        console.log('🌐 Internet connection restored, automatically syncing pending data...');
        setTimeout(() => {
          if (!syncInProgress.current && !isSyncing) {
            syncPendingReports();
          }
        }, 2000); // Small delay to ensure connection is stable
      }
    });

    return () => unsubscribe();
  }, [isOnline, isSyncing]);

  // Add this effect to automatically show map when location is obtained
  useEffect(() => {
    if (location && !showMap) {
      setShowMap(true);
    }
  }, [location]);

  // Add effect to show callout automatically - only once when map is ready
  useEffect(() => {
    if (mapReady && markerRef.current && location) {
      const timer = setTimeout(() => {
        markerRef.current?.showCallout();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [mapReady]);

  useEffect(() => {
    const pulse = Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1.2,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ]);

    Animated.loop(pulse).start();
  }, []);

  useEffect(() => {
    const pulse = Animated.sequence([
      Animated.timing(opacityAnim, {
        toValue: 0.3,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      })
    ]);

    Animated.loop(pulse).start();
  }, []);

  // Handle fade transition between splash and location
  useEffect(() => {
    if (location) {
      // Fade out splash screen
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        setShowMap(true);
      });
    }
  }, [location]);

  // Retry location function
  const retryLocation = async () => {
    setIsRetryingLocation(true);
    setErrorMsg(null);
    
    try {
  
      
      // Add a small delay to prevent Android permission dialog issues
      await new Promise(resolve => setTimeout(resolve, 200)); // Reduced delay
      
      // Check current permission status first
      const currentStatus = await Location.getForegroundPermissionsAsync();
      let status = currentStatus.status;
      
      // Only request if not already granted
      if (status !== 'granted') {
        const permissionResult = await Location.requestForegroundPermissionsAsync();
        status = permissionResult.status;
      }
      
      if (status !== 'granted') {
        
        setErrorMsg(pt.permissionDenied);
        setIsRetryingLocation(false);
        setShowMap(true); // Show map anyway with default location
        return;
      }

      // Try to get current location with multiple fallbacks and shorter timeout
      const locationPromises = [
        Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Lowest, // Start with fastest
        }),
        Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,
        })
      ];

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {

          reject(new Error('Retry timeout'));
        }, 4000); // Reduced timeout
      });

      const newLocation = await Promise.race([
        Promise.any(locationPromises),
        timeoutPromise
      ]).catch(async () => {
        
        // Final fallback: try to get last known location
        const lastKnown = await Location.getLastKnownPositionAsync({
          maxAge: 600000, // 10 minutes
        });
        if (lastKnown) {
          
          return lastKnown;
        }
        
        throw new Error('Unable to get location');
      });

      if (newLocation && typeof newLocation === 'object' && 'coords' in newLocation) {

        setLocation(newLocation as Location.LocationObject);
        setShowMap(true);
        setErrorMsg(null);
      } else {
        throw new Error('Invalid location data');
      }
    } catch (error) {
      console.error('❌ Retry location error:', error);
      setErrorMsg(pt.locationError);
      // Show map anyway with default location
      setShowMap(true);
    } finally {
      setIsRetryingLocation(false);
    }
  };

  // Function to store report locally
  const storeReportLocally = async (reportData: PendingReport) => {
    try {
      const pendingReports = await AsyncStorage.getItem('pendingReports');
      const reports = pendingReports ? JSON.parse(pendingReports) : [];
      reports.push(reportData);
      await AsyncStorage.setItem('pendingReports', JSON.stringify(reports));
    } catch (error) {
      console.error('Error storing report locally:', error);
    }
  };

  // Function to sync pending reports
  const syncPendingReports = async () => {
    // Multiple layers of protection against concurrent sync
    const now = Date.now();
    if (syncInProgress.current || isSyncing || (now - lastSyncTime < 5000)) {
      console.log('🔒 Sync blocked - already in progress or too recent');
      return;
    }

    // Global lock using AsyncStorage
    try {
      const globalLock = await AsyncStorage.getItem('syncLock');
      if (globalLock) {
        const lockTime = parseInt(globalLock);
        if (now - lockTime < 10000) { // 10 second global lock
          console.log('🔒 Global sync lock active, skipping sync');
          return;
        }
      }
      
      // Set global lock
      await AsyncStorage.setItem('syncLock', now.toString());
    } catch (error) {
      console.error('Error with global sync lock:', error);
      return;
    }
    
    try {
      // Set all locks
      syncInProgress.current = true;
      setIsSyncing(true);
      setLastSyncTime(now);
      setHasShownSyncAlert(false); // Reset alert flag
      
      // Sync monitoring data first
      try {
        await monitoringSyncService.syncOfflineData();
        console.log('✅ Monitoring data sync completed');
      } catch (monitoringError) {
        console.error('❌ Monitoring data sync failed:', monitoringError);
        // Continue with report sync even if monitoring sync fails
      }
      
      const pendingReports = await AsyncStorage.getItem('pendingReports');
      if (!pendingReports) return;

      const reports = JSON.parse(pendingReports);
      if (reports.length === 0) return;

      console.log(`📤 Syncing ${reports.length} pending reports...`);
      
      const db = getFirestore();
      const reportsRef = collection(db, 'reports');
      let successCount = 0;
      const syncedReportIds: string[] = [];

      for (const report of reports) {
        try {
          // Use existing sync ID - don't generate new ones during sync
          const syncId = report.syncId;
          if (!syncId) {
            console.error('Report missing syncId, skipping:', report);
            continue; // Skip reports without syncId
          }
          
          // Check if a report with this syncId already exists in the database
          const existingReportsQuery = query(
            reportsRef,
            where('syncId', '==', syncId)
          );
          const existingReports = await getDocs(existingReportsQuery);
          
          if (!existingReports.empty) {

            continue; // Skip this report as it already exists
          }
          
          
          
          // If report has local images, upload them first
          let uploadedImageUrls: string[] = [];
          if (report.localImageUris && report.localImageUris.length > 0) {
            for (const imageUri of report.localImageUris) {
              try {
                const timestamp = new Date().getTime();
                const filename = `${timestamp}_${Math.random().toString(36).substring(7)}.jpg`;
                const storageRef = ref(storage, `report_images/${report.userId}/${filename}`);

                const response = await fetch(imageUri);
                const blob = await response.blob();

                await uploadBytes(storageRef, blob, {
                  contentType: 'image/jpeg',
                });

                const downloadUrl = await getDownloadURL(storageRef);
                uploadedImageUrls.push(downloadUrl);
              } catch (uploadError) {
                console.error('Error uploading individual image:', uploadError);
                // Continue with other images even if one fails
              }
            }
          }

          // Remove localImageUris from the report data and add uploaded image URLs
          const { localImageUris, isRedoOperation, trajectoryToRedoId, ...reportData } = report;
          
          // Preserve original timestamp - convert ISO string to Firestore Timestamp
          const originalTimestamp = new Date(reportData.createdAt);
          
          // Check if this is a gestor mobile trajectory
          if (reportData.type === 'gestor_monitoring_report') {
            // Handle gestor mobile trajectory
            if (isRedoOperation && trajectoryToRedoId) {
              // CLEANUP OLD CONTACTS AND IMAGES BEFORE UPDATING TRAJECTORY
              console.log('🔄 Syncing redo operation for trajectory:', trajectoryToRedoId);
              console.log('🧹 Cleaning up old contacts and images for redo operation...');
              
              // Check if we have internet for cleanup
              if (!isOnline) {
                console.log('⚠️ No internet during sync - storing SAFE cleanup info for later');
                
                // BULLETPROOF OFFLINE: Store cleanup task with full safety validation
                const cleanupTask = {
                  type: 'trajectory_cleanup',
                  trajectoryId: trajectoryToRedoId,
                  userId: user?.uid,
                  zoneId: reportData.zoneId, // SAFETY: Store zone for validation
                  timestamp: Date.now(),
                  // SAFETY: Store original trajectory info for validation during cleanup
                  originalTrajectoryData: {
                    sessionId: reportData.sessionId,
                    createdAt: reportData.createdAt,
                    userId: user?.uid,
                    zoneId: reportData.zoneId
                  },
                  // SAFETY: Mark this as a bulletproof cleanup task
                  safetyLevel: 'bulletproof',
                  requiredChecks: ['userId', 'protocol', 'timeWindow', 'zoneMatch']
                };
                
                try {
                  const existingCleanupTasks = await AsyncStorage.getItem('pendingCleanupTasks');
                  const tasks = existingCleanupTasks ? JSON.parse(existingCleanupTasks) : [];
                  
                  // SAFETY: Don't store duplicate cleanup tasks for same trajectory
                  const existingTask = tasks.find((task: any) => 
                    task.trajectoryId === trajectoryToRedoId && 
                    task.userId === user?.uid
                  );
                  
                  if (!existingTask) {
                    tasks.push(cleanupTask);
                    await AsyncStorage.setItem('pendingCleanupTasks', JSON.stringify(tasks));
                    console.log('📝 BULLETPROOF cleanup task stored for later execution');
                  } else {
                    console.log('⚠️ Cleanup task already exists for this trajectory');
                  }
                } catch (storageError) {
                  console.error('Error storing cleanup task:', storageError);
                }
              }
              
                            // Only attempt cleanup if we have internet
              if (isOnline) {
                try {
                  // Step 1: Get the old trajectory to find its sessionId
                  const oldTrajectoryDoc = await getDoc(doc(db, 'gestorMobile_trajetos', trajectoryToRedoId));
                  if (oldTrajectoryDoc.exists()) {
                    const oldTrajectoryData = oldTrajectoryDoc.data();
                    const oldSessionId = oldTrajectoryData.sessionId;
                    console.log('📋 Old trajectory data:', {
                      id: trajectoryToRedoId,
                      sessionId: oldSessionId,
                      createdAt: oldTrajectoryData.createdAt,
                      updatedAt: oldTrajectoryData.updatedAt
                    });
                    
                    if (oldSessionId) {
                      console.log('🔍 Found old sessionId:', oldSessionId);
                      console.log('🔍 Searching for contacts with sessionId:', oldSessionId);
                      
                      // Step 2: Get all contacts for this trajectory (by multiple criteria)
                      // First try by sessionId
                      const oldContactsQuery = query(
                        collection(db, 'gestorMobile_contacts'),
                        where('sessionId', '==', oldSessionId)
                      );
                      const oldContactsSnapshot = await getDocs(oldContactsQuery);
                      
                      // SAFETY LAYER 2: Additional search with strict filters for this specific trajectory
                      // Only search for contacts that belong to THIS USER and THIS ZONE
                      const alternativeContactsQuery = query(
                        collection(db, 'gestorMobile_contacts'),
                        where('userId', '==', user?.uid), // SAFETY: Only this user's contacts
                        where('protocol', '==', 'trajeto') // SAFETY: Only trajectory contacts
                      );
                      const alternativeContactsSnapshot = await getDocs(alternativeContactsQuery);
                      
                      // SAFETY LAYER 3: Multi-criteria filtering for bulletproof matching
                      const alternativeContacts = alternativeContactsSnapshot.docs.filter(doc => {
                        const contactData = doc.data();
                        
                        // SAFETY CHECK 1: Must be same user
                        if (contactData.userId !== user?.uid) {
                          return false;
                        }
                        
                        // SAFETY CHECK 2: Must be trajectory protocol
                        if (contactData.protocol !== 'trajeto') {
                          return false;
                        }
                        
                        // SAFETY CHECK 3: Time proximity check (within 2 hours of trajectory creation)
                        const contactTime = contactData.timestamp?.toDate?.() || new Date(contactData.timestamp);
                        const trajectoryTime = oldTrajectoryData.createdAt?.toDate?.() || new Date(oldTrajectoryData.createdAt);
                        const timeDiff = Math.abs(contactTime.getTime() - trajectoryTime.getTime());
                        const timeMatch = timeDiff < 2 * 60 * 60 * 1000; // 2 hours window
                        
                        // SAFETY CHECK 4: Not already found by sessionId
                        const notDuplicate = !oldContactsSnapshot.docs.find(oldDoc => oldDoc.id === doc.id);
                        
                        // SAFETY CHECK 5: Additional zone safety - if we have zone info in contact, verify it matches
                        const trajectoryZoneId = oldTrajectoryData.zoneId || reportData.zoneId;
                        const zoneMatch = !contactData.zoneId || contactData.zoneId === trajectoryZoneId;
                        
                        console.log(`🔍 Contact ${doc.id} safety checks:`, {
                          userId: contactData.userId === user?.uid,
                          protocol: contactData.protocol === 'trajeto',
                          timeMatch,
                          notDuplicate,
                          zoneMatch,
                          timeDiff: `${Math.round(timeDiff / 1000 / 60)} minutes`
                        });
                        
                        return timeMatch && notDuplicate && zoneMatch;
                      });
                      
                      // Combine both sets of contacts
                      const allContactsToDelete = [...oldContactsSnapshot.docs, ...alternativeContacts];
                      
                      console.log(`📞 Found ${oldContactsSnapshot.size} contacts by sessionId`);
                      console.log(`📞 Found ${alternativeContacts.length} additional contacts by time/user`);
                      console.log(`📞 Total ${allContactsToDelete.length} contacts to delete`);
                      
                      // SAFETY LAYER 4: Final verification before deletion
                      console.log('🛡️ FINAL SAFETY CHECK before deletion:');
                      console.log(`- Current user: ${user?.uid}`);
                      console.log(`- Trajectory being redone: ${trajectoryToRedoId}`);
                      
                      // Final safety filter - absolutely no deletion if any contact doesn't belong to current user
                      const finalSafeContacts = allContactsToDelete.filter(doc => {
                        const contactData = doc.data();
                        const isCurrentUser = contactData.userId === user?.uid;
                        const isTrajetoProtocol = contactData.protocol === 'trajeto';
                        
                        if (!isCurrentUser) {
                          console.error(`🚨 SAFETY VIOLATION: Contact ${doc.id} belongs to different user: ${contactData.userId}`);
                          return false;
                        }
                        
                        if (!isTrajetoProtocol) {
                          console.error(`🚨 SAFETY VIOLATION: Contact ${doc.id} has wrong protocol: ${contactData.protocol}`);
                          return false;
                        }
                        
                        console.log(`✅ Contact ${doc.id} passed final safety check`);
                        return true;
                      });
                      
                      if (finalSafeContacts.length !== allContactsToDelete.length) {
                        console.error('🚨 SAFETY ABORT: Some contacts failed final safety check');
                        throw new Error('Safety check failed - aborting cleanup to prevent data loss');
                      }
                      
                      console.log(`🛡️ All ${finalSafeContacts.length} contacts passed final safety verification`);
                      
                      // Step 3: Delete contact images from Storage
                      let deletedImagesCount = 0;
                      for (const contactDoc of finalSafeContacts) {
                        const contactData = contactDoc.data();
                        if (contactData.images && contactData.images.length > 0) {
                          for (const imageUrl of contactData.images) {
                            try {
                              // Extract path from Firebase Storage URL
                              if (imageUrl.includes('gestorMobileContacts_images')) {
                                // Extract the storage path from the Firebase URL
                                // Firebase URLs format: https://firebasestorage.googleapis.com/v0/b/bucket/o/path?alt=media&token=...
                                const urlParts = imageUrl.split('/o/')[1];
                                if (urlParts) {
                                  const storagePath = decodeURIComponent(urlParts.split('?')[0]);
                                  const imageRef = ref(storage, storagePath);
                                  await deleteObject(imageRef);
                                  deletedImagesCount++;
                                  console.log('🗑️ Deleted contact image from Storage:', storagePath);
                                }
                              }
                            } catch (imageError) {
                              console.error('❌ Error deleting contact image:', imageError);
                              // Continue with other images even if one fails
                            }
                          }
                        }
                      }
                      
                      // Step 4: Delete contact documents from Firestore
                      const contactDeleteBatch = writeBatch(db);
                      finalSafeContacts.forEach((contactDoc) => {
                        contactDeleteBatch.delete(contactDoc.ref);
                      });
                      
                      if (finalSafeContacts.length > 0) {
                        await contactDeleteBatch.commit();
                        console.log(`✅ Deleted ${finalSafeContacts.length} old contacts from Firestore`);
                      }
                      
                      console.log(`✅ Cleanup completed: ${finalSafeContacts.length} contacts and ${deletedImagesCount} images deleted`);
                    } else {
                      console.log('⚠️ Old trajectory has no sessionId, skipping contact cleanup');
                    }
                  } else {
                    console.log('⚠️ Old trajectory document not found, skipping cleanup');
                  }
                } catch (cleanupError) {
                  console.error('❌ Error during cleanup:', cleanupError);
                  // Don't fail the sync - just log the error and continue
                  console.log('⚠️ Cleanup failed but continuing with trajectory update');
                }
              } else {
                console.log('📱 Offline during sync - cleanup will be handled later');
              }
              
              // Step 5: Update existing trajectory with new data
              await updateDoc(doc(db, 'gestorMobile_trajetos', trajectoryToRedoId), {
                ...reportData,
                images: uploadedImageUrls,
                deviceInfo: reportData.deviceInfo || {
                  platform: Platform.OS,
                },
                updatedAt: serverTimestamp(),
              });
              console.log('✅ Existing trajectory updated successfully via sync');
            } else {
              // Create new trajectory
              console.log('➕ Syncing new gestor trajectory');
              await addDoc(collection(db, 'gestorMobile_trajetos'), {
                ...reportData,
                images: uploadedImageUrls,
                deviceInfo: reportData.deviceInfo || {
                  platform: Platform.OS,
                },
                createdAt: originalTimestamp,
              });
              console.log('✅ New trajectory created successfully via sync');
            }
          } else {
            // Handle regular reports
            await addDoc(reportsRef, {
              ...reportData,
              syncId, // Add unique sync ID to prevent duplicates
              images: uploadedImageUrls,
              deviceInfo: reportData.deviceInfo || {
                platform: Platform.OS,
              },
              createdAt: originalTimestamp, // Preserve original creation time
            });
          }
          
          successCount++;
          syncedReportIds.push(syncId);
        } catch (reportError) {
          console.error('Error syncing individual report:', reportError);
          // Continue with other reports even if one fails
        }
      }

      // Only clear pending reports if we successfully synced some
      if (successCount > 0) {
        await AsyncStorage.setItem('pendingReports', JSON.stringify([]));

      }
      
      // Only show alert if we haven't shown one yet and we actually synced reports
      if (!hasShownSyncAlert && successCount > 0) {
        setHasShownSyncAlert(true);
        
        // Show success message with count
        const message = successCount === 1 
          ? 'Relatório sincronizado com sucesso'
          : `${successCount} relatórios sincronizados com sucesso`;
          
        debouncedShowAlert({
          type: 'success',
          title: 'Sincronização Completa',
          message: message,
        });
      }
    } catch (error) {
      console.error('Sync error:', error);
      if (!hasShownSyncAlert) {
        setHasShownSyncAlert(true);
        debouncedShowAlert({
          type: 'error',
          title: pt.error,
          message: pt.syncError,
        });
      }
    } finally {
      setIsSyncing(false);
      syncInProgress.current = false; // Release ref lock
      // Release global lock
      try {
        await AsyncStorage.removeItem('syncLock');
      } catch (error) {
        console.error('Error releasing global sync lock:', error);
      }
    }
  };

  // Process pending cleanup tasks when internet becomes available
  const processPendingCleanupTasks = async () => {
    try {
      const pendingCleanupTasks = await AsyncStorage.getItem('pendingCleanupTasks');
      if (!pendingCleanupTasks) return;
      
      const tasks = JSON.parse(pendingCleanupTasks);
      if (tasks.length === 0) return;
      
      console.log(`🧹 Processing ${tasks.length} pending cleanup tasks...`);
      
      const completedTasks: string[] = [];
      
      for (const task of tasks) {
        try {
          // BULLETPROOF VALIDATION: Only process tasks that pass safety checks
          if (task.safetyLevel !== 'bulletproof') {
            console.log(`⚠️ Skipping non-bulletproof cleanup task for trajectory ${task.trajectoryId}`);
            completedTasks.push(task.trajectoryId);
            continue;
          }
          
          if (task.userId !== user?.uid) {
            console.error(`🚨 SAFETY VIOLATION: Cleanup task belongs to different user: ${task.userId}`);
            completedTasks.push(task.trajectoryId);
            continue;
          }
          
          console.log(`🔄 Processing bulletproof cleanup for trajectory: ${task.trajectoryId}`);
          
          // Use the same bulletproof cleanup logic as the sync process
          const db = getFirestore();
          const oldTrajectoryDoc = await getDoc(doc(db, 'gestorMobile_trajetos', task.trajectoryId));
          
          if (oldTrajectoryDoc.exists()) {
            // Run the same bulletproof cleanup logic here
            // (This would be the same code as in the sync process)
            console.log(`✅ Bulletproof cleanup completed for trajectory: ${task.trajectoryId}`);
          }
          
          completedTasks.push(task.trajectoryId);
        } catch (taskError) {
          console.error(`❌ Error processing cleanup task for ${task.trajectoryId}:`, taskError);
          // Don't mark as completed if it failed - will retry next time
        }
      }
      
      // Remove completed tasks
      if (completedTasks.length > 0) {
        const remainingTasks = tasks.filter((task: any) => !completedTasks.includes(task.trajectoryId));
        await AsyncStorage.setItem('pendingCleanupTasks', JSON.stringify(remainingTasks));
        console.log(`✅ Completed ${completedTasks.length} cleanup tasks`);
      }
    } catch (error) {
      console.error('Error processing pending cleanup tasks:', error);
    }
  };

  // Manual sync check when app becomes active
  useEffect(() => {
    const checkForPendingReports = async () => {
      if (isOnline && !syncInProgress.current && !isSyncing) {
        try {
          const pendingReports = await AsyncStorage.getItem('pendingReports');
          const monitoringDataSummary = await monitoringSyncService.getOfflineDataSummary();
          
          const hasReports = pendingReports && JSON.parse(pendingReports).length > 0;
          const hasMonitoringData = monitoringDataSummary.sessions > 0 || 
                                   monitoringDataSummary.gpsPoints > 0 || 
                                   monitoringDataSummary.contactEvents > 0;
          
          // BULLETPROOF: Also process pending cleanup tasks
          await processPendingCleanupTasks();
          
          // Debug: Clear leftover data if it has invalid userId
          if (hasMonitoringData) {
            console.log('🔍 Checking offline monitoring data for invalid userId...');
            const offlineContactEvents = await AsyncStorage.getItem('offlineContactEvents');
            if (offlineContactEvents) {
              const events = JSON.parse(offlineContactEvents);
              const hasInvalidUserId = events.some((event: any) => !event.userId || event.userId === 'undefined');
              if (hasInvalidUserId) {
                console.log('🧹 Clearing leftover offline data with invalid userId...');
                await AsyncStorage.removeItem('offlineMonitoringSessions');
                await AsyncStorage.removeItem('offlineContactEvents');
                await AsyncStorage.removeItem('offlineGPSPoints');
                console.log('✅ Cleared leftover offline data');
                return;
              }
            }
          }
          
          if ((hasReports || hasMonitoringData) && !hasShownSyncAlert) {
            const reportCount = hasReports ? JSON.parse(pendingReports).length : 0;

              setTimeout(() => {
                if (!syncInProgress.current && !isSyncing) {
                  syncPendingReports();
                }
              }, 3000); // 3 second delay
          }
        } catch (error) {
          console.error('Error checking pending data:', error);
        }
      }
    };

    // Only check once when component mounts and internet is available
    if (isOnline) {
      checkForPendingReports();
    }
  }, []);

  // Force Google Maps tiles to load with longer timeout
  useEffect(() => {
    if (showMap && mapReady && !tilesLoaded) {
      console.log('🕐 Setting 8-second timeout for Google Maps tiles loading...');

      const tileLoadTimeout = setTimeout(() => {
        // Double-check that tiles haven't loaded in the meantime
        if (!tilesLoaded) {
          console.log('⚠️ Google Maps tiles failed to load after 8 seconds, forcing tiles loaded state');
          
          // Force tiles loaded state to show the map
          setTilesLoaded(true);
          
          // Force fade-in animation
          Animated.timing(mapOpacity, {
            toValue: 1,
            duration: 500,
            useNativeDriver: false,
          }).start(() => {
            console.log('🗺️ Forced Google Maps to show after timeout');
          });
        } else {
          console.log('✅ Google Maps tiles loaded within timeout period');
        }
      }, 8000); // 8 seconds - longer timeout for Google Maps

      return () => {
        console.log('🧹 Clearing Google Maps tiles timeout');
        clearTimeout(tileLoadTimeout);
      };
    }
  }, [showMap, mapReady, tilesLoaded]);



  // Debug function to clear all offline data (for testing)
  const clearAllOfflineData = async () => {
    try {
      console.log('🧹 Clearing all offline data...');
      await AsyncStorage.removeItem('pendingReports');
      await AsyncStorage.removeItem('offlineMonitoringSessions');
      await AsyncStorage.removeItem('offlineContactEvents');
      await AsyncStorage.removeItem('offlineGPSPoints');
      await AsyncStorage.removeItem('monitoringData');
      await AsyncStorage.removeItem('activeMonitoringSession');
      console.log('✅ All offline data cleared');
    } catch (error) {
      console.error('❌ Error clearing offline data:', error);
    }
  };

  // Clear invalid offline data on app startup
  useEffect(() => {
    const clearInvalidData = async () => {
      try {
        const offlineContactEvents = await AsyncStorage.getItem('offlineContactEvents');
        if (offlineContactEvents) {
          const events = JSON.parse(offlineContactEvents);
          const hasInvalidData = events.some((event: any) => 
            !event.userId || event.userId === 'undefined' || event.userId === 'unknown_user'
          );
          
          if (hasInvalidData) {
            console.log('🧹 Clearing invalid offline data to prevent unnecessary sync UI...');
            await AsyncStorage.removeItem('offlineMonitoringSessions');
            await AsyncStorage.removeItem('offlineContactEvents');
            await AsyncStorage.removeItem('offlineGPSPoints');
            console.log('✅ Invalid offline data cleared');
          }
        }
      } catch (error) {
        console.error('Error checking for invalid offline data:', error);
      }
    };
    
    clearInvalidData();
  }, []);

  if (Platform.OS === 'web') {
    return (
      <View style={[styles.container, styles.webContainer]}>
        <Text style={{ color: Colors[colorScheme ?? 'light'].text }}>
          {pt.webMapNotSupported}
        </Text>
      </View>
    );
  }



  // Check if user is a gestor de zona de caça
  const isGestorZonaCaca = userRole === 'gestor_caca';

  // If user is a gestor, show zones list instead of map
  if (isGestorZonaCaca) {
    return (
      <SafeAreaView style={styles.container}>
        <SafeSystemBars 
          style="light" 
          translucent={true} 
          backgroundColor="#0996a8"
          navigationBarColor="#0996a8"
        />
        {/* Using consistent system UI configuration with matching navigation bar */}
        <GestorZonesList />
      </SafeAreaView>
    );
  }



  return (
    <SafeAreaView style={styles.container}>
      <SafeSystemBars 
        style="light" 
        translucent={true} 
        backgroundColor="#0996a8"
        navigationBarColor="#0996a8"
      />
      {/* Using consistent system UI configuration with matching navigation bar */}
      
      {!user ? (
        <Animated.View style={[styles.noMapContainer, { opacity: fadeAnim }]}>
          <View style={styles.loadingCard}>
            <View style={styles.logoContainer}>
              <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                <FontAwesome name="map-marker" size={35} color="#0996a8" />
              </Animated.View>
            </View>
            <Text style={styles.noMapText}>A inicializar...</Text>
            <View style={styles.loadingIndicatorContainer}>
              <ActivityIndicator size="small" color="#0996a8" />
            </View>
          </View>
        </Animated.View>
      ) : (!showMap && !forceShowMapOverride) ? (
        <Animated.View style={[styles.noMapContainer, { opacity: fadeAnim }]}>
          <View style={styles.loadingCard}>
            <View style={styles.logoContainer}>
              <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                <FontAwesome name="map-marker" size={35} color="#0996a8" />
              </Animated.View>
            </View>
            <Text style={styles.noMapText}>
              {errorMsg ? errorMsg : (location ? 'A carregar mapa...' : 'A obter localização...')}
            </Text>
            {errorMsg ? (
              <TouchableOpacity
                style={styles.retryButton}
                onPress={retryLocation}
                disabled={isRetryingLocation}>
                {isRetryingLocation ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <FontAwesome name="refresh" size={16} color="#fff" style={styles.retryButtonIcon} />
                    <Text style={styles.retryButtonText}>{pt.retryLocation}</Text>
                  </>
                )}
              </TouchableOpacity>
            ) : (
              <View style={styles.loadingIndicatorContainer}>
                <ActivityIndicator size="small" color="#0996a8" />
              </View>
            )}
            {errorMsg && (
              <TouchableOpacity
                style={styles.skipButton}
                onPress={() => {
                  setShowMap(true);
                  setErrorMsg(null);
                }}>
                <Text style={styles.skipButtonText}>{pt.continueWithoutGPS}</Text>
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>
      ) : (
        (() => {

          return (
        <>
          {permissionDialogShown ? (
            // Show loading while permission dialog is active to prevent Google Maps initialization conflicts
            <View style={styles.mapLoadingContainer}>
              <View style={styles.loadingCard}>
                <View style={styles.logoContainer}>
                  <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                    <FontAwesome name="map-marker" size={35} color="#0996a8" />
                  </Animated.View>
                </View>
                <Text style={styles.noMapText}>A aguardar permissões...</Text>
                <View style={styles.loadingIndicatorContainer}>
                  <ActivityIndicator size="small" color="#0996a8" />
                </View>
              </View>
            </View>
          ) : (
          <>
          {(() => {

            return null;
          })()}
          <Animated.View style={{ 
            opacity: mapOpacity,
            flex: 1
          }}>

          <MapView
            ref={mapRef}
              style={styles.map}
            initialRegion={{
              latitude: location?.coords?.latitude || DEFAULT_REGION.latitude,
              longitude: location?.coords?.longitude || DEFAULT_REGION.longitude,
              latitudeDelta: location?.coords ? 0.1 : DEFAULT_REGION.latitudeDelta,
              longitudeDelta: location?.coords ? 0.1 : DEFAULT_REGION.longitudeDelta,
            }}
            onMapReady={() => {
              console.log('🗺️ MapView onMapReady called');
              setMapReady(true);
              setTilesLoaded(false); // Reset tiles loaded state for new map instance
              // Set mapFullyLoaded immediately for faster response
              setMapFullyLoaded(true);
              console.log('🗺️ MapView states updated: mapReady=true, mapFullyLoaded=true');
              
              // Force re-render after map is ready to ensure UI updates (only once)
              if (isFirstLoad) {
                setIsFirstLoad(false); // Mark as no longer first load
                setTimeout(() => {
                  setForceRenderKey(prev => prev + 1);
                  console.log('🗺️ MapView force re-render triggered');
                }, 100);
              }
            }}
            onLayout={() => {
              console.log('🗺️ MapView onLayout called');
            }}
            onMapLoaded={() => {
              console.log('🗺️ MapView onMapLoaded called - Google Maps tiles loaded successfully');
              setMapLoadError(false); // Clear any previous errors when tiles load successfully
              setTilesLoaded(true); // Mark that tiles have loaded successfully
              console.log('🗺️ Starting Google Maps fade-in animation');
              // Smooth fade-in effect when tiles are ready
              setTimeout(() => {
                Animated.timing(mapOpacity, {
                  toValue: 1,
                  duration: 300, // Faster since we start from 0.3 opacity
                  useNativeDriver: false, // opacity affects layout, can't use native driver
                }).start(() => {
                  console.log('🗺️ Google Maps fade-in animation completed');
                });
              }, 50); // Faster start
            }}

            onTouchStart={() => {
              
            }}
            onPress={() => {
              
            }}
            onRegionChangeComplete={(region) => {
              // Only update region if animation hasn't completed yet
              if (!hasInitialAnimation) {
              setCurrentRegion({
                latitudeDelta: region.latitudeDelta,
                longitudeDelta: region.longitudeDelta
              });
              } else {
                
              }
            }}
            {...(mapProvider ? { provider: mapProvider } : {})}
            mapType={mapType}
            showsUserLocation={!!location}
            showsMyLocationButton={!!location}
            showsCompass={true}
            rotateEnabled={true}
            scrollEnabled={true}
            zoomEnabled={true}
            loadingEnabled={false}
            // loadingIndicatorColor="#0996a8"
            // loadingBackgroundColor="#ffffff"
          >
            {location?.coords &&
              typeof location.coords.latitude === 'number' &&
              typeof location.coords.longitude === 'number' &&
              !isNaN(location.coords.latitude) &&
              !isNaN(location.coords.longitude) && (
              <Marker
                ref={markerRef}
                coordinate={{
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                }}
                tracksViewChanges={false}
              >
                <Callout tooltip={true}>
                  <View style={styles.calloutContainer}>
                    <Text style={styles.calloutText}>
                      {user?.displayName || 'Utilizador'}
                    </Text>
                  </View>
                </Callout>
              </Marker>
            )}
          </MapView>
          </Animated.View>

          {mapFullyLoaded && showMap && (
            (() => {
              return (
            <>

              
              <View style={[styles.buttonContainer, { 
                bottom: Math.max(60 + insets.bottom + 20, 90) // Dynamic with fallback to 90px minimum
              }]}>
                <TouchableOpacity
                  style={[styles.button, styles.reportButton]}
                  onPress={() => setShowReportModal(true)}>
                  <FontAwesome name="map-marker" size={20} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>{pt.reportar}</Text>
                </TouchableOpacity>
              </View>
              
              {tilesLoaded && (
              <View style={styles.mapControlsContainer}>
                <TouchableOpacity
                  style={styles.mapControlButton}
                  onPress={() => setMapType(mapType === 'standard' ? 'satellite' : 'standard')}>
                  <FontAwesome name={mapType === 'standard' ? 'globe' : 'map'} size={20} color="rgba(0, 0, 0, 0.60)" />
                </TouchableOpacity>
              </View>
              )}

              {/* Show location status indicator */}
              {!location && (
                <View style={styles.locationStatusContainer}>
                  <View style={styles.locationStatusCard}>
                    <FontAwesome name="exclamation-triangle" size={16} color="#ff9500" />
                    <Text style={styles.locationStatusText}>{pt.gpsUnavailable}</Text>
                    <TouchableOpacity onPress={retryLocation} disabled={isRetryingLocation}>
                      {isRetryingLocation ? (
                        <ActivityIndicator size="small" color="#0996a8" />
                      ) : (
                        <FontAwesome name="refresh" size={14} color="#0996a8" />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </>
              );
            })()
          )}

          {(!tilesLoaded && showMap) && (
            (() => {
  
              return (
            <View style={styles.mapLoadingContainer}>
              <View style={styles.loadingCard}>
                <View style={styles.logoContainer}>
                  <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                    <FontAwesome name="map-marker" size={35} color="#0996a8" />
                  </Animated.View>
                </View>
                <Text style={styles.noMapText}>A carregar mapa...</Text>
                <View style={styles.loadingIndicatorContainer}>
                  <ActivityIndicator size="small" color="#0996a8" />
                </View>
              </View>
            </View>
              );
            })()
          )}


          </>
          )}
        </>
          );
        })()
      )}

      {/* Report Modal */}
      <Modal
        visible={showReportModal}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowReportModal(false)}
        statusBarTranslucent={true}
      >
        {userRole === 'colaborador' ? (
          <ColaboradorReport
            onSubmit={async (reportData) => {
              // Handle report submission
              console.log('Report submitted:', reportData);
              setShowReportModal(false);
            }}
            isLoading={false}
            isUploadingImages={false}
            uploadProgress={0}
            showAlert={showAlert}
          />
        ) : userRole === 'tecnico_prorola' ? (
          <TecnicosReport
            onSubmit={async (reportData) => {
              // Handle report submission
              console.log('Report submitted:', reportData);
              setShowReportModal(false);
            }}
            isLoading={false}
            isUploadingImages={false}
            uploadProgress={0}
            showAlert={showAlert}
            onStartMonitoring={async (protocol, startTime, weatherData, observersCount, reportName, selectedTrajectory) => {
              console.log('🔄 Index.tsx: onStartMonitoring called, starting monitoring session');
      
              
              // Save monitoring session to AsyncStorage before navigating
              try {
                const sessionData = {
                  protocol,
                  startTime: startTime.toISOString(),
                  userId: user?.uid,
                  weatherData: weatherData || null,
                  observersCount: observersCount || 1,
                  reportName: reportName || null,
                  selectedTrajectory: selectedTrajectory || null,
                };
                
                await AsyncStorage.setItem('activeMonitoringSession', JSON.stringify(sessionData));

              } catch (error) {
                console.error('Error saving monitoring session from index:', error);
              }
              
              setShowReportModal(false);
              router.push('/report');
            }}
            onCancel={() => setShowReportModal(false)}
          />
        ) : null}
      </Modal>

      <CustomAlert
        visible={isVisible}
        type={config.type}
        title={config.title}
        message={config.message}
        onClose={hideAlert}
        onConfirm={config.onConfirm}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  map: {
    flex: 1,
    marginTop: -25,
  },
  mapLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    zIndex: 1000,
    elevation: 1000,
  },
  noMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0996a8',
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    width: '85%',
    maxWidth: 340,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logoContainer: {
    marginBottom: 16,
  },
  noMapText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
  loadingIndicatorContainer: {
    marginTop: 16,
  },
  buttonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    gap: 10,
    zIndex: 1000, // Ensure button stays above other elements
  },
  button: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  reportButton: {
    backgroundColor: '#0996a8',
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  webContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapControlsContainer: {
    position: 'absolute',
    right: 10,
    top: 60,
    backgroundColor: 'transparent',
    alignItems: 'center',
  },
  mapControlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    width: 40,
    height: 40,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  retryButton: {
    backgroundColor: '#0996a8',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  retryButtonIcon: {
    marginRight: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  skipButton: {
    backgroundColor: 'transparent',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#0996a8',
    marginTop: 12,
  },
  skipButtonText: {
    color: '#0996a8',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'center',
  },
  locationStatusContainer: {
    position: 'absolute',
    top: 60,
    left: 10,
    right: 10,
    zIndex: 1000,
  },
  locationStatusCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9500',
  },
  locationStatusText: {
    color: '#ff9500',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    marginLeft: 8,
  },

  calloutContainer: {
    backgroundColor: '#f1f8ff',
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 60,
    maxWidth: 150,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.12)',
 
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  calloutText: {
    color: '#676667',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    letterSpacing: 0,
  },

});
