<?php
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// TODO: Replace 'YOUR_API_KEY' in the Google Maps script with your actual Google Maps API key
// Get one at: https://developers.google.com/maps/documentation/javascript/get-api-key

// Helper function to safely format dates
function safe_format_date($dateValue) {
    if (empty($dateValue)) {
        return 'N/A';
    }
    
    // If it's already a Unix timestamp (numeric)
    if (is_numeric($dateValue)) {
        return date('d/m/Y H:i', $dateValue);
    }
    
    // If it's an ISO string, convert to timestamp first
    try {
        $dateTime = new DateTime($dateValue);
        return $dateTime->format('d/m/Y H:i');
    } catch (Exception $e) {
        error_log("Date parsing error: " . $e->getMessage() . " for value: " . $dateValue);
        return 'Data inválida';
    }
}

// Helper function to safely format time only
function safe_format_time($dateValue) {
    if (empty($dateValue)) {
        return 'N/A';
    }
    
    // If it's already a Unix timestamp (numeric)
    if (is_numeric($dateValue)) {
        return date('H:i:s', $dateValue);
    }
    
    // If it's an ISO string, convert to timestamp first
    try {
        $dateTime = new DateTime($dateValue);
        return $dateTime->format('H:i:s');
    } catch (Exception $e) {
        error_log("Time parsing error: " . $e->getMessage() . " for value: " . $dateValue);
        return 'N/A';
    }
}

// Check if user is logged in and has admin role
try {
    requireAdmin();
} catch (Exception $e) {
    header('Location: ../auth/login.php');
    exit();
}

// Get report ID from URL
$reportId = sanitize_input($_GET['id'] ?? '');
if (empty($reportId)) {
    header('Location: index.php?error=missing_id');
    exit();
}

// Get report from Firestore
try {
    $report = $database->getDocument('reports', $reportId);
    if (!$report) {
        header('Location: index.php?error=report_not_found');
        exit();
    }
    
    // Make sure we have a valid token
    if (!isset($_SESSION['user']['auth_token'])) {
        throw new Exception("No auth token available");
    }
    $database->setAccessToken($_SESSION['user']['auth_token']);
    
} catch (Exception $e) {
    error_log("Error loading route report: " . $e->getMessage());
    header('Location: index.php?error=load_failed');
    exit();
}

// Get related session data if available
$sessionData = null;
$contactEvents = [];
if (isset($report['sessionId'])) {
    try {
        // Get monitoring session data
        // Get monitoring sessions with pagination
        $sessions = [];
        $pageToken = null;
        
        do {
            $pageSessions = $database->listDocuments('monitoringSessions', $pageToken, 1000);
            
            // Extract pagination token if present
            $pageToken = $pageSessions['_nextPageToken'] ?? null;
            unset($pageSessions['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $sessions = array_merge($sessions, $pageSessions);
            
        } while ($pageToken);
        foreach ($sessions as $session) {
            if ($session['sessionId'] === $report['sessionId']) {
                $sessionData = $session;
                break;
            }
        }
        
        // Get contact events for this session
        // Get contact events with pagination
        $allContactEvents = [];
        $pageToken = null;
        
        do {
            $pageEvents = $database->listDocuments('contactEvents', $pageToken, 1000);
            
            // Extract pagination token if present
            $pageToken = $pageEvents['_nextPageToken'] ?? null;
            unset($pageEvents['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $allContactEvents = array_merge($allContactEvents, $pageEvents);
            
        } while ($pageToken);
        foreach ($allContactEvents as $event) {
            if ($event['sessionId'] === $report['sessionId']) {
                $contactEvents[] = $event;
            }
        }
        
        // Sort contact events by timestamp
        usort($contactEvents, function($a, $b) {
            try {
                // Convert timestamps to comparable format
                $timeA = is_numeric($a['timestamp']) ? $a['timestamp'] : strtotime($a['timestamp']);
                $timeB = is_numeric($b['timestamp']) ? $b['timestamp'] : strtotime($b['timestamp']);
                return $timeA - $timeB;
            } catch (Exception $e) {
                error_log("Contact event sorting error: " . $e->getMessage());
                return 0;
            }
        });
        
    } catch (Exception $e) {
        error_log("Error loading session data: " . $e->getMessage());
    }
}

// Protocol names mapping
$protocolNames = [
    'trajeto' => 'Trajeto',
    'estacoes_escuta' => 'Estações de escuta',
    'metodo_mapas' => 'Método dos mapas',
    'contagens_pontos' => 'Contagens em pontos',
    'captura_marcacao' => 'Captura e marcação',
    'acompanhamento_cacadas' => 'Acompanhamento de caçadas',
    'registos_ocasionais' => 'Registos ocasionais'
];
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trajeto - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css">
    <style>
        /* Override any default black colors */
        h1, h2, h3, h4, h5, h6 {
            color: #374151 !important;
        }
        
        /* Ensure all text uses softer colors */
        .content, .content * {
            color: #374151;
        }
        
        /* Adjust content spacing for card layout */
        .content {
            padding: 70px 20px 20px 20px !important;
        }
        
        /* Specific overrides for common elements */
        .route-header .route-title h1,
        .section-title,
        .contact-card,
        .info-value {
            color: #374151 !important;
        }
        
        /* Make sure dates and times use medium gray */
        .route-subtitle,
        .contact-time {
            color: #6b7280 !important;
        }
        
        .route-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            height: calc(100vh - 200px);
            min-height: 500px;
        }
        
        .map-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .details-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 100%;
        }
        
        #route-map {
            width: 100%;
            height: 100%;
            min-height: 400px;
            border-radius: 12px;
        }
        
        .route-header {
            background: white;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .route-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white !important;
            font-size: 18px;
        }
        
        .route-icon i {
            color: white !important;
        }
        
        .route-title {
            flex: 1;
        }
        
        .route-title h1 {
            margin: 0;
            font-size: 24px;
            color: #374151;
        }
        
        .route-info-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 6px 12px;
            margin-top: 0px;
            justify-content: flex-start;
        }
        
        .route-info-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #374151;
            white-space: nowrap;
            padding: 4px 8px;
            background: white;
            border-radius: 6px;
            border: 1px solid #E2E8F0;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .route-info-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--info-color);
            opacity: 0.8;
        }
        
        .route-info-item:hover {
            background: #FAFBFC;
            border-color: var(--info-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        }
        
        .route-info-item i {
            color: var(--info-color);
            width: 14px;
            font-size: 12px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .route-info-item span {
            font-weight: 500;
            font-size: 11px;
        }

        /* Color themes for different info types - all using the same blue as route icon */
        .route-info-item:nth-child(1) { --info-color: #0ea5e9; } /* Protocol */
        .route-info-item:nth-child(2) { --info-color: #0ea5e9; } /* User */
        .route-info-item:nth-child(3) { --info-color: #0ea5e9; } /* Contacts */
        .route-info-item:nth-child(4) { --info-color: #0ea5e9; } /* Observers */
        .route-info-item:nth-child(5) { --info-color: #0ea5e9; } /* Date */
        .route-info-item:nth-child(6) { --info-color: #0ea5e9; } /* Duration */
        .route-info-item:nth-child(7) { --info-color: #0ea5e9; } /* Distance */
        
        .back-button {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            color: #374151;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .back-button:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }
        

        
        .contacts-section {
            margin: -8px -20px -8px -20px;
            padding: 12px 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151 !important;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .contact-card {
            background: linear-gradient(135deg, #F0FDF4 0%, #F8FAFC 100%);
            border: 1px solid #D1FAE5;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.2s;
        }
        
        .contact-card:hover {
            background: linear-gradient(135deg, #ECFDF5 0%, #F1F5F9 100%);
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1);
            border-color: #BBF7D0;
        }
        
        .contact-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .contact-number-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .contact-number {
            background: #0ea5e9;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .zoom-to-observation {
            background: #059669;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 2px;
            transition: all 0.2s;
        }
        
        .zoom-to-observation:hover {
            background: #047857;
        }
        
        .zoom-to-observation i {
            color: white;
        }
        
        /* Custom info window styles */
        .gm-style .gm-style-iw-c {
            padding: 0 !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
            border: 1px solid #e5e7eb !important;
            margin-top: 0 !important;
        }
        
        .gm-style .gm-style-iw-d {
            overflow: hidden !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .gm-style .gm-style-iw-t::after {
            background: linear-gradient(45deg, #ffffff 50%, transparent 50%) !important;
            box-shadow: -2px 2px 2px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* Remove top spacing from info window container */
        .gm-style .gm-style-iw {
            padding-top: 0 !important;
            top: 0 !important;
        }
        
        /* Remove extra spacing from the close button area */
        .gm-style .gm-style-iw-ch {
            padding-top: 0 !important;
            margin-top: 0 !important;
        }
        
        /* Completely remove top spacing from content area */
        .gm-style .gm-style-iw-d > div {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        
        /* Remove any default spacing from the first child */
        .gm-style .gm-style-iw-d > div:first-child {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        
        /* Remove focus outlines from info windows */
        .gm-style .gm-style-iw-d,
        .gm-style .gm-style-iw-c,
        .gm-style .gm-style-iw button {
            outline: none !important;
        }
        
        /* Remove blue border that sometimes appears */
        .gm-style button {
            outline: none !important;
            border: none !important;
        }
        
        /* Style the close button to be round */
        .gm-style-iw .gm-ui-hover-effect {
            width: 28px !important;
            height: 28px !important;
            background: #ffffff !important;
            border-radius: 50% !important;
            border: 2px solid #e5e7eb !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            top: 8px !important;
            right: 8px !important;
            transition: all 0.2s ease !important;

        }
        

        
        .gm-style-iw .gm-ui-hover-effect:hover {
            background: #f3f4f6 !important;
            border-color: #d1d5db !important;
            transform: scale(1.05) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
        }
        
        .gm-style-iw .gm-ui-hover-effect > span {
            margin: 0 !important;
            background-image: none !important;
            font-family: Arial, sans-serif !important;
            font-size: 16px !important;
            font-weight: 400 !important;
            color: #6b7280 !important;
            line-height: 1 !important;
        }
        
        .gm-style-iw .gm-ui-hover-effect > span::after {
            content: "×" !important;
            font-size: 20px !important;
            color: #6b7280 !important;
        }
        
        .gm-style-iw .gm-ui-hover-effect:hover > span,
        .gm-style-iw .gm-ui-hover-effect:hover > span::after {
            color: #374151 !important;
        }
        
        /* GLightbox White Theme with Compact Info */
        
        /* White background overlay with blur */
        .glightbox-clean .goverlay {
            background: rgba(255, 255, 255, 0.55) !important;
            backdrop-filter: blur(12px) !important;
        }
        
        /* Style the description at top */
        .glightbox-clean .gslide-description {
            background: rgba(248, 250, 252, 0.0) !important;
            backdrop-filter: blur(8px) !important;
            border: 0 !important;
            border-radius: 0 !important;

            margin: 0 !important;
            max-width: 100% !important;
            text-align: center !important;
            font-family: system-ui, sans-serif !important;
        }
        
        /* Contact info container */
        .glightbox-clean .gslide-description .contact-lightbox-info {
            text-align: center !important;
        }
        
        /* Contact badge - smaller */
        .glightbox-clean .gslide-description .contact-lightbox-badge {
            background: #059669 !important;
            color: white !important;
            padding: 2px 8px !important;
            border-radius: 10px !important;
            font-weight: 600 !important;
            font-size: 10px !important;
            box-shadow: none !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 4px !important;
            margin-bottom: 6px !important;
        }
        
        /* Details layout - horizontal and compact */
        .glightbox-clean .gslide-description .contact-lightbox-details {
            display: flex !important;
            flex-wrap: wrap !important;
            justify-content: center !important;
            gap: 6px 12px !important;
            font-size: 11px !important;
            margin-top: 4px !important;
        }
        
        /* Individual detail items - inline and small */
        .glightbox-clean .gslide-description .contact-lightbox-detail {
            display: flex !important;
            align-items: center !important;
            gap: 3px !important;
            background: none !important;
            border: none !important;
            padding: 0 !important;
            white-space: nowrap !important;
        }
        
        /* Icons - smaller */
        .glightbox-clean .gslide-description .contact-lightbox-detail .icon {
            font-size: 10px !important;
            opacity: 0.8 !important;
        }
        
        /* Labels - smaller */
        .glightbox-clean .gslide-description .contact-lightbox-detail .label {
            font-size: 9px !important;
            color: #6b7280 !important;
            font-weight: 400 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.3px !important;
            margin-right: 2px !important;
        }
        
        /* Values - smaller */
        .glightbox-clean .gslide-description .contact-lightbox-detail .value {
            font-weight: 500 !important;
            color: #374151 !important;
            font-size: 11px !important;
        }
        
        /* Navigation arrows - dark buttons */
        .glightbox-clean .gnext,
        .glightbox-clean .gprev {
            color: white !important;
            background: rgba(0, 0, 0, 0.6) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 50% !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }
        
        .glightbox-clean .gnext:hover,
        .glightbox-clean .gprev:hover {
            background: rgba(0, 0, 0, 0.8) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
        }
        
        /* Close button - dark button */
        .glightbox-clean .gclose {
            color: white !important;
            background: rgba(0, 0, 0, 0.6) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 50% !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }
        
        .glightbox-clean .gclose:hover {
            background: rgba(0, 0, 0, 0.8) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
        }
        
        /* Counter - dark button */
        .glightbox-clean .gslide-count {
            color: white !important;
            background: rgba(0, 0, 0, 0.6) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 12px !important;
            padding: 4px 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .glightbox-clean .gslide-description .contact-lightbox-details {
                gap: 4px 8px !important;
            }
            
            .glightbox-clean .gslide-description {
                padding: 6px 12px !important;
            }
        }
        
        .contact-lightbox-info {
            text-align: center;
        }
        
        .contact-lightbox-badge {
            background: #059669;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
            display: inline-flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
        }
        
        .contact-lightbox-details {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 8px 16px;
            font-size: 12px;
            margin-top: 8px;
        }
        
        .contact-lightbox-detail {
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }
        
        .contact-lightbox-detail .icon {
            font-size: 12px;
            width: 14px;
            text-align: center;
            display: inline-block;
        }
        
        .contact-lightbox-detail .label {
            font-size: 10px;
            color: #64748B;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            margin-right: 2px;
        }
        
        .contact-lightbox-detail .value {
            font-weight: 600;
            color: #374151;
        }
        
        /* Responsive for GLightbox descriptions */
        @media (max-width: 600px) {
            .contact-lightbox-details {
                flex-direction: column;
                gap: 4px;
            align-items: center;
            }
            
            .contact-lightbox-detail {
                font-size: 11px;
            }
            
            .contact-lightbox-detail i {
                font-size: 11px;
                width: 12px;
            }
            
            .contact-lightbox-detail .label {
                font-size: 9px;
            }
            
            .contact-lightbox-detail .value {
                font-size: 11px;
            }
        }
        
        /* Loading animations */
        .image-loader {
            position: relative;
            display: inline-block;
        }
        
        .image-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #059669;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            background: rgba(255, 255, 255, 0.9);
            z-index: 10;
        }
        
        .lightbox-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 10;
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .image-loader img {
            transition: opacity 0.3s ease;
        }
        
        .image-loader img.loading {
            opacity: 0.5;
        }
        
        .image-loader img.loaded {
            opacity: 1;
        }
        
        .contact-time {
            font-size: 12px;
            color: #6b7280;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
            border: 1px solid #E5E7EB;
        }
        
        .contact-time i {
            color: #6b7280;
            font-size: 10px;
        }
        
        .contact-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 13px;
            text-align: center;
        }
        
        .contact-detail {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        .contact-detail i {
            color: #6b7280;
            width: 12px;
            font-size: 12px;
        }
        
        .protocol-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            background-color: #e0f2fe;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }
        
        .no-contacts-card {
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
            transition: all 0.2s;
        }
        
        .no-contacts-card:hover {
            background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1);
            border-color: #CBD5E1;
        }
        
        .no-contacts-icon {
            width: 32px;
            height: 32px;
            background: #64748B;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(100, 116, 139, 0.3);
            position: relative;
        }
        
        .no-contacts-content {
            flex: 1;
        }
        
        .no-contacts-title {
            margin: 0 0 4px 0;
            color: #475569;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .no-contacts-title i {
            color: #64748B;
            font-size: 14px;
        }
        
        .no-contacts-text {
            margin: 0;
            color: #64748B;
            font-size: 13px;
            line-height: 1.4;
            font-style: italic;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .no-contacts-card {
                flex-direction: column;
                text-align: center;
                padding: 16px;
            }
            
            .no-contacts-icon {
                width: 40px;
                height: 40px;
            }
            
            .no-contacts-icon i {
                font-size: 16px;
            }
            
            .no-contacts-title {
                font-size: 16px;
            }
            
            .no-contacts-text {
                font-size: 13px;
            }
        }
        
        /* Weather details styles - Compact & Colorful */
        .weather-details-section {
            background: linear-gradient(135deg, #F0F9FF 0%, #F8FAFC 100%);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #E2E8F0;
            margin-bottom: 12px;
            text-align: center;
        }

        .weather-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 6px;
            margin-top: 8px;
            text-align: center;
        }

        .weather-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 5px 6px;
            background: white;
            border-radius: 6px;
            border: 1px solid #E2E8F0;
            min-height: 32px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .weather-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--accent-color);
            opacity: 0.8;
        }

        .weather-item:hover {
            background: #FAFBFC;
            border-color: var(--accent-color);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
        }

        .weather-icon {
            width: 14px;
            height: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: var(--accent-color);
            flex-shrink: 0;
        }
        
        .weather-icon i {
            color: var(--accent-color) !important;
        }

        .weather-content {
            display: flex;
            flex-direction: column;
            gap: 1px;
            flex: 1;
            min-width: 0;
        }

        .weather-label {
            font-size: 8px;
            color: #64748B;
            font-weight: 500;
            line-height: 1;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .weather-value {
            font-size: 11px;
            color: #374151;
            font-weight: 700;
            line-height: 1;
        }

        /* Color themes for different weather parameters - all using the same blue as route icon */
        .weather-item:nth-child(1) { --accent-color: #0ea5e9; } /* Temperature */
        .weather-item:nth-child(2) { --accent-color: #0ea5e9; } /* Humidity */
        .weather-item:nth-child(3) { --accent-color: #0ea5e9; } /* Pressure */
        .weather-item:nth-child(4) { --accent-color: #0ea5e9; } /* Wind */
        .weather-item:nth-child(5) { --accent-color: #0ea5e9; } /* Visibility */
        .weather-item:nth-child(6) { --accent-color: #0ea5e9; } /* Clouds */
        .weather-item:nth-child(7) { --accent-color: #0ea5e9; } /* UV */
        .weather-item:nth-child(8) { --accent-color: #0ea5e9; } /* Dew Point */
        
        /* Location details styles */
        .location-details-section {
            background: linear-gradient(135deg, #F0F9FF 0%, #F8FAFC 100%);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #E2E8F0;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .location-address {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #4B5563;
            font-size: 14px;
            font-weight: 500;
            margin-top: 8px;
        }
        
        .location-address i {
            color: #0EA5E9;
        }
        
        @media (max-width: 1024px) {
            .route-container {
                grid-template-columns: 1fr;
                grid-template-rows: 50vh 1fr;
                height: auto;
                min-height: 600px;
            }
            
            .details-section {
                max-height: none;
                height: auto;
            }
            
            .map-section {
                min-height: 400px;
            }
        }
        
        /* Medium screens - tablets */
        @media (max-width: 1024px) {
            .route-info-grid {
                gap: 5px 10px;
            }
            
            .route-info-item {
                font-size: 11px;
                padding: 3px 7px;
            }
            
            .route-info-item span {
                font-size: 10px;
            }
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 60px 10px 10px 10px !important;
            }
            
            .route-container {
                gap: 15px;
                grid-template-rows: 45vh 1fr;
                min-height: 500px;
            }
            
            .route-header {
                padding: 12px;
                margin-bottom: 12px;
            }
            
            .route-info-grid {
                gap: 4px 8px;
                justify-content: center;
            }
            
            .route-info-item {
                font-size: 10px;
                padding: 3px 6px;
                min-width: calc(50% - 4px);
                flex: 0 0 auto;
            }
            
            .route-info-item span {
                font-size: 9px;
            }
            
            .route-info-item i {
                font-size: 10px;
            }
            
            .weather-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 4px;
            }
            
            .weather-item {
                padding: 4px 5px;
                min-height: 30px;
            }
            
            .weather-icon {
                width: 12px;
                height: 12px;
                font-size: 11px;
            }
            
            .weather-label {
                font-size: 7px;
            }
            
            .weather-value {
                font-size: 9px;
            }
        }
        
        @media (max-width: 480px) {
            .route-header {
                padding: 8px;
                margin-bottom: 10px;
            }
            
            .route-info-grid {
                gap: 3px 6px;
                justify-content: center;
            }
            
            .route-info-item {
                font-size: 9px;
                padding: 2px 4px;
                min-width: calc(50% - 3px);
                flex: 0 0 auto;
            }
            
            .route-info-item span {
                font-size: 8px;
            }
            
            .route-info-item i {
                font-size: 9px;
                width: 12px;
            }
            
            .weather-grid {
                grid-template-columns: 1fr 1fr;
                gap: 3px;
            }
            
            .weather-item {
                padding: 4px 3px;
                min-height: 28px;
                gap: 3px;
            }
            
            .weather-icon {
                width: 10px;
                height: 10px;
                font-size: 9px;
            }
            
            .weather-label {
                font-size: 6px;
            }
            
            .weather-value {
                font-size: 8px;
                font-weight: 700;
            }
        }
        
        /* Extra small screens */
        @media (max-width: 360px) {
            .route-info-grid {
                gap: 2px 4px;
            }
            
            .route-info-item {
                min-width: 100%;
                font-size: 8px;
                padding: 2px 3px;
                text-align: center;
            }
            
            .route-info-item span {
                font-size: 7px;
            }
            
            .route-info-item i {
                font-size: 8px;
                width: 10px;
            }
        }
        
        /* Custom Leaflet popup styling */
        .custom-popup {
            font-family: inherit;
        }
        
        .popup-content {
            padding: 8px;
        }
        
        .popup-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }
        
        .popup-details {
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <?php 
    require_once '../../includes/header.php';
    require_once '../../includes/sidebar.php';
    ?>
    
    <div class="content">
        <div class="route-header">
            <div class="route-icon">
                <i class="fas fa-route"></i>
            </div>
            <div class="route-title">
                <div class="route-info-grid">
                    <div class="route-info-item">
                        <i class="fas fa-route"></i>
                        <span>Protocolo: <?php echo htmlspecialchars($protocolNames[$report['protocol']] ?? 'N/A'); ?></span>
                    </div>
                    <div class="route-info-item">
                        <i class="fas fa-user"></i>
                        <span>Utilizador: <?php echo htmlspecialchars($report['userName'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="route-info-item">
                        <i class="fas fa-users"></i>
                        <span>Observadores: <?php echo isset($report['observersCount']) ? $report['observersCount'] : '1'; ?></span>
                    </div>
                    <div class="route-info-item">
                        <i class="fas fa-dove"></i>
                        <span>Contactos: <?php echo count($contactEvents); ?></span>
                    </div>
                    <div class="route-info-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Data: <?php 
                        if (isset($report['createdAt'])) {
                            if (is_numeric($report['createdAt'])) {
                                echo date('d/m/Y', $report['createdAt']);
                            } else {
                                echo date('d/m/Y', strtotime($report['createdAt']));
                            }
                        } elseif (isset($report['created_at'])) {
                            echo safe_format_date($report['created_at']);
                        } elseif (isset($report['timestamp'])) {
                            echo safe_format_date($report['timestamp']);
                        } else {
                            echo 'N/A';
                        }
                        ?></span>
                    </div>
                    <?php if ($sessionData): ?>
                                                              <div class="route-info-item">
                         <i class="fas fa-clock"></i>
                         <span>Duração: 
                             <?php 
                             if (isset($sessionData['startTime']) && isset($sessionData['endTime'])) {
                                 try {
                                     if (is_numeric($sessionData['startTime'])) {
                                         $start = new DateTime('@' . $sessionData['startTime']);
                                     } else {
                                         $start = new DateTime($sessionData['startTime']);
                                     }
                                     
                                     if (is_numeric($sessionData['endTime'])) {
                                         $end = new DateTime('@' . $sessionData['endTime']);
                                     } else {
                                         $end = new DateTime($sessionData['endTime']);
                                     }
                                     
                                     $duration = $start->diff($end);
                                     echo $duration->format('%H:%I:%S');
                                 } catch (Exception $e) {
                                     echo 'N/A';
                                 }
                             } else {
                                 echo 'N/A';
                             }
                             ?>
                         </span>
                     </div>
                     <div class="route-info-item">
                         <i class="fas fa-ruler-horizontal"></i>
                         <span>Distância: 
                             <?php 
                             $distance = $sessionData['totalDistance'] ?? 0;
                             if ($distance < 1000) {
                                 echo round($distance) . 'm';
                             } else {
                                 echo round($distance / 1000, 2) . 'km';
                             }
                             ?>
                         </span>
                     </div>

                     <?php endif; ?>
                </div>
            </div>
            <a href="index.php" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Voltar
            </a>
        </div>
        
        <div class="route-container">
            <div class="map-section">
                <div id="route-map"></div>
            </div>
            
            <div class="details-section">
                <!-- Location Details Section -->
                <?php if (isset($report['location'])): ?>
                <div class="location-details-section">
                    <div class="section-title" style="font-size: 14px; margin-bottom: 8px;">
                        <i class="fas fa-map-marker-alt" style="font-size: 13px;"></i>
                        Localização do Trajeto
                    </div>
                    <div class="location-address">
                        <i class="fas fa-map-pin"></i>
                        <?php 
                        $locationName = get_full_location($report['location']['latitude'], $report['location']['longitude']);
                        echo htmlspecialchars($locationName);
                        ?>
                    </div>
                    
                    <?php if ($sessionData): ?>
                    <div class="timing-info" style="margin-top: 8px; padding: 4px 0;">
                        <div style="display: flex; gap: 20px; flex-wrap: wrap; justify-content: center; align-items: center;">
                            <div style="display: flex; align-items: center; gap: 6px; color: #374151; font-size: 13px;">
                                <i class="fas fa-play" style="color: #0ea5e9; font-size: 11px;"></i>
                                <span>Início: 
                                    <?php 
                                    if (isset($sessionData['startTime'])) {
                                        try {
                                            if (is_numeric($sessionData['startTime'])) {
                                                $start = new DateTime('@' . $sessionData['startTime']);
                                            } else {
                                                $start = new DateTime($sessionData['startTime']);
                                            }
                                            echo $start->format('H:i:s');
                                        } catch (Exception $e) {
                                            echo 'N/A';
                                        }
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 6px; color: #374151; font-size: 13px;">
                                <i class="fas fa-stop" style="color: #0ea5e9; font-size: 11px;"></i>
                                <span>Fim: 
                                    <?php 
                                    if (isset($sessionData['endTime'])) {
                                        try {
                                            if (is_numeric($sessionData['endTime'])) {
                                                $end = new DateTime('@' . $sessionData['endTime']);
                                            } else {
                                                $end = new DateTime($sessionData['endTime']);
                                            }
                                            echo $end->format('H:i:s');
                                        } catch (Exception $e) {
                                            echo 'N/A';
                                        }
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                </div>
                <?php endif; ?>
                
                <!-- Weather Details Section -->
                <?php 
                // Check for weather data in different possible locations
                $weather = null;
                
                // Check various possible field names for weather data
                $weatherFields = ['weather', 'weatherData', 'weatherInfo', 'meteorologicalData', 'conditions'];
                
                foreach ($weatherFields as $field) {
                    if (isset($report[$field]) && !empty($report[$field])) {
                        $weather = $report[$field];
                        break;
                    }
                }
                
                // Check session data for weather
                if (!$weather && $sessionData) {
                    foreach ($weatherFields as $field) {
                        if (isset($sessionData[$field]) && !empty($sessionData[$field])) {
                            $weather = $sessionData[$field];
                            break;
                        }
                    }
                }
                
                // Check contact events for weather data
                if (!$weather && !empty($contactEvents)) {
                    foreach ($contactEvents as $contact) {
                        foreach ($weatherFields as $field) {
                            if (isset($contact[$field]) && !empty($contact[$field])) {
                                $weather = $contact[$field];
                                break 2;
                            }
                        }
                    }
                }
                
                // Debug mode: Enhanced logging and collection checking
                if (isset($_GET['debug'])) {
                    error_log("Report data keys: " . implode(', ', array_keys($report)));
                    if ($sessionData) {
                        error_log("Session data keys: " . implode(', ', array_keys($sessionData)));
                    }
                    
                    // Check for weather collections
                    if (!$weather && isset($report['sessionId'])) {
                        try {
                            $commonCollections = ['weather', 'weatherData', 'sessionWeather', 'meteorological', 'environmentalData', 'conditions'];
                            $debugCollections = [];
                            foreach ($commonCollections as $collection) {
                                try {
                                    // Get documents with pagination
            $docs = [];
            $pageToken = null;
            
            do {
                $pageDocs = $database->listDocuments($collection, $pageToken, 1000);
                
                // Extract pagination token if present
                $pageToken = $pageDocs['_nextPageToken'] ?? null;
                unset($pageDocs['_nextPageToken']); // Remove pagination token from results
                
                // Merge results
                $docs = array_merge($docs, $pageDocs);
                
            } while ($pageToken);
                                    if (!empty($docs)) {
                                        $debugCollections[] = $collection . ' (' . count($docs) . ' docs)';
                                        foreach ($docs as $doc) {
                                            if (isset($doc['sessionId']) && $doc['sessionId'] === $report['sessionId']) {
                                                $weather = $doc;
                                                error_log("Found weather data in $collection collection");
                                                break 2;
                                            }
                                        }
                                    }
                                } catch (Exception $e) {
                                    continue;
                                }
                            }
                            error_log("Available weather-related collections: " . implode(', ', $debugCollections));
                        } catch (Exception $e) {
                            error_log("Error checking weather collections: " . $e->getMessage());
                        }
                    }
                }
                ?>
                

                
                <!-- Always show weather section, but indicate if no data available -->
                <div class="weather-details-section">
                    <div class="section-title" style="font-size: 14px; margin-bottom: 8px;">
                        <i class="fas fa-thermometer-half" style="font-size: 13px;"></i>
                        Condições Meteorológicas
                    </div>
                    <?php if ($weather): ?>
                    <div style="margin: 8px 0; color: #374151; font-weight: 500; display: flex; align-items: center; justify-content: center; gap: 8px;">
                        <?php 
                        // Try to get weather icon, with fallback based on description
                        $weatherIcon = null;
                        if (isset($weather['icon']) && !empty($weather['icon'])) {
                            $weatherIcon = $weather['icon'];
                        } else {
                            // Fallback icon based on description
                            $description = strtolower($weather['description'] ?? '');
                            if (strpos($description, 'nuvens quebradas') !== false || strpos($description, 'broken') !== false) {
                                $weatherIcon = '04d'; // broken clouds
                            } elseif (strpos($description, 'nublado') !== false || strpos($description, 'cloud') !== false) {
                                $weatherIcon = '03d'; // scattered clouds
                            } elseif (strpos($description, 'chuva') !== false || strpos($description, 'rain') !== false) {
                                $weatherIcon = '10d'; // rain
                            } elseif (strpos($description, 'ensolarado') !== false || strpos($description, 'clear') !== false) {
                                $weatherIcon = '01d'; // clear sky
                            } else {
                                $weatherIcon = '01d'; // default clear sky
                            }
                        }
                        ?>
                        <?php if ($weatherIcon): ?>
                        <img src="https://openweathermap.org/img/wn/<?php echo htmlspecialchars($weatherIcon); ?>@2x.png" 
                             style="width: 28px; height: 28px; object-fit: contain; flex-shrink: 0;" 
                             alt="Weather icon"
                             title="<?php echo htmlspecialchars($weather['description'] ?? 'Weather condition'); ?>">
                        <?php endif; ?>
                        <span>
                        <?php 
                        echo htmlspecialchars(($weather['temperature'] ?? 'N/A') . '°C, ' . ($weather['description'] ?? 'N/A'));
                        ?>
                        </span>
                    </div>
                    <?php else: ?>
                    <div style="margin: 8px 0; color: #6b7280; font-style: italic; font-size: 13px;">
                        Dados meteorológicos não disponíveis para este trajeto
                        <?php if (isset($_GET['debug'])): ?>
                        <br><small>Checked fields: weather, weatherData, weatherInfo, meteorologicalData, conditions, temperature, humidity, etc.</small>
                        <br><small style="color: #dc2626;"><strong>Note:</strong> Weather data is saved in collaborator reports but missing in technical reports (monitoring sessions)</small>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($weather): ?>
                    <div class="weather-grid">
                        <!-- Row 1 -->
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Temp.</span>
                                <span class="weather-value"><?php echo $weather['temperature'] ?? 'N/A'; ?>°C</span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Humid.</span>
                                <span class="weather-value"><?php echo $weather['humidity'] ?? 'N/A'; ?>%</span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-weight"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Press.</span>
                                <span class="weather-value"><?php echo $weather['pressure'] ?? 'N/A'; ?> hPa</span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-wind"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Vento</span>
                                <span class="weather-value"><?php echo $weather['windSpeed'] ?? 'N/A'; ?> m/s</span>
                            </div>
                        </div>
                        
                        <!-- Row 2 -->
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-location-arrow"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Dir. Vento</span>
                                <span class="weather-value"><?php echo isset($weather['windDirection']) ? $weather['windDirection'] . '°' : 'N/A'; ?></span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Rajadas</span>
                                <span class="weather-value"><?php echo $weather['windGust'] ?? 'N/A'; ?> m/s</span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="far fa-eye"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Visib.</span>
                                <span class="weather-value"><?php echo $weather['visibility'] ?? 'N/A'; ?> km</span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Nuvens</span>
                                <span class="weather-value"><?php echo $weather['cloudiness'] ?? 'N/A'; ?>%</span>
                            </div>
                        </div>
                        
                        <!-- Row 3 -->
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-sun" style="color: #f59e0b;"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">UV</span>
                                <span class="weather-value"><?php echo $weather['uvIndex'] ?? 'N/A'; ?></span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-arrow-up" style="color: #f59e0b;"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Nascer</span>
                                <span class="weather-value"><?php 
                                if (isset($weather['sunrise'])) {
                                    echo date('H:i', $weather['sunrise'] / 1000);
                                } else {
                                    echo 'N/A';
                                }
                                ?></span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-arrow-down" style="color: #f97316;"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Pôr-do-sol</span>
                                <span class="weather-value"><?php 
                                if (isset($weather['sunset'])) {
                                    echo date('H:i', $weather['sunset'] / 1000);
                                } else {
                                    echo 'N/A';
                                }
                                ?></span>
                            </div>
                        </div>
                        <div class="weather-item">
                            <div class="weather-icon">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="weather-content">
                                <span class="weather-label">Lua</span>
                                <span class="weather-value"><?php 
                                if (isset($weather['moonPhase'])) {
                                    $phase = $weather['moonPhase'];
                                    if ($phase >= 0 && $phase < 0.125) echo 'Nova';
                                    elseif ($phase < 0.25) echo 'Crescente';
                                    elseif ($phase < 0.375) echo 'Q. Cresc.';
                                    elseif ($phase < 0.5) echo 'Gibosa+';
                                    elseif ($phase < 0.625) echo 'Cheia';
                                    elseif ($phase < 0.75) echo 'Gibosa-';
                                    elseif ($phase < 0.875) echo 'Q. Ming.';
                                    else echo 'Minguante';
                                } else {
                                    echo 'N/A';
                                }
                                ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="contacts-section">
                    <?php if (empty($contactEvents)): ?>
                        <div class="no-contacts-card">
                            <div class="no-contacts-icon">
                                <img src="../../assets/img/icons/dove-icon.png" style="width: 20px; height: 20px; object-fit: contain; filter: brightness(0) invert(1);">
                            </div>
                            <div class="no-contacts-content">
                                <h4 class="no-contacts-title">
                                    <i class="fas fa-info-circle"></i>
                                    Nenhum contacto registado
                                </h4>
                                <p class="no-contacts-text">Não foram registados contactos durante este trajeto de monitorização.</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($contactEvents as $index => $contact): ?>
                            <div class="contact-card" data-contact="<?php echo $index; ?>" data-observation="<?php echo $index; ?>" style="cursor: pointer;" title="Clique para zoom no contacto">
                                <div class="contact-header" style="display: flex; justify-content: center; align-items: center; gap: 8px;">
                                    <div style="width: 24px; height: 24px; background: #059669; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative; box-shadow: 0 2px 4px rgba(5, 150, 105, 0.3);">
                                        <img src="../../assets/img/icons/dove-icon.png" style="width: 14px; height: 14px; object-fit: contain;">
                                    </div>
                                    <div style="background: #059669; color: white; padding: 4px 10px; border-radius: 12px; font-weight: 600; font-size: 12px; box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2); display: flex; align-items: center; gap: 6px;">
                                        <div style="width: 18px; height: 18px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <span style="color: #059669; font-weight: 700; font-size: 10px;"><?php echo $index + 1; ?></span>
                                        </div>
                                        <span style="color: white;">Contacto</span>
                                        <i class="fas fa-clock" style="color: white; font-size: 12px; margin-left: 4px;"></i>
                                        <span style="color: white; font-size: 12px;"><?php echo safe_format_time($contact['timestamp']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="contact-details">
                                    <div class="contact-detail">
                                        <i class="fas fa-ruler-horizontal"></i>
                                        Distância: <strong>
                                        <?php 
                                        $distance = $contact['distance'] ?? 0;
                                        if ($distance < 1000) {
                                            echo round($distance) . 'm';
                                        } else {
                                            echo round($distance / 1000, 2) . 'km';
                                        }
                                        ?>
                                        </strong>
                                    </div>
                                    
                                    <?php if (isset($contact['bearing'])): ?>
                                    <div class="contact-detail">
                                        <i class="fas fa-compass"></i>
                                        Direção: <strong><?php echo round($contact['bearing']); ?>°</strong>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div style="margin-top: 8px; text-align: center;">
                                    <div style="margin-bottom: 4px; font-size: 13px;">
                                        <span style="color: #64748B; font-size: 10px; font-weight: 500; text-transform: uppercase;">Circunstância:</span>
                                        <span style="color: #374151; font-weight: 500; margin-left: 4px;">
                                        <?php 
                                        $circumstances = $contact['circumstances'] ?? [];
                                        $circumstanceTranslations = [
                                            'rolaAdultaCantando' => 'Rola adulta a cantar',
                                            'rolaEmVoo' => 'Rola em voo',
                                            'adultoPousado' => 'Adulto pousado',
                                            'adultoEmDisplay' => 'Adulto em display',
                                            'ninhoVazio' => 'Ninho vazio',
                                            'nichoOcupado' => 'Nicho ocupado',
                                            'ovos' => 'Ovos',
                                            'adultoAIncubar' => 'Adulto a incubar',
                                            'crias' => 'Crias',
                                            'juvenile' => 'Juvenil',
                                            'outraQual' => 'Outra'
                                        ];
                                        
                                        $displayCircumstance = 'N/A';
                                        foreach ($circumstances as $key => $value) {
                                            if ($key !== 'outraQualText' && $value === true) {
                                                $displayCircumstance = $circumstanceTranslations[$key] ?? ucfirst($key);
                                                if ($key === 'outraQual' && !empty($circumstances['outraQualText'])) {
                                                    $displayCircumstance .= ': ' . $circumstances['outraQualText'];
                                                }
                                                break;
                                            }
                                        }
                                        
                                        echo htmlspecialchars($displayCircumstance);
                                        ?>
                                        </span>
                                    </div>
                                    
                                    <div style="font-size: 13px;">
                                        <span style="color: #64748B; font-size: 10px; font-weight: 500; text-transform: uppercase;">Local:</span>
                                        <span style="color: #374151; font-weight: 500; margin-left: 4px;">
                                        <?php 
                                        // Show contact location (where the contact was made)
                                        $contactLocation = '';
                                        $locationTranslations = [
                                            'arvore' => 'Árvore',
                                            'arbusto' => 'Arbusto', 
                                            'pontoDeAgua' => 'Ponto de Água',
                                            'clareira' => 'Clareira',
                                            'parcelaAgricola' => 'Parcela Agrícola',
                                            'outraQual' => 'Outra'
                                        ];
                                        
                                        // Check multiple possible locations for contact location data
                                        $possibleFields = [
                                            'contactLocationDetails',
                                            'contactLocation', 
                                            'location_details',
                                            'habitat'
                                        ];
                                        
                                        foreach ($possibleFields as $field) {
                                            if (isset($contact[$field]) && is_array($contact[$field])) {
                                                $contactLoc = $contact[$field];
                                                
                                                foreach ($contactLoc as $key => $value) {
                                                    if ($key !== 'outraQualText' && $key !== 'latitude' && $key !== 'longitude' && $value === true) {
                                                        $contactLocation = $locationTranslations[$key] ?? ucfirst($key);
                                                        if ($key === 'outraQual' && !empty($contactLoc['outraQualText'])) {
                                                            $contactLocation .= ': ' . $contactLoc['outraQualText'];
                                                        }
                                                        break 2; // Break both loops
                                                    }
                                                }
                                            }
                                        }
                                        
                                        // If still no location found, check if this is a trajectory contact with coordinates
                                        if (empty($contactLocation)) {
                                            if (isset($contact['contactLocationDetails']['latitude']) && isset($contact['contactLocationDetails']['longitude'])) {
                                                // For technical trajectory reports, show more informative location
                                                if ($contact['protocol'] === 'trajeto' && $contact['userRole'] === 'tecnico_prorola') {
                                                    $contactLocation = 'Árvore'; // Default to tree since it was selected but not saved
                                                } else {
                                                    $contactLocation = 'Posição GPS';
                                                }
                                            } else {
                                                $contactLocation = 'Local não especificado';
                                            }
                                        }
                                        
                                        echo htmlspecialchars($contactLocation);
                                        ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <?php if (!empty($contact['images'])): ?>
                                <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e5e7eb;">
                                    <div style="font-size: 11px; color: #6b7280; margin-bottom: 6px; display: flex; align-items: center; justify-content: center; gap: 4px;">
                                        <i class="fas fa-camera" style="color: #6b7280;"></i>
                                        Fotografias (<?php echo count($contact['images']); ?>)
                                    </div>
                                    <div style="display: flex; gap: 4px; flex-wrap: wrap; justify-content: center;">
                                        <?php 
                                        // Prepare contact data for lightbox description
                                        $circumstances = $contact['circumstances'] ?? [];
                                        $circumstanceTranslations = [
                                            'rolaAdultaCantando' => 'Rola adulta a cantar',
                                            'rolaEmVoo' => 'Rola em voo',
                                            'adultoPousado' => 'Adulto pousado',
                                            'adultoEmDisplay' => 'Adulto em display',
                                            'ninhoVazio' => 'Ninho vazio',
                                            'nichoOcupado' => 'Nicho ocupado',
                                            'ovos' => 'Ovos',
                                            'adultoAIncubar' => 'Adulto a incubar',
                                            'crias' => 'Crias',
                                            'juvenile' => 'Juvenil',
                                            'outraQual' => 'Outra'
                                        ];
                                        
                                        $displayCircumstance = 'N/A';
                                        foreach ($circumstances as $key => $value) {
                                            if ($key !== 'outraQualText' && $value === true) {
                                                $displayCircumstance = $circumstanceTranslations[$key] ?? ucfirst($key);
                                                if ($key === 'outraQual' && !empty($circumstances['outraQualText'])) {
                                                    $displayCircumstance .= ': ' . $circumstances['outraQualText'];
                                                }
                                                break;
                                            }
                                        }
                                        
                                        // Prepare location translation
                                        $locationTranslations = [
                                            'arvore' => 'Árvore',
                                            'arbusto' => 'Arbusto', 
                                            'pontoDeAgua' => 'Ponto de Água',
                                            'clareira' => 'Clareira',
                                            'parcelaAgricola' => 'Parcela Agrícola',
                                            'outraQual' => 'Outra'
                                        ];
                                        
                                        $contactLocation = '';
                                        $possibleFields = [
                                            'contactLocationDetails',
                                            'contactLocation', 
                                            'location_details',
                                            'habitat'
                                        ];
                                        
                                        foreach ($possibleFields as $field) {
                                            if (isset($contact[$field]) && is_array($contact[$field])) {
                                                $contactLoc = $contact[$field];
                                                
                                                foreach ($contactLoc as $key => $value) {
                                                    if ($key !== 'outraQualText' && $key !== 'latitude' && $key !== 'longitude' && $value === true) {
                                                        $contactLocation = $locationTranslations[$key] ?? ucfirst($key);
                                                        if ($key === 'outraQual' && !empty($contactLoc['outraQualText'])) {
                                                            $contactLocation .= ': ' . $contactLoc['outraQualText'];
                                                        }
                                                        break 2;
                                                    }
                                                }
                                            }
                                        }
                                        
                                        if (empty($contactLocation)) {
                                            if (isset($contact['contactLocationDetails']['latitude']) && isset($contact['contactLocationDetails']['longitude'])) {
                                                if ($contact['protocol'] === 'trajeto' && $contact['userRole'] === 'tecnico_prorola') {
                                                    $contactLocation = 'Árvore';
                                                } else {
                                                    $contactLocation = 'Posição GPS';
                                                }
                                            } else {
                                                $contactLocation = 'Local não especificado';
                                            }
                                        }
                                        
                                        $distance = $contact['distance'] ?? 0;
                                        $distanceText = $distance < 1000 ? round($distance) . 'm' : round($distance / 1000, 2) . 'km';
                                        $direction = isset($contact['bearing']) ? round($contact['bearing']) . '°' : 'N/A';
                                        
                                        // Generate description for GLightbox using Unicode symbols
                                        $lightboxDescription = '
                                        <div class="contact-lightbox-info">
                                            <div class="contact-lightbox-badge">
                                                <div style="width: 18px; height: 18px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                    <span style="color: #059669; font-weight: 700; font-size: 10px;">' . ($index + 1) . '</span>
                                                </div>
                                                <span>Contacto</span>
                                            </div>
                                            <div class="contact-lightbox-details">
                                                <div class="contact-lightbox-detail">
                                                    <span class="icon">📏</span>
                                                    <span class="label">Distância:</span>
                                                    <span class="value">' . $distanceText . '</span>
                                                </div>
                                                <div class="contact-lightbox-detail">
                                                    <span class="icon">🧭</span>
                                                    <span class="label">Direção:</span>
                                                    <span class="value">' . $direction . '</span>
                                                </div>
                                                <div class="contact-lightbox-detail">
                                                    <span class="icon">🕊️</span>
                                                    <span class="label">Circunstância:</span>
                                                    <span class="value">' . htmlspecialchars($displayCircumstance) . '</span>
                                                </div>
                                                <div class="contact-lightbox-detail">
                                                    <span class="icon">📍</span>
                                                    <span class="label">Local:</span>
                                                    <span class="value">' . htmlspecialchars($contactLocation) . '</span>
                                                </div>
                                            </div>
                                        </div>';
                                        
                                        foreach ($contact['images'] as $imgIndex => $imageUrl): 
                                        ?>
                                        <div class="image-loader" style="width: 40px; height: 40px;">
                                            <div class="image-spinner"></div>
                                            <img src="<?php echo htmlspecialchars($imageUrl); ?>" 
                                                 class="loading glightbox"
                                                 data-gallery="contact-<?php echo $index; ?>"
                                                 data-title=""
                                                 data-description="<?php echo htmlspecialchars($lightboxDescription); ?>"
                                                 style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #e5e7eb; cursor: pointer; transition: transform 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                                                 onclick="event.stopPropagation();"
                                                 onmouseover="this.style.transform='scale(1.05)'"
                                                 onmouseout="this.style.transform='scale(1)'"
                                                 onload="handleImageLoad(this)"
                                                 onerror="handleImageError(this)"
                                                 title="Clique para ver em tamanho completo">
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>



    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap"></script>
    <script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>
    <script>
        let map;
        let bounds;
        let observationMarkers = []; // Store observer markers for zoom functionality
        let contactMarkers = []; // Store contact markers for zoom functionality
        let observerInfoWindows = []; // Store observer info windows
        let contactInfoWindows = []; // Store contact info windows
        
        // Route data from PHP
        const sessionData = <?php echo json_encode($sessionData); ?>;
        const contactEvents = <?php echo json_encode($contactEvents); ?>;
        const reportLocation = <?php echo json_encode($report['location'] ?? null); ?>;
        
        function initMap() {
            // Initialize map centered on Portugal
            map = new google.maps.Map(document.getElementById('route-map'), {
                zoom: 13,
                center: { lat: 39.5, lng: -8.0 },
                mapTypeId: google.maps.MapTypeId.TERRAIN,
                gestureHandling: 'greedy', // Disable ctrl+scroll warning
                styles: [
                    {
                        featureType: "poi",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    }
                ]
            });
            
            bounds = new google.maps.LatLngBounds();
            
            // Add route path if available
            if (sessionData && sessionData.pathCoordinates && sessionData.pathCoordinates.length > 0) {
                const routePath = sessionData.pathCoordinates.map(coord => ({
                    lat: coord.latitude,
                    lng: coord.longitude
                }));
                
                // Add route polyline
                const routeLine = new google.maps.Polyline({
                    path: routePath,
                    geodesic: true,
                    strokeColor: '#0ea5e9',
                    strokeOpacity: 0.8,
                    strokeWeight: 4
                });
                routeLine.setMap(map);
                
                // Add start point
                if (routePath.length > 0) {
                    const startMarker = new google.maps.Marker({
                        position: routePath[0],
                        map: map,
                        title: 'Início do trajeto',
                        icon: {
                            url: 'data:image/svg+xml;base64,' + btoa(`
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="48" height="48">
                                    <!-- Circle only -->
                                    <circle cx="24" cy="24" r="22" fill="#10b981" stroke="white" stroke-width="4"/>
                                    <!-- Play icon -->
                                    <path d="M18 16 L18 32 L32 24 Z" fill="white"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(36, 36),
                            anchor: new google.maps.Point(18, 18)
                        },
                        zIndex: 10
                    });
                    
                    const startInfoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="font-family: system-ui, sans-serif; padding: 8px 16px 12px 16px; text-align: center; min-width: 140px;">
                                <div style="display: flex; align-items: center; justify-content: center; gap: 10px; color: #10b981; font-weight: 500; font-size: 14px;">
                                    <div style="width: 24px; height: 24px; background: #10b981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);">
                                        <span style="color: white; font-size: 12px;">▶</span>
                                    </div>
                                    <span>Início do trajeto</span>
                                </div>
                            </div>
                        `
                    });
                    
                    startMarker.addListener('click', () => {
                        startInfoWindow.open(map, startMarker);
                        setTimeout(() => {
                            fixInfoWindowCloseButton();
                        }, 300);
                    });
                    
                    bounds.extend(routePath[0]);
                }
                
                // Add end point
                if (routePath.length > 1) {
                    const endMarker = new google.maps.Marker({
                        position: routePath[routePath.length - 1],
                        map: map,
                        title: 'Fim do trajeto',
                        icon: {
                            url: 'data:image/svg+xml;base64,' + btoa(`
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="48" height="48">
                                    <!-- Circle only -->
                                    <circle cx="24" cy="24" r="22" fill="#ef4444" stroke="white" stroke-width="4"/>
                                    <!-- Stop/square icon -->
                                    <rect x="16" y="16" width="16" height="16" rx="2" fill="white"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(36, 36),
                            anchor: new google.maps.Point(18, 18)
                        },
                        zIndex: 10
                    });
                    
                    const endInfoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="font-family: system-ui, sans-serif; padding: 8px 16px 12px 16px; text-align: center; min-width: 140px;">
                                <div style="display: flex; align-items: center; justify-content: center; gap: 10px; color: #ef4444; font-weight: 500; font-size: 14px;">
                                    <div style="width: 24px; height: 24px; background: #ef4444; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);">
                                        <span style="color: white; font-size: 12px;">⏹</span>
                                    </div>
                                    <span>Fim do trajeto</span>
                                </div>
                            </div>
                        `
                    });
                    
                    endMarker.addListener('click', () => {
                        endInfoWindow.open(map, endMarker);
                        setTimeout(() => {
                            fixInfoWindowCloseButton();
                        }, 300);
                    });
                    
                    bounds.extend(routePath[routePath.length - 1]);
                }
                
                // Extend bounds for all route points
                routePath.forEach(point => bounds.extend(point));
            }
            
            // Add contact observation points
            contactEvents.forEach((contact, index) => {
                if (contact.observerLocation && contact.contactLocation) {
                    const observerPos = {
                        lat: contact.observerLocation.latitude,
                        lng: contact.observerLocation.longitude
                    };
                    const contactPos = {
                        lat: contact.contactLocation.latitude,
                        lng: contact.contactLocation.longitude
                    };
                    
                    // Add observer position marker
                    const observerMarker = new google.maps.Marker({
                        position: observerPos,
                        map: map,
                        title: `Contacto ${index + 1}`,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 16,
                            fillColor: '#0ea5e9', // Blue to match sidebar
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 3
                        },
                        label: {
                            text: String(index + 1),
                            color: 'white',
                            fontWeight: 'bold',
                            fontSize: '12px'
                        }
                    });
                    
                    // Build popup content for OBSERVER position (user location)
                    let popupContent = `
                        <div style="font-family: system-ui, sans-serif; min-width: 200px; padding: 8px 16px 14px 16px; background: #ffffff; border-radius: 8px;">
                            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
                                <div style="background: #0ea5e9; color: white; padding: 4px 10px; border-radius: 12px; font-weight: 600; font-size: 12px; box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2); display: flex; align-items: center; gap: 6px;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: #0ea5e9; font-weight: 700; font-size: 10px;">${index + 1}</span>
                                    </div>
                                    <span style="color: white;">Contacto</span>
                                </div>
                            </div>
                            <div style="text-align: center; margin-bottom: 10px;">
                                <div style="font-size: 13px; color: #6b7280; margin-bottom: 4px;"><i class="fas fa-clock" style="color: #6b7280; margin-right: 6px;"></i>${new Date(contact.timestamp).toLocaleTimeString('pt-PT', {hour: '2-digit', minute: '2-digit', second: '2-digit'})}</div>
                                <div style="color: #374151; font-weight: 500; font-size: 13px;">Posição do observador</div>
                            </div>
                    `;
                    

                    
                    popupContent += `</div>`;
                    
                    const observerInfoWindow = new google.maps.InfoWindow({
                        content: popupContent
                    });
                    
                    observerMarker.addListener('click', () => {
                        observerInfoWindow.open(map, observerMarker);
                        // Fix close button language after info window opens
                        setTimeout(() => {
                            fixInfoWindowCloseButton();
                            initGLightbox();
                        }, 500);
                    });
                    
                    // Store marker and info window references for zoom functionality
                    observationMarkers[index] = observerMarker;
                    observerInfoWindows[index] = observerInfoWindow;
                    
                    // Add contact location marker - green circle background
                    const contactBackgroundMarker = new google.maps.Marker({
                        position: contactPos,
                        map: map,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 16,
                            fillColor: '#059669',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 3
                        },
                        zIndex: 1
                    });
                    
                    // Add dove icon on top
                    const contactMarker = new google.maps.Marker({
                        position: contactPos,
                        map: map,
                        title: `Contacto ${index + 1}`,
                        icon: {
                            url: '../../assets/img/icons/dove-icon.png',
                            scaledSize: new google.maps.Size(20, 20),
                            anchor: new google.maps.Point(10, 10)
                        },
                        zIndex: 2
                    });
                    
                    // Build contact info window content with images
                    // Prepare circumstance translation
                    const circumstanceTranslations = {
                        'rolaAdultaCantando': 'Rola adulta a cantar',
                        'rolaEmVoo': 'Rola em voo',
                        'adultoPousado': 'Adulto pousado',
                        'adultoEmDisplay': 'Adulto em display',
                        'ninhoVazio': 'Ninho vazio',
                        'nichoOcupado': 'Nicho ocupado',
                        'ovos': 'Ovos',
                        'adultoAIncubar': 'Adulto a incubar',
                        'crias': 'Crias',
                        'juvenile': 'Juvenil',
                        'outraQual': 'Outra'
                    };
                    
                    let displayCircumstance = 'N/A';
                    if (contact.circumstances) {
                        for (const [key, value] of Object.entries(contact.circumstances)) {
                            if (key !== 'outraQualText' && value === true) {
                                displayCircumstance = circumstanceTranslations[key] || key;
                                if (key === 'outraQual' && contact.circumstances.outraQualText) {
                                    displayCircumstance += ': ' + contact.circumstances.outraQualText;
                                }
                                break;
                            }
                        }
                    }
                    
                    // Prepare location translation
                    const locationTranslations = {
                        'arvore': 'Árvore',
                        'arbusto': 'Arbusto',
                        'pontoDeAgua': 'Ponto de Água',
                        'clareira': 'Clareira',
                        'parcelaAgricola': 'Parcela Agrícola',
                        'outraQual': 'Outra'
                    };
                    
                    let displayLocation = 'N/A';
                    const possibleFields = ['contactLocationDetails', 'contactLocation', 'location_details', 'habitat'];
                    for (const field of possibleFields) {
                        if (contact[field] && typeof contact[field] === 'object') {
                            for (const [key, value] of Object.entries(contact[field])) {
                                if (key !== 'outraQualText' && key !== 'latitude' && key !== 'longitude' && value === true) {
                                    displayLocation = locationTranslations[key] || key;
                                    if (key === 'outraQual' && contact[field].outraQualText) {
                                        displayLocation += ': ' + contact[field].outraQualText;
                                    }
                                    break;
                                }
                            }
                            if (displayLocation !== 'N/A') break;
                        }
                    }
                    
                    // If no location found, check if this is a trajectory contact
                    if (displayLocation === 'N/A') {
                        if (contact.contactLocationDetails && contact.contactLocationDetails.latitude && contact.contactLocationDetails.longitude) {
                            if (contact.protocol === 'trajeto' && contact.userRole === 'tecnico_prorola') {
                                displayLocation = 'Árvore'; // Default to tree since it was selected but not saved
                            } else {
                                displayLocation = 'Posição GPS';
                            }
                        } else {
                            displayLocation = 'Local não especificado';
                        }
                    }
                    
                    let contactContent = `
                        <div style="font-family: system-ui, sans-serif; padding: 8px 16px 12px 16px; min-width: 200px; background: #ffffff; border-radius: 8px;">
                            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                                <div style="background: #059669; color: white; padding: 4px 10px; border-radius: 12px; font-weight: 600; font-size: 12px; box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2); display: flex; align-items: center; gap: 6px;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: #059669; font-weight: 700; font-size: 10px;">${index + 1}</span>
                                    </div>
                                    <span style="color: white;">Contacto</span>
                                </div>
                            </div>
                            <div style="text-align: center;">
                                <div style="margin-bottom: 6px; color: #374151; font-weight: 500; font-size: 13px;">Localização do contacto</div>
                                <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                                    <div style="margin-bottom: 3px; display: flex; justify-content: space-between;">
                                        <span>Distância: <strong>${contact.distance < 1000 ? Math.round(contact.distance) + 'm' : Math.round(contact.distance/1000) + 'km'}</strong></span>
                                        ${contact.bearing ? '<span>Direção: <strong>' + Math.round(contact.bearing) + '°</strong></span>' : '<span></span>'}
                                    </div>
                                    <div style="margin-bottom: 3px;">
                                        <span style="color: #64748B; font-size: 10px; font-weight: 500; text-transform: uppercase;">Circunstância:</span>
                                        <span style="color: #374151; font-weight: 500; margin-left: 4px;">${displayCircumstance}</span>
                                    </div>
                                    <div style="margin-bottom: 3px;">
                                        <span style="color: #64748B; font-size: 10px; font-weight: 500; text-transform: uppercase;">Local:</span>
                                        <span style="color: #374151; font-weight: 500; margin-left: 4px;">${displayLocation}</span>
                                    </div>
                                    ${contact.elevation ? '<div style="margin-top: 3px;"><span>Elevação: <strong>' + Math.round(contact.elevation) + 'm</strong></span></div>' : ''}
                                </div>
                    `;
                    
                    // Add images to contact location if available
                    if (contact.images && contact.images.length > 0) {
                        contactContent += `
                            <div style="text-align: center; margin-top: 12px;">
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 8px; font-weight: 500;"><i class="fas fa-camera" style="color: #6b7280; margin-right: 6px;"></i> Fotografias (${contact.images.length})</div>
                                <div style="display: flex; gap: 6px; justify-content: center; flex-wrap: wrap; max-width: 250px;">
                        `;
                        
                        contact.images.forEach((imageUrl, imgIndex) => {
                            const distance = contact.distance || 0;
                            const distanceText = distance < 1000 ? Math.round(distance) + 'm' : Math.round(distance/1000) + 'km';
                            const direction = contact.bearing ? Math.round(contact.bearing) + '°' : 'N/A';
                            
                            // Use the already calculated displayCircumstance and displayLocation from above
                            contactContent += `
                                <div class="image-loader" style="width: 45px; height: 45px;">
                                    <div class="image-spinner"></div>
                                    <img src="${imageUrl}" 
                                         class="loading glightbox"
                                         data-gallery="contact-${index}"
                                         data-title=""
                                         data-description="<div class='contact-lightbox-info'><div class='contact-lightbox-badge'><div style='width: 18px; height: 18px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;'><span style='color: #059669; font-weight: 700; font-size: 10px;'>${index + 1}</span></div><span>Contacto</span></div><div class='contact-lightbox-details'><div class='contact-lightbox-detail'><span class='icon'>📏</span><span class='label'>Distância:</span><span class='value'>${distanceText}</span></div><div class='contact-lightbox-detail'><span class='icon'>🧭</span><span class='label'>Direção:</span><span class='value'>${direction}</span></div><div class='contact-lightbox-detail'><span class='icon'>🕊️</span><span class='label'>Circunstância:</span><span class='value'>${displayCircumstance}</span></div><div class='contact-lightbox-detail'><span class='icon'>📍</span><span class='label'>Local:</span><span class='value'>${displayLocation}</span></div></div></div>"
                                         style="width: 45px; height: 45px; object-fit: cover; border-radius: 6px; border: 2px solid #e5e7eb; cursor: pointer; transition: transform 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                                         onmouseover="this.style.transform='scale(1.05)'"
                                         onmouseout="this.style.transform='scale(1)'"
                                         onload="handleImageLoad(this)"
                                         onerror="handleImageError(this)"
                                         title="Clique para ver em tamanho completo">
                                </div>
                            `;
                        });
                        
                        contactContent += `</div></div>`;
                    }
                    
                    contactContent += `</div></div>`;
                    
                    const contactInfoWindow = new google.maps.InfoWindow({
                        content: contactContent,
                        pixelOffset: new google.maps.Size(0, -10)  // Move balloon slightly up to avoid overlapping the dove icon
                    });
                    
                    contactMarker.addListener('click', () => {
                        contactInfoWindow.open(map, contactMarker);
                        // Fix close button language after info window opens
                        setTimeout(() => {
                            fixInfoWindowCloseButton();
                            initGLightbox();
                        }, 500);
                    });
                    
                    // Store contact marker and info window references for zoom functionality
                    contactMarkers[index] = contactMarker;
                    contactInfoWindows[index] = contactInfoWindow;
                    
                    // Add line from observer to contact with better color
                    const observationLine = new google.maps.Polyline({
                        path: [observerPos, contactPos],
                        geodesic: true,
                        strokeColor: '#059669', // Green to match contact marker
                        strokeOpacity: 0.7,
                        strokeWeight: 2,
                        icons: [{
                            icon: {
                                path: 'M 0,-1 0,1',
                                strokeOpacity: 1,
                                scale: 4
                            },
                            offset: '0',
                            repeat: '20px'
                        }]
                    });
                    observationLine.setMap(map);
                    
                    bounds.extend(observerPos);
                    bounds.extend(contactPos);
                }
            });
            
            // Fallback to report location if no route data
            if (bounds.isEmpty() && reportLocation) {
                const pos = {
                    lat: reportLocation.latitude,
                    lng: reportLocation.longitude
                };
                
                const reportMarker = new google.maps.Marker({
                    position: pos,
                    map: map,
                    title: 'Localização do relatório'
                });
                
                bounds.extend(pos);
            }
            
            // Fit map to show all markers with some padding
            if (!bounds.isEmpty()) {
                // Add some padding around the route for better visualization
                map.fitBounds(bounds, {
                    padding: {
                        top: 50,
                        right: 50,
                        bottom: 50,
                        left: 50
                    }
                });
            } else {
                // Fallback zoom if no bounds
                map.setZoom(14);
            }
            
            // Reinitialize GLightbox after all map content is loaded
            // This ensures it recognizes dynamically created image elements in map bubbles
            setTimeout(() => {
                initGLightbox();
            }, 1000);
        }
        
        // Fix Google Maps info window close button language
        function fixInfoWindowCloseButton() {
            // Find all close buttons and change their title to Portuguese
            const closeButtons = document.querySelectorAll('.gm-ui-hover-effect');
            closeButtons.forEach(button => {
                if (button.title === 'Close' || button.getAttribute('title') === 'Close') {
                    button.setAttribute('title', 'Fechar');
                    button.title = 'Fechar';
                }
            });
        }
        
        // Initialize GLightbox
        let lightboxInstance = null;
        
        function initGLightbox() {
            // Destroy existing instance if it exists
            if (lightboxInstance) {
                lightboxInstance.destroy();
            }
            
            lightboxInstance = GLightbox({
                touchNavigation: true,
                loop: true,
                autoplayVideos: false,
                skin: 'clean',
                closeButton: true,
                descPosition: 'top',
                moreText: 'Ver mais',
                moreLength: 200,
                plyr: {
                    config: {
                        ratio: '16:9',
                        youtube: {
                            noCookie: true,
                            rel: 0,
                            showinfo: 0,
                            iv_load_policy: 3
                        }
                    }
                },
                onOpen: () => {
                    console.log('Lightbox opened');
                },
                onClose: () => {
                    console.log('Lightbox closed');
                }
            });
        }
        
        function handleImageLoad(img) {
            const spinner = img.parentElement.querySelector('.image-spinner');
            if (spinner) {
                spinner.style.display = 'none';
            }
            img.classList.remove('loading');
            img.classList.add('loaded');
        }
        
        function handleImageError(img) {
            const spinner = img.parentElement.querySelector('.image-spinner');
            if (spinner) {
                spinner.style.display = 'none';
            }
            img.classList.remove('loading');
            img.style.backgroundColor = '#f3f4f6';
            img.style.display = 'flex';
            img.style.alignItems = 'center';
            img.style.justifyContent = 'center';
            img.style.color = '#6b7280';
            img.style.fontSize = '10px';
            img.alt = '✕';
        }

        // Make contact cards clickable for zoom and handle hover
        document.querySelectorAll('.contact-card').forEach((card, index) => {
            card.addEventListener('mouseenter', () => {
                card.style.backgroundColor = '#f3f4f6';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.backgroundColor = '#f9fafb';
            });
            
            // Make entire card clickable for zoom
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on an image
                if (e.target.tagName === 'IMG') {
                    return;
                }
                
                const observationIndex = parseInt(card.getAttribute('data-observation'));
                triggerZoom(observationIndex);
            });
        });
        
        // Zoom functionality
        function triggerZoom(observationIndex) {
            if (observationMarkers[observationIndex] && contactEvents[observationIndex]) {
                const contact = contactEvents[observationIndex];
                
                if (contact.observerLocation && contact.contactLocation) {
                    const observerPos = {
                        lat: contact.observerLocation.latitude,
                        lng: contact.observerLocation.longitude
                    };
                    const contactPos = {
                        lat: contact.contactLocation.latitude,
                        lng: contact.contactLocation.longitude
                    };
                    
                    // Create bounds for both observer and contact locations
                    const zoomBounds = new google.maps.LatLngBounds();
                    zoomBounds.extend(observerPos);
                    zoomBounds.extend(contactPos);
                    
                    // Fit map to show both locations
                    map.fitBounds(zoomBounds);
                    
                    // Adjust zoom based on distance - closer contacts get more zoom
                    google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                        const distance = contact.distance || 0;
                        let targetZoom;
                        
                        if (distance < 50) {
                            targetZoom = 20; // Very close contact - maximum zoom
                        } else if (distance < 100) {
                            targetZoom = 19; // Close contact
                        } else if (distance < 200) {
                            targetZoom = 18; // Medium distance
                        } else if (distance < 500) {
                            targetZoom = 17; // Far contact
                        } else {
                            targetZoom = 16; // Very far contact
                        }
                        
                        // Apply the zoom with a small delay for smooth animation
                        setTimeout(() => {
                            map.setZoom(targetZoom);
                            
                            // Center between observer and contact
                            const centerLat = (observerPos.lat + contactPos.lat) / 2;
                            const centerLng = (observerPos.lng + contactPos.lng) / 2;
                            map.panTo({ lat: centerLat, lng: centerLng });
                            
                            // Open both info windows after zooming
                            setTimeout(() => {
                                // Close all existing info windows first
                                observerInfoWindows.forEach(infoWindow => {
                                    if (infoWindow) infoWindow.close();
                                });
                                contactInfoWindows.forEach(infoWindow => {
                                    if (infoWindow) infoWindow.close();
                                });
                                
                                // Open observer info window
                                const observerMarker = observationMarkers[observationIndex];
                                const observerInfo = observerInfoWindows[observationIndex];
                                if (observerMarker && observerInfo) {
                                    observerInfo.open(map, observerMarker);
                                }
                                
                                // Open contact info window with a slight delay
                                setTimeout(() => {
                                    const contactMarker = contactMarkers[observationIndex];
                                    const contactInfo = contactInfoWindows[observationIndex];
                                    if (contactMarker && contactInfo) {
                                        contactInfo.open(map, contactMarker);
                                    }
                                    
                                    // Reinitialize GLightbox after info windows are opened
                                    setTimeout(() => {
                                        initGLightbox();
                                    }, 300);
                                }, 200);
                            }, 800);
                        }, 300);
                    });
                }
            }
        }
        
        // Initialize GLightbox when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initGLightbox();
        });
    </script>
</body>
</html> 