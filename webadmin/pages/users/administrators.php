<?php
require_once dirname(dirname(__DIR__)) . '/includes/config.php';
require_once dirname(dirname(__DIR__)) . '/includes/auth.php';

// Check if user is logged in and has admin role
try {
    error_log("Checking admin authentication");
    requireAdmin();
    error_log("Admin check passed");
    
    // Verify Firebase token
    if (!isset($_SESSION['user']['auth_token'])) {
        throw new Exception("No auth token found");
    }
    error_log("Auth token exists: " . substr($_SESSION['user']['auth_token'], 0, 10) . "...");
} catch (Exception $e) {
    error_log("Authentication error: " . $e->getMessage());
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}

// Get users from Firestore
try {
    error_log("Starting to fetch users");
    
    // Get users from users collection with pagination
    $dbUsers = [];
    $pageToken = null;
    
    do {
        $pageUsers = $database->listDocuments('users', $pageToken, 1000);
        
        // Extract pagination token if present
        $pageToken = $pageUsers['_nextPageToken'] ?? null;
        unset($pageUsers['_nextPageToken']); // Remove pagination token from results
        
        // Merge results
        $dbUsers = array_merge($dbUsers, $pageUsers);
        
    } while ($pageToken);
    
    error_log("Found " . count($dbUsers) . " users in database");

    // Make sure we have a valid token
    if (!isset($_SESSION['user']['auth_token'])) {
        throw new Exception("No auth token found");
    }
    
    // Set the token for Firebase requests
    $database->setAccessToken($_SESSION['user']['auth_token']);
    
    // Process admin users
    $administradores = [];

    // Process database users
    foreach ($dbUsers as $userId => $userData) {
        $userData['id'] = $userId;
        
        // Define super admin accounts that should not appear in management lists
        $superAdminEmails = ['<EMAIL>', '<EMAIL>'];
        
        // Only include admin users, exclude current user and super admins
        if ($userData['role'] === 'administrador' && 
            $userData['email'] !== $_SESSION['user']['email'] && 
            !in_array($userData['email'], $superAdminEmails)) {
            $administradores[$userId] = $userData;
        }
    }

    // Sort by name
    uasort($administradores, function($a, $b) {
        return strcasecmp($a['name'] ?? '', $b['name'] ?? '');
    });
    
} catch (Exception $e) {
    error_log("Users error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    $error = "Error loading users: " . $e->getMessage();
    $administradores = [];
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administradores - <?php echo SITE_NAME; ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <style>
        /* DataTable modern styling */
        .table {
            width: 100%;
            background: white;
            border-collapse: collapse;
        }

        .page-header {
            background: white;
            margin-bottom: 3rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-header h1 {
            margin: 0;
            font-size: 1.5rem;
            color: #374151;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-header h1 i {
            color: #3B82F6;
        }

        .card {
            background: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .card-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
            color: white;
        }

        .card-body {
            padding: 1.5rem;
        }

        .table thead {
            background-color: #F8F9FB;
        }

        .table th {
            padding: 12px 16px;
            font-weight: 500;
            color: #6B7280;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #E5E7EB;
            white-space: nowrap;
            text-align: center;
        }

        .table td {
            padding: 12px 16px;
            vertical-align: middle;
            border-bottom: 1px solid #E5E7EB;
            color: #374151;
            text-align: center;
        }

        /* Column specific styling */
        #users-table th:nth-child(1), /* Nome */
        #users-table td:nth-child(1) {
            text-align: left;
            min-width: 150px;
        }

        #users-table th:nth-child(2), /* Email */
        #users-table td:nth-child(2) {
            text-align: left;
            min-width: 200px;
        }

        #users-table th:nth-child(3), /* Última Atividade */
        #users-table td:nth-child(3) {
            width: 150px;
        }

        #users-table th:nth-child(4), /* Ações */
        #users-table td:nth-child(4) {
            width: 180px;
            text-align: center;
        }

        /* Action buttons styling */
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.25rem;
            border-radius: 0.375rem;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            gap: 0.5rem;
        }

        .btn i {
            font-size: 0.875rem;
        }

        .btn-secondary {
            color: #374151;
            background-color: #F3F4F6;
            border-color: #E5E7EB;
        }

        .btn-secondary:hover {
            background-color: #E5E7EB;
            border-color: #D1D5DB;
        }

        .btn-primary {
            color: #fff;
            background-color: #0297ac;
            border-color: #0297ac;
        }

        .btn-primary:hover {
            background-color: #018698;
            border-color: #018698;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .btn-sm i {
            font-size: 0.875rem;
        }

        .card-header .btn-primary {
            background-color: #0297ac;
            border-color: #0297ac;
            color: white;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-header .btn-primary:hover {
            background-color: #018698;
            border-color: #018698;
        }

        /* Modal styling - consistent with global profile modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-dialog {
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 500px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .modal-content {
            border-radius: 16px;
            overflow: hidden;
        }

        .modal-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .modal-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .modal-title i {
            font-size: 1.125rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-body {
            padding: 2rem 1.5rem;
        }

        .form-group {
            margin-bottom: 1.25rem;
            transition: all 0.3s ease;
        }

        /* Smooth transitions for dynamic fields */
        #currentPasswordGroup,
        #confirmPasswordGroup {
            transition: opacity 0.3s ease, max-height 0.3s ease;
            overflow: hidden;
        }

        #currentPasswordGroup[style*="display: none"],
        #confirmPasswordGroup[style*="display: none"] {
            opacity: 0;
            max-height: 0;
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #0a7ea4;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25);
        }

        .form-text {
            margin-top: 0.25rem;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .close {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 1rem;
            font-weight: 500;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .close:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        /* Modal buttons - consistent with profile modal */
        .modal-footer .btn {
            margin: 0;
            display: inline-flex;
            align-items: center;
            padding: 0.625rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
            gap: 0.5rem;
            border: none;
        }

        .modal-footer .btn-primary {
            background-color: #0a7ea4 !important;
            color: white !important;
            border-color: #0a7ea4 !important;
        }

        .modal-footer .btn-primary:hover {
            background-color: #096d8c !important;
            color: white !important;
            border-color: #096d8c !important;
        }

        .modal-footer .btn-secondary {
            background-color: #6b7280 !important;
            color: white !important;
            border-color: #6b7280 !important;
        }

        .modal-footer .btn-secondary:hover {
            background-color: #374151 !important;
            color: white !important;
            border-color: #374151 !important;
        }

        .modal-footer .btn-danger {
            background-color: #dc2626 !important;
            color: white !important;
            border-color: #dc2626 !important;
        }

        .modal-footer .btn-danger:hover {
            background-color: #b91c1c !important;
            color: white !important;
            border-color: #b91c1c !important;
        }

        .btn-outline-primary {
            color: #0a7ea4;
            border-color: #0a7ea4;
            background-color: transparent;
        }

        .btn-outline-primary:hover {
            color: white;
            background-color: #0a7ea4;
            border-color: #0a7ea4;
        }

        /* DataTables layout adjustments */
        .dataTables_wrapper .row {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .dataTables_wrapper .col-sm-6 {
            flex: 0 0 50%;
            display: flex;
            align-items: center;
        }

        .dataTables_wrapper .col-sm-6:last-child {
            justify-content: flex-end;
        }

        .dataTables_wrapper .col-sm-5 {
            flex: 0 0 41.666667%;
        }

        .dataTables_wrapper .col-sm-7 {
            flex: 0 0 58.333333%;
            text-align: right;
        }

        .dataTables_wrapper .dataTables_length {
            margin-right: 2rem;
        }

        .dataTables_wrapper .dataTables_length label,
        .dataTables_wrapper .dataTables_filter label {
            display: flex;
            align-items: center;
            margin: 0;
        }

        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            margin-left: 8px;
        }

        /* DataTables pagination styling - centered like reports */
        .dataTables_wrapper .dataTables_paginate {
            text-align: center !important;
            float: none !important;
            margin-top: 1rem;
            width: 100% !important;
            display: block !important;
        }
        
        .dataTables_wrapper .dataTables_paginate .pagination {
            justify-content: center !important;
            margin: 0 auto !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            display: inline-flex !important;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            margin: 0 2px;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: white;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #374151;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #0a7ea4;
            border-color: #0a7ea4;
            color: white;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: #096d8c;
            border-color: #096d8c;
            color: white;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
            background: #f9fafb;
            border-color: #f3f4f6;
            color: #d1d5db;
            cursor: not-allowed;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            background: #f9fafb;
            border-color: #f3f4f6;
            color: #d1d5db;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
        .dataTables_wrapper .dataTables_paginate .paginate_button.next {
            font-weight: 600;
            padding: 8px 16px;
        }

        /* Align pagination with bottom info */
        .dataTables_wrapper .row:last-child {
            display: block !important;
            margin-top: 1rem;
            text-align: center !important;
        }

        .dataTables_wrapper .dataTables_info {
            float: left !important;
            margin: 0;
            padding-top: 0.75rem;
            text-align: left !important;
        }

        .dataTables_wrapper .dataTables_paginate {
            float: none !important;
            margin: 0 auto !important;
            text-align: center !important;
            clear: both;
            padding-top: 1rem;
            display: block !important;
            width: 100% !important;
        }

        /* Override DataTables specific pagination classes */
        #users-table_paginate {
            text-align: center !important;
            margin: 0 auto !important;
            display: block !important;
            width: 100% !important;
        }

        .dataTables_wrapper div.dataTables_paginate {
            text-align: center !important;
            margin: 0 auto !important;
        }

        /* Sortable column headers cursor */
        .dataTables_wrapper table thead th.sorting,
        .dataTables_wrapper table thead th.sorting_asc,
        .dataTables_wrapper table thead th.sorting_desc {
            cursor: pointer !important;
        }

        .dataTables_wrapper table thead th.sorting:hover,
        .dataTables_wrapper table thead th.sorting_asc:hover,
        .dataTables_wrapper table thead th.sorting_desc:hover {
            background-color: #f8fafc !important;
        }

        /* Fix action buttons layout - keep them inline */
        #users-table td:last-child {
            white-space: nowrap;
            min-width: 180px;
        }

        #users-table td .btn {
            margin-right: 0.5rem;
            margin-bottom: 0;
            display: inline-block;
            vertical-align: top;
        }

        #users-table td .btn:last-child {
            margin-right: 0;
        }
    </style>
</head>
<body>
    <?php 
    include_file('includes/header.php');
    include_file('includes/sidebar.php');
    ?>
    
    <div class="content">
        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <button type="button" class="btn btn-success" onclick="showAddUserModal()">
                <i class="fas fa-plus"></i>
                Novo Administrador
            </button>
        </div>
            <div class="card-body">
                <table id="users-table" class="table">
                    <thead>
                        <tr>
                            <th>NOME</th>
                            <th>EMAIL</th>
                            <th>ÚLTIMA ATIVIDADE</th>
                            <th>AÇÕES</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($administradores as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['name'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($user['email'] ?? ''); ?></td>
                            <td><?php echo isset($user['lastActivity']) && $user['lastActivity'] > 0 ? format_date($user['lastActivity']) : 'Nunca'; ?></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" onclick="editUser('<?php echo $user['id']; ?>')">
                                    <i class="fas fa-edit"></i>
                                    Editar
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteUser('<?php echo $user['id']; ?>')">
                                    <i class="fas fa-trash"></i>
                                    Eliminar
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Confirmar Eliminação
                    </h5>
                    <button type="button" class="close" onclick="closeDeleteModal()">×</button>
                </div>
                <div class="modal-body">
                    <div style="text-align: center; padding: 1rem 0;">
                        <i class="fas fa-user-times" style="font-size: 3rem; color: #dc2626; margin-bottom: 1rem;"></i>
                        <p style="font-size: 1.1rem; color: #374151; margin-bottom: 0.5rem;">
                            Tem certeza que deseja eliminar este administrador?
                        </p>
                        <p id="deleteUserInfo" style="font-weight: 600; color: #dc2626; margin-bottom: 1rem;"></p>
                        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                            <p style="color: #991b1b; font-size: 0.875rem; margin: 0;">
                                <i class="fas fa-exclamation-circle" style="margin-right: 0.5rem;"></i>
                                <strong>Atenção:</strong> Esta ação não pode ser desfeita. O utilizador será permanentemente removido do sistema.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i>
                        Eliminar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div class="modal" id="userModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Novo Administrador</h5>
                    <button type="button" class="close" onclick="closeUserModal()">×</button>
                </div>
                <div class="modal-body">
                    <form id="userForm" onsubmit="saveUser(event)">
                        <input type="hidden" id="userId" name="id">
                        <!-- Email info removed for cleaner edit interface -->
                        <div id="emailInput" style="display: none;">
                            <div class="form-group">
                                <label for="email">Email:</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <small class="form-text">O email não pode ser alterado após a criação.</small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="name">Nome:</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="form-group" id="currentPasswordGroup" style="display: none;">
                            <label for="currentPassword">Palavra-passe Atual:</label>
                            <input type="password" class="form-control" id="currentPassword" name="current_password">
                            <small class="form-text">Obrigatório para alterar a palavra-passe</small>
                        </div>
                        <div class="form-group">
                            <label for="password">Nova Palavra-passe:</label>
                            <div style="display: flex; gap: 0.5rem; align-items: flex-start;">
                                <input type="text" class="form-control" id="password" name="password" minlength="6" style="flex: 1;">
                                <button type="button" class="btn btn-outline-primary" id="generatePasswordBtn" onclick="generatePassword()" style="white-space: nowrap;">
                                    <i class="fas fa-key"></i> Gerar
                                </button>
                            </div>
                            <small id="passwordHelpText" class="form-text" style="display: none;">Deixe em branco para manter a palavra-passe atual</small>
                            <small id="passwordRequirements" class="form-text">A palavra-passe deve ter pelo menos 6 caracteres</small>
                            <div id="passwordGeneratedInfo" class="form-text" style="display: none; margin-top: 0.5rem; padding: 0.75rem; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 6px; color: #0d47a1;">
                                <i class="fas fa-info-circle"></i> <strong>Palavra-passe gerada:</strong> Copie esta palavra-passe e envie-a ao novo administrador de forma segura.
                            </div>
                        </div>
                        <div class="form-group" id="confirmPasswordGroup" style="display: none;">
                            <label for="confirmPassword">Confirmar Nova Palavra-passe:</label>
                            <input type="text" class="form-control" id="confirmPassword" name="confirm_password" minlength="6">
                            <small class="form-text text-danger" id="passwordMismatch" style="display: none;">As palavras-passe não coincidem</small>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeUserModal()">
                                <i class="fas fa-times"></i>
                                Cancelar
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Guardar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Add jQuery and DataTables -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script>
    const siteUrl = '<?php echo SITE_URL; ?>';
    
    $(document).ready(function() {
        $('#users-table').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/pt-PT.json'
            },
            order: [[2, 'desc']], // Sort by last activity by default
            pageLength: 25,
            dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-sm-5"i><"col-sm-7"p>>'
        });

        // Add password confirmation validation - only listen to main password fields
        $('#password, #confirmPassword').on('input', validatePasswords);
    });

    // Simple validation function like the working profile edit
    function validatePasswords() {
        // Basic validation for new users only
        const password = $('#password').val();
        const confirmPassword = $('#confirmPassword').val();
        const userIdValue = document.getElementById('userId').value;
        
        if (!userIdValue) { // New user
            return password.length >= 6 && password === confirmPassword;
        }
        return true; // For existing users, validation is done in saveUser
    }

    function generatePassword() {
        // Generate a secure password with a mix of characters
        const length = 12;
        const charset = {
            uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            lowercase: 'abcdefghijklmnopqrstuvwxyz',
            numbers: '0123456789',
            symbols: '!@#$%&*'
        };
        
        let password = '';
        
        // Ensure at least one character from each set
        password += charset.uppercase.charAt(Math.floor(Math.random() * charset.uppercase.length));
        password += charset.lowercase.charAt(Math.floor(Math.random() * charset.lowercase.length));
        password += charset.numbers.charAt(Math.floor(Math.random() * charset.numbers.length));
        password += charset.symbols.charAt(Math.floor(Math.random() * charset.symbols.length));
        
        // Fill the rest randomly
        const allChars = charset.uppercase + charset.lowercase + charset.numbers + charset.symbols;
        for (let i = password.length; i < length; i++) {
            password += allChars.charAt(Math.floor(Math.random() * allChars.length));
        }
        
        // Shuffle the password to avoid predictable patterns
        password = password.split('').sort(() => Math.random() - 0.5).join('');
        
        // Set the password in both fields
        document.getElementById('password').value = password;
        document.getElementById('confirmPassword').value = password;
        
        // Show the info message
        document.getElementById('passwordGeneratedInfo').style.display = 'block';
        
        // Add visual feedback to the generate button
        const generateBtn = document.getElementById('generatePasswordBtn');
        const originalText = generateBtn.innerHTML;
        generateBtn.innerHTML = '<i class="fas fa-check"></i> Gerada!';
        generateBtn.style.backgroundColor = '#10b981';
        generateBtn.style.borderColor = '#10b981';
        generateBtn.style.color = 'white';
        
        // Reset button after 2 seconds
        setTimeout(() => {
            generateBtn.innerHTML = originalText;
            generateBtn.style.backgroundColor = '';
            generateBtn.style.borderColor = '';
            generateBtn.style.color = '';
        }, 2000);
    }

    function showAddUserModal() {
        document.getElementById('userForm').reset();
        document.getElementById('userId').value = '';
        document.querySelector('.modal-title').innerHTML = '<i class="fas fa-user-plus"></i> Novo Administrador';
        document.getElementById('passwordHelpText').style.display = 'none';
        document.getElementById('passwordRequirements').style.display = 'block';
        document.getElementById('confirmPasswordGroup').style.display = 'block';
        document.getElementById('currentPasswordGroup').style.display = 'none';
        document.getElementById('password').required = true;
        document.getElementById('confirmPassword').required = true;
        document.getElementById('currentPassword').required = false;
        // Show email input for new users and make it required
        document.getElementById('emailInput').style.display = 'block';
        document.getElementById('email').required = true;
        // Hide password generation info
        document.getElementById('passwordGeneratedInfo').style.display = 'none';
        // Show generate button for new users and reset its text
        const generateBtn = document.getElementById('generatePasswordBtn');
        generateBtn.style.display = 'inline-block';
        generateBtn.innerHTML = '<i class="fas fa-key"></i> Gerar';
        generateBtn.style.backgroundColor = '';
        generateBtn.style.borderColor = '';
        generateBtn.style.color = '';
        document.getElementById('userModal').classList.add('show');
    }

    function closeUserModal() {
        document.getElementById('userModal').classList.remove('show');
        $('#passwordMismatch').hide();
        $('#userForm button[type="submit"]').prop('disabled', false);
        // Clear all form fields to prevent values from persisting
        document.getElementById('userForm').reset();
        // Reset field visibility
        document.getElementById('confirmPasswordGroup').style.display = 'none';
        document.getElementById('currentPasswordGroup').style.display = 'none';
    }

    function editUser(userId) {
        fetch(`${siteUrl}/pages/users/get_user.php?id=${userId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('userId').value = userId;
                document.getElementById('name').value = data.name || '';
                // Display email as info text
                document.getElementById('emailInput').style.display = 'none';
                // Set the email value in the hidden field for form submission
                document.getElementById('email').value = data.email || '';
                document.querySelector('.modal-title').innerHTML = '<i class="fas fa-user-edit"></i> Editar Administrador';
                document.getElementById('passwordHelpText').style.display = 'block';
                document.getElementById('passwordRequirements').style.display = 'block';
                // For editing users, show all password fields from the start to avoid layout changes
                document.getElementById('confirmPasswordGroup').style.display = 'block';
                document.getElementById('currentPasswordGroup').style.display = 'block';
                document.getElementById('password').required = false;
                document.getElementById('confirmPassword').required = false;
                document.getElementById('currentPassword').required = false;
                // Remove required attribute from hidden email field
                document.getElementById('email').required = false;
                // Hide password generation info and button for existing users
                document.getElementById('passwordGeneratedInfo').style.display = 'none';
                document.getElementById('generatePasswordBtn').style.display = 'none';
                document.getElementById('userModal').classList.add('show');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erro ao carregar dados do utilizador');
            });
    }

    function saveUser(event) {
        event.preventDefault();
        
        const form = document.getElementById('userForm');
        const isNewUser = !document.getElementById('userId').value;
        
        // Get password values
        const currentPassword = form.currentPassword.value;
        const newPassword = form.password.value;
        const confirmPassword = form.confirmPassword.value;
        
        // Validation logic differs for new users vs existing users
        if (isNewUser) {
            // For new users, password is required
            if (!newPassword) {
                showError('A palavra-passe é obrigatória');
                return;
            }
            if (!confirmPassword) {
                showError('A confirmação da palavra-passe é obrigatória');
                return;
            }
            if (newPassword.length < 6) {
                showError('A palavra-passe deve ter pelo menos 6 caracteres');
                return;
            }
            if (newPassword !== confirmPassword) {
                showError('As palavras-passe não coincidem');
                return;
            }
        } else {
            // For existing users, if any password field is filled, all must be validated
            if (currentPassword || newPassword || confirmPassword) {
                if (!currentPassword) {
                    showError('A palavra-passe atual é obrigatória para alterar a palavra-passe');
                    return;
                }
                if (!newPassword) {
                    showError('A nova palavra-passe é obrigatória');
                    return;
                }
                if (!confirmPassword) {
                    showError('A confirmação da palavra-passe é obrigatória');
                    return;
                }
                if (newPassword.length < 6) {
                    showError('A nova palavra-passe deve ter pelo menos 6 caracteres');
                    return;
                }
                if (newPassword !== confirmPassword) {
                    showError('As palavras-passe não coincidem');
                    return;
                }
            }
        }
        
        // Create form data matching save_admin_user.php expected field names
        const formData = new FormData();
        formData.set('id', form.userId.value);
        formData.set('name', form.name.value);
        formData.set('email', form.email.value);
        formData.set('role', 'administrador');
        
        // Only send password fields if user is changing password or creating new user
        if (isNewUser || currentPassword || newPassword || confirmPassword) {
            formData.set('current_password', currentPassword);
            formData.set('password', newPassword);
            formData.set('confirm_password', confirmPassword);
        }
        
        // Log the form data (excluding password)
        const formDataLog = {};
        formData.forEach((value, key) => {
            if (key !== 'password' && key !== 'confirmPassword') {
                formDataLog[key] = value;
            } else {
                formDataLog[key] = value ? '[SET]' : '[EMPTY]';
            }
        });
        console.log('Sending form data:', formDataLog);

        // Show loading state
        const submitButton = document.querySelector('#userForm button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A guardar...';

        // Check if this is a new user or update (use the existing isNewUser variable)
        
        // Regular update
        updateUserData(formData)
            .then(data => {
                if (data && data.success) {
                    showSuccess(data.message || (isNewUser ? 'Utilizador criado com sucesso' : 'Utilizador atualizado com sucesso'));
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showError(data?.error || 'Erro ao processar utilizador');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError(error.message || 'Erro ao processar utilizador');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            });
    }
    
    // Function to update user data
    function updateUserData(formData) {
        return fetch(`${siteUrl}/pages/users/save_admin_user.php`, {
            method: 'POST',
            body: formData,
            credentials: 'include',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            console.log('Response status:', response.status);
            
            // Get the response text first
            const text = await response.text();
            console.log('Response text:', text);
            
            let data;
            try {
                // Try to parse the text as JSON
                data = JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse response as JSON:', e);
                console.error('Raw response:', text);
                
                // Try to handle JSON-like response with single quotes
                if (text.includes("'success':") || text.includes("'error':")) {
                    try {
                        // Convert single quotes to double quotes and parse
                        const fixedText = text.replace(/'/g, '"');
                        data = JSON.parse(fixedText);
                        console.log('Parsed fixed JSON:', data);
                    } catch (e2) {
                        console.error('Failed to parse fixed JSON:', e2);
                    }
                }
                
                // If we still don't have data, handle as error
                if (!data) {
                    // If status is 401, redirect to login
                    if (response.status === 401) {
                        window.location.href = `${siteUrl}/pages/auth/login.php?expired=1`;
                        return;
                    }
                    
                    throw new Error('Resposta inválida do servidor. Por favor, tente novamente.');
                }
            }
            
            // Check if response was successful
            if (!response.ok) {
                // If unauthorized, redirect to login
                if (response.status === 401) {
                    window.location.href = `${siteUrl}/pages/auth/login.php?expired=1`;
                    return;
                }
                throw new Error(data.error || `Erro ${response.status}: ${response.statusText}`);
            }
            
            return data;
        });
    }

    function showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success';
        alertDiv.style.cssText = 'margin-bottom: 1rem; padding: 1rem; border-radius: 0.375rem; background-color: #DEF7EC; border: 1px solid #31C48D; color: #03543F;';
        alertDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
        
        const form = document.getElementById('userForm');
        form.insertBefore(alertDiv, form.firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    function showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger';
        alertDiv.style.cssText = 'margin-bottom: 1rem; padding: 1rem; border-radius: 0.375rem; background-color: #FDE8E8; border: 1px solid #F98080; color: #9B1C1C;';
        alertDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        
        const form = document.getElementById('userForm');
        form.insertBefore(alertDiv, form.firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    function showInfo(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-info';
        alertDiv.style.cssText = 'margin-bottom: 1rem; padding: 1rem; border-radius: 0.375rem; background-color: #EFF6FF; border: 1px solid #3B82F6; color: #1E40AF;';
        alertDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
        
        const form = document.getElementById('userForm');
        form.insertBefore(alertDiv, form.firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    function showWarning(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning';
        alertDiv.style.cssText = 'margin-bottom: 1rem; padding: 1rem; border-radius: 0.375rem; background-color: #FFFBEB; border: 1px solid #FDE68A; color: #A16207;';
        alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        const form = document.getElementById('userForm');
        form.insertBefore(alertDiv, form.firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    // Variable to store the user ID to delete
    let userToDelete = null;

    function deleteUser(userId) {
        // Find the user row to get the name
        const rows = document.querySelectorAll('#users-table tbody tr');
        let userName = '';
        
        rows.forEach(row => {
            const editButton = row.querySelector('button[onclick*="editUser"]');
            if (editButton && editButton.getAttribute('onclick').includes(userId)) {
                userName = row.cells[0].textContent.trim(); // Get name from first column
            }
        });

        // Store the user ID for later use
        userToDelete = userId;
        
        // Update the modal with user info
        document.getElementById('deleteUserInfo').textContent = userName || 'Utilizador selecionado';
        
        // Show the modal
        document.getElementById('deleteConfirmModal').classList.add('show');
    }

    function closeDeleteModal() {
        document.getElementById('deleteConfirmModal').classList.remove('show');
        userToDelete = null;
    }

    function confirmDelete() {
        if (!userToDelete) return;

        // Show loading state
        const deleteBtn = document.getElementById('confirmDeleteBtn');
        const originalText = deleteBtn.innerHTML;
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A eliminar...';

        fetch(`${siteUrl}/pages/users/delete_user.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: userToDelete })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeDeleteModal();
                // Show success message briefly before reload
                showSuccess('Administrador eliminado com sucesso');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showError(data.error || 'Erro ao eliminar utilizador');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Erro ao eliminar utilizador');
        })
        .finally(() => {
            // Reset button state
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalText;
        });
    }

    // Remove the local editProfile function since it should use the global one from profile_modal.php
    // The global editProfile function is already available from the header.php include
    </script>
</body>
</html> 