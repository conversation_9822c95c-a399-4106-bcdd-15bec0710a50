<?php
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';

// Check if user is authenticated and is superadmin
if (!isAuthenticated()) {
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}

if (!isSuperAdmin()) {
    header('Location: ' . SITE_URL . '/pages/error/unauthorized.php');
    exit();
}

$pageTitle = 'Sistema';

// Handle storage files backup request
if ($_POST['action'] ?? '' === 'backup_storage') {
    try {
        // Check if ZipArchive is available
        if (!class_exists('ZipArchive')) {
            throw new Exception("Extensão ZipArchive não está disponível no servidor. Contacte o administrador do sistema.");
        }
        
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception("Token de administrador não disponível para acesso ao Storage");
        }
        
        $storageBucket = FIREBASE_PROJECT_ID . '.firebasestorage.app';
        $storageUrl = "https://firebasestorage.googleapis.com/v0/b/{$storageBucket}/o";
        
        // Get list of all files
        $ch = curl_init($storageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status !== 200) {
            throw new Exception("Erro ao aceder ao Firebase Storage: HTTP $status");
        }
        
        $storageData = json_decode($response, true);
        $storageFiles = $storageData['items'] ?? [];
        
        if (empty($storageFiles)) {
            throw new Exception("Nenhum ficheiro encontrado no Firebase Storage");
        }
        
        // Create a ZIP file with all storage files
        $zipFileName = 'firebase_storage_backup_' . date('Y-m-d_H-i-s') . '.zip';
        $zipPath = sys_get_temp_dir() . '/' . $zipFileName;
        
        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
            throw new Exception("Erro ao criar ficheiro ZIP");
        }
        
        $downloadedCount = 0;
        $totalFiles = count($storageFiles);
        
        foreach ($storageFiles as $index => $file) {
            $fileName = $file['name'] ?? '';
            $downloadToken = $file['downloadTokens'] ?? '';
            
            if (empty($fileName)) continue;
            
            // Log progress
            $progress = round(($index + 1) / $totalFiles * 100, 1);
            error_log("Processing file " . ($index + 1) . "/$totalFiles ($progress%): $fileName");
            
            // Construct download URL
            $downloadUrl = "https://firebasestorage.googleapis.com/v0/b/{$storageBucket}/o/" . 
                          urlencode($fileName) . "?alt=media";
            
            if (!empty($downloadToken)) {
                $downloadUrl .= "&token=" . $downloadToken;
            }
            
            // Download file content with retry logic
            $maxRetries = 3;
            $fileContent = false;
            $httpCode = 0;
            
            for ($retry = 0; $retry < $maxRetries; $retry++) {
                $ch = curl_init($downloadUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increased timeout
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'ProROLA-WebAdmin/1.0');
                
                $fileContent = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($httpCode === 200 && $fileContent !== false) {
                    break; // Success
                } else {
                    error_log("Retry $retry for $fileName: HTTP $httpCode, Error: $error");
                    if ($retry < $maxRetries - 1) {
                        sleep(1); // Wait before retry
                    }
                }
            }
            
            if ($httpCode === 200 && $fileContent !== false && !empty($fileContent)) {
                // Add file to ZIP with proper path structure
                $zip->addFromString($fileName, $fileContent);
                $downloadedCount++;
                error_log("Successfully downloaded ($downloadedCount/$totalFiles): $fileName (" . strlen($fileContent) . " bytes)");
            } else {
                error_log("Failed to download after $maxRetries attempts: $fileName (HTTP $httpCode)");
            }
            
            // Prevent memory issues with large files
            unset($fileContent);
            
            // Add small delay to prevent rate limiting
            if ($index % 10 === 0 && $index > 0) {
                usleep(500000); // 0.5 second pause every 10 files
            }
        }
        
        $zip->close();
        
        if ($downloadedCount === 0) {
            unlink($zipPath);
            throw new Exception("Nenhum ficheiro foi descarregado com sucesso");
        }
        
        // Send ZIP file to browser
        if (file_exists($zipPath)) {
            $zipSize = filesize($zipPath);
            $zipSizeMB = round($zipSize / (1024 * 1024), 2);
            
            error_log("Storage backup completed: $downloadedCount/$totalFiles files, ZIP size: {$zipSizeMB}MB");
            
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename="' . $zipFileName . '"');
            header('Content-Length: ' . $zipSize);
            header('X-Backup-Stats: ' . json_encode([
                'downloaded' => $downloadedCount,
                'total' => $totalFiles,
                'size_mb' => $zipSizeMB
            ]));
            
            readfile($zipPath);
            unlink($zipPath); // Clean up
            exit();
        } else {
            throw new Exception("Ficheiro ZIP não foi criado");
        }
        
    } catch (Exception $e) {
        error_log("Storage backup failed: " . $e->getMessage());
        $backup_error = "Erro ao criar backup do Storage: " . $e->getMessage();
    }
}

// Handle success messages
$backup_success = '';
if (isset($_GET['backup_success'])) {
    switch ($_GET['backup_success']) {
        case 'complete':
            $backup_success = "Backup completo criado com sucesso!";
            break;
        case 'storage':
            $backup_success = "Backup do Storage criado com sucesso!";
            break;
    }
}

// Handle comprehensive backup request
if ($_POST['action'] ?? '' === 'backup_complete') {
    try {
        $backupData = [
            'backup_info' => [
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '2.0',
                'type' => 'complete_firebase_backup'
            ],
            'firestore' => [],
            'authentication' => [],
            'storage' => []
        ];
        
        // 1. Backup Firestore Collections
        $collections = ['users', 'reports', 'zonasCaca', 'contacts', 'gestoresZonaCaca', 'contactEvents'];
        foreach ($collections as $collection) {
            try {
                $documents = $database->listDocuments($collection);
                if (isset($documents['_nextPageToken'])) {
                    unset($documents['_nextPageToken']);
                }
                $backupData['firestore'][$collection] = $documents;
                error_log("Backed up collection: $collection with " . count($documents) . " documents");
            } catch (Exception $e) {
                error_log("Failed to backup collection $collection: " . $e->getMessage());
                $backupData['firestore'][$collection] = [];
            }
        }
        
        // 2. Backup Firebase Authentication Users
        try {
            $adminToken = $database->getAdminAccessToken();
            if ($adminToken) {
                $authUrl = "https://identitytoolkit.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/accounts:query";
                
                $ch = curl_init($authUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                    'returnUserInfo' => true,
                    'maxResults' => 1000
                ]));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $adminToken
                ]);
                
                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($status === 200) {
                    $authData = json_decode($response, true);
                    $backupData['authentication'] = $authData['users'] ?? [];
                    error_log("Backed up " . count($backupData['authentication']) . " authentication users");
                } else {
                    error_log("Failed to backup authentication users: HTTP $status - $response");
                    $backupData['authentication'] = [];
                }
            }
        } catch (Exception $e) {
            error_log("Failed to backup authentication: " . $e->getMessage());
            $backupData['authentication'] = [];
        }
        
        // 3. Backup Firebase Storage File List
        try {
            $adminToken = $database->getAdminAccessToken();
            if ($adminToken) {
                $storageBucket = FIREBASE_PROJECT_ID . '.firebasestorage.app';
                $storageUrl = "https://firebasestorage.googleapis.com/v0/b/{$storageBucket}/o";
                
                $ch = curl_init($storageUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $adminToken
                ]);
                
                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($status === 200) {
                    $storageData = json_decode($response, true);
                    $backupData['storage'] = $storageData['items'] ?? [];
                    error_log("Backed up " . count($backupData['storage']) . " storage files metadata");
                } else {
                    error_log("Failed to backup storage metadata: HTTP $status - $response");
                    $backupData['storage'] = [];
                }
            }
        } catch (Exception $e) {
            error_log("Failed to backup storage: " . $e->getMessage());
            $backupData['storage'] = [];
        }
        
        // Create backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "prorola_complete_backup_$timestamp.json";
        
        // Set headers for download
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen(json_encode($backupData, JSON_PRETTY_PRINT)));
        
        // Output the backup data
        echo json_encode($backupData, JSON_PRETTY_PRINT);
        exit();
        
    } catch (Exception $e) {
        error_log("Complete backup failed: " . $e->getMessage());
        $backup_error = "Erro ao criar backup completo: " . $e->getMessage();
    }
}

// Get comprehensive system statistics
$stats = [];
$detailedInfo = [];

try {
    // Firestore Collections
    $collections = ['users', 'reports', 'zonasCaca', 'contacts', 'gestoresZonaCaca', 'contactEvents'];
    foreach ($collections as $collection) {
        try {
            // For large collections like zonasCaca, use pagination to get all documents
            if ($collection === 'zonasCaca') {
                $allDocuments = [];
                $pageToken = null;
                $totalFetched = 0;
                
                do {
                    $result = $database->listDocuments($collection, $pageToken, 1000);
                    
                    // Extract nextPageToken before processing
                    $nextPageToken = null;
                    if (is_array($result) && isset($result['_nextPageToken'])) {
                        $nextPageToken = $result['_nextPageToken'];
                        unset($result['_nextPageToken']);
                    }
                    
                    if ($result && is_array($result)) {
                        $totalFetched += count($result);
                        foreach ($result as $docId => $data) {
                            if ($docId !== '_nextPageToken') {
                                $allDocuments[$docId] = $data;
                            }
                        }
                        $pageToken = $nextPageToken;
                    } else {
                        break;
                    }
                } while ($pageToken);
                
                $documents = $allDocuments;
                error_log("System page: Retrieved " . count($documents) . " documents from $collection collection");
            } else {
                // For smaller collections, use single request
            $documents = $database->listDocuments($collection);
            if (isset($documents['_nextPageToken'])) {
                unset($documents['_nextPageToken']);
                }
            }
            
            $stats[$collection] = count($documents);
            
            // Get detailed info for each collection
            $detailedInfo['firestore'][$collection] = [
                'count' => count($documents),
                'sample_fields' => []
            ];
            
            // Get sample fields from first document
            if (!empty($documents)) {
                $firstDoc = reset($documents);
                $detailedInfo['firestore'][$collection]['sample_fields'] = array_keys($firstDoc);
            }
        } catch (Exception $e) {
            $stats[$collection] = 0;
            $detailedInfo['firestore'][$collection] = ['count' => 0, 'error' => $e->getMessage()];
            error_log("Error counting documents in $collection: " . $e->getMessage());
        }
    }
    
    // Firebase Authentication Users
    try {
        $adminToken = $database->getAdminAccessToken();
        error_log("Admin token available: " . ($adminToken ? 'Yes' : 'No'));
        error_log("Firebase Project ID: " . FIREBASE_PROJECT_ID);
        
        if ($adminToken) {
            // Try Firebase Admin SDK REST API for listing users
            // First try the accounts:query endpoint
            $authUrl = "https://identitytoolkit.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/accounts:query";
            error_log("Firebase Auth API URL: " . $authUrl);
            
            $ch = curl_init($authUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                'returnUserInfo' => true,
                'maxResults' => 1000
            ]));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $adminToken
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $httpError = curl_error($ch);
            curl_close($ch);
            
            // If accounts:query fails, try the Admin API endpoint
            if ($status !== 200) {
                error_log("accounts:query failed with status $status, trying Admin API");
                
                $adminAuthUrl = "https://firebase.googleapis.com/v1beta1/projects/" . FIREBASE_PROJECT_ID . "/accounts:query";
                error_log("Trying Admin API URL: " . $adminAuthUrl);
                
                                 $ch = curl_init($adminAuthUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                    'returnUserInfo' => true,
                    'maxResults' => 1000
                ]));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $adminToken
                ]);
                
                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $httpError = curl_error($ch);
                curl_close($ch);
            }
            
            error_log("Final Firebase Auth API request status: $status");
            if ($httpError) {
                error_log("Firebase Auth API curl error: $httpError");
            }
            if ($response && $status !== 200) {
                error_log("Firebase Auth API error response: " . substr($response, 0, 500));
            }
            
            if ($status === 200) {
                $authData = json_decode($response, true);
                $authUsers = $authData['users'] ?? [];
                $stats['auth_users'] = count($authUsers);
                
                $detailedInfo['authentication'] = [
                    'total_users' => count($authUsers),
                    'verified_users' => count(array_filter($authUsers, function($user) {
                        return $user['emailVerified'] ?? false;
                    })),
                    'providers' => []
                ];
                
                // Count providers
                foreach ($authUsers as $user) {
                    foreach ($user['providerUserInfo'] ?? [] as $provider) {
                        $providerId = $provider['providerId'] ?? 'unknown';
                        $detailedInfo['authentication']['providers'][$providerId] = 
                            ($detailedInfo['authentication']['providers'][$providerId] ?? 0) + 1;
                    }
                }
                
                error_log("Successfully retrieved " . count($authUsers) . " Firebase Auth users");
            } else {
                // Log the full error response for debugging
                error_log("Firebase Auth API error response: $response");
                error_log("Firebase Auth API status: $status");
                
                // Fallback: Count users from Firestore collections as approximation
                $firestoreUserCount = ($stats['users'] ?? 0) + ($stats['gestoresZonaCaca'] ?? 0);
                error_log("Fallback user count calculation: users=" . ($stats['users'] ?? 0) . ", gestores=" . ($stats['gestoresZonaCaca'] ?? 0) . ", total=" . $firestoreUserCount);
                
                $stats['auth_users'] = $firestoreUserCount;
                $detailedInfo['authentication'] = [
                    'total_users' => $firestoreUserCount,
                    'verified_users' => 'N/A',
                    'note' => 'Contagem baseada em coleções do Firestore (Auth API indisponível)',
                    'api_status' => "HTTP $status",
                    'firestore_breakdown' => [
                        'users_collection' => $stats['users'] ?? 0,
                        'gestores_collection' => $stats['gestoresZonaCaca'] ?? 0
                    ]
                ];
            }
        } else {
            // Fallback: Count users from Firestore collections
            $firestoreUserCount = ($stats['users'] ?? 0) + ($stats['gestoresZonaCaca'] ?? 0);
            error_log("No admin token - Fallback user count: users=" . ($stats['users'] ?? 0) . ", gestores=" . ($stats['gestoresZonaCaca'] ?? 0) . ", total=" . $firestoreUserCount);
            
            $stats['auth_users'] = $firestoreUserCount;
            $detailedInfo['authentication'] = [
                'total_users' => $firestoreUserCount,
                'verified_users' => 'N/A',
                'note' => 'Contagem baseada em coleções do Firestore (sem token admin)',
                'api_status' => 'No admin token',
                'firestore_breakdown' => [
                    'users_collection' => $stats['users'] ?? 0,
                    'gestores_collection' => $stats['gestoresZonaCaca'] ?? 0
                ]
            ];
        }
    } catch (Exception $e) {
        error_log("Firebase Auth API exception: " . $e->getMessage());
        
        // Fallback: Count users from Firestore collections
        $firestoreUserCount = ($stats['users'] ?? 0) + ($stats['gestoresZonaCaca'] ?? 0);
        error_log("Exception fallback user count: users=" . ($stats['users'] ?? 0) . ", gestores=" . ($stats['gestoresZonaCaca'] ?? 0) . ", total=" . $firestoreUserCount);
        
        $stats['auth_users'] = $firestoreUserCount;
        $detailedInfo['authentication'] = [
            'total_users' => $firestoreUserCount,
            'verified_users' => 'N/A',
            'note' => 'Contagem baseada em coleções do Firestore (erro na API)',
            'api_status' => 'Exception: ' . $e->getMessage(),
            'firestore_breakdown' => [
                'users_collection' => $stats['users'] ?? 0,
                'gestores_collection' => $stats['gestoresZonaCaca'] ?? 0
            ]
        ];
    }
    
    // Firebase Storage Files
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $storageBucket = FIREBASE_PROJECT_ID . '.firebasestorage.app';
            $storageUrl = "https://firebasestorage.googleapis.com/v0/b/{$storageBucket}/o";
            
            $ch = curl_init($storageUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status === 200) {
                $storageData = json_decode($response, true);
                $storageFiles = $storageData['items'] ?? [];
                $stats['storage_files'] = count($storageFiles);
                
                $totalSize = 0;
                $fileTypes = [];
                
                foreach ($storageFiles as $file) {
                    $size = intval($file['size'] ?? 0);
                    $totalSize += $size;
                    
                    $name = $file['name'] ?? '';
                    $extension = pathinfo($name, PATHINFO_EXTENSION);
                    $fileTypes[$extension] = ($fileTypes[$extension] ?? 0) + 1;
                }
                
                $detailedInfo['storage'] = [
                    'total_files' => count($storageFiles),
                    'total_size_bytes' => $totalSize,
                    'total_size_mb' => round($totalSize / (1024 * 1024), 2),
                    'file_types' => $fileTypes
                ];
            } else {
                $stats['storage_files'] = 0;
                $detailedInfo['storage'] = ['error' => "HTTP $status"];
            }
        } else {
            $stats['storage_files'] = 0;
            $detailedInfo['storage'] = ['error' => 'No admin token'];
        }
    } catch (Exception $e) {
        $stats['storage_files'] = 0;
        $detailedInfo['storage'] = ['error' => $e->getMessage()];
    }
    
} catch (Exception $e) {
    error_log("Failed to get system statistics: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Page Load Overlay -->
    <div id="pageLoadOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner-large"></div>
            <h3>A carregar informações do sistema...</h3>
            <p>A consultar base de dados Firebase...</p>
        </div>
    </div>

    <?php include __DIR__ . '/../../includes/header.php'; ?>
    
    <?php include __DIR__ . '/../../includes/sidebar.php'; ?>
            
    <div class="content">
                        <div class="page-header">
            <h1><i class="fas fa-cogs"></i> <?php echo $pageTitle; ?></h1>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay d-none">
            <div class="loading-content">
                <div class="spinner-large"></div>
                <h3>A carregar informações do sistema...</h3>
                <p id="loadingText">Por favor aguarde...</p>
            </div>
        </div>

                <?php if (isset($backup_error)): ?>
                <div class="alert alert-danger" style="margin: 0 1.5rem 1.5rem 1.5rem;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($backup_error); ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($backup_success)): ?>
                <div class="alert alert-success" style="margin: 0 1.5rem 1.5rem 1.5rem;">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($backup_success); ?>
                </div>
                <?php endif; ?>

                <!-- System Statistics -->
                <div class="stats-grid">
                    <!-- Row 1: Main Collections -->
                    <div class="stat-card">
                        <div class="icon bg-blue">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['users'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Utilizadores</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon bg-purple">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['reports'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Relatórios</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon bg-green">
                            <i class="fas fa-binoculars"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['zonasCaca'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Zonas de Caça</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon bg-orange">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['contacts'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Contactos</div>
                        </div>
                    </div>
                    
                    <!-- Row 2: Additional Stats -->
                    <div class="stat-card">
                        <div class="icon bg-indigo">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['gestoresZonaCaca'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Gestores</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon bg-pink">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['auth_users'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Auth Users</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon bg-red">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['contactEvents'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Eventos</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon bg-teal">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="content" style="display: flex !important; flex-direction: column;">
                            <div class="value" style="color: #111827 !important; font-size: 1.25rem; font-weight: 600; display: block !important;"><?php echo number_format($stats['storage_files'] ?? 0); ?></div>
                            <div class="title" style="color: #6B7280 !important; font-size: 0.75rem; display: block !important;">Ficheiros Storage</div>
                        </div>
                    </div>
                </div>

        <!-- Detailed Information -->
        <div class="details-section">
            <div class="details-header">
                <h2><i class="fas fa-info-circle"></i> Informação Detalhada do Sistema</h2>
            </div>
            <div class="details-body">
                
                <!-- Firestore Details -->
                <div class="detail-card">
                    <h3><i class="fas fa-database"></i> Firestore Database</h3>
                    <div class="detail-grid">
                        <?php foreach ($detailedInfo['firestore'] ?? [] as $collection => $info): ?>
                        <div class="detail-item">
                            <div class="detail-main">
                                <strong><?php echo ucfirst($collection); ?>:</strong>
                                <span><?php echo $info['count']; ?> documentos</span>
                            </div>
                            <?php if (!empty($info['sample_fields'])): ?>
                            <div class="fields-list">
                                <small>Campos: <?php echo implode(', ', array_slice($info['sample_fields'], 0, 3)); ?>
                                <?php if (count($info['sample_fields']) > 3): ?>...<?php endif; ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Authentication Details -->
                <div class="detail-card">
                    <h3><i class="fas fa-key"></i> Firebase Authentication</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong>Total de Utilizadores:</strong>
                            <span><?php echo $detailedInfo['authentication']['total_users'] ?? 0; ?></span>
                        </div>
                        <div class="detail-item">
                            <strong>Emails Verificados:</strong>
                            <span><?php echo $detailedInfo['authentication']['verified_users'] ?? 'N/A'; ?></span>
                        </div>
                        <?php if (!empty($detailedInfo['authentication']['providers'])): ?>
                        <div class="detail-item full-width">
                            <strong>Fornecedores de Autenticação:</strong>
                            <div class="providers-list">
                                <?php foreach ($detailedInfo['authentication']['providers'] as $provider => $count): ?>
                                <span class="provider-badge"><?php echo $provider; ?>: <?php echo $count; ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if (isset($detailedInfo['authentication']['firestore_breakdown'])): ?>
                        <div class="detail-item">
                            <strong>Utilizadores (Firestore):</strong>
                            <span><?php echo $detailedInfo['authentication']['firestore_breakdown']['users_collection']; ?></span>
                        </div>
                        <div class="detail-item">
                            <strong>Gestores (Firestore):</strong>
                            <span><?php echo $detailedInfo['authentication']['firestore_breakdown']['gestores_collection']; ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if (isset($detailedInfo['authentication']['note'])): ?>
                        <div class="detail-item full-width">
                            <div class="info-note">
                                <i class="fas fa-info-circle"></i>
                                <?php echo htmlspecialchars($detailedInfo['authentication']['note']); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if (isset($detailedInfo['authentication']['api_status'])): ?>
                        <div class="detail-item full-width">
                            <div class="<?php echo strpos($detailedInfo['authentication']['api_status'], 'HTTP') === 0 ? 'warning-note' : 'info-note'; ?>">
                                <i class="fas fa-<?php echo strpos($detailedInfo['authentication']['api_status'], 'HTTP') === 0 ? 'exclamation-triangle' : 'info-circle'; ?>"></i>
                                API Status: <?php echo htmlspecialchars($detailedInfo['authentication']['api_status']); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Storage Details -->
                <div class="detail-card">
                    <h3><i class="fas fa-cloud"></i> Firebase Storage</h3>
                    <?php if (isset($detailedInfo['storage']['error'])): ?>
                    <div class="error-info">
                        <i class="fas fa-exclamation-triangle"></i>
                        Erro: <?php echo htmlspecialchars($detailedInfo['storage']['error']); ?>
                    </div>
                    <?php else: ?>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong>Total de Ficheiros:</strong>
                            <span><?php echo $detailedInfo['storage']['total_files'] ?? 0; ?></span>
                        </div>
                        <div class="detail-item">
                            <strong>Tamanho Total:</strong>
                            <span><?php echo $detailedInfo['storage']['total_size_mb'] ?? 0; ?> MB</span>
                        </div>
                        <?php if (!empty($detailedInfo['storage']['file_types'])): ?>
                        <div class="detail-item full-width">
                            <strong>Tipos de Ficheiro:</strong>
                            <div class="file-types-list">
                                <?php foreach ($detailedInfo['storage']['file_types'] as $type => $count): ?>
                                <span class="file-type-badge"><?php echo $type ?: 'sem extensão'; ?>: <?php echo $count; ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- System Requirements -->
                <div class="detail-card">
                    <h3><i class="fas fa-server"></i> Requisitos do Sistema</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong>PHP Version:</strong>
                            <span><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="detail-item">
                            <strong>ZipArchive:</strong>
                            <span class="<?php echo class_exists('ZipArchive') ? 'text-success' : 'text-danger'; ?>">
                                <?php echo class_exists('ZipArchive') ? '✓ Disponível' : '✗ Não disponível'; ?>
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>cURL:</strong>
                            <span class="<?php echo function_exists('curl_init') ? 'text-success' : 'text-danger'; ?>">
                                <?php echo function_exists('curl_init') ? '✓ Disponível' : '✗ Não disponível'; ?>
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>JSON:</strong>
                            <span class="<?php echo function_exists('json_encode') ? 'text-success' : 'text-danger'; ?>">
                                <?php echo function_exists('json_encode') ? '✓ Disponível' : '✗ Não disponível'; ?>
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>Temp Directory:</strong>
                            <span class="<?php echo is_writable(sys_get_temp_dir()) ? 'text-success' : 'text-danger'; ?>">
                                <?php echo is_writable(sys_get_temp_dir()) ? '✓ Gravável' : '✗ Não gravável'; ?>
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>Memory Limit:</strong>
                            <span><?php echo ini_get('memory_limit'); ?></span>
                        </div>
                        <?php if (!class_exists('ZipArchive')): ?>
                        <div class="detail-item full-width">
                            <div class="error-note">
                                <i class="fas fa-exclamation-triangle"></i>
                                ZipArchive não disponível - O backup de ficheiros do Storage não funcionará.
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

                <!-- Backup Sections -->
        <div class="backup-section">
            <div class="backup-header">
                <h2><i class="fas fa-download"></i> Backup do Firebase</h2>
            </div>
            <div class="backup-body">
                
                <!-- Complete Backup -->
                <div class="backup-option">
                    <h3><i class="fas fa-database"></i> Backup Completo</h3>
                    <div class="info-alert">
                        <i class="fas fa-info-circle"></i>
                        <div class="info-alert-content">
                            <strong>Inclui:</strong> Firestore Database + Firebase Authentication + Metadados do Storage
                            <br><em>Nota: Imagens não incluídas no JSON devido ao tamanho.</em>
                        </div>
                    </div>
                    
                    <form method="post" id="completeBackupForm">
                        <input type="hidden" name="action" value="backup_complete">
                        <button type="submit" class="btn-primary" id="completeBackupBtn">
                            <i class="fas fa-download"></i>
                            <span class="btn-text">Criar Backup Completo</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </form>
                </div>

                <!-- Storage Files Backup -->
                <div class="backup-option">
                    <h3><i class="fas fa-cloud-download-alt"></i> Backup de Ficheiros do Storage</h3>
                    <div class="info-alert">
                        <i class="fas fa-info-circle"></i>
                        <div class="info-alert-content">
                            <strong>Inclui:</strong> Download direto de todos os ficheiros do Firebase Storage
                            <br><em>Nota: Pode ser um ficheiro grande dependendo do número de imagens.</em>
                        </div>
                    </div>
                    
                    <form method="post" id="storageBackupForm">
                        <input type="hidden" name="action" value="backup_storage">
                        <button type="submit" class="btn-secondary" id="storageBackupBtn" <?php echo !class_exists('ZipArchive') ? 'disabled title="ZipArchive não disponível"' : ''; ?>>
                            <i class="fas fa-cloud-download-alt"></i>
                            <span class="btn-text">Descarregar Ficheiros Storage</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </form>
                    <?php if (!class_exists('ZipArchive')): ?>
                    <div class="backup-warning">
                        <small>
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                            Extensão ZipArchive não disponível. Contacte o administrador do sistema.
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="backup-note">
                    <small>
                        <i class="fas fa-clock"></i>
                        Os processos de backup podem demorar vários minutos dependendo do tamanho dos dados.
                    </small>
                </div>
            </div>
        </div>
            </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery (needed for some functionality) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Profile Modal -->
    <?php include __DIR__ . '/../../includes/profile_modal.php'; ?>
    
    <!-- Logout Modal -->
    <?php include __DIR__ . '/../../includes/logout_modal.php'; ?>

    <style>
    .content {
        padding: 0;
    }

    .page-header {
        background: white;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .page-header h1 {
        margin: 0;
        font-size: 1.5rem;
        color: #374151;
        font-weight: 500;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
        margin: 0 1.5rem 2rem 1.5rem;
    }

    .stat-card {
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 0.75rem;
        display: flex !important;
        align-items: center;
        gap: 0.75rem;
        position: relative;
        overflow: visible;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        min-height: 60px;
    }

    .stat-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }

    .stat-card .icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .stat-card .icon i {
        font-size: 16px;
        color: white;
    }

    .stat-card .content {
        display: flex !important;
        flex-direction: column;
        min-width: 0;
        flex: 1;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .stat-card .value {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827 !important;
        margin: 0;
        line-height: 1.2;
        display: block;
    }

    .stat-card .title {
        font-size: 0.75rem;
        color: #6B7280 !important;
        margin: 0;
        font-weight: 500;
        display: block;
        margin-top: 2px;
    }

    .bg-blue { background-color: #3B82F6; }
    .bg-purple { background-color: #8B5CF6; }
    .bg-green { background-color: #10B981; }
    .bg-orange { background-color: #F59E0B; }
    .bg-indigo { background-color: #6366F1; }
    .bg-pink { background-color: #EC4899; }
    .bg-red { background-color: #EF4444; }
    .bg-teal { background-color: #14B8A6; }

    .details-section, .backup-section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin: 0 1.5rem 2rem 1.5rem;
        overflow: hidden;
    }

    .details-header, .backup-header {
        background: #F9FAFB;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #E5E7EB;
    }

    .details-header h2, .backup-header h2 {
        margin: 0;
        font-size: 1rem;
        color: #374151;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .details-body {
        padding: 1rem 1.5rem;
    }

    .backup-body {
        padding: 1.5rem;
    }

    .detail-card {
        margin-bottom: 1rem;
        padding: 1rem;
        border: 1px solid #E5E7EB;
        border-radius: 6px;
        background: #F9FAFB;
    }

    .detail-card:last-child {
        margin-bottom: 0;
    }

    .detail-card h3 {
        margin: 0 0 0.75rem 0;
        font-size: 0.95rem;
        color: #374151;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 0.5rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        padding: 0.5rem 0.75rem;
        background: white;
        border-radius: 4px;
        border: 1px solid #E5E7EB;
        min-height: auto;
        gap: 0.25rem;
    }

    .detail-item.full-width {
        grid-column: 1 / -1;
    }

    .detail-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .detail-item strong {
        color: #374151;
        font-weight: 600;
        font-size: 0.85rem;
        flex-shrink: 0;
    }

    .detail-item span {
        color: #6B7280;
        font-weight: 500;
        font-size: 0.85rem;
        text-align: right;
    }

    .fields-list, .providers-list, .file-types-list {
        width: 100%;
        margin-top: 0;
    }

    /* Loading Overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.3s ease;
    }

    .loading-content {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
        max-width: 400px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .spinner-large {
        width: 60px;
        height: 60px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #0a7ea4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Backup Options */
    .backup-option {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .backup-option h3 {
        margin-bottom: 1rem;
        color: #374151;
        font-size: 1.1rem;
    }

    .backup-option .info-alert {
        margin-bottom: 1rem;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-secondary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Status colors */
    .text-success {
        color: #10B981 !important;
        font-weight: 600;
    }

    .text-danger {
        color: #EF4444 !important;
        font-weight: 600;
    }

    /* Backup warning */
    .backup-warning {
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: #FEF2F2;
        border: 1px solid #FECACA;
        border-radius: 4px;
    }

    .backup-warning small {
        color: #DC2626;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .fields-list small {
        color: #9CA3AF;
        font-size: 0.7rem;
        line-height: 1.3;
        display: block;
        word-break: break-word;
    }

    .provider-badge, .file-type-badge {
        display: inline-block;
        background: #EFF6FF;
        color: #1D4ED8;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-size: 0.7rem;
        margin: 0.2rem 0.3rem 0.2rem 0;
        border: 1px solid #DBEAFE;
        font-weight: 500;
    }

    .error-info {
        color: #DC2626;
        background: #FEF2F2;
        border: 1px solid #FECACA;
        border-radius: 4px;
        padding: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-note {
        color: #1D4ED8;
        background: #EFF6FF;
        border: 1px solid #DBEAFE;
        border-radius: 4px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .error-note {
        color: #DC2626;
        background: #FEF2F2;
        border: 1px solid #FECACA;
        border-radius: 4px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .warning-note {
        color: #D97706;
        background: #FFFBEB;
        border: 1px solid #FED7AA;
        border-radius: 4px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-alert {
        background-color: #EFF6FF;
        border: 1px solid #BFDBFE;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .info-alert i {
        color: #3B82F6;
        margin-right: 0.75rem;
    }

    .info-alert-content {
        color: #1E40AF;
        font-size: 0.875rem;
        line-height: 1.5;
    }

    .info-alert-content ul {
        margin: 0.5rem 0;
        padding-left: 1.5rem;
    }

    .info-alert-content li {
        margin-bottom: 0.25rem;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
        cursor: pointer;
    }

    .btn-primary:hover:not(:disabled) {
        background-color: #0891b2;
        border-color: #0891b2;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(10, 126, 164, 0.3);
    }

    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .backup-note {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #E5E7EB;
    }

    .backup-note small {
        color: #6B7280;
        font-size: 0.813rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 900px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 0 1rem 1rem 1rem;
        }
        
        .details-section, .backup-section {
            margin: 0 1rem 1rem 1rem;
        }
        
        .detail-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Force text visibility */
    .stat-card .value,
    .stat-card .title {
        color: inherit !important;
        opacity: 1 !important;
        visibility: visible !important;
        font-family: inherit !important;
    }
    </style>

    <script>
    // System page specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Hide page load overlay
        const pageLoadOverlay = document.getElementById('pageLoadOverlay');
        if (pageLoadOverlay) {
            // Add a small delay to ensure everything is rendered
            setTimeout(function() {
                pageLoadOverlay.style.opacity = '0';
                setTimeout(function() {
                    pageLoadOverlay.classList.add('d-none');
                }, 300);
            }, 500);
        }
        // Backup form handling
        const backupForm = document.getElementById('completeBackupForm');
        const storageBackupForm = document.getElementById('storageBackupForm');
        
        if (backupForm) {
            backupForm.addEventListener('submit', function(e) {
                const btn = document.getElementById('completeBackupBtn');
                const btnText = btn.querySelector('.btn-text');
                const spinner = btn.querySelector('.spinner-border');
                
                // Show loading state
                btn.disabled = true;
                btnText.textContent = 'A criar backup completo...';
                spinner.classList.remove('d-none');
                
                // Show loading overlay
                showLoadingOverlay('A criar backup completo do Firebase...', 'Isto pode demorar alguns minutos...');
                
                // Re-enable button after 15 seconds (complete backup takes longer)
                setTimeout(function() {
                    btn.disabled = false;
                    btnText.textContent = 'Criar Backup Completo';
                    spinner.classList.add('d-none');
                    hideLoadingOverlay();
                }, 15000);
            });
        }

        if (storageBackupForm) {
            storageBackupForm.addEventListener('submit', function(e) {
                const btn = document.getElementById('storageBackupBtn');
                const btnText = btn.querySelector('.btn-text');
                const spinner = btn.querySelector('.spinner-border');
                
                // Show loading state
                btn.disabled = true;
                btnText.textContent = 'A descarregar ficheiros...';
                spinner.classList.remove('d-none');
                
                // Show loading overlay
                showLoadingOverlay('A descarregar ficheiros do Storage...', 'A criar ficheiro ZIP com todas as imagens...');
                
                // Re-enable button after 30 seconds (storage backup can take longer)
                setTimeout(function() {
                    btn.disabled = false;
                    btnText.textContent = 'Descarregar Ficheiros Storage';
                    spinner.classList.add('d-none');
                    hideLoadingOverlay();
                }, 30000);
            });
        }

        // Loading overlay functions
        function showLoadingOverlay(title, subtitle) {
            const overlay = document.getElementById('loadingOverlay');
            const titleElement = overlay.querySelector('h3');
            const textElement = overlay.querySelector('#loadingText');
            
            titleElement.textContent = title;
            textElement.textContent = subtitle;
            overlay.classList.remove('d-none');
        }

        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            overlay.classList.add('d-none');
        }

        // Initialize tooltips if Bootstrap is available
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Debug: Check if card content is loading
        console.log('System page loaded');
        const statCards = document.querySelectorAll('.stat-card');
        console.log('Found stat cards:', statCards.length);
        
        // Ensure card content is visible
        statCards.forEach((card, index) => {
            const content = card.querySelector('.content');
            const value = card.querySelector('.value');
            const title = card.querySelector('.title');
            
            if (content) content.style.display = 'flex';
            if (value) {
                value.style.display = 'block';
                value.style.visibility = 'visible';
            }
            if (title) {
                title.style.display = 'block';
                title.style.visibility = 'visible';
            }
        });
    });
    </script>
</body>
</html> 