<?php
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in and has admin role
try {
    requireAdmin();
} catch (Exception $e) {
    header('Location: ../auth/login.php');
    exit();
}

// Initialize database if not already done
if (!isset($database)) {
    $database = new Firebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
    error_log("Database initialized for map page");
    
    // Check if we have a valid token
    if (isset($_SESSION['user']) && isset($_SESSION['user']['auth_token'])) {
        error_log("Using auth token from session for map data");
    } else {
        error_log("WARNING: No auth token found in session");
    }
}

// Get filter parameters
$type = $_GET['type'] ?? 'all';
$dateRange = $_GET['dateRange'] ?? 'all';

// Debug filter parameters
error_log("Filter parameters - type: '$type', dateRange: '$dateRange'");

// Progressive loading strategy - no limits on trajectories, smart contact loading
$loadContactsImmediately = false; // Load contacts on demand via AJAX
$maxContactsForInitialLoad = 0; // Don't load contacts initially for performance

// Add simple caching for performance with compression
$cacheDir = '../../cache/map/';
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0755, true);
}
$cacheFile = $cacheDir . 'map_data_all.json.gz'; // Generic cache for all filter combinations
$cacheExpiry = 300; // 5 minutes cache

// Debug cache file path
error_log("Cache file path: $cacheFile");

// Check if we have valid cached data with compression support
$useCache = false;

// Temporary: Force fresh data loading for debugging
$forceRefresh = isset($_GET['refresh']) || isset($_GET['debug']); // Refresh if ?refresh or ?debug
if (!$forceRefresh && file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheExpiry) {
    $compressedData = file_get_contents($cacheFile);
    if ($compressedData !== false) {
        $cachedContent = gzuncompress($compressedData);
        if ($cachedContent !== false) {
            $cachedData = json_decode($cachedContent, true);
            if ($cachedData && isset($cachedData['reports']) && isset($cachedData['trajetos'])) {
                $reports = $cachedData['reports'];
                $trajetos = $cachedData['trajetos'];
                $useCache = true;
                error_log("Using compressed cached map data (generic cache)");
                error_log("Cached data counts - Reports: " . count($reports) . ", Trajectories: " . count($trajetos));
                
                // Debug: Verify data types
                error_log("Reports is array: " . (is_array($reports) ? 'yes' : 'no'));
                error_log("Trajetos is array: " . (is_array($trajetos) ? 'yes' : 'no'));
            } else {
                error_log("Cache data invalid or incomplete - cachedData exists: " . (isset($cachedData) ? 'yes' : 'no') . 
                         ", has reports: " . (isset($cachedData['reports']) ? 'yes' : 'no') . 
                         ", has trajetos: " . (isset($cachedData['trajetos']) ? 'yes' : 'no'));
            }
        }
    }
}

// Initialize arrays (only if not already set by cache)
if (!isset($reports)) $reports = [];
if (!isset($trajetos)) $trajetos = [];
$error = null;

// Debug cache status
error_log("Cache status - useCache: " . ($useCache ? 'true' : 'false') . ", cacheFile: $cacheFile");

// Get reports and trajetos from Firestore (only if not using cache)
if (!$useCache) {
try {
    // Get reports with pagination and limits
    $reports = [];
    $pageToken = null;
    
    error_log("Starting to load reports from Firestore...");
    
    do {
        $pageReports = $database->listDocuments('reports', $pageToken, 1000);
        
        if (!is_array($pageReports)) {
            error_log("ERROR: Reports query returned non-array: " . gettype($pageReports));
            break;
        }
        
        // Extract pagination token if present
        $pageToken = $pageReports['_nextPageToken'] ?? null;
        unset($pageReports['_nextPageToken']); // Remove pagination token from results
        
        // Merge results
        $reports = array_merge($reports, $pageReports);
        
        // No artificial limits - load all reports for complete data visualization
        
    } while ($pageToken);
    
    // Get manual trajetos from 'zonas' collection with pagination
    $manualTrajetos = [];
    $pageToken = null;
    
    error_log("Starting to load manual trajectories from 'zonas' collection...");
    
    do {
        $pageTrajetos = $database->listDocuments('zonas', $pageToken, 1000);
        
        if (!is_array($pageTrajetos)) {
            error_log("ERROR: Zonas query returned non-array: " . gettype($pageTrajetos));
            break;
        }
        
        // Extract pagination token if present
        $pageToken = $pageTrajetos['_nextPageToken'] ?? null;
        unset($pageTrajetos['_nextPageToken']); // Remove pagination token from results
        
        // Merge results
        $manualTrajetos = array_merge($manualTrajetos, $pageTrajetos);
        
    } while ($pageToken);
    
    // Get GPS trajetos from 'gestorMobile_trajetos' collection with pagination
    $gpsTrajetos = [];
    $pageToken = null;
    
    error_log("Starting to load GPS trajectories from 'gestorMobile_trajetos' collection...");
    
    do {
        $pageGpsTrajetos = $database->listDocuments('gestorMobile_trajetos', $pageToken, 1000);
        
        if (!is_array($pageGpsTrajetos)) {
            error_log("ERROR: GPS trajectories query returned non-array: " . gettype($pageGpsTrajetos));
            break;
        }
        
        // Extract pagination token if present
        $pageToken = $pageGpsTrajetos['_nextPageToken'] ?? null;
        unset($pageGpsTrajetos['_nextPageToken']); // Remove pagination token from results
        
        // Merge results
        $gpsTrajetos = array_merge($gpsTrajetos, $pageGpsTrajetos);
        
    } while ($pageToken);
    
    // Debug: Log raw data counts before processing
    error_log("Raw data loaded - Reports: " . count($reports) . ", Manual trajectories: " . count($manualTrajetos) . ", GPS trajectories: " . count($gpsTrajetos));
    
    // Filter out test accounts before processing
    $testEmails = ['<EMAIL>']; // Add more test emails here if needed
    
    $originalReportCount = count($reports);
    $reports = array_filter($reports, function($report) use ($testEmails) {
        $userEmail = $report['userEmail'] ?? '';
        return !in_array($userEmail, $testEmails);
    });
    $filteredReportCount = count($reports);
    
    if ($originalReportCount !== $filteredReportCount) {
        error_log("Filtered out " . ($originalReportCount - $filteredReportCount) . " test reports (from test accounts)");
    }
    
    // Debug: Check if reports have location data
    $reportsWithLocation = 0;
    foreach ($reports as $report) {
        if (isset($report['location']) && is_array($report['location'])) {
            $reportsWithLocation++;
        }
    }
    error_log("Reports with location data: $reportsWithLocation out of " . count($reports));
    
    // Combine all trajectories and add type indicators
    $trajetos = [];
    
    // Process ALL manual trajectories - no limits for complete visualization
    foreach ($manualTrajetos as $docId => $trajeto) {
        if (isset($trajeto['coordinates']) && is_array($trajeto['coordinates']) && !empty($trajeto['coordinates'])) {
            // Filter out test accounts
            $createdByEmail = $trajeto['createdByEmail'] ?? '';
            if (in_array($createdByEmail, $testEmails)) {
                continue; // Skip test account trajectories
            }
            
            if (!isset($trajeto['id'])) {
                $trajeto['id'] = $docId;
            }
            $trajeto['type'] = 'Manual';
            $trajetos[] = $trajeto;
        }
    }
    
    // Process ALL GPS trajectories - no limits for complete visualization
    foreach ($gpsTrajetos as $docId => $trajeto) {
        if ((isset($trajeto['coordinates']) && is_array($trajeto['coordinates']) && !empty($trajeto['coordinates'])) ||
            (isset($trajeto['route']) && is_array($trajeto['route']) && !empty($trajeto['route']))) {
            
            // Filter out test accounts
            $createdByEmail = $trajeto['createdByEmail'] ?? '';
            if (in_array($createdByEmail, $testEmails)) {
                continue; // Skip test account trajectories
            }
            
            if (!isset($trajeto['id'])) {
                $trajeto['id'] = $docId;
            }
            $trajeto['type'] = 'GPS';
            
            // Standardize coordinates field for GPS trajectories
            if (!isset($trajeto['coordinates']) && isset($trajeto['route'])) {
                $trajeto['coordinates'] = $trajeto['route'];
            }
            
            $trajetos[] = $trajeto;
        }
    }
    
    // PROGRESSIVE LOADING: Minimize data transfer and optimize for visualization
    foreach ($trajetos as &$trajeto) {
        $trajeto['contacts'] = []; // Empty initially
        $trajeto['contactsLoaded'] = false; // Flag to track if contacts are loaded
        $trajeto['hasContacts'] = null; // Will be determined on demand
        
        // Data minimization: Keep only essential fields for map rendering
        // Include both date field variants to ensure compatibility
        $essentialFields = ['id', 'name', 'type', 'coordinates', 'createdAt', 'created_at', 'createdBy', 'createdByEmail', 'distance'];
        $optimizedTrajeto = [];
        foreach ($essentialFields as $field) {
            if (isset($trajeto[$field])) {
                $optimizedTrajeto[$field] = $trajeto[$field];
            }
        }
        $trajeto = $optimizedTrajeto;
        
        // Coordinate optimization: Reduce precision for initial view (keep full precision in cache)
        if (isset($trajeto['coordinates']) && is_array($trajeto['coordinates'])) {
            foreach ($trajeto['coordinates'] as &$coord) {
                if (isset($coord['lat']) && isset($coord['lng'])) {
                    // Round to 6 decimal places (sufficient for map display, reduces data size)
                    $coord['lat'] = round((float)$coord['lat'], 6);
                    $coord['lng'] = round((float)$coord['lng'], 6);
                }
            }
            unset($coord);
        }
    }
    unset($trajeto); // Break reference
    
    // Raw data loaded - filtering will be applied after cache handling
    error_log("Raw data loaded (after test account filtering) - Reports: " . count($reports) . ", Trajectories: " . count($trajetos));
    
} catch (Exception $e) {
    error_log("Map error: " . $e->getMessage());
    $error = "Error loading map data: " . $e->getMessage();
    $reports = [];
    $trajetos = [];
}

    // Save unfiltered data to cache if we loaded fresh data
    // This allows the same cache to be used for different filter combinations
    if (!$useCache && !isset($error)) {
        $cacheData = [
            'reports' => $reports,
            'trajetos' => $trajetos,
            'timestamp' => time()
        ];
        $compressedData = gzcompress(json_encode($cacheData));
        
        // Save to cache file (same as used for loading)
        file_put_contents($cacheFile, $compressedData);
        error_log("Saved unfiltered map data to cache");
    }
} // End of if (!$useCache)

// Apply filters to data (whether cached or fresh)
// This ensures consistent filtering regardless of data source
if (!isset($error)) {
    // Apply proper filtering now that cache issue is fixed
    $skipFiltering = false;
    
    if ($skipFiltering) {
        error_log("TEMPORARY: Skipping all filtering for debugging");
        error_log("Final data counts (no filtering) - Reports: " . count($reports) . ", Trajectories: " . count($trajetos));
    } else {
    // Debug: Check data structure before filtering
    error_log("Before filtering - Reports count: " . count($reports) . ", Trajetos count: " . count($trajetos));
    if (!empty($reports) && is_array($reports)) {
        $sampleReport = $reports[0];
        if (is_array($sampleReport)) {
            error_log("Sample report structure: " . json_encode(array_keys($sampleReport)));
        }
    }
    if (!empty($trajetos) && is_array($trajetos)) {
        $sampleTrajeto = $trajetos[0];
        if (is_array($sampleTrajeto)) {
            error_log("Sample trajeto structure: " . json_encode(array_keys($sampleTrajeto)));
        }
    }
    
    // Apply filters to reports
        $filteredReports = [];
        
        foreach ($reports as $report) {
            $include = true;
            
        // Filter by type (include reports if type is 'all' or 'reports')
            // Note: For 'contactos' filter, we still exclude reports but keep trajectories for contact loading
            if ($type !== 'all' && $type !== 'reports') {
                $include = false;
            error_log("Report filtered out by type - type: '$type'");
            }
            
            // Filter by date range
            if ($dateRange !== 'all' && isset($report['created_at'])) {
                $timestamp = $report['created_at'];
                switch ($dateRange) {
                    case 'today':
                        if ($timestamp < strtotime('today')) {
                            $include = false;
                        error_log("Report filtered out by date (today) - timestamp: $timestamp");
                        }
                        break;
                    case 'week':
                        if ($timestamp < strtotime('-7 days')) {
                            $include = false;
                        error_log("Report filtered out by date (week) - timestamp: $timestamp");
                        }
                        break;
                    case 'month':
                        if ($timestamp < strtotime('-30 days')) {
                            $include = false;
                        error_log("Report filtered out by date (month) - timestamp: $timestamp");
                        }
                        break;
                }
        } else if ($dateRange !== 'all' && !isset($report['created_at'])) {
            $include = false;
            error_log("Report filtered out - no created_at field");
            }
            
            if ($include) {
                $filteredReports[] = $report;
            }
        }
        
        $reports = $filteredReports;
    
    // Apply filters to trajetos
        $filteredTrajetos = [];
        
        foreach ($trajetos as $trajeto) {
            $include = true;
            
        // Filter by type (include trajectories if type is 'all', 'trajetos', or 'contactos')
            // For 'contactos' filter, we keep trajectories for contact loading but hide them visually
            if ($type !== 'all' && $type !== 'trajetos' && $type !== 'contactos') {
                $include = false;
            error_log("Trajeto filtered out by type - type: '$type'");
            }
            
            // Filter by date range
            if ($dateRange !== 'all' && isset($trajeto['createdAt'])) {
                $timestamp = strtotime($trajeto['createdAt']);
                switch ($dateRange) {
                    case 'today':
                        if ($timestamp < strtotime('today')) {
                            $include = false;
                        error_log("Trajeto filtered out by date (today) - timestamp: $timestamp");
                        }
                        break;
                    case 'week':
                        if ($timestamp < strtotime('-7 days')) {
                            $include = false;
                        error_log("Trajeto filtered out by date (week) - timestamp: $timestamp");
                        }
                        break;
                    case 'month':
                        if ($timestamp < strtotime('-30 days')) {
                            $include = false;
                        error_log("Trajeto filtered out by date (month) - timestamp: $timestamp");
                        }
                        break;
                }
        } else if ($dateRange !== 'all' && !isset($trajeto['createdAt'])) {
            $include = false;
            error_log("Trajeto filtered out - no createdAt field");
            }
            
            if ($include) {
                $filteredTrajetos[] = $trajeto;
            }
        }
        
        $trajetos = $filteredTrajetos;
    
    // Debug: Log data counts after final filtering
    error_log("After final filtering (type=$type, dateRange=$dateRange) - Reports: " . count($reports) . ", Trajectories: " . count($trajetos));
    
    // TEMPORARY DEBUG: Check if filtering is the issue
    if (count($reports) == 0 && count($trajetos) == 0) {
        error_log("WARNING: All data was filtered out! This suggests a filtering bug.");
        error_log("Filter conditions - type: '$type', dateRange: '$dateRange'");
    }
    } // End of else block for filtering
} // End of if (!isset($error))

// Ensure arrays are properly initialized
$reports = is_array($reports) ? $reports : [];
$trajetos = is_array($trajetos) ? $trajetos : [];

// Count statistics
$reportCount = count($reports);
$trajetoCount = count($trajetos);

// Calculate real contact count from both collections
$contactCount = 0;

// Define test emails for filtering (same as used earlier in the file)
$testEmails = ['<EMAIL>'];

try {
    // Count contacts from regular contacts collection
    $regularContacts = $database->listDocuments('contacts', null, 1000);
    if (is_array($regularContacts)) {
        unset($regularContacts['_nextPageToken']); // Remove pagination token
        foreach ($regularContacts as $contact) {
            // Skip if contact is not an array
            if (!is_array($contact)) {
                continue;
            }
            
            // Filter out test accounts
            $skipContact = false;
            if (isset($contact['trajectoryId'])) {
                try {
                    $trajectory = $database->getDocument('zonas', $contact['trajectoryId']);
                    if ($trajectory && is_array($trajectory) && isset($trajectory['createdByEmail']) && in_array($trajectory['createdByEmail'], $testEmails)) {
                        $skipContact = true;
                    }
                } catch (Exception $e) {
                    // If trajectory fetch fails, don't skip the contact
                    error_log("Failed to fetch trajectory for contact filtering: " . $e->getMessage());
                }
            }
            if (!$skipContact) {
                $contactCount++;
            }
        }
    }
    
    // Count contacts from mobile contacts collection  
    $mobileContacts = $database->listDocuments('gestorMobile_contacts', null, 1000);
    if (is_array($mobileContacts)) {
        unset($mobileContacts['_nextPageToken']); // Remove pagination token
        foreach ($mobileContacts as $contact) {
            // Skip if contact is not an array
            if (!is_array($contact)) {
                continue;
            }
            
            // Filter out test accounts
            $skipContact = false;
            if (isset($contact['trajectoryId'])) {
                try {
                    $trajectory = $database->getDocument('gestorMobile_trajetos', $contact['trajectoryId']);
                    if ($trajectory && is_array($trajectory) && isset($trajectory['createdByEmail']) && in_array($trajectory['createdByEmail'], $testEmails)) {
                        $skipContact = true;
                    }
                } catch (Exception $e) {
                    // If trajectory fetch fails, don't skip the contact
                    error_log("Failed to fetch trajectory for contact filtering: " . $e->getMessage());
                }
            }
            if (!$skipContact) {
                $contactCount++;
            }
        }
    }
} catch (Exception $e) {
    error_log("Error counting contacts: " . $e->getMessage());
    $contactCount = 0;
}

// Debug: Log what data we have
error_log("Map data loaded - Reports: $reportCount, Trajectories: $trajetoCount, Contacts: $contactCount");
if (!empty($trajetos) && is_array($trajetos)) {
    $sampleTrajeto = $trajetos[0];
    if (is_array($sampleTrajeto)) {
        $sampleCoords = array_slice($sampleTrajeto['coordinates'] ?? [], 0, 2);
        error_log("Sample trajectory coordinates: " . json_encode($sampleCoords));
    }
}

// Removed getTrajectoryContacts function - now using optimized bulk loading approach
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa - <?php echo SITE_NAME; ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
        .map-container {
            background: white;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            margin-bottom: 0;
            overflow: hidden;
            position: relative;
            height: calc(98vh - 80px);
        }
        
        #reports-map {
            height: 100%;
            width: 100%;
            z-index: 1;
        }
        
        .map-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.8s ease-out;
            backdrop-filter: blur(2px);
        }

        .map-loading.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .map-loading.show {
            opacity: 1;
            pointer-events: all;
        }

        .map-loading-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 24px;
            color: #6c757d;
            animation: pulse 2s infinite;
        }

        .map-loading-text {
            color: #6c757d;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .map-loading-progress {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 20px;
            text-align: center;
            min-height: 20px;
        }

        .map-loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e9ecef;
            border-top: 3px solid #0EA5E9;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Performance optimizations */
        .map-container {
            transform: translateZ(0); /* Enable GPU acceleration */
            will-change: transform; /* Hint to browser for optimization */
        }

        #reports-map {
            contain: layout style paint; /* CSS containment for better performance */
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .map-filters {
            position: absolute;
            top: 20px;
            left: 20px;
            background: white;
            padding: 1rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            max-width: 300px;
            backdrop-filter: blur(8px);
        }
        
        .filters {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .filter-group label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .filter-group select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.875rem;
            color: var(--text-color);
            background-color: white;
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .filter-group select:focus {
            outline: none;
            border-color: #0EA5E9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }
        
        .map-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: white;
            padding: 1rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(8px);
        }
        
        .map-stats h4 {
            margin: 0 0 0.75rem 0;
            font-size: 0.875rem;
            color: var(--text-color);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stats-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }
        
        .stats-label {
            color: #6B7280;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stats-value {
            font-weight: 600;
            color: var(--text-color);
            background: #F3F4F6;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            min-width: 2rem;
            text-align: center;
        }
        
        /* Custom Google Maps Info Window Styling */
        .gm-style .gm-style-iw-c {
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .gm-style .gm-style-iw-d {
            overflow: hidden !important;
        }
        
        .map-popup {
            font-size: 0.875rem;
            line-height: 1.4;
            min-width: 280px;
            padding: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .map-popup h3 {
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            color: #1F2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #E5E7EB;
            font-weight: 600;
        }
        
        .map-popup h3 i {
            color: #0EA5E9;
            font-size: 1rem;
        }
        
        .map-popup p {
            margin: 0.75rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #4B5563;
        }
        
        .map-popup i {
            width: 16px;
            color: #6B7280;
            margin-top: 0;
            flex-shrink: 0;
        }
        
        .map-popup .device-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            background-color: #F3F4F6;
            color: #374151;
            border: 1px solid #E5E7EB;
        }
        
        .map-popup .device-badge i {
            width: auto;
            margin: 0;
            color: inherit;
        }
        
        .map-popup .actions {
            margin-top: 1.25rem;
            padding-top: 1rem;
            border-top: 2px solid #E5E7EB;
            display: flex;
            justify-content: center;
        }
        
        .map-popup .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%);
            color: white;
            border: none;
            transition: all 0.2s ease;
            text-decoration: none;
            width: 100%;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
        }
        
        .map-popup .btn:hover {
            background: linear-gradient(135deg, #0284C7 0%, #0369A1 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
        }
        
        .map-popup .btn i {
            color: white;
            width: auto;
            margin: 0;
        }
        
        /* Debug styles */
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 12px;
            display: none;
        }
        
        .device-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            background-color: #F3F4F6;
            color: #374151;
        }
        
        .device-badge i {
            font-size: 14px;
        }

        /* Trajectory specific styling */
        .trajectory-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            background-color: #ECFDF5;
            color: #059669;
            border: 1px solid #D1FAE5;
        }

        .trajectory-badge i {
            font-size: 14px;
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .alert-danger {
            background-color: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        .alert-info {
            background-color: #f0f9ff;
            border-color: #0ea5e9;
            color: #0369a1;
        }

        /* Button styling */
        .btn {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
            text-decoration: none;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0284C7 0%, #0369A1 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
        }

        .btn-secondary {
            background: #F3F4F6;
            color: #6B7280;
            border: 1px solid #E5E7EB;
        }

        .btn-secondary:hover {
            background: #E5E7EB;
            color: #374151;
        }

        .d-flex {
            display: flex;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        @media (max-width: 768px) {
            .map-filters {
                top: 10px;
                left: 10px;
                right: 10px;
                max-width: none;
                padding: 0.75rem;
            }
            
            .map-stats {
                bottom: 10px;
                left: 10px;
                right: 10px;
                padding: 0.75rem;
            }
            
            .stats-item {
                font-size: 0.8rem;
            }
            
            .map-filters h4 {
                font-size: 0.8rem;
            }
            
            .filter-group label {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <?php 
    require_once '../../includes/header.php';
    require_once '../../includes/sidebar.php';
    ?>
    
    <div class="content">
        <!-- Debug information -->
        <div class="debug-info">
            <p>Reports count: <?php echo count($reports); ?></p>
            <p>Trajetos count: <?php echo count($trajetos); ?></p>
            <p>Current filters: Type=<?php echo $type; ?>, DateRange=<?php echo $dateRange; ?></p>
        </div>
        
        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if (count($trajetos) > 100): ?>
        <div class="alert alert-info" style="margin: 0 1rem 1rem 1rem; background-color: #f0f9ff; border-color: #0ea5e9; color: #0369a1;">
            <i class="fas fa-info-circle"></i>
            <strong>Visualização otimizada:</strong>
            Mostrando <?php echo count($trajetos); ?> trajetos em clusters. 
            Zoom para ver detalhes e contactos individuais.
        </div>
        <?php endif; ?>
        
        <div class="map-container">
            <div id="reports-map"></div>
            
            <div id="mapLoading" class="map-loading show">
                <div class="map-loading-icon">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
                <div class="map-loading-text">A carregar mapa...</div>
                <div id="loadingProgress" class="map-loading-progress">A inicializar...</div>
                <div class="map-loading-spinner"></div>
            </div>
            
            <div class="map-filters">
                <h4 style="margin: 0 0 1rem 0; font-size: 0.875rem; color: var(--text-color); font-weight: 600; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-filter"></i>
                    Filtros
                </h4>
                <div class="filters">
                    <div class="filter-group">
                        <label for="type">
                            <i class="fas fa-layer-group" style="margin-right: 0.5rem; color: #6B7280;"></i>
                            Tipo
                        </label>
                        <select name="type" id="type">
                            <option value="all" <?php echo $type === 'all' ? 'selected' : ''; ?>>Todos</option>
                            <option value="reports" <?php echo $type === 'reports' ? 'selected' : ''; ?>>Relatórios</option>
                            <option value="trajetos" <?php echo $type === 'trajetos' ? 'selected' : ''; ?>>Trajetos</option>
                            <option value="contactos" <?php echo $type === 'contactos' ? 'selected' : ''; ?>>Contactos</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="dateRange">
                            <i class="fas fa-calendar-alt" style="margin-right: 0.5rem; color: #6B7280;"></i>
                            Período
                        </label>
                        <select name="dateRange" id="dateRange">
                            <option value="all" <?php echo $dateRange === 'all' ? 'selected' : ''; ?>>Todos</option>
                            <option value="today" <?php echo $dateRange === 'today' ? 'selected' : ''; ?>>Hoje</option>
                            <option value="week" <?php echo $dateRange === 'week' ? 'selected' : ''; ?>>Última Semana</option>
                            <option value="month" <?php echo $dateRange === 'month' ? 'selected' : ''; ?>>Último Mês</option>
                        </select>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" id="filterBtn" style="flex: 1;">
                            <i class="fas fa-search"></i>
                            Filtrar
                        </button>
                        <button type="button" class="btn btn-secondary" id="clearBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="map-stats">
                <h4>
                    <i class="fas fa-chart-bar"></i>
                    Estatísticas
                </h4>
                <div class="stats-item">
                    <span class="stats-label">
                        <i class="fas fa-map-marker-alt"></i>
                        Relatórios
                    </span>
                    <span class="stats-value"><?php echo count($reports); ?></span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">
                        <i class="fas fa-route"></i>
                        Trajetos
                    </span>
                    <span class="stats-value"><?php echo count($trajetos); ?></span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">
                        <i class="fas fa-phone"></i>
                        Contactos
                    </span>
                    <span class="stats-value"><?php echo $contactCount; ?></span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">
                        <i class="fas fa-calendar"></i>
                        Período
                    </span>
                    <span class="stats-value">
                        <?php 
                        switch($dateRange) {
                            case 'today': echo 'Hoje'; break;
                            case 'week': echo '7 dias'; break;
                            case 'month': echo '30 dias'; break;
                            default: echo 'Todos'; break;
                        }
                        ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let map;
        let markers = [];
        let markerClusterer;
        
        // Current filter settings
        const currentFilterType = '<?php echo $type; ?>';
        const currentDateRange = '<?php echo $dateRange; ?>';
        
        // Progress update function
        function updateLoadingProgress(message) {
            const progressElement = document.getElementById('loadingProgress');
            if (progressElement) {
                progressElement.textContent = message;
            }
        }
        
        // Hide loading screen
        function hideLoadingScreen() {
            const loadingElement = document.getElementById('mapLoading');
            if (loadingElement) {
                loadingElement.classList.add('hidden');
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                }, 800);
            }
        }
        
        // Initialize Google Map
        function initMap() {
            console.log('Initializing Google Maps...');
            updateLoadingProgress('A inicializar Google Maps...');
            
            map = new google.maps.Map(document.getElementById('reports-map'), {
                zoom: 7,
                center: { lat: 39.5, lng: -8.0 }, // Portugal center
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_RIGHT,
                    mapTypeIds: [
                        google.maps.MapTypeId.ROADMAP,
                        google.maps.MapTypeId.SATELLITE,
                        google.maps.MapTypeId.HYBRID
                    ]
                },
                streetViewControl: true,
                fullscreenControl: true,
                zoomControl: true,
                styles: [
                    {
                        featureType: "poi",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    }
                ]
            });
            
            console.log('Google Maps initialized');
            updateLoadingProgress('A carregar dados...');
            
            // Array to store all markers for clustering
            const mapMarkers = [];
            const bounds = new google.maps.LatLngBounds();
            let markerCount = 0;
            
            <?php
            if (!empty($reports)) {
                echo "console.log('Loading " . count($reports) . " reports...');";
            } else {
                echo "console.log('No reports found to display');";
            }
            if (!empty($reports)) {
                foreach ($reports as $report) {
                    if (isset($report['location']) && is_array($report['location'])) {
                        $lat = $report['location']['latitude'];
                        $lng = $report['location']['longitude'];
                        $location = get_detailed_location($lat, $lng);
                        $timestamp = isset($report['created_at']) ? $report['created_at'] : time();
                        $date = format_date($timestamp);
                        $comment = !empty($report['comment']) ? htmlspecialchars($report['comment']) : 'Sem descrição';
                        $id = $report['id'];
                        $userName = htmlspecialchars($report['userName'] ?? 'N/A');
                        $userEmail = htmlspecialchars($report['userEmail'] ?? 'N/A');
                        
                        // Get device info
                        $platform = $report['deviceInfo']['platform'] ?? '';
                        $deviceIcon = '';
                        $deviceText = '';
                        
                        if (strtolower($platform) === 'android') {
                            $deviceIcon = 'fab fa-android';
                            $deviceText = 'Android';
                        } elseif (strtolower($platform) === 'ios') {
                            $deviceIcon = 'fab fa-apple';
                            $deviceText = 'iPhone';
                        } else {
                            $deviceIcon = 'fas fa-mobile-alt';
                            $deviceText = 'Dispositivo';
                        }
                        
                        echo "
                            console.log('Adding marker at $lat, $lng');
                            
                            const position$id = { lat: $lat, lng: $lng };
                            const marker$id = new google.maps.Marker({
                                position: position$id,
                                title: 'Relatório de Observação',
                                icon: {
                                    url: 'data:image/svg+xml;base64,' + btoa(`
                                        <svg xmlns='http://www.w3.org/2000/svg' width='28' height='28' viewBox='0 0 24 24'>
                                            <defs>
                                                <filter id='shadow' x='-50%' y='-50%' width='200%' height='200%'>
                                                    <feDropShadow dx='1' dy='1' stdDeviation='1' flood-color='#000000' flood-opacity='0.4'/>
                                                </filter>
                                            </defs>
                                            <circle cx='12' cy='9' r='7' fill='#FFFFFF' stroke='#DC2626' stroke-width='1.5' filter='url(#shadow)'/>
                                            <path fill='#DC2626' d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z' filter='url(#shadow)'/>
                                        </svg>
                                    `),
                                    scaledSize: new google.maps.Size(28, 28),
                                    anchor: new google.maps.Point(14, 28)
                                }
                            });
                            
                            const infoWindow$id = new google.maps.InfoWindow({
                                content: `
                                    <div class='map-popup'>
                                        <h3>
                                            <i class='fas fa-binoculars'></i>
                                            Relatório de Observação
                                        </h3>
                                        <p><i class='fas fa-calendar'></i> $date</p>
                                        <p><i class='fas fa-user'></i> $userName</p>
                                        <p><i class='fas fa-envelope'></i> $userEmail</p>
                                        <p><i class='fas fa-map-marker-alt'></i> $location</p>
                                        <p><i class='fas fa-comment'></i> $comment</p>
                                        <p><i class='fas fa-mobile-alt'></i> <span class='device-badge'><i class='$deviceIcon'></i> $deviceText</span></p>
                                        <div class='actions'>
                                            <a href='../reports/view.php?id=$id' class='btn'>
                                                <i class='fas fa-eye'></i>
                                                Ver Detalhes
                                            </a>
                                        </div>
                                    </div>
                                `
                            });
                            
                            marker$id.addListener('click', () => {
                                // Close all other info windows
                                markers.forEach(m => {
                                    if (m.infoWindow) {
                                        m.infoWindow.close();
                                    }
                                });
                                infoWindow$id.open(map, marker$id);
                            });
                            
                            // Store marker reference for cleanup
                            markers.push({
                                marker: marker$id,
                                infoWindow: infoWindow$id
                            });
                            
                            // Add reportId property for filtering
                            marker$id.reportId = '$id';
                            
                            // Add to clustering array
                            mapMarkers.push(marker$id);
                            
                            bounds.extend(position$id);
                            markerCount++;
                        ";
                    }
                }
            }
            ?>
            
            console.log('Report markers added:', markerCount);
            
            // Add trajetos to the map
            <?php
            if (!empty($trajetos)) {
                echo "console.log('Loading " . count($trajetos) . " trajectories...');";
            } else {
                echo "console.log('No trajectories found to display');";
            }
            if (!empty($trajetos)) {
                foreach ($trajetos as $trajeto) {
                    if (isset($trajeto['coordinates']) && is_array($trajeto['coordinates']) && !empty($trajeto['coordinates'])) {
                        $id = preg_replace('/[^a-zA-Z0-9_]/', '_', $trajeto['id']); // Sanitize ID for JavaScript
                        $originalId = $trajeto['id']; // Keep original for debugging
                        $name = htmlspecialchars($trajeto['name'] ?? 'Trajeto sem nome');
                        $description = htmlspecialchars($trajeto['description'] ?? 'Sem descrição');
                        $distance = htmlspecialchars($trajeto['distance'] ?? '0 km');
                        $createdBy = htmlspecialchars($trajeto['createdByEmail'] ?? $trajeto['createdBy'] ?? 'N/A');
                        $createdAt = isset($trajeto['createdAt']) ? format_date(strtotime($trajeto['createdAt'])) : 'N/A';
                        $status = htmlspecialchars($trajeto['status'] ?? 'draft');
                        $difficulty = htmlspecialchars($trajeto['difficulty'] ?? 'medio');
                        $pointsCount = $trajeto['pointsCount'] ?? count($trajeto['coordinates']);
                        
                        // Additional info
                        $date = htmlspecialchars($trajeto['date'] ?? '');
                        $startTime = htmlspecialchars($trajeto['startTime'] ?? '');
                        $weatherCondition = htmlspecialchars($trajeto['weatherCondition'] ?? '');
                        $numberOfObservers = $trajeto['numberOfObservers'] ?? '';
                        
                        // Build the complete info window content as a PHP string
                        $infoWindowContent = "<div class='map-popup'><h3><i class='fas fa-route'></i> $name</h3>";
                        $infoWindowContent .= "<p><i class='fas fa-info-circle'></i> $description</p>";
                        $infoWindowContent .= "<p><i class='fas fa-calendar'></i> Criado: $createdAt</p>";
                        $infoWindowContent .= "<p><i class='fas fa-user'></i> Criado por: $createdBy</p>";
                        $infoWindowContent .= "<p><i class='fas fa-ruler'></i> Distância: $distance</p>";
                        $infoWindowContent .= "<p><i class='fas fa-map-pin'></i> Pontos: $pointsCount</p>";
                        $infoWindowContent .= "<p><i class='fas fa-signal'></i> Dificuldade: " . ucfirst($difficulty) . "</p>";
                        $infoWindowContent .= "<p><i class='fas fa-flag'></i> Estado: " . ucfirst($status) . "</p>";
                        
                        if ($date) {
                            $infoWindowContent .= "<p><i class='fas fa-calendar-day'></i> Data: $date</p>";
                        }
                        if ($startTime) {
                            $infoWindowContent .= "<p><i class='fas fa-clock'></i> Hora: $startTime</p>";
                        }
                        if ($weatherCondition) {
                            $infoWindowContent .= "<p><i class='fas fa-cloud-sun'></i> Tempo: $weatherCondition</p>";
                        }
                        if ($numberOfObservers) {
                            $infoWindowContent .= "<p><i class='fas fa-users'></i> Observadores: $numberOfObservers</p>";
                        }
                        $infoWindowContent .= "</div>";
                        
                        // Properly escape for JavaScript
                        $infoWindowContent = addslashes(str_replace(["\r", "\n", "\t"], '', $infoWindowContent));
                        
                        // Convert coordinates to JavaScript format
                        $coordinates = [];
                        foreach ($trajeto['coordinates'] as $coord) {
                            $coordinates[] = "{ lat: " . $coord['lat'] . ", lng: " . $coord['lng'] . " }";
                        }
                        $coordinatesJs = '[' . implode(', ', $coordinates) . ']';
                        
                        // Calculate center point for marker
                        $centerLat = 0;
                        $centerLng = 0;
                        foreach ($trajeto['coordinates'] as $coord) {
                            $centerLat += $coord['lat'];
                            $centerLng += $coord['lng'];
                        }
                        $centerLat = $centerLat / count($trajeto['coordinates']);
                        $centerLng = $centerLng / count($trajeto['coordinates']);
                        
                        echo "
                            
                            // Create shadow path (dotted line)
                            const shadowPath$id = new google.maps.Polyline({
                                path: $coordinatesJs,
                                geodesic: true,
                                strokeColor: '#0a7ea4',
                                strokeOpacity: 0.6,
                                strokeWeight: 8,
                                clickable: false,
                                zIndex: 1
                            });
                            
                            // Create main trajectory path
                            const trajetoPath$id = new google.maps.Polyline({
                                path: $coordinatesJs,
                                geodesic: true,
                                strokeColor: '#0a7ea4',
                                strokeOpacity: 1.0,
                                strokeWeight: 4,
                                clickable: true,
                                zIndex: 2
                            });
                            
                            shadowPath$id.setMap(map);
                            trajetoPath$id.setMap(map);
                            
                            // Create numbered waypoint markers for each coordinate
                            const trajetoCoords$id = $coordinatesJs;
                            const trajetoMarkers$id = [];
                            
                            trajetoCoords$id.forEach((coord, index) => {
                                bounds.extend(coord);
                                
                                let markerIcon;
                                let markerLabel;
                                
                                if (index === 0) {
                                    // Start point - enhanced green pin with shadow
                                    markerIcon = {
                                        url: 'data:image/svg+xml;base64,' + btoa(`
                                            <svg xmlns='http://www.w3.org/2000/svg' width='30' height='38' viewBox='0 0 30 38'>
                                                <defs>
                                                    <filter id='shadowStart' x='-50%' y='-50%' width='200%' height='200%'>
                                                        <feDropShadow dx='2' dy='2' stdDeviation='2' flood-color='#000000' flood-opacity='0.5'/>
                                                    </filter>
                                                </defs>
                                                <path d='M15 4c-6 0-11 4-11 10 0 9 11 20 11 20s11-11 11-20c0-6-5-10-11-10z' fill='#FFFFFF' stroke='#22c55e' stroke-width='2' filter='url(#shadowStart)'/>
                                                <path d='M15 6c-4 0-8 3-8 7 0 7 8 15 8 15s8-8 8-15c0-4-4-7-8-7z' fill='#22c55e' filter='url(#shadowStart)'/>
                                                <text x='15' y='12' text-anchor='middle' font-family='Arial, sans-serif' font-size='6' font-weight='bold' fill='#FFFFFF'>I</text>
                                            </svg>
                                        `),
                                        scaledSize: new google.maps.Size(30, 38),
                                        anchor: new google.maps.Point(15, 34),
                                        labelOrigin: new google.maps.Point(15, -4)
                                    };
                                    markerLabel = {
                                        text: 'INÍCIO',
                                        fontSize: '10px',
                                        fontWeight: 'bold',
                                        color: '#22c55e'
                                    };
                                } else if (index === trajetoCoords$id.length - 1 && trajetoCoords$id.length > 1) {
                                    // End point - enhanced red pin with shadow
                                    markerIcon = {
                                        url: 'data:image/svg+xml;base64,' + btoa(`
                                            <svg xmlns='http://www.w3.org/2000/svg' width='30' height='38' viewBox='0 0 30 38'>
                                                <defs>
                                                    <filter id='shadowEnd' x='-50%' y='-50%' width='200%' height='200%'>
                                                        <feDropShadow dx='2' dy='2' stdDeviation='2' flood-color='#000000' flood-opacity='0.5'/>
                                                    </filter>
                                                </defs>
                                                <path d='M15 4c-6 0-11 4-11 10 0 9 11 20 11 20s11-11 11-20c0-6-5-10-11-10z' fill='#FFFFFF' stroke='#dc2626' stroke-width='2' filter='url(#shadowEnd)'/>
                                                <path d='M15 6c-4 0-8 3-8 7 0 7 8 15 8 15s8-8 8-15c0-4-4-7-8-7z' fill='#dc2626' filter='url(#shadowEnd)'/>
                                                <text x='15' y='12' text-anchor='middle' font-family='Arial, sans-serif' font-size='6' font-weight='bold' fill='#FFFFFF'>F</text>
                                            </svg>
                                        `),
                                        scaledSize: new google.maps.Size(30, 38),
                                        anchor: new google.maps.Point(15, 34),
                                        labelOrigin: new google.maps.Point(15, -4)
                                    };
                                    markerLabel = {
                                        text: 'FIM',
                                        fontSize: '10px',
                                        fontWeight: 'bold',
                                        color: '#dc2626'
                                    };
                                } else {
                                    // Regular point - enhanced blue circle with shadow
                                    markerIcon = {
                                        url: 'data:image/svg+xml;base64,' + btoa(`
                                            <svg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22'>
                                                <defs>
                                                    <filter id='shadowWay' x='-50%' y='-50%' width='200%' height='200%'>
                                                        <feDropShadow dx='1' dy='1' stdDeviation='1' flood-color='#000000' flood-opacity='0.4'/>
                                                    </filter>
                                                </defs>
                                                <circle cx='11' cy='11' r='9' fill='#FFFFFF' stroke='#0a7ea4' stroke-width='1.5' filter='url(#shadowWay)'/>
                                                <circle cx='11' cy='11' r='6' fill='#0a7ea4' filter='url(#shadowWay)'/>
                                                <text x='11' y='14' text-anchor='middle' font-family='Arial, sans-serif' font-size='8' font-weight='bold' fill='#FFFFFF'>` + (index + 1) + `</text>
                                            </svg>
                                        `),
                                        scaledSize: new google.maps.Size(22, 22),
                                        anchor: new google.maps.Point(11, 11)
                                    };
                                    markerLabel = null;
                                }
                                
                                const waypointMarker = new google.maps.Marker({
                                    position: coord,
                                    map: null, // Initially hidden, will be shown on zoom
                                    title: index === 0 ? 'Início' : (index === trajetoCoords$id.length - 1 && trajetoCoords$id.length > 1 ? 'Fim' : 'Ponto ' + (index + 1)),
                                    icon: markerIcon,
                                    label: markerLabel,
                                    zIndex: index === 0 || (index === trajetoCoords$id.length - 1 && trajetoCoords$id.length > 1) ? 1000 : 100
                                });
                                
                                trajetoMarkers$id.push(waypointMarker);
                                // Don't add individual waypoints to clustering
                                
                                // Add ground point for start and end (same as gestores view)
                                if (index === 0 || (index === trajetoCoords$id.length - 1 && trajetoCoords$id.length > 1)) {
                                    const groundMarker = new google.maps.Marker({
                                        position: coord,
                                        map: null, // Initially hidden, will be shown on zoom
                                        icon: {
                                            url: 'data:image/svg+xml;base64,' + btoa(`
                                                <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'>
                                                    <defs>
                                                        <filter id='shadowGround' x='-50%' y='-50%' width='200%' height='200%'>
                                                            <feDropShadow dx='1' dy='1' stdDeviation='1' flood-color='#000000' flood-opacity='0.4'/>
                                                        </filter>
                                                    </defs>
                                                    <circle cx='10' cy='10' r='8' fill='#FFFFFF' stroke='` + (index === 0 ? '#22c55e' : '#dc2626') + `' stroke-width='1.5' filter='url(#shadowGround)'/>
                                                    <circle cx='10' cy='10' r='5' fill='` + (index === 0 ? '#22c55e' : '#dc2626') + `' filter='url(#shadowGround)'/>
                                                    <text x='10' y='13' text-anchor='middle' font-family='Arial, sans-serif' font-size='7' font-weight='bold' fill='#FFFFFF'>` + (index + 1) + `</text>
                                                </svg>
                                            `),
                                            scaledSize: new google.maps.Size(20, 20),
                                            anchor: new google.maps.Point(10, 10)
                                        },
                                        title: index === 0 ? 'Início Ground' : 'Fim Ground',
                                        zIndex: 50
                                    });
                                    trajetoMarkers$id.push(groundMarker);
                                    // Don't add individual ground markers to clustering
                                }
                                
                                bounds.extend(coord);
                            });
                            
                            // Create info window for trajectory details
                            const trajetoInfoWindow$id = new google.maps.InfoWindow({
                                content: '" . $infoWindowContent . "'
                            });
                            
                            // Add click listener to polyline to show info
                            trajetoPath$id.addListener('click', (event) => {
                                // Close all other info windows
                                markers.forEach(m => {
                                    if (m.infoWindow) {
                                        m.infoWindow.close();
                                    }
                                });
                                trajetoInfoWindow$id.setPosition(event.latLng);
                                trajetoInfoWindow$id.open(map);
                            });
                            
                            // Extend bounds to include all trajeto markers
                            trajetoMarkers$id.forEach(marker => {
                                bounds.extend(marker.getPosition());
                            });
                            
                            // Create a single representative marker for clustering (invisible, at center)
                            const centerLat$id = trajetoCoords$id.reduce((sum, coord) => sum + coord.lat, 0) / trajetoCoords$id.length;
                            const centerLng$id = trajetoCoords$id.reduce((sum, coord) => sum + coord.lng, 0) / trajetoCoords$id.length;
                            
                            // Create trajectory marker for clustering system
                            const trajetoVisibleMarker$id = new google.maps.Marker({
                                position: { lat: centerLat$id, lng: centerLng$id },
                                map: null, // Will be added to clustering system
                                icon: {
                                    url: 'data:image/svg+xml;base64,' + btoa(`
                                        <svg xmlns='http://www.w3.org/2000/svg' width='28' height='28' viewBox='0 0 28 28'>
                                            <defs>
                                                <filter id='shadowT' x='-50%' y='-50%' width='200%' height='200%'>
                                                    <feDropShadow dx='1' dy='1' stdDeviation='1' flood-color='#000000' flood-opacity='0.4'/>
                                                </filter>
                                            </defs>
                                            <circle cx='14' cy='14' r='12' fill='#FFFFFF' stroke='#0a7ea4' stroke-width='2' filter='url(#shadowT)'/>
                                            <circle cx='14' cy='14' r='9' fill='#0a7ea4' filter='url(#shadowT)'/>
                                            <text x='14' y='18' text-anchor='middle' font-family='Arial, sans-serif' font-size='11' font-weight='bold' fill='#FFFFFF'>T</text>
                                        </svg>
                                    `),
                                    scaledSize: new google.maps.Size(28, 28),
                                    anchor: new google.maps.Point(14, 14)
                                },
                                title: '$name',
                                zIndex: 150
                            });
                            
                            // Add click listener to visible trajectory marker
                            trajetoVisibleMarker$id.addListener('click', () => {
                                // Close all other info windows
                                markers.forEach(m => {
                                    if (m.infoWindow) {
                                        m.infoWindow.close();
                                    }
                                });
                                trajetoInfoWindow$id.setPosition({ lat: centerLat$id, lng: centerLng$id });
                                trajetoInfoWindow$id.open(map);
                            });
                            
                            // Store all markers and polylines for cleanup (after all variables are declared)
                            markers.push({
                                markers: trajetoMarkers$id,
                                visibleMarker: trajetoVisibleMarker$id,
                                infoWindow: trajetoInfoWindow$id,
                                polyline: trajetoPath$id,
                                shadowPath: shadowPath$id
                            });
                            
                            // Add the visible marker to the clustering system
                            mapMarkers.push(trajetoVisibleMarker$id);
                            
                            // Reduced logging for performance
                            markerCount += 1; // Only count the cluster marker for clustering
                            
                            // Add contact markers if any exist
                            " . (isset($trajeto['contacts']) && !empty($trajeto['contacts']) ? "
                            console.log('Adding " . count($trajeto['contacts']) . " contacts for trajeto: $name');
                            " : "") . "
                            
                            " . (isset($trajeto['contacts']) && !empty($trajeto['contacts']) ? 
                                implode("\n", array_map(function($contact, $index) use ($id) {
                                    if (isset($contact['coordinates']) && isset($contact['coordinates']['lat']) && isset($contact['coordinates']['lng'])) {
                                        $lat = $contact['coordinates']['lat'];
                                        $lng = $contact['coordinates']['lng'];
                                        $time = htmlspecialchars($contact['time'] ?? '');
                                        $circumstance = htmlspecialchars($contact['circumstance'] ?? '');
                                        $location = htmlspecialchars($contact['location'] ?? '');
                                        
                                        return "
                            // Create canvas for dove marker (same as gestores)
                            const canvas{$id}_{$index} = document.createElement('canvas');
                            canvas{$id}_{$index}.width = 32;
                            canvas{$id}_{$index}.height = 32;
                            const ctx{$id}_{$index} = canvas{$id}_{$index}.getContext('2d');
                            
                            // Draw circle background
                            ctx{$id}_{$index}.beginPath();
                            ctx{$id}_{$index}.arc(16, 16, 14, 0, 2 * Math.PI);
                            ctx{$id}_{$index}.fillStyle = '#16a34a';
                            ctx{$id}_{$index}.fill();
                            ctx{$id}_{$index}.strokeStyle = 'white';
                            ctx{$id}_{$index}.lineWidth = 2;
                            ctx{$id}_{$index}.stroke();
                            
                            // Load and draw dove icon
                            const doveImg{$id}_{$index} = new Image();
                            doveImg{$id}_{$index}.onload = function() {
                                // Create a temporary canvas to process the dove icon
                                const tempCanvas{$id}_{$index} = document.createElement('canvas');
                                tempCanvas{$id}_{$index}.width = 16;
                                tempCanvas{$id}_{$index}.height = 16;
                                const tempCtx{$id}_{$index} = tempCanvas{$id}_{$index}.getContext('2d');
                                
                                // Draw the dove icon on temp canvas
                                tempCtx{$id}_{$index}.drawImage(doveImg{$id}_{$index}, 0, 0, 16, 16);
                                
                                // Get image data to process pixels
                                const imageData{$id}_{$index} = tempCtx{$id}_{$index}.getImageData(0, 0, 16, 16);
                                const data{$id}_{$index} = imageData{$id}_{$index}.data;
                                
                                // Convert non-transparent pixels to white
                                for (let i = 0; i < data{$id}_{$index}.length; i += 4) {
                                    if (data{$id}_{$index}[i + 3] > 0) { // If pixel is not transparent
                                        data{$id}_{$index}[i] = 255;     // Red = 255 (white)
                                        data{$id}_{$index}[i + 1] = 255; // Green = 255 (white)
                                        data{$id}_{$index}[i + 2] = 255; // Blue = 255 (white)
                                    }
                                }
                                
                                // Put the processed image data back
                                tempCtx{$id}_{$index}.putImageData(imageData{$id}_{$index}, 0, 0);
                                
                                // Draw the white dove icon on the main canvas
                                ctx{$id}_{$index}.drawImage(tempCanvas{$id}_{$index}, 8, 8);
                                
                                // Create marker with the canvas image
                                const contactMarker{$id}_{$index} = new google.maps.Marker({
                                    position: { lat: $lat, lng: $lng },
                                    map: null, // Initially hidden, will be shown on zoom
                                    icon: {
                                        url: canvas{$id}_{$index}.toDataURL(),
                                        scaledSize: new google.maps.Size(32, 32),
                                        anchor: new google.maps.Point(16, 16)
                                    },
                                    title: 'Contacto: $time',
                                    zIndex: 200
                                });
                                
                                // Add click listener for info window
                                contactMarker{$id}_{$index}.addListener('click', () => {
                                    markers.forEach(m => {
                                        if (m.infoWindow) {
                                            m.infoWindow.close();
                                        }
                                    });
                                    contactInfoWindow{$id}_{$index}.open(map, contactMarker{$id}_{$index});
                                });
                                
                                trajetoMarkers$id.push(contactMarker{$id}_{$index});
                                // Don't add individual contact markers to clustering
                                bounds.extend({ lat: $lat, lng: $lng });
                                // Create info window
                                const contactInfoWindow{$id}_{$index} = new google.maps.InfoWindow({
                                    content: '<div class=\"map-popup\"><h4><i class=\"fas fa-dove\"></i> Contacto com Rola-Brava</h4>" . 
                                    ($time ? "<p><i class=\"fas fa-clock\"></i> Hora: $time</p>" : "") .
                                    ($circumstance ? "<p><i class=\"fas fa-info-circle\"></i> Circunstância: $circumstance</p>" : "") .
                                    ($location ? "<p><i class=\"fas fa-map-marker-alt\"></i> Local: $location</p>" : "") .
                                    "</div>'
                                });
                            };
                            doveImg{$id}_{$index}.src = '../../assets/img/icons/dove-icon.png';
                            
                            // Note: contactMarker creation and event listeners are now handled in the image onload callback above";
                                    }
                                    return '';
                                }, $trajeto['contacts'], array_keys($trajeto['contacts']))) 
                                : "") . "
                        ";
                    }
                }
            }
            ?>
            
            console.log('Map data loaded: ' + markerCount + ' markers');
            
            // Update loading progress
            updateLoadingProgress('Processando dados do mapa...');
            
            // Trajectory data available: <?php echo count($trajetos); ?> trajectories
            
            // Handle different filter types
            console.log('Current filter type:', currentFilterType);
            
            if (currentFilterType === 'contactos') {
                console.log('Contactos filter active - hiding reports and trajetos, showing only contacts');
                updateLoadingProgress('A carregar contactos...');
                
                // Clear the clustering system and hide all markers
                if (markerClusterer) {
                    markerClusterer.clearMarkers();
                }
                
                // Hide all individual markers
                mapMarkers.forEach(marker => {
                    marker.setMap(null);
                });
                
                // Queue all trajectories for batch contact loading
                <?php if (!empty($trajetos)): ?>
                    <?php foreach ($trajetos as $trajeto): ?>
                        <?php if (isset($trajeto['id'])): ?>
                            // Create a dummy marker group for contact loading
                            const dummyGroup<?php echo $trajeto['id']; ?> = {
                                trajectoryId: '<?php echo $trajeto["id"]; ?>',
                                trajectoryType: '<?php echo $trajeto["type"] ?? "Manual"; ?>',
                                contactsLoaded: false,
                                contactMarkers: []
                            };
                            loadTrajectoryContacts('<?php echo $trajeto["id"]; ?>', '<?php echo $trajeto["type"] ?? "Manual"; ?>', dummyGroup<?php echo $trajeto['id']; ?>);
                            markers.push(dummyGroup<?php echo $trajeto['id']; ?>);
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    // No trajectories found, try to load all contacts directly
                    console.log('No trajectories found, attempting to load all contacts directly...');
                    loadAllContactsDirectly();
                <?php endif; ?>
            } else if (currentFilterType === 'trajetos') {
                console.log('Trajetos filter active - showing only trajectories, hiding contacts');
                
                // Show trajectory markers but hide contacts
                markers.forEach(markerGroup => {
                    // Show trajectory markers
                    if (markerGroup.visibleMarker) {
                        markerGroup.visibleMarker.setMap(map);
                    }
                    if (markerGroup.markers && Array.isArray(markerGroup.markers)) {
                        markerGroup.markers.forEach(marker => {
                            // Show trajectory waypoint markers
                            if (marker.getTitle && (
                                marker.getTitle().includes('Início') || 
                                marker.getTitle().includes('Fim') || 
                                marker.getTitle().includes('Ponto')
                            )) {
                                marker.setMap(map);
                            }
                        });
                    }
                    
                    // Hide contact markers
                    if (markerGroup.contactMarkers && Array.isArray(markerGroup.contactMarkers)) {
                        markerGroup.contactMarkers.forEach(contactMarker => {
                            contactMarker.setMap(null);
                        });
                    }
                });
            } else if (currentFilterType === 'reports') {
                console.log('Reports filter active - showing only reports, hiding trajectories and contacts');
                
                // Hide all trajectory markers and contacts
                markers.forEach(markerGroup => {
                    // Hide trajectory markers
                    if (markerGroup.visibleMarker) {
                        markerGroup.visibleMarker.setMap(null);
                    }
                    if (markerGroup.markers && Array.isArray(markerGroup.markers)) {
                        markerGroup.markers.forEach(marker => {
                            marker.setMap(null);
                        });
                    }
                    
                    // Hide contact markers
                    if (markerGroup.contactMarkers && Array.isArray(markerGroup.contactMarkers)) {
                        markerGroup.contactMarkers.forEach(contactMarker => {
                            contactMarker.setMap(null);
                        });
                    }
                });
                
                // Show only report markers (these are added separately to mapMarkers)
                mapMarkers.forEach(marker => {
                    if (marker.reportId) { // Assuming report markers have a reportId property
                        marker.setMap(map);
                    } else {
                        marker.setMap(null);
                    }
                });
            }
            
            // Progressive loading: Enhanced zoom listener for trajectory details and contact loading
            map.addListener('zoom_changed', function() {
                const currentZoom = map.getZoom();
                const showTrajectoryDetails = currentZoom >= 14; // Show trajectory waypoints at zoom 14+
                const showContacts = currentZoom >= 16; // Show contacts at zoom 16+
                
                
                
                // Show/hide trajectory details and load contacts on demand
                markers.forEach(markerGroup => {
                    // Handle trajectory detail markers (waypoints)
                    if (markerGroup.markers && Array.isArray(markerGroup.markers)) {
                        markerGroup.markers.forEach(marker => {
                            if (marker.getTitle && (
                                marker.getTitle().includes('Início') || 
                                marker.getTitle().includes('Fim') || 
                                marker.getTitle().includes('Ponto') ||
                                marker.getTitle().includes('Ground')
                            )) {
                                marker.setMap(showTrajectoryDetails ? map : null);
                            }
                        });
                    }
                    
                    // Handle visible trajectory marker (opposite visibility of details)
                    if (markerGroup.visibleMarker) {
                        markerGroup.visibleMarker.setMap(showTrajectoryDetails ? null : map);
                    }
                    
                    // Progressive contact loading: Load contacts when zoomed in enough
                    if (showContacts && markerGroup.trajectoryId && !markerGroup.contactsLoaded) {
                        loadTrajectoryContacts(markerGroup.trajectoryId, markerGroup.trajectoryType, markerGroup);
                    }
                    
                    // Show/hide contact markers based on zoom
                    if (markerGroup.contactMarkers && Array.isArray(markerGroup.contactMarkers)) {
                        markerGroup.contactMarkers.forEach(contactMarker => {
                            contactMarker.setMap(showContacts ? map : null);
                        });
                    }
                });
            });
            
            // Optimized batch contact loading to prevent network spam
            let contactLoadQueue = [];
            let isLoadingContacts = false;
            
            // Function to load contacts for a trajectory on demand
            function loadTrajectoryContacts(trajectoryId, trajectoryType, markerGroup) {
                if (markerGroup && markerGroup.contactsLoaded) return; // Already loaded
                
                // Add to queue instead of immediate loading
                contactLoadQueue.push({ trajectoryId, trajectoryType, markerGroup });
                
                // Process queue if not already processing
                if (!isLoadingContacts) {
                    processContactLoadQueue();
                }
            }
            
            // Process contact loading queue in batches
            function processContactLoadQueue() {
                if (contactLoadQueue.length === 0 || isLoadingContacts) return;
                
                isLoadingContacts = true;
                const batchSize = 10; // Load 10 trajectories at once
                const batch = contactLoadQueue.splice(0, batchSize);
                
                // Mark all in batch as loading
                batch.forEach(item => {
                    if (item.markerGroup) item.markerGroup.contactsLoaded = true;
                });
                
                // Build batch request
                const trajectoryIds = batch.map(item => item.trajectoryId);
                const trajectoryTypes = batch.map(item => item.trajectoryType);
                
                const params = new URLSearchParams();
                params.append('batch_load', '1');
                params.append('trajectory_ids', JSON.stringify(trajectoryIds));
                params.append('trajectory_types', JSON.stringify(trajectoryTypes));
                
                // Single batch request for multiple trajectories
                fetch(`load_contacts.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: params.toString()
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.trajectories) {
                        let totalContactsLoaded = 0;
                        
                        // Process each trajectory's contacts
                        Object.keys(data.trajectories).forEach(trajectoryId => {
                            const contacts = data.trajectories[trajectoryId];
                            const batchItem = batch.find(item => item.trajectoryId === trajectoryId);
                            
                            if (batchItem && batchItem.markerGroup) {
                                batchItem.markerGroup.contactMarkers = [];
                                
                                contacts.forEach((contact, index) => {
                                    if (contact.coordinates && contact.coordinates.lat && contact.coordinates.lng) {
                                        const contactMarker = createContactMarker(contact, trajectoryId, index);
                                        batchItem.markerGroup.contactMarkers.push(contactMarker);
                                        totalContactsLoaded++;
                                        
                                        // Show immediately if zoomed in enough OR if contactos filter is active
                                        if (map.getZoom() >= 16 || currentFilterType === 'contactos') {
                                            contactMarker.setMap(map);
                                        }
                                    }
                                });
                                
                                // Update marker icon to show contacts loaded
                                if (batchItem.markerGroup.visibleMarker) {
                                    const hasContacts = contacts.length > 0;
                                    batchItem.markerGroup.visibleMarker.setIcon({
                                        url: 'data:image/svg+xml;base64,' + btoa(`
                                            <svg xmlns='http://www.w3.org/2000/svg' width='28' height='28' viewBox='0 0 28 28'>
                                                <defs>
                                                    <filter id='shadowSuccess' x='-50%' y='-50%' width='200%' height='200%'>
                                                        <feDropShadow dx='1' dy='1' stdDeviation='1' flood-color='#000000' flood-opacity='0.4'/>
                                                    </filter>
                                                </defs>
                                                <circle cx='14' cy='14' r='12' fill='#FFFFFF' stroke='` + (hasContacts ? '#10b981' : '#0a7ea4') + `' stroke-width='2' filter='url(#shadowSuccess)'/>
                                                <circle cx='14' cy='14' r='9' fill='` + (hasContacts ? '#10b981' : '#0a7ea4') + `' filter='url(#shadowSuccess)'/>
                                                <text x='14' y='18' text-anchor='middle' font-family='Arial, sans-serif' font-size='11' font-weight='bold' fill='#FFFFFF'>` + (hasContacts ? '✓' : 'T') + `</text>
                                            </svg>
                                        `),
                                        scaledSize: new google.maps.Size(28, 28),
                                        anchor: new google.maps.Point(14, 14)
                                    });
                                }
                            }
                        });
                        
                        // Batch processing completed
                    }
                    
                    // Refresh clustering
                    if (markerClusterer) {
                        markerClusterer.repaint();
                    }
                    
                    isLoadingContacts = false;
                    
                    // Process next batch if queue not empty
                    if (contactLoadQueue.length > 0) {
                        setTimeout(() => processContactLoadQueue(), 100);
                    }
                })
                .catch(error => {
                    console.error('Batch contact loading error:', error);
                    
                    // Reset loading flags for retry
                    batch.forEach(item => {
                        if (item.markerGroup) item.markerGroup.contactsLoaded = false;
                    });
                    
                    isLoadingContacts = false;
                    
                    // Retry after delay
                    setTimeout(() => processContactLoadQueue(), 1000);
                });
            }
            
            // Function to load all contacts directly (when no trajectories are available)
            function loadAllContactsDirectly() {
                console.log('Loading all contacts directly from both collections...');
                
                // Create a global container for direct contact markers
                if (!window.directContactMarkers) {
                    window.directContactMarkers = [];
                }
                
                // Load from both contact collections
                Promise.all([
                    fetch('load_contacts.php?load_all=true&collection=contacts'),
                    fetch('load_contacts.php?load_all=true&collection=gestorMobile_contacts')
                ])
                .then(responses => Promise.all(responses.map(r => r.json())))
                .then(results => {
                    let totalContacts = 0;
                    
                    results.forEach((data, index) => {
                        const collectionName = index === 0 ? 'contacts' : 'gestorMobile_contacts';
                        
                        if (data.success && data.contacts) {
                            console.log(`Loaded ${data.contacts.length} contacts from ${collectionName}`);
                            totalContacts += data.contacts.length;
                            
                            // Create markers for each contact
                            data.contacts.forEach((contact, contactIndex) => {
                                if (contact.coordinates && contact.coordinates.lat && contact.coordinates.lng) {
                                    const contactMarker = createContactMarker(contact, 'direct_' + collectionName, contactIndex);
                                    window.directContactMarkers.push(contactMarker);
                                    
                                    // Show immediately since contactos filter is active
                                    contactMarker.setMap(map);
                                }
                            });
                        } else {
                            console.error(`Failed to load contacts from ${collectionName}:`, data.error || 'Unknown error');
                        }
                    });
                    
                    console.log(`Total contacts loaded directly: ${totalContacts}`);
                    
                    // Update statistics
                    const contactsElement = document.querySelector('.stats-grid .stat-item:nth-child(3) .stat-value');
                    if (contactsElement) {
                        contactsElement.textContent = totalContacts;
                    }
                })
                .catch(error => {
                    console.error('Error loading contacts directly:', error);
                });
            }
            
            // Function to create contact markers
            function createContactMarker(contact, trajectoryId, index) {
                const lat = parseFloat(contact.coordinates.lat);
                const lng = parseFloat(contact.coordinates.lng);
                const time = contact.time || 'N/A';
                const circumstance = contact.circumstance || 'N/A';
                const location = contact.location || 'N/A';
                
                // Create enhanced dove icon with shadow
                const contactIcon = {
                    url: 'data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns='http://www.w3.org/2000/svg' width='26' height='26' viewBox='0 0 26 26'>
                            <defs>
                                <filter id='shadowC' x='-50%' y='-50%' width='200%' height='200%'>
                                    <feDropShadow dx='1' dy='1' stdDeviation='1' flood-color='#000000' flood-opacity='0.4'/>
                                </filter>
                            </defs>
                            <circle cx='13' cy='13' r='11' fill='#FFFFFF' stroke='#10b981' stroke-width='2' filter='url(#shadowC)'/>
                            <circle cx='13' cy='13' r='8' fill='#10b981' filter='url(#shadowC)'/>
                            <path d='M9 11 L13 14 L17 11 L13 17 Z' fill='#FFFFFF'/>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(26, 26),
                    anchor: new google.maps.Point(13, 13)
                };
                
                // Create marker
                const contactMarker = new google.maps.Marker({
                    position: { lat: lat, lng: lng },
                    map: null, // Initially hidden
                    icon: contactIcon,
                    title: `Contacto: ${time}`,
                    zIndex: 200
                });
                
                // Create info window for contact
                const contactInfoWindow = new google.maps.InfoWindow({
                    content: `<div class="map-popup">
                        <h4><i class="fas fa-dove"></i> Contacto com Rola-Brava</h4>
                        ${time !== 'N/A' ? `<p><i class="fas fa-clock"></i> Hora: ${time}</p>` : ''}
                        ${circumstance !== 'N/A' ? `<p><i class="fas fa-info-circle"></i> Circunstância: ${circumstance}</p>` : ''}
                        ${location !== 'N/A' ? `<p><i class="fas fa-map-marker-alt"></i> Local: ${location}</p>` : ''}
                    </div>`
                });
                
                // Add click listener
                contactMarker.addListener('click', () => {
                    // Close other info windows
                    markers.forEach(m => {
                        if (m.infoWindow) m.infoWindow.close();
                        if (m.contactMarkers) {
                            m.contactMarkers.forEach(cm => {
                                if (cm.infoWindow) cm.infoWindow.close();
                            });
                        }
                    });
                    contactInfoWindow.open(map, contactMarker);
                });
                
                contactMarker.infoWindow = contactInfoWindow;
                return contactMarker;
            }
            
            // Check if we have any data to show on the map
            console.log('Map data summary:', {
                markerCount: markerCount,
                boundsIsEmpty: bounds.isEmpty(),
                boundsNorthEast: bounds.isEmpty() ? null : bounds.getNorthEast().toString(),
                boundsSouthWest: bounds.isEmpty() ? null : bounds.getSouthWest().toString()
            });
            
            // Debug: Log specific bounds coordinates
            if (!bounds.isEmpty()) {
                const ne = bounds.getNorthEast();
                const sw = bounds.getSouthWest();
                console.log('Bounds details - NE:', ne.lat(), ne.lng(), 'SW:', sw.lat(), sw.lng());
                
                // Check if coordinates look like Portugal (rough bounds)
                const centerLat = (ne.lat() + sw.lat()) / 2;
                const centerLng = (ne.lng() + sw.lng()) / 2;
                console.log('Calculated center:', centerLat, centerLng);
                
                if (centerLat >= 36 && centerLat <= 42 && centerLng >= -10 && centerLng <= -6) {
                    console.log('✅ Coordinates appear to be in Portugal');
                } else {
                    console.log('❌ Coordinates do NOT appear to be in Portugal');
                }
            }
            
            // Fallback: If bounds are empty but we have markers, create manual bounds
            if (bounds.isEmpty() && mapMarkers.length > 0) {
                console.log('Bounds empty but markers exist, creating manual bounds...');
                mapMarkers.forEach(marker => {
                    if (marker.getPosition) {
                        bounds.extend(marker.getPosition());
                    }
                });
                console.log('Manual bounds created from', mapMarkers.length, 'markers');
            }
            
            // Always fit bounds if we have data, regardless of marker count
            if (!bounds.isEmpty()) {
                console.log('Fitting map to data bounds...');
                
                // Add a timeout to ensure bounds are properly set
                setTimeout(() => {
                    try {
                        // Double-check bounds before fitting
                        if (!bounds.isEmpty()) {
                            const ne = bounds.getNorthEast();
                            const sw = bounds.getSouthWest();
                            console.log('About to fit bounds - NE:', ne.lat(), ne.lng(), 'SW:', sw.lat(), sw.lng());
                        }
                        
                        map.fitBounds(bounds);
                        console.log('Map bounds fitted successfully');
                        
                        // Ensure reasonable zoom levels for Portugal
                        google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                            const zoom = map.getZoom();
                            console.log('Current zoom after fitBounds:', zoom);
                            
                            // Adjust zoom if too close or too far
                            if (zoom > 15) {
                                console.log('Zoom too close, setting to 15');
                                map.setZoom(15);
                            } else if (zoom < 6) {
                                console.log('Zoom too far, setting to 6');
                                map.setZoom(6);
                            }
                        });
                    } catch (error) {
                        console.error('Error fitting bounds:', error);
                        // Fallback to Portugal view
                        map.setCenter({ lat: 39.5, lng: -8.0 });
                        map.setZoom(7);
                    }
                }, 100);
                
            } else {
                console.log('No data bounds available, using default Portugal view');
                // Default view of Portugal if no data
                map.setCenter({ lat: 39.5, lng: -8.0 });
                map.setZoom(7);
            }
            
            // Initialize optimized marker clustering
            if (markerCount > 0) {
                updateLoadingProgress('A organizar marcadores...');
                console.log('Available global objects:', {
                    MarkerClusterer: typeof MarkerClusterer,
                    google: typeof google,
                    map: typeof map
                });
                
                // Check if MarkerClusterer is available
                if (typeof MarkerClusterer !== 'undefined') {
                    console.log('Initializing optimized MarkerClusterer with', markerCount, 'markers');
                    
                    // Add a small delay to ensure map is fully initialized
                    setTimeout(() => {
                        try {
                            // Performance-optimized clustering configuration
                            markerClusterer = new MarkerClusterer(map, mapMarkers, {
                                gridSize: 50, // Optimal grid size for clustering
                                maxZoom: 12, // Cluster until zoom level 12 (reduced for better clustering)
                                minimumClusterSize: 2, // Require at least 2 markers to cluster (reduced for better clustering)
                                averageCenter: true, // Better cluster positioning
                                zoomOnClick: true, // Auto-zoom when clicking clusters
                                imagePath: 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m',
                                // Performance optimizations
                                batchSize: 1000, // Process markers in batches
                                batchSizeIE: 500, // Smaller batches for IE
                                calculator: function(markers, numStyles) {
                                    // Custom calculator for better performance
                                    const count = markers.length;
                                    let index = 0;
                                    if (count < 10) index = 0;
                                    else if (count < 50) index = 1;
                                    else if (count < 100) index = 2;
                                    else index = 3;
                                    
                                    return {
                                        text: count.toString(),
                                        index: Math.min(index, numStyles - 1)
                                    };
                                }
                            });
                            
                            console.log('Optimized marker clustering initialized successfully');
                            
                            // Memory management: Clean up unused marker references
                            setTimeout(() => {
                                if (window.gc && typeof window.gc === 'function') {
                                    window.gc(); // Force garbage collection if available
                                }
                            }, 1000);
                            
                        } catch (error) {
                            console.error('Error initializing MarkerClusterer:', error);
                            // Fallback: add markers in batches to prevent browser freeze
                            let batchIndex = 0;
                            const batchSize = 50;
                            
                            function addMarkerBatch() {
                                const endIndex = Math.min(batchIndex + batchSize, mapMarkers.length);
                                for (let i = batchIndex; i < endIndex; i++) {
                                    mapMarkers[i].setMap(map);
                                }
                                batchIndex = endIndex;
                                
                                if (batchIndex < mapMarkers.length) {
                                    setTimeout(addMarkerBatch, 10); // Small delay between batches
                                }
                            }
                            addMarkerBatch();
                        }
                    }, 100);
                } else {
                    console.warn('MarkerClusterer not available, using optimized batch loading');
                    // Optimized batch loading for better performance
                    let batchIndex = 0;
                    const batchSize = 100;
                    
                    function addMarkerBatch() {
                        const endIndex = Math.min(batchIndex + batchSize, mapMarkers.length);
                        for (let i = batchIndex; i < endIndex; i++) {
                            mapMarkers[i].setMap(map);
                        }
                        batchIndex = endIndex;
                        
                        if (batchIndex < mapMarkers.length) {
                            requestAnimationFrame(addMarkerBatch); // Use RAF for smoother rendering
                        }
                    }
                    addMarkerBatch();
                }
                
                console.log('Marker clustering setup complete');
                
                // Hide loading screen after clustering is complete
                setTimeout(() => {
                    if (!document.getElementById('mapLoading').classList.contains('hidden')) {
                        updateLoadingProgress('Concluído!');
                        setTimeout(() => {
                            hideLoadingScreen();
                        }, 200);
                    }
                }, 500);
            }
            
            // Check for saved map state and restore it
            setTimeout(() => {
                const savedState = sessionStorage.getItem('mapState');
                if (savedState) {
                    try {
                        const state = JSON.parse(savedState);
                        console.log('Restoring saved map state:', state);
                        
                        if (state.center) {
                            map.setCenter(new google.maps.LatLng(state.center.lat, state.center.lng));
                            map.setZoom(state.zoom || 7);
                            console.log('Map state restored successfully');
                        }
                    } catch (e) {
                        console.error('Error restoring map state:', e);
                    }
                }
            }, 200);
            
            // Hide loading animation when map is ready
            google.maps.event.addListenerOnce(map, 'tilesloaded', function() {
                updateLoadingProgress('Concluído!');
                setTimeout(() => {
                    hideLoadingScreen();
                }, 500);
            });
        }
        
        // Store current map state
        let currentMapState = {
            center: null,
            zoom: null,
            bounds: null
        };
        
        // Save map state
        function saveMapState() {
            if (map) {
                currentMapState.center = map.getCenter();
                currentMapState.zoom = map.getZoom();
                currentMapState.bounds = map.getBounds();
                console.log('Map state saved:', currentMapState);
            }
        }
        
        // Restore map state
        function restoreMapState() {
            if (map && currentMapState.center) {
                console.log('Restoring map state:', currentMapState);
                map.setCenter(currentMapState.center);
                map.setZoom(currentMapState.zoom);
            }
        }
        
        // Apply filters with AJAX
        function applyFilters() {
            console.log('Applying filters...');
            
            // Save current map state
            saveMapState();
            
            const typeSelect = document.getElementById('type');
            const dateRangeSelect = document.getElementById('dateRange');
            
            const type = typeSelect.value;
            const dateRange = dateRangeSelect.value;
            
            // Show loading indicator
            const loadingElement = document.getElementById('mapLoading');
            if (loadingElement) {
                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('hidden');
            }
            
            // Build URL with filters
            const url = new URL(window.location.href);
            url.searchParams.set('type', type);
            url.searchParams.set('dateRange', dateRange);
            
            // Update browser URL without reloading
            window.history.pushState({}, '', url.toString());
            
            // Reload the page content but preserve map state
            window.location.reload();
        }
        
        // Clear filters
        function clearFilters() {
            console.log('Clearing filters...');
            
            // Save current map state
            saveMapState();
            
            // Reset selects
            document.getElementById('type').value = 'all';
            document.getElementById('dateRange').value = 'all';
            
            // Show loading indicator
            const loadingElement = document.getElementById('mapLoading');
            if (loadingElement) {
                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('hidden');
            }
            
            // Clear URL parameters
            const url = new URL(window.location.href);
            url.searchParams.delete('type');
            url.searchParams.delete('dateRange');
            
            // Update browser URL and reload
            window.history.pushState({}, '', url.toString());
            window.location.reload();
        }
        
        // Restore map state after page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we have saved state in sessionStorage
            const savedState = sessionStorage.getItem('mapState');
            if (savedState) {
                try {
                    const state = JSON.parse(savedState);
                    currentMapState = state;
                    console.log('Loaded saved map state from sessionStorage:', currentMapState);
                } catch (e) {
                    console.error('Error parsing saved map state:', e);
                }
            }
            
            // Add event listeners for filter controls (CSP-compliant)
            const typeSelect = document.getElementById('type');
            const dateRangeSelect = document.getElementById('dateRange');
            const filterBtn = document.getElementById('filterBtn');
            const clearBtn = document.getElementById('clearBtn');
            
            if (typeSelect) {
                typeSelect.addEventListener('change', applyFilters);
            }
            if (dateRangeSelect) {
                dateRangeSelect.addEventListener('change', applyFilters);
            }
            if (filterBtn) {
                filterBtn.addEventListener('click', applyFilters);
            }
            if (clearBtn) {
                clearBtn.addEventListener('click', clearFilters);
            }
        });
        
        // Save map state to sessionStorage before page unload
        window.addEventListener('beforeunload', function() {
            if (map) {
                const state = {
                    center: {
                        lat: map.getCenter().lat(),
                        lng: map.getCenter().lng()
                    },
                    zoom: map.getZoom(),
                    bounds: map.getBounds() ? {
                        north: map.getBounds().getNorthEast().lat(),
                        south: map.getBounds().getSouthWest().lat(),
                        east: map.getBounds().getNorthEast().lng(),
                        west: map.getBounds().getSouthWest().lng()
                    } : null
                };
                sessionStorage.setItem('mapState', JSON.stringify(state));
                console.log('Map state saved to sessionStorage:', state);
            }
        });
        
        // Load Google Maps API and MarkerClusterer
        window.onload = function() {
            // Use the older, more compatible MarkerClusterer version
            const markerClustererScript = document.createElement('script');
            markerClustererScript.src = 'https://unpkg.com/js-marker-clusterer@1.0.0/src/markerclusterer.js';
            markerClustererScript.onload = function() {
                console.log('MarkerClusterer library loaded successfully');
                // Then load Google Maps API
                const mapsScript = document.createElement('script');
                mapsScript.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap';
                mapsScript.async = true;
                mapsScript.defer = true;
                document.head.appendChild(mapsScript);
            };
            markerClustererScript.onerror = function() {
                console.error('Failed to load MarkerClusterer, trying alternative...');
                // Try alternative CDN
                const altScript = document.createElement('script');
                altScript.src = 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/markerclusterer.js';
                altScript.onload = function() {
                    console.log('MarkerClusterer loaded from alternative CDN');
                    const mapsScript = document.createElement('script');
                    mapsScript.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap';
                    mapsScript.async = true;
                    mapsScript.defer = true;
                    document.head.appendChild(mapsScript);
                };
                altScript.onerror = function() {
                    console.error('All MarkerClusterer CDNs failed, loading maps without clustering');
                    // Load Google Maps anyway, just without clustering
                    const mapsScript = document.createElement('script');
                    mapsScript.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap';
                    mapsScript.async = true;
                    mapsScript.defer = true;
                    document.head.appendChild(mapsScript);
                };
                document.head.appendChild(altScript);
            };
            document.head.appendChild(markerClustererScript);
        };
    </script>
</body>
</html> 