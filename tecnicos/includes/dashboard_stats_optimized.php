<?php
/**
 * Optimized Dashboard Statistics with Caching
 * Reduces Firebase queries and improves performance
 */

require_once __DIR__ . '/dashboard_cache.php';
require_once __DIR__ . '/cache_config.php';
require_once __DIR__ . '/config.php';

/**
 * Get technician statistics with caching and query optimization
 */
function getTechnicianStatsOptimized($database, $userId, $forceRefresh = false) {
    $cache = new DashboardCache($userId);
    
    // Check if cache should be bypassed
    if (CacheConfig::shouldBypassCache(['force_refresh' => $forceRefresh])) {
        error_log("Dashboard: Bypassing cache for user $userId");
    } else {
        // Try to get cached data first
        $cachedStats = $cache->getCachedStats(CacheConfig::getExpiry('stats'));
        if ($cachedStats !== null) {
            error_log("Dashboard: Using cached stats for user $userId");
            return $cachedStats;
        }
    }
    
    error_log("Dashboard: Fetching fresh stats for user $userId");
    
    // Initialize stats array
    $stats = [
        'totalReports' => 0,
        'gpsReports' => 0,
        'manualTrajectories' => 0,
        'totalContacts' => 0,
        'totalPhotos' => 0,
        'uniqueLocations' => 0,
        'totalSessions' => 0,
        'totalFieldHours' => 0,
        'totalKilometers' => 0,
        'recentReports' => 0,
        'todayReports' => 0,
        'reports' => [],
        'contactEventsData' => [],
        'manualTrajectoriesData' => []
    ];
    
    try {
        // Get cached reports or fetch fresh
        $reports = $cache->getCachedReports(CacheConfig::getExpiry('reports'));
        if ($reports === null) {
            $reports = fetchOptimizedReports($database, $userId);
            $cache->cacheReports($reports);
        }

        // Get cached contact events or fetch fresh
        $contactEvents = $cache->getCachedContactEvents(CacheConfig::getExpiry('contacts'));
        if ($contactEvents === null) {
            $contactEvents = fetchOptimizedContactEvents($database, $userId);
            $cache->cacheContactEvents($contactEvents);
        }

        // Get cached manual trajectories or fetch fresh
        $manualTrajectories = $cache->getCachedManualTrajectories(CacheConfig::getExpiry('manual_trajectories'));
        if ($manualTrajectories === null) {
            $manualTrajectories = fetchOptimizedManualTrajectories($database, $userId);
            $cache->cacheManualTrajectories($manualTrajectories);
        }

        // Process the data
        $stats = processStatsData($reports, $contactEvents, $stats, $manualTrajectories);
        
        // Cache the final stats
        $cache->cacheStats($stats);
        
        // Clean expired cache periodically (1% chance)
        if (rand(1, 100) === 1) {
            $cache->cleanExpiredCache();
        }
        
    } catch (Exception $e) {
        error_log("Error fetching optimized technician stats: " . $e->getMessage());
    }
    
    return $stats;
}

/**
 * Fetch reports with optimized queries and mobile app duplicate handling
 */
function fetchOptimizedReports($database, $userId) {
    $allDocuments = [];
    $pageToken = null;
    $limits = CacheConfig::getQueryLimits();
    $maxPages = $limits['max_pages'];
    $pageSize = $limits['page_size'];
    $maxReports = $limits['max_reports'];
    $currentPage = 0;

    // First, fetch all documents from reports collection for this user
    do {
        try {
            // Use configured page size for better performance
            $pageReports = $database->listDocuments('reports', $pageToken, $pageSize);

            // Extract pagination token
            $pageToken = $pageReports['_nextPageToken'] ?? null;
            unset($pageReports['_nextPageToken']);

            // Filter for this technician's documents
            foreach ($pageReports as $reportId => $report) {
                if (isset($report['userId']) && $report['userId'] === $userId &&
                    isset($report['userRole']) && $report['userRole'] === 'tecnico_prorola') {
                    $allDocuments[$reportId] = $report;
                }
            }

            $currentPage++;

            // Break if we have enough data or hit page limit
            if (count($allDocuments) >= $maxReports || $currentPage >= $maxPages) {
                break;
            }

        } catch (Exception $e) {
            error_log("Error fetching reports page $currentPage: " . $e->getMessage());
            break;
        }

    } while ($pageToken);

    // Now process documents to handle mobile app duplicates
    $reports = [];
    $sessionGroups = [];

    // Group documents by sessionId
    foreach ($allDocuments as $docId => $doc) {
        $sessionId = $doc['sessionId'] ?? $docId;
        if (!isset($sessionGroups[$sessionId])) {
            $sessionGroups[$sessionId] = [];
        }
        $sessionGroups[$sessionId][$docId] = $doc;
    }

    // Process each session group
    foreach ($sessionGroups as $sessionId => $sessionDocs) {
        if (count($sessionDocs) === 1) {
            // Single document - use as is
            $reports = array_merge($reports, $sessionDocs);
        } else {
            // Multiple documents for same session - merge them
            $mainReport = null;
            $contactData = null;
            $mainReportId = null;

            foreach ($sessionDocs as $docId => $doc) {
                $type = $doc['type'] ?? '';

                if ($type === 'tecnico_monitoring_report' || isset($doc['reportName'])) {
                    // This is the main report document
                    $mainReport = $doc;
                    $mainReportId = $docId;
                } elseif ($type === 'monitoring_session' || isset($doc['contactLocationDetails'])) {
                    // This is the contact events document
                    $contactData = $doc;
                }
            }

            if ($mainReport && $mainReportId) {
                // Use main report and merge contact data if available
                if ($contactData) {
                    // Merge contact-related fields from contact document
                    $mainReport['contactLocationDetails'] = $contactData['contactLocationDetails'] ?? null;
                    $mainReport['circumstances'] = $contactData['circumstances'] ?? null;
                    $mainReport['images'] = $contactData['images'] ?? null;

                    // Update contact count if contact document has more recent data
                    if (isset($contactData['contactsCount'])) {
                        $mainReport['contactsCount'] = $contactData['contactsCount'];
                    }
                }

                $reports[$mainReportId] = $mainReport;
            } else {
                // No main report found, use the first document but mark it as incomplete
                $firstDoc = reset($sessionDocs);
                $firstDocId = key($sessionDocs);
                $reports[$firstDocId] = $firstDoc;
            }
        }
    }

    error_log("Fetched " . count($allDocuments) . " documents, processed into " . count($reports) . " reports for user $userId");
    return $reports;
}

/**
 * Fetch contact events with optimized queries (GPS + Manual contacts)
 */
function fetchOptimizedContactEvents($database, $userId) {
    $contactEvents = [];
    $limits = CacheConfig::getQueryLimits();
    $maxPages = min(5, $limits['max_pages']); // Limit contact events pages more strictly
    $pageSize = $limits['page_size'];
    $maxContacts = $limits['max_contacts'];

    // 1. Fetch GPS contact events from 'contactEvents' collection
    $pageToken = null;
    $currentPage = 0;

    do {
        try {
            $pageEvents = $database->listDocuments('contactEvents', $pageToken, $pageSize);

            $pageToken = $pageEvents['_nextPageToken'] ?? null;
            unset($pageEvents['_nextPageToken']);

            // Filter for this technician's contact events
            foreach ($pageEvents as $eventId => $event) {
                if (isset($event['userId']) && $event['userId'] === $userId) {
                    $event['_source'] = 'gps';
                    $event['_sourceCollection'] = 'contactEvents';
                    $contactEvents[$eventId] = $event;
                }
            }

            $currentPage++;

            if (count($contactEvents) >= $maxContacts || $currentPage >= $maxPages) {
                break;
            }

        } catch (Exception $e) {
            error_log("Error fetching GPS contact events page $currentPage: " . $e->getMessage());
            break;
        }

    } while ($pageToken);

    // 2. Fetch manual contact events from 'tecnicosTrajetosContactos' collection
    $pageToken = null;
    $currentPage = 0;
    $maxManualContacts = min($maxContacts - count($contactEvents), 250); // Reserve space for manual contacts

    do {
        try {
            $pageManualContacts = $database->listDocuments('tecnicosTrajetosContactos', $pageToken, $pageSize);

            $pageToken = $pageManualContacts['_nextPageToken'] ?? null;
            unset($pageManualContacts['_nextPageToken']);

            // Filter for this technician's manual contacts
            foreach ($pageManualContacts as $contactId => $contact) {
                if (isset($contact['createdBy']) && $contact['createdBy'] === $userId) {
                    // Transform manual contact data to match GPS contact structure
                    $transformedContact = [
                        'sessionId' => $contact['trajectoryId'] ?? null,
                        'userId' => $contact['createdBy'],
                        'userEmail' => $contact['createdByEmail'] ?? '',
                        'timestamp' => $contact['createdAt'] ?? '',
                        'circumstance' => $contact['circumstance'] ?? '',
                        'contactLocation' => [
                            'latitude' => $contact['coordinates']['lat'] ?? 0,
                            'longitude' => $contact['coordinates']['lng'] ?? 0
                        ],
                        'location' => $contact['location'] ?? '',
                        'time' => $contact['time'] ?? '',
                        '_source' => 'manual',
                        '_sourceCollection' => 'tecnicosTrajetosContactos',
                        '_originalId' => $contactId,
                        '_trajectoryId' => $contact['trajectoryId'] ?? null
                    ];

                    // Use a unique key to avoid conflicts with GPS contacts
                    $contactEvents['manual_' . $contactId] = $transformedContact;
                }
            }

            $currentPage++;

            if (count($contactEvents) >= $maxContacts || $currentPage >= $maxPages) {
                break;
            }

        } catch (Exception $e) {
            error_log("Error fetching manual contact events page $currentPage: " . $e->getMessage());
            break;
        }

    } while ($pageToken);

    error_log("Fetched " . count($contactEvents) . " total contact events (GPS + Manual) for user $userId");
    return $contactEvents;
}

/**
 * Fetch manual trajectories with optimized queries
 */
function fetchOptimizedManualTrajectories($database, $userId) {
    $manualTrajectories = [];
    $pageToken = null;
    $limits = CacheConfig::getQueryLimits();
    $maxPages = min(5, $limits['max_pages']); // Limit manual trajectory pages
    $pageSize = $limits['page_size'];
    $maxTrajectories = 500; // Reasonable limit for manual trajectories
    $currentPage = 0;

    do {
        try {
            // Fetch from tecnicosTrajetosManual collection (Relatórios Manuais feature)
            $pageTrajectories = $database->listDocuments('tecnicosTrajetosManual', $pageToken, $pageSize);

            $pageToken = $pageTrajectories['_nextPageToken'] ?? null;
            unset($pageTrajectories['_nextPageToken']);

            // Filter for this technician's manual trajectories
            foreach ($pageTrajectories as $trajectoryId => $trajectory) {
                if (isset($trajectory['createdBy']) && $trajectory['createdBy'] === $userId) {
                    $manualTrajectories[$trajectoryId] = $trajectory;
                }
            }

            $currentPage++;

            // Break if we have enough data or hit page limit
            if (count($manualTrajectories) >= $maxTrajectories || $currentPage >= $maxPages) {
                break;
            }

        } catch (Exception $e) {
            error_log("Error fetching manual trajectories page $currentPage: " . $e->getMessage());
            break;
        }

    } while ($pageToken);

    error_log("Fetched " . count($manualTrajectories) . " manual trajectories for user $userId");
    return $manualTrajectories;
}

/**
 * Process raw data into statistics
 */
function processStatsData($reports, $contactEvents, $stats, $manualTrajectories = []) {
    $uniqueLocations = [];
    $uniqueSessions = [];
    $lastWeekTimestamp = time() - (7 * 24 * 60 * 60);
    $todayTimestamp = strtotime('today');
    
    // Process reports
    foreach ($reports as $reportId => $report) {
        // Count photos
        if (isset($report['images']) && is_array($report['images'])) {
            $stats['totalPhotos'] += count($report['images']);
        }
        
        // Count unique locations by name instead of coordinates
        if (isset($report['location']) && isset($report['location']['latitude']) && isset($report['location']['longitude'])) {
            $locationName = get_short_location($report['location']['latitude'], $report['location']['longitude']);
            if (!empty($locationName)) {
                $uniqueLocations[$locationName] = true;
            }
        }
        
        // Count unique sessions
        if (isset($report['sessionId'])) {
            $uniqueSessions[$report['sessionId']] = true;
        }
        
        // Count field hours
        if (isset($report['sessionDuration'])) {
            $stats['totalFieldHours'] += $report['sessionDuration'];
        }
        
        // Calculate total kilometers
        if (isset($report['totalDistance']) && is_numeric($report['totalDistance'])) {
            $stats['totalKilometers'] += $report['totalDistance'] / 1000;
        }
        
        // Count recent reports
        if (isset($report['createdAt'])) {
            $timestamp = is_numeric($report['createdAt']) ? $report['createdAt'] : strtotime($report['createdAt']);
            if ($timestamp > $lastWeekTimestamp) {
                $stats['recentReports']++;
            }
            if ($timestamp > $todayTimestamp) {
                $stats['todayReports']++;
            }
        }
    }
    
    // Process manual trajectories for distance calculation
    foreach ($manualTrajectories as $trajectoryId => $trajectory) {
        // Calculate distance from manual trajectories
        if (isset($trajectory['distance'])) {
            // Manual trajectories store distance as string like "1.48 km"
            $distanceStr = $trajectory['distance'];
            // Extract numeric value from string like "1.48 km"
            if (preg_match('/([0-9]+\.?[0-9]*)\s*km/i', $distanceStr, $matches)) {
                $distanceKm = floatval($matches[1]);
                $stats['totalKilometers'] += $distanceKm;
            }
        }

        // Count photos from manual trajectories
        if (isset($trajectory['images']) && is_array($trajectory['images'])) {
            $stats['totalPhotos'] += count($trajectory['images']);
        }

        // Count unique locations from manual trajectories by name instead of coordinates
        if (isset($trajectory['coordinates']) && is_array($trajectory['coordinates'])) {
            foreach ($trajectory['coordinates'] as $coord) {
                if (isset($coord['lat']) && isset($coord['lng'])) {
                    $locationName = get_short_location($coord['lat'], $coord['lng']);
                    if (!empty($locationName)) {
                        $uniqueLocations[$locationName] = true;
                    }
                }
            }
        }
    }

    // Count GPS vs Manual contacts
    $gpsContacts = 0;
    $manualContacts = 0;
    foreach ($contactEvents as $contact) {
        $source = $contact['_source'] ?? '';
        if ($source === 'gps') {
            $gpsContacts++;
        } elseif ($source === 'manual') {
            $manualContacts++;
        } else {
            // Fallback for contacts without _source (assume GPS)
            $gpsContacts++;
        }
    }

    // Finalize calculations
    $stats['gpsReports'] = count($reports); // GPS reports from 'reports' collection
    $stats['manualTrajectories'] = count($manualTrajectories); // Manual trajectories from 'tecnicosTrajetosManual' collection
    $stats['totalReports'] = $stats['gpsReports'] + $stats['manualTrajectories']; // Combined total
    $stats['totalContacts'] = count($contactEvents); // Total contacts (GPS + Manual)
    $stats['contactEvents'] = count($contactEvents); // Backward compatibility with dashboard
    $stats['gpsContacts'] = $gpsContacts; // GPS contacts only
    $stats['manualContacts'] = $manualContacts; // Manual contacts only
    $stats['totalSessions'] = count($uniqueSessions);
    $stats['uniqueLocations'] = count($uniqueLocations);
    $stats['totalFieldHours'] = round($stats['totalFieldHours'] / 3600, 1);
    $stats['totalKilometers'] = round($stats['totalKilometers'], 1);

    // Process report names for display (handle mobile app reportName field)
    foreach ($reports as $reportId => &$report) {
        if (!isset($report['name']) && isset($report['reportName'])) {
            $report['name'] = $report['reportName'];
        }
    }
    unset($report); // Break reference

    // Sort reports by createdAt in descending order (newest first)
    uasort($reports, function($a, $b) {
        $timestampA = 0;
        $timestampB = 0;

        if (isset($a['createdAt'])) {
            $timestampA = is_numeric($a['createdAt']) ? $a['createdAt'] : strtotime($a['createdAt']);
        }

        if (isset($b['createdAt'])) {
            $timestampB = is_numeric($b['createdAt']) ? $b['createdAt'] : strtotime($b['createdAt']);
        }

        // Sort in descending order (newest first)
        return $timestampB - $timestampA;
    });

    // Add raw data for charts
    $stats['reports'] = $reports;
    $stats['contactEventsData'] = $contactEvents;
    $stats['manualTrajectoriesData'] = $manualTrajectories;

    return $stats;
}

/**
 * Invalidate cache when new data is added
 */
function invalidateDashboardCache($userId) {
    $cache = new DashboardCache($userId);
    return $cache->invalidateUserCache();
}

/**
 * Get cache statistics for monitoring
 */
function getDashboardCacheStats($userId) {
    $cache = new DashboardCache($userId);
    return $cache->getCacheStats();
}

/**
 * Warm up cache for a user (pre-load data)
 */
function warmDashboardCache($database, $userId) {
    error_log("Warming dashboard cache for user $userId");

    // Force refresh to populate cache
    $stats = getTechnicianStatsOptimized($database, $userId, true);

    return [
        'success' => true,
        'reports_count' => count($stats['reports'] ?? []),
        'contacts_count' => count($stats['contactEventsData'] ?? []),
        'cache_warmed' => true
    ];
}

/**
 * Get chart data with caching
 */
function getCachedChartData($database, $userId, $chartType, $period) {
    $cache = new DashboardCache($userId);

    // Try cache first
    $cachedData = $cache->getCachedChartData($chartType, $period, 300);
    if ($cachedData !== null) {
        return $cachedData;
    }

    // Get base stats
    $stats = getTechnicianStatsOptimized($database, $userId);

    // Process chart-specific data
    $chartData = processChartData($stats, $chartType, $period);

    // Cache the result
    $cache->cacheChartData($chartType, $period, $chartData);

    return $chartData;
}

/**
 * Process data for specific chart types
 */
function processChartData($stats, $chartType, $period) {
    $reports = $stats['reports'] ?? [];
    $contactEvents = $stats['contactEventsData'] ?? [];
    $manualTrajectories = $stats['manualTrajectoriesData'] ?? [];

    switch ($chartType) {
        case 'trends':
            return processTrendsData($reports, $contactEvents, $period);
        case 'kilometers':
            return processKilometersData($reports, $manualTrajectories, $period);
        case 'distance':
            return processDistanceData($reports, $contactEvents, $period);
        case 'productivity':
            return processProductivityData($stats, $period);
        default:
            return [];
    }
}

/**
 * Process trends chart data
 */
function processTrendsData($reports, $contactEvents, $period) {
    // Implementation for trends data processing
    return [
        'type' => 'trends',
        'period' => $period,
        'data' => [] // Processed data would go here
    ];
}

/**
 * Process kilometers evolution data (GPS + Manual trajectories)
 */
function processKilometersData($reports, $manualTrajectories, $period) {
    $dailyKilometers = [];

    // Create date range
    $today = new DateTime();
    $days = $period === 'all' ? 30 : (int)$period;

    for ($i = $days - 1; $i >= 0; $i--) {
        $date = clone $today;
        $date->sub(new DateInterval("P{$i}D"));
        $dateStr = $date->format('Y-m-d');
        $dailyKilometers[$dateStr] = 0;
    }

    // Process GPS reports
    foreach ($reports as $report) {
        if (isset($report['createdAt'], $report['totalDistance'])) {
            $timestamp = is_numeric($report['createdAt']) ? $report['createdAt'] : strtotime($report['createdAt']);
            $date = date('Y-m-d', $timestamp);

            if (isset($dailyKilometers[$date])) {
                $distance = is_numeric($report['totalDistance']) ? $report['totalDistance'] / 1000 : 0;
                $dailyKilometers[$date] += $distance;
            }
        }
    }

    // Process manual trajectories
    foreach ($manualTrajectories as $trajectory) {
        if (isset($trajectory['createdAt'], $trajectory['distance'])) {
            $timestamp = is_numeric($trajectory['createdAt']) ? $trajectory['createdAt'] : strtotime($trajectory['createdAt']);
            $date = date('Y-m-d', $timestamp);

            if (isset($dailyKilometers[$date])) {
                // Manual trajectories store distance as string like "1.48 km"
                $distanceStr = $trajectory['distance'];
                if (preg_match('/([0-9]+\.?[0-9]*)\s*km/i', $distanceStr, $matches)) {
                    $distanceKm = floatval($matches[1]);
                    $dailyKilometers[$date] += $distanceKm;
                }
            }
        }
    }

    return [
        'type' => 'kilometers',
        'period' => $period,
        'daily_data' => $dailyKilometers,
        'total' => array_sum($dailyKilometers)
    ];
}

/**
 * Process distance efficiency data
 */
function processDistanceData($reports, $contactEvents, $period) {
    return [
        'type' => 'distance',
        'period' => $period,
        'data' => [] // Implementation would go here
    ];
}

/**
 * Process productivity data
 */
function processProductivityData($stats, $period) {
    return [
        'type' => 'productivity',
        'period' => $period,
        'stats' => [
            'reports' => $stats['totalReports'] ?? 0,
            'contacts' => $stats['totalContacts'] ?? 0,
            'photos' => $stats['totalPhotos'] ?? 0,
            'kilometers' => $stats['totalKilometers'] ?? 0
        ]
    ];
}

/**
 * Smart cache invalidation based on data type
 */
function invalidateCacheForDataType($userId, $dataType) {
    $cache = new DashboardCache($userId);

    switch ($dataType) {
        case 'trajectory':
        case 'manual_trajectory':
            // Invalidate trajectory-related caches
            $cache->invalidateUserCache(); // Full invalidation for now
            error_log("Cache invalidated for user $userId - trajectory data changed");
            break;

        case 'contact':
        case 'contact_event':
            // Invalidate contact-related caches
            $cache->invalidateUserCache(); // Full invalidation for now
            error_log("Cache invalidated for user $userId - contact data changed");
            break;

        case 'report':
        case 'gps_report':
            // Invalidate report-related caches
            $cache->invalidateUserCache(); // Full invalidation for now
            error_log("Cache invalidated for user $userId - report data changed");
            break;

        default:
            // Full cache invalidation for unknown types
            $cache->invalidateUserCache();
            error_log("Cache invalidated for user $userId - unknown data type: $dataType");
    }

    return true;
}

/**
 * Invalidate cache and optionally warm it immediately
 */
function invalidateAndWarmCache($userId, $dataType = 'unknown', $warmImmediately = false) {
    // Invalidate first
    invalidateCacheForDataType($userId, $dataType);

    // Optionally warm cache immediately for better UX
    if ($warmImmediately) {
        try {
            global $database;
            if ($database) {
                // Warm the cache in background
                getTechnicianStatsOptimized($database, $userId, true);
                error_log("Cache warmed immediately for user $userId after $dataType change");
            }
        } catch (Exception $e) {
            error_log("Failed to warm cache for user $userId: " . $e->getMessage());
        }
    }

    return true;
}

/**
 * Get protocol display name for reports
 * CRITICAL: This function was missing from optimized version, causing fatal error
 */
function getProtocolDisplayName($protocol) {
    $protocols = [
        'trajeto' => 'Trajeto',
        'estacoes_escuta' => 'Estações de escuta',
        'metodo_mapas' => 'Método dos mapas',
        'contagens_pontos' => 'Contagens em pontos',
        'captura_marcacao' => 'Captura e marcação',
        'acompanhamento_cacadas' => 'Acompanhamento de caçadas',
        'registos_ocasionais' => 'Registos ocasionais',
        // Legacy protocol mappings for backward compatibility
        'point_count' => 'Contagem por Pontos',
        'line_transect' => 'Transecto Linear',
        'distance_sampling' => 'Amostragem por Distância',
        'monitoring_session' => 'Sessão de Monitorização',
        'general_observation' => 'Observação Geral'
    ];

    return $protocols[$protocol] ?? $protocol ?? 'Desconhecido';
}

/**
 * Format timestamp to readable date
 * CRITICAL: This function was missing from optimized version, causing fatal error in contacts page
 */
function formatReportDate($timestamp) {
    if (!$timestamp) return 'N/A';

    $date = new DateTime();

    // Handle different timestamp formats
    if (is_numeric($timestamp)) {
        $date->setTimestamp($timestamp);
    } elseif (is_string($timestamp)) {
        $date = new DateTime($timestamp);
    } else {
        return 'N/A';
    }

    return $date->format('d/m/Y H:i');
}
