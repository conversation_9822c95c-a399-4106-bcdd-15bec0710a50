<?php
/**
 * Cache Management API for Dashboard
 * Provides endpoints for cache warming, invalidation, and monitoring
 */

require_once '../includes/config.php';
require_once '../includes/dashboard_stats_optimized.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

$currentUser = getCurrentUser();
$userId = $currentUser['id'];

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($action, $userId);
            break;
        case 'POST':
            handlePostRequest($action, $userId);
            break;
        case 'DELETE':
            handleDeleteRequest($action, $userId);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    error_log("Cache management error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Handle GET requests (cache status, stats)
 */
function handleGetRequest($action, $userId) {
    global $database;
    
    switch ($action) {
        case 'status':
            $cache = new DashboardCache($userId);
            $stats = $cache->getCacheStats();
            
            echo json_encode([
                'success' => true,
                'cache_stats' => $stats,
                'cache_enabled' => true,
                'user_id' => $userId
            ]);
            break;
            
        case 'health':
            $cache = new DashboardCache($userId);
            
            // Check if main caches exist and are valid
            $statsValid = $cache->isCacheValid($cache->getCacheKey('stats'), 300);
            $reportsValid = $cache->isCacheValid($cache->getCacheKey('reports'), 600);
            $contactsValid = $cache->isCacheValid($cache->getCacheKey('contact_events'), 600);
            
            echo json_encode([
                'success' => true,
                'health' => [
                    'stats_cache' => $statsValid,
                    'reports_cache' => $reportsValid,
                    'contacts_cache' => $contactsValid,
                    'overall_health' => $statsValid && $reportsValid && $contactsValid
                ]
            ]);
            break;
            
        case 'performance':
            // Measure cache vs no-cache performance
            $startTime = microtime(true);
            
            // Cached request
            $cachedStats = getTechnicianStatsOptimized($database, $userId, false);
            $cachedTime = microtime(true) - $startTime;
            
            $startTime = microtime(true);
            
            // Fresh request
            $freshStats = getTechnicianStatsOptimized($database, $userId, true);
            $freshTime = microtime(true) - $startTime;
            
            echo json_encode([
                'success' => true,
                'performance' => [
                    'cached_time_ms' => round($cachedTime * 1000, 2),
                    'fresh_time_ms' => round($freshTime * 1000, 2),
                    'improvement_factor' => round($freshTime / $cachedTime, 2),
                    'data_consistency' => count($cachedStats['reports']) === count($freshStats['reports'])
                ]
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Handle POST requests (cache warming, refresh)
 */
function handlePostRequest($action, $userId) {
    global $database;
    
    switch ($action) {
        case 'warm':
            $result = warmDashboardCache($database, $userId);
            echo json_encode($result);
            break;
            
        case 'refresh':
            // Force refresh specific cache type
            $cacheType = $_POST['type'] ?? 'all';
            
            $cache = new DashboardCache($userId);
            $refreshed = [];
            
            if ($cacheType === 'all' || $cacheType === 'stats') {
                getTechnicianStatsOptimized($database, $userId, true);
                $refreshed[] = 'stats';
            }
            
            if ($cacheType === 'all' || $cacheType === 'reports') {
                $reports = fetchOptimizedReports($database, $userId);
                $cache->cacheReports($reports);
                $refreshed[] = 'reports';
            }
            
            if ($cacheType === 'all' || $cacheType === 'contacts') {
                $contacts = fetchOptimizedContactEvents($database, $userId);
                $cache->cacheContactEvents($contacts);
                $refreshed[] = 'contacts';
            }
            
            echo json_encode([
                'success' => true,
                'refreshed' => $refreshed,
                'timestamp' => time()
            ]);
            break;
            
        case 'preload':
            // Preload chart data for common periods
            $periods = [7, 30, 90, 'all'];
            $chartTypes = ['trends', 'kilometers', 'distance', 'productivity'];
            $preloaded = [];
            
            foreach ($chartTypes as $chartType) {
                foreach ($periods as $period) {
                    try {
                        $data = getCachedChartData($database, $userId, $chartType, $period);
                        $preloaded[] = "{$chartType}_{$period}";
                    } catch (Exception $e) {
                        error_log("Failed to preload $chartType $period: " . $e->getMessage());
                    }
                }
            }
            
            echo json_encode([
                'success' => true,
                'preloaded' => $preloaded,
                'count' => count($preloaded)
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Handle DELETE requests (cache invalidation)
 */
function handleDeleteRequest($action, $userId) {
    switch ($action) {
        case 'invalidate':
            $invalidated = invalidateDashboardCache($userId);
            
            echo json_encode([
                'success' => true,
                'invalidated_files' => $invalidated,
                'message' => "Invalidated $invalidated cache files"
            ]);
            break;
            
        case 'cleanup':
            $cache = new DashboardCache($userId);
            $cleaned = $cache->cleanExpiredCache();
            
            echo json_encode([
                'success' => true,
                'cleaned_files' => $cleaned,
                'message' => "Cleaned $cleaned expired cache files"
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Helper function to get cache key (exposed for API)
 */
function getCacheKey($type, $params = []) {
    global $userId;
    $cache = new DashboardCache($userId);
    return $cache->getCacheKey($type, $params);
}
