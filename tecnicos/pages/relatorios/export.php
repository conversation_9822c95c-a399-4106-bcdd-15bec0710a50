<?php
// Start output buffering to prevent any unwanted output
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as tecnico
if (!isTecnicosUser()) {
    header('Location: ../auth/login.php');
    exit();
}

// Check if format is provided
if (!isset($_POST['format']) || empty($_POST['format'])) {
    header('Location: index.php?error=missing_format');
    exit();
}

$format = sanitize_input($_POST['format']);
$currentUser = getCurrentUser();

try {
    // Ensure database is available
    global $database;
    if (!$database) {
        throw new Exception("Database connection not available");
    }

    // Get real data from database with caching
    require_once '../../includes/dashboard_stats_optimized.php';

    // Log export attempt
    error_log("Export attempt by user: " . ($currentUser['email'] ?? 'unknown') . " in format: " . $format);

    $stats = getTechnicianStatsOptimized($database, $currentUser['id']);
    $reports = $stats['reports'] ?? [];

    // Log data retrieval
    // Debug: Retrieved reports count (commented out to prevent output issues)
    // error_log("Retrieved " . count($reports) . " reports for export");

    // Group contact events by sessionId for counting
    $contactEvents = [];
    if (!empty($stats['contactEventsData'])) {
        foreach ($stats['contactEventsData'] as $event) {
            if (isset($event['sessionId'])) {
                $sessionId = $event['sessionId'];
                if (!isset($contactEvents[$sessionId])) {
                    $contactEvents[$sessionId] = 0;
                }
                $contactEvents[$sessionId]++;
            }
        }
    }

    // Prepare export data from real reports
    $exportData = [];

    if (empty($reports)) {
        // If no reports found, create a message row
        $exportData[] = [
            'Nome' => 'N/A',
            'Protocolo' => 'N/A',
            'Dispositivo' => 'N/A',
            'Data' => 'N/A',
            'Localização' => 'N/A',
            'Distância' => 'N/A',
            'Contactos' => 'N/A',
            'Fotos' => 'N/A',
            'ID' => 'N/A',
            'Técnico' => $currentUser['name'] ?? $currentUser['email'],
            'Observação' => 'Nenhum relatório encontrado para este técnico'
        ];
    } else {
        foreach ($reports as $reportId => $report) {
            // Get report name with fallback
            $reportName = $report['reportName'] ?? '';
            if (empty($reportName)) {
                $reportName = 'Sem nome';
            }

            $exportRow = [
                'Nome' => $reportName,
                'Protocolo' => exportGetProtocolName($report['protocol'] ?? ''),
                'Dispositivo' => exportGetDeviceInfo($report),
                'Data' => exportFormatReportDate($report['createdAt'] ?? ''),
                'Localização' => exportGetLocationString($report),
                'Distância' => exportFormatDistance($report['totalDistance'] ?? 0),
                'Contactos' => (string)($contactEvents[$report['sessionId'] ?? ''] ?? 0),
                'Fotos' => (string)(isset($report['images']) ? count($report['images']) : 0),
                'ID' => $reportId,
                'Técnico' => $currentUser['name'] ?? $currentUser['email'],
                'Sessão ID' => $report['sessionId'] ?? 'N/A'
            ];
            $exportData[] = $exportRow;
        }
    }

    // Generate filename with timestamp
    $timestamp = date('Y-m-d_H-i-s');
    $filename = "relatorios_prorola_{$timestamp}";
    
    if ($format === 'excel') {
        exportToExcel($exportData, $filename);
    } elseif ($format === 'dbf') {
        exportToDBF($exportData, $filename);
    } else {
        header('Location: index.php?error=invalid_format');
        exit();
    }
    
} catch (Exception $e) {
    error_log("Export error: " . $e->getMessage());
    header('Location: index.php?error=export_failed&message=' . urlencode($e->getMessage()));
    exit();
}

function exportToExcel($data, $filename) {
    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Ensure UTF-8 encoding
    mb_http_output('UTF-8');

    // Set headers for proper CSV that Excel can open without warnings
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // Create output stream
    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM for Excel compatibility
    fwrite($output, "\xEF\xBB\xBF");

    // Write headers and data
    if (!empty($data)) {
        // Add coordinate system information as comment in Portuguese
        fwrite($output, "# Sistema de Coordenadas: WGS84 (EPSG:4326)\n");
        fwrite($output, "# Latitude/Longitude em graus decimais\n");

        // Write headers
        fputcsv($output, array_keys($data[0]), ';', '"'); // Use semicolon for Excel compatibility

        // Write data
        foreach ($data as $row) {
            fputcsv($output, $row, ';', '"'); // Use semicolon for Excel compatibility
        }
    }

    fclose($output);
    exit();
}

function exportToDBF($data, $filename) {
    // Clear any previous output and disable error reporting to prevent HTML output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Disable error display to prevent HTML in binary output
    ini_set('display_errors', 0);
    error_reporting(0);

    // Set headers for DBF file
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '.dbf"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    if (empty($data)) {
        exit();
    }

    // Create a simple DBF file structure
    // Define expected fields to prevent unexpected columns (in new column order)
    $expectedFields = ['Nome', 'Protocolo', 'Dispositivo', 'Data', 'Localização', 'Distância', 'Contactos', 'Fotos', 'ID', 'Técnico', 'Sessão ID'];

    // Filter to only include expected fields and ensure clean data
    $fields = [];
    $cleanData = [];

    foreach ($data as $row) {
        $cleanRow = [];
        foreach ($expectedFields as $field) {
            if (isset($row[$field])) {
                $cleanRow[$field] = $row[$field];
                if (!in_array($field, $fields)) {
                    $fields[] = $field;
                }
            }
        }
        $cleanData[] = $cleanRow;
    }

    // Use the cleaned data
    $data = $cleanData;

    $recordCount = count($data);

    // DBF Header (32 bytes) - WGS84 compatible
    $header = pack('C', 0x03); // DBF version
    $header .= pack('C3', date('y'), date('m'), date('d')); // Last update date
    $header .= pack('V', $recordCount); // Number of records
    $header .= pack('v', 32 + (count($fields) * 32) + 1); // Header length
    $header .= pack('v', 0); // Record length (will calculate)
    $header .= str_repeat("\x00", 17); // Reserved bytes (17 bytes)
    $header .= pack('C', 0x03); // Code page mark (ISO-8859-1/Latin-1)
    $header .= str_repeat("\x00", 2); // Reserved bytes (2 bytes)

    // Calculate record length and create field descriptors
    $fieldDescriptors = '';
    $recordLength = 1; // Start with 1 for deletion flag

    foreach ($fields as $field) {
        // Keep Portuguese field names and encode them properly
        $fieldName = substr(str_pad($field, 11, "\x00"), 0, 11); // Field name (11 bytes)
        $fieldType = 'C'; // Character type
        $fieldLength = 50; // Max length for character fields

        $fieldDescriptors .= $fieldName;
        $fieldDescriptors .= pack('C', ord($fieldType)); // Field type
        $fieldDescriptors .= str_repeat("\x00", 4); // Field data address (not used)
        $fieldDescriptors .= pack('C', $fieldLength); // Field length
        $fieldDescriptors .= pack('C', 0); // Decimal count
        $fieldDescriptors .= str_repeat("\x00", 14); // Reserved

        $recordLength += $fieldLength;
    }

    // Update record length in header
    $header = substr($header, 0, 10) . pack('v', $recordLength) . substr($header, 12);

    // Write header
    echo $header;
    echo $fieldDescriptors;
    echo "\x0D"; // Header terminator

    // Write records
    foreach ($data as $row) {
        echo ' '; // Deletion flag (space = not deleted)
        foreach ($fields as $field) {
            // Only process fields that are in our expected list
            if (!in_array($field, $expectedFields)) {
                continue;
            }

            $value = isset($row[$field]) ? (string)$row[$field] : '';

            // Convert to ISO-8859-1 (Latin-1) encoding for DBF compatibility with Portuguese characters
            if (function_exists('mb_convert_encoding')) {
                $value = mb_convert_encoding($value, 'ISO-8859-1', 'UTF-8');
            } elseif (function_exists('iconv')) {
                $value = iconv('UTF-8', 'ISO-8859-1//IGNORE', $value);
            }

            // Clean up problematic characters but preserve Portuguese ones
            $value = str_replace(['"', "'", "\n", "\r", "\t"], ['', '', '', '', ' '], $value);
            echo str_pad(substr($value, 0, 50), 50, ' ');
        }
    }

    echo "\x1A"; // End of file marker
    exit();
}

/**
 * Helper functions for data formatting (with export prefix to avoid conflicts)
 */
function exportGetDeviceInfo($report) {
    // Try different possible device info locations
    if (isset($report['deviceInfo']['model'])) {
        return $report['deviceInfo']['model'];
    }
    if (isset($report['deviceInfo']['platform'])) {
        return ucfirst($report['deviceInfo']['platform']);
    }
    if (isset($report['device']['model'])) {
        return $report['device']['model'];
    }
    if (isset($report['device']['platform'])) {
        return ucfirst($report['device']['platform']);
    }
    if (isset($report['platform'])) {
        return ucfirst($report['platform']);
    }

    return 'Dispositivo móvel';
}

function exportGetProtocolName($protocol) {
    // Map protocols to Portuguese names (matching main application)
    $protocolNames = [
        'trajeto' => 'Trajeto',
        'estacoes_escuta' => 'Estações de escuta',
        'metodo_mapas' => 'Método dos mapas',
        'contagens_pontos' => 'Contagens em pontos',
        'captura_marcacao' => 'Captura e marcação',
        'acompanhamento_cacadas' => 'Acompanhamento de caçadas',
        'registos_ocasionais' => 'Registos ocasionais'
    ];

    return $protocolNames[$protocol] ?? $protocol ?? 'Desconhecido';
}

function exportFormatReportDate($createdAt) {
    if (empty($createdAt)) {
        return 'N/A';
    }

    // Handle different timestamp formats
    if (is_numeric($createdAt)) {
        return date('d/m/Y H:i', $createdAt);
    } elseif (is_string($createdAt)) {
        $timestamp = strtotime($createdAt);
        return $timestamp ? date('d/m/Y H:i', $timestamp) : 'N/A';
    }

    return 'N/A';
}

function exportGetLocationString($report) {
    if (isset($report['location']) && isset($report['location']['latitude']) && isset($report['location']['longitude'])) {
        $lat = $report['location']['latitude'];
        $lon = $report['location']['longitude'];

        // Try to get a readable location name
        $locationName = get_short_location($lat, $lon);
        if ($locationName && $locationName !== "Lat: $lat, Long: $lon") {
            return $locationName;
        }

        // Fallback to coordinates (WGS84 format with dot decimal separator)
        return "Lat: " . number_format($lat, 8, '.', '') . ", Long: " . number_format($lon, 8, '.', '');
    }

    return 'N/A';
}

function exportFormatDistance($distance) {
    if (empty($distance) || $distance <= 0) {
        return '0m';
    }

    if ($distance >= 1000) {
        return number_format($distance / 1000, 1, ',', '') . 'km';
    } else {
        return number_format($distance, 0, ',', '') . 'm';
    }
}
?>