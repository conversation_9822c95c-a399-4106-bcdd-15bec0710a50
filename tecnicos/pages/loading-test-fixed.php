<?php
// Test page to verify the loading system fixes
require_once '../includes/config.php';
require_once '../includes/auth.php';

// Check if user is logged in as tecnico
if (!isTecnicosUser()) {
    header('Location: ../auth/login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading System Test - Fixed Issues</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/gestores-common.css">
    
    <!-- Loading System CSS - Direct Link -->
    <link rel="stylesheet" href="../assets/css/loading.css?v=<?php echo time(); ?>">

    <!-- Loading System (BEFORE jQuery to test dependency handling) -->
    <?php include '../includes/loading.php'; ?>
    
    <style>
        body { font-family: Arial, sans-serif; padding: 2rem; }
        .test-section { background: white; border-radius: 8px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
        .test-result { padding: 0.5rem; margin: 0.5rem 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .color-test { width: 50px; height: 50px; border-radius: 50%; display: inline-block; margin: 0.5rem; }
        .expected-color { background-color: #0a7ea4; }
        .old-color { background-color: #10b981; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-bug"></i>
                Loading System - Issues Fixed
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content">
        <div class="container-fluid">
            
            <!-- jQuery Dependency Test -->
            <div class="test-section">
                <h3><i class="fas fa-code text-primary"></i> jQuery Dependency Test</h3>
                <p>Testing if the loading system initializes without jQuery errors:</p>
                
                <div id="jqueryTestResults">
                    <div class="test-result info">
                        <i class="fas fa-clock"></i> Testing jQuery dependency handling...
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="testJQueryDependency()">
                    <i class="fas fa-play"></i> Test jQuery Integration
                </button>
            </div>

            <!-- Color Consistency Test -->
            <div class="test-section">
                <h3><i class="fas fa-palette text-success"></i> Color Consistency Test</h3>
                <p>Verifying that loading indicators use the correct app color scheme:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>Expected Color (#0a7ea4)</h5>
                        <div class="color-test expected-color"></div>
                        <span>#0a7ea4 (App Blue)</span>
                    </div>
                    <div class="col-md-6">
                        <h5>Old Color (#10b981)</h5>
                        <div class="color-test old-color"></div>
                        <span>#10b981 (Green - Should NOT be used)</span>
                    </div>
                </div>
                
                <button class="btn btn-info" onclick="testColorConsistency()">
                    <i class="fas fa-eye"></i> Test Loading Colors
                </button>
                
                <div id="colorTestResults" class="mt-3"></div>
            </div>

            <!-- Error Console Test -->
            <div class="test-section">
                <h3><i class="fas fa-terminal text-warning"></i> Console Error Test</h3>
                <p>Check browser console for JavaScript errors:</p>
                
                <div id="consoleTestResults">
                    <div class="test-result info">
                        <i class="fas fa-info-circle"></i> Open browser console (F12) and check for errors.
                    </div>
                </div>
                
                <button class="btn btn-warning" onclick="testConsoleErrors()">
                    <i class="fas fa-search"></i> Simulate Error-Prone Operations
                </button>
            </div>

            <!-- Integration Test -->
            <div class="test-section">
                <h3><i class="fas fa-cogs text-danger"></i> Full Integration Test</h3>
                <p>Test all loading system features together:</p>
                
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-outline-primary w-100 mb-2" onclick="testBasicLoading()">
                            Basic Loading
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-success w-100 mb-2" onclick="testDataTableLoading()">
                            DataTable Loading
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testFormLoading()">
                            Form Loading
                        </button>
                    </div>
                </div>
                
                <div id="integrationTestResults" class="mt-3"></div>
            </div>

        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery (loaded AFTER loading system to test dependency handling) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // Test functions
        function testJQueryDependency() {
            const results = document.getElementById('jqueryTestResults');
            results.innerHTML = '';
            
            // Test 1: Check if TecnicosLoading exists
            if (typeof window.TecnicosLoading !== 'undefined') {
                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> TecnicosLoading object exists</div>';
            } else {
                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> TecnicosLoading object missing</div>';
                return;
            }
            
            // Test 2: Check if basic methods work
            try {
                window.TecnicosLoading.show('test', 'Testing...');
                setTimeout(() => {
                    window.TecnicosLoading.hide('test');
                }, 1000);
                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> Basic loading methods work</div>';
            } catch (error) {
                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> Basic methods failed: ' + error.message + '</div>';
            }
            
            // Test 3: Check jQuery integration
            if (typeof $ !== 'undefined') {
                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> jQuery is now available</div>';

                // Force jQuery integration if not already done
                if (!window.TecnicosLoading.jqueryAjaxSetup) {
                    results.innerHTML += '<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> jQuery AJAX integration not enabled, forcing initialization...</div>';

                    // Force reinitialize
                    if (typeof window.TecnicosLoading.reinitializeWithJQuery === 'function') {
                        window.TecnicosLoading.reinitializeWithJQuery();

                        setTimeout(() => {
                            if (window.TecnicosLoading.jqueryAjaxSetup) {
                                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> jQuery AJAX integration now enabled after forced initialization</div>';
                            } else {
                                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> jQuery AJAX integration still not enabled after forced initialization</div>';
                            }
                        }, 100);
                    }
                } else {
                    results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> jQuery AJAX integration already enabled</div>';
                }
            } else {
                results.innerHTML += '<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> jQuery still not available</div>';
            }
        }

        function testColorConsistency() {
            const results = document.getElementById('colorTestResults');
            results.innerHTML = '';

            // Check if CSS file is loaded
            const cssLinks = document.querySelectorAll('link[href*="loading.css"]');
            if (cssLinks.length === 0) {
                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> Loading CSS file not found in page</div>';
                return;
            } else {
                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> Loading CSS file found: ' + cssLinks[0].href + '</div>';
            }

            // Check CSS variables
            const rootStyles = getComputedStyle(document.documentElement);
            const primaryColor = rootStyles.getPropertyValue('--loading-primary').trim();
            const primaryDark = rootStyles.getPropertyValue('--loading-primary-dark').trim();

            results.innerHTML += '<div class="test-result info"><i class="fas fa-info-circle"></i> Raw CSS variable value: "' + primaryColor + '"</div>';

            if (primaryColor === '#0a7ea4' || primaryColor === 'rgb(10, 126, 164)') {
                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> CSS variables use correct color: ' + primaryColor + '</div>';
            } else if (primaryColor === '') {
                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> CSS variables not found - CSS file may not be loaded</div>';

                // Try to load CSS manually for testing
                const testLink = document.createElement('link');
                testLink.rel = 'stylesheet';
                testLink.href = '../assets/css/loading.css?v=' + Date.now();
                document.head.appendChild(testLink);

                results.innerHTML += '<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> Attempting to load CSS manually...</div>';

                setTimeout(() => {
                    const newRootStyles = getComputedStyle(document.documentElement);
                    const newPrimaryColor = newRootStyles.getPropertyValue('--loading-primary').trim();
                    if (newPrimaryColor === '#0a7ea4' || newPrimaryColor === 'rgb(10, 126, 164)') {
                        results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> Manual CSS load successful: ' + newPrimaryColor + '</div>';
                    } else {
                        results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> Manual CSS load failed: ' + newPrimaryColor + '</div>';
                    }
                }, 1000);
            } else {
                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> CSS variables use wrong color: ' + primaryColor + ' (expected #0a7ea4)</div>';
            }

            // Show loading to test visual color
            if (window.TecnicosLoading) {
                window.TecnicosLoading.show('color-test', 'Testing visual color...');

                setTimeout(() => {
                    window.TecnicosLoading.hide('color-test');
                    results.innerHTML += '<div class="test-result info"><i class="fas fa-eye"></i> Visual color test completed - check if spinner was blue</div>';
                }, 2000);
            }
        }

        function testConsoleErrors() {
            const results = document.getElementById('consoleTestResults');
            results.innerHTML = '<div class="test-result info"><i class="fas fa-info-circle"></i> Performing operations that previously caused errors...</div>';
            
            // Test operations that previously caused jQuery errors
            try {
                // Test DataTable integration
                if (window.TecnicosLoading) {
                    window.TecnicosLoading.showDataTable();
                    setTimeout(() => window.TecnicosLoading.hide('datatable'), 1000);
                }
                
                // Test AJAX integration
                fetch('data:application/json,{"test":true}')
                    .then(response => response.json())
                    .then(data => {
                        results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> Fetch API integration works without errors</div>';
                    });
                
                results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> No JavaScript errors detected during operations</div>';
            } catch (error) {
                results.innerHTML += '<div class="test-result error"><i class="fas fa-times"></i> Error detected: ' + error.message + '</div>';
            }
        }

        function testBasicLoading() {
            if (window.TecnicosLoading) {
                window.TecnicosLoading.show('basic', 'Basic loading test...');
                setTimeout(() => {
                    window.TecnicosLoading.hide('basic');
                    updateIntegrationResults('Basic loading: ✅ Success');
                }, 2000);
            }
        }

        function testDataTableLoading() {
            if (window.TecnicosLoading) {
                window.TecnicosLoading.showDataTable();
                setTimeout(() => {
                    window.TecnicosLoading.hide('datatable');
                    updateIntegrationResults('DataTable loading: ✅ Success');
                }, 2000);
            }
        }

        function testFormLoading() {
            if (window.TecnicosLoading) {
                window.TecnicosLoading.showForm('Form processing test...');
                setTimeout(() => {
                    window.TecnicosLoading.hide('form');
                    updateIntegrationResults('Form loading: ✅ Success');
                }, 2000);
            }
        }

        function updateIntegrationResults(message) {
            const results = document.getElementById('integrationTestResults');
            results.innerHTML += '<div class="test-result success"><i class="fas fa-check"></i> ' + message + '</div>';
        }

        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Loading system test page initialized');
            console.log('TecnicosLoading available:', typeof window.TecnicosLoading);
            console.log('jQuery available:', typeof $);
            
            // Auto-run jQuery dependency test after a short delay
            setTimeout(testJQueryDependency, 1000);
        });
    </script>
</body>
</html>
