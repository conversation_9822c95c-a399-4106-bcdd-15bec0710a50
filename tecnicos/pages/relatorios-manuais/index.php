<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/dashboard_cache.php';

// Check if user is logged in as tecnico
if (!isTecnicosUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../../auth/login.php');
    exit();
}

// Clean any previous output
ob_end_clean();

// Get current user
$currentUser = getCurrentUser();

// Get technician-specific stats with caching
require_once '../../includes/dashboard_stats_optimized.php';
$stats = getTechnicianStatsOptimized($database, $currentUser['id']);

// Load existing trajectories from database with caching
$trajectories = [];
$contactCounts = [];

try {
    // Initialize cache
    $cache = new DashboardCache($currentUser['id']);
    $cacheKey = 'relatorios_manuais_data';

    // Try to get cached data first
    $cachedData = $cache->loadCache($cacheKey);

    if ($cachedData === null || !$cache->isCacheValid($cacheKey, 600)) { // 10 minute cache
        // Fetch fresh data
        $result = $database->listDocumentsAsAdmin('tecnicosTrajetosManual');
        if (isset($result['documents']) && is_array($result['documents'])) {
            $trajectories = $result['documents'];

            // Load contact counts for each trajectory
            $trajectoryIds = array_keys($trajectories);
            if (!empty($trajectoryIds)) {
                try {
                    // Get all contacts from tecnicosTrajetosContactos collection
                    $contactsResult = $database->listDocumentsAsAdmin('tecnicosTrajetosContactos');
                    if (isset($contactsResult['documents']) && is_array($contactsResult['documents'])) {
                        // Count contacts per trajectory
                        foreach ($contactsResult['documents'] as $contact) {
                            if (isset($contact['trajectoryId']) && in_array($contact['trajectoryId'], $trajectoryIds)) {
                                $contactCounts[$contact['trajectoryId']] = ($contactCounts[$contact['trajectoryId']] ?? 0) + 1;
                            }
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error loading trajectory contacts: " . $e->getMessage());
                }
            }

            // Add contact count to each trajectory
            foreach ($trajectories as $id => &$trajectory) {
                $trajectory['contactCount'] = $contactCounts[$id] ?? 0;
            }

            // Sort trajectories by createdAt in descending order (newest first)
            uasort($trajectories, function($a, $b) {
                $timestampA = 0;
                $timestampB = 0;

                if (isset($a['createdAt'])) {
                    $timestampA = is_numeric($a['createdAt']) ? $a['createdAt'] : strtotime($a['createdAt']);
                }

                if (isset($b['createdAt'])) {
                    $timestampB = is_numeric($b['createdAt']) ? $b['createdAt'] : strtotime($b['createdAt']);
                }

                // Sort in descending order (newest first)
                return $timestampB - $timestampA;
            });

            // Cache the processed data
            $dataToCache = [
                'trajectories' => $trajectories,
                'contactCounts' => $contactCounts
            ];
            $cache->saveCache($cacheKey, $dataToCache);
        }
    } else {
        // Use cached data
        $trajectories = $cachedData['trajectories'] ?? [];
        $contactCounts = $cachedData['contactCounts'] ?? [];

        // Sort cached trajectories by createdAt in descending order (newest first)
        uasort($trajectories, function($a, $b) {
            $timestampA = 0;
            $timestampB = 0;

            if (isset($a['createdAt'])) {
                $timestampA = is_numeric($a['createdAt']) ? $a['createdAt'] : strtotime($a['createdAt']);
            }

            if (isset($b['createdAt'])) {
                $timestampB = is_numeric($b['createdAt']) ? $b['createdAt'] : strtotime($b['createdAt']);
            }

            // Sort in descending order (newest first)
            return $timestampB - $timestampA;
        });
    }
} catch (Exception $e) {
    error_log("Error loading manual trajectories: " . $e->getMessage());
}

// Initialize variables
$error = '';
$success = '';
?>
<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="language" content="pt-PT">
    <meta http-equiv="Content-Language" content="pt-PT">
    <title>Relatórios Manuais - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS - EXACT MATCH TO RELATORIOS -->
    <!-- No DataTables CSS needed - using custom styling only -->

    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Tecnicos Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    <!-- DataTables Custom CSS - EXACT MATCH TO RELATORIOS -->
    <link rel="stylesheet" href="../../assets/css/datatables.css?v=<?php echo time(); ?>">
    <!-- Tecnicos Buttons CSS -->
    <link href="../../assets/css/buttons.css" rel="stylesheet">
    <!-- Date Time Pickers CSS -->
    <link href="../../assets/css/date-time-pickers.css" rel="stylesheet">

    <!-- Loading System -->
    <?php include '../../includes/loading.php'; ?>

    <style>
        /* Header Styling - Match criar-kmz exactly */
        .header {
            background-color: #fff;
            padding: 0 1.5rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            right: 0;
            left: 220px;
            z-index: 999;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid #e5e7eb;
        }

        .header.sidebar-collapsed {
            left: 60px !important;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-title {
            color: #374151;
            font-size: 1.25rem;
            font-weight: 500;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-title i {
            color: #0a7ea4;
            font-size: 1.125rem;
        }

        /* Modern Card Container */
        .trajeto-card {
            background: var(--bg-white, white);
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(229, 231, 235, 0.8);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }

        .trajeto-card:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(10, 126, 164, 0.1);
            border-color: rgba(10, 126, 164, 0.2);
        }

        /* Card Header */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            position: relative;
        }

        .card-header h5 {
            color: #1f2937;
            font-weight: 700;
            font-size: 1.125rem;
            margin: 0;
            display: flex;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            letter-spacing: -0.025em;
        }

        .card-header h5 i {
            color: #0a7ea4;
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        .info-badge {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.08) 0%, rgba(10, 126, 164, 0.03) 100%);
            color: #0a7ea4;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.2rem 0.875rem;
            border-radius: 20px;
            border: 1px solid rgba(10, 126, 164, 0.15);
            display: flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 1px 3px 0 rgba(10, 126, 164, 0.1), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(4px);
        }

        .info-badge i {
            color: inherit;
            font-size: 0.7rem;
        }

        .stats-section {
            padding: 0.5rem;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
            border-bottom: 1px solid #e5e7eb;
        }
        
        .stats-flex-container {
            display: flex;
            gap: 0.5rem;
            align-items: stretch;
        }

        .stat-item-wrapper {
            display: flex;
            flex-direction: column;
        }

        .stat-item-wrapper.stat-address-wrapper {
            flex: 1;
            min-width: 0;
        }

        .stat-item {
            display: flex;
            align-items: center;
            padding: 0.35rem 0.75rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 1px solid rgba(229, 231, 235, 0.6);
            height: 32px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: visible;
            backdrop-filter: blur(8px);
        }

        .stat-address-wrapper .stat-item {
            width: 100%;
        }

        .stat-distance-wrapper .stat-item {
            width: auto;
            white-space: nowrap;
            min-width: fit-content;
            padding-right: 0.75rem;
        }

        .stat-points-wrapper .stat-item {
            width: fit-content;
            white-space: nowrap;
        }

        .stat-item i {
            color: #0a7ea4;
            font-size: 0.8rem;
            margin-right: 0.25rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
        }

        .stat-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .stat-value {
            font-weight: 700;
            color: #1f2937;
            margin-left: 0.25rem;
        }

        /* Address stat item */
        .stat-item-address .stat-label {
            font-size: 0.7rem;
            white-space: nowrap;
            font-weight: 600;
        }

        .stat-item-address .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            color: #1f2937;
            flex: 1;
            min-width: 0;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.3;
        }

        /* Compact stat items for distance and points */
        .stat-item-compact .stat-label {
            font-size: 0.7rem;
            white-space: nowrap;
        }

        .stat-item-compact .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            white-space: nowrap;
        }

        /* Special styling for address - allow natural wrapping */
        #startingAddress {
            width: 100% !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
        }
        
        /* Trajectory creation interface specific styles */
        #trajectoryCreationInterface {
            height: calc(100vh - 100px); /* Account for header and content padding */
            overflow: hidden;
        }

        #trajectoryCreationInterface[style*="display: block"] {
            display: flex !important;
            flex-direction: column;
        }



        #trajectoryCreationInterface > div {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        #trajectoryCreationInterface .row {
            flex: 1;
            display: flex;
            margin: 0;
        }

        #trajectoryCreationInterface .col-12 {
            display: flex;
            flex-direction: column;
            padding: 0;
        }

        #trajectoryCreationInterface .trajeto-card {
            margin-bottom: 0; /* Remove bottom margin to prevent overflow */
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        .map-container {
            position: relative;
            flex: 1; /* Take remaining space */
            min-height: 300px;
            background: #f1f5f9;
        }

        .google-map {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Ensure card header and stats section don't grow */
        #trajectoryCreationInterface .card-header,
        #trajectoryCreationInterface .stats-section,
        #trajectoryCreationInterface .card-actions {
            flex-shrink: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
            #trajectoryCreationInterface {
                height: calc(100vh - 90px);
            }
        }

        @media (max-width: 768px) {
            #trajectoryCreationInterface {
                height: calc(100vh - 80px);
            }

            .map-container {
                min-height: 250px;
            }
        }

        @media (max-width: 576px) {
            #trajectoryCreationInterface {
                height: calc(100vh - 70px);
            }

            .map-container {
                min-height: 200px;
            }
        }
        
        .card-actions {
            padding: 1.5rem;
            background: white;
            border-top: 1px solid #e2e8f0;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            align-items: center;
        }

        /* Button group styling for mode switching */
        #trajectoryModeButtons,
        #contactModeButtons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .distance-insufficient {
            color: #dc2626 !important;
            font-weight: 600 !important;
        }
        
        .distance-sufficient {
            color: #16a34a !important;
            font-weight: 600 !important;
        }
        
        .distance-warning {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border: 1px solid #f87171;
            color: #dc2626;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.15rem;
            white-space: nowrap;
            line-height: 1;
            margin-left: 0.4rem;
            vertical-align: text-top;
            height: 18px;
            transform: translateY(1px);
            animation: flashWarning 1.5s infinite;
        }
        
        @keyframes flashWarning {
            0%, 100% { 
                opacity: 1;
                background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
                border-color: #f87171;
            }
            50% { 
                opacity: 0.7;
                background: linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%);
                border-color: #ef4444;
            }
        }
        
        .distance-success {
            background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
            border: 1px solid #86efac;
            color: #16a34a;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.15rem;
            white-space: nowrap;
            line-height: 1;
            margin-left: 0.4rem;
            vertical-align: text-top;
            height: 18px;
            transform: translateY(1px);
        }
        
        /* Modal styles */
        .modal-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            border-bottom: none;
        }

        .modal-header .modal-title,
        .modal-header h5,
        .modal-header h4,
        .modal-header h3 {
            color: white !important;
        }

        .modal-header .btn-close {
            color: white !important;
            opacity: 0.8;
        }

        .modal-header .btn-close:hover {
            opacity: 1;
        }

        /* Modal body styling - Match gestores reference exactly */
        .modal-body {
            background: white !important;
            padding: 1.5rem !important;
        }

        /* Specific modal content styling */
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        /* Fix modal z-index issues - ensure modals appear above header and sidebar */
        .modal {
            z-index: 1060 !important;
        }

        .modal-backdrop {
            z-index: 1055 !important;
        }

        /* Specific z-index for location choice modal to ensure it's on top */
        #locationChoiceModal {
            z-index: 1070 !important;
        }

        #locationChoiceModal .modal-backdrop {
            z-index: 1065 !important;
        }

        /* Specific z-index for time picker modal to ensure it appears above contact details modal */
        #timePickerModal {
            z-index: 1085 !important;
        }

        #timePickerModal .modal-backdrop {
            z-index: 1080 !important;
        }

        /* Specific z-index for date picker modal to ensure it appears above contact details modal */
        #datePickerModal {
            z-index: 1085 !important;
        }

        #datePickerModal .modal-backdrop {
            z-index: 1080 !important;
        }

        /* Location Choice Modal - Match gestores reference exactly */
        .location-choice-modal {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .location-choice-modal .modal-header {
            border-radius: 16px 16px 0 0;
            border-bottom: none;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .location-choice-modal .modal-body {
            border-radius: 0 0 16px 16px;
        }

        /* Step indicator styling */
        .step-indicator {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
        }

        .step-number {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-weight: 600;
        }

        .step-text {
            font-weight: 500;
        }

        /* Location choice button styling */
        .location-choice-btn {
            display: flex !important;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .location-choice-btn i {
            flex-shrink: 0;
        }

        .location-choice-btn span {
            flex: 1;
            text-align: center;
        }

        /* Trajectory setup modal styling */
        .trajectory-setup-modal {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .trajectory-setup-modal .modal-header {

            border-bottom: none;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .trajectory-setup-modal .modal-body {
            border-radius: 0 0 16px 16px;
        }

        /* Contact Modal styling - Match gestores reference exactly */
        .contact-modal .modal-content {
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .contact-modal .modal-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 16px 16px 0 0;
            border: none;
        }

        .contact-modal .modal-body {
            padding: 2rem;
        }

        .contact-modal .modal-footer {
            border-top: 1px solid #dee2e6;
            padding: 1rem 2rem;
        }

        /* Contact Details Modal - Match Weather Options Styling */
        #contactDetailsModal .contact-circumstances .form-check,
        #contactDetailsModal .contact-location .form-check {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            min-height: 52px;
        }

        #contactDetailsModal .contact-circumstances .form-check:hover,
        #contactDetailsModal .contact-location .form-check:hover {
            background: rgba(10, 126, 164, 0.02);
            border-color: rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        #contactDetailsModal .contact-circumstances .form-check:has(input:checked),
        #contactDetailsModal .contact-location .form-check:has(input:checked) {
            background: rgba(10, 126, 164, 0.05);
            border-color: #0a7ea4;
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.15);
        }

        #contactDetailsModal .contact-circumstances .form-check:has(input:checked):hover,
        #contactDetailsModal .contact-location .form-check:has(input:checked):hover {
            background: rgba(10, 126, 164, 0.08);
            transform: translateY(-1px);
        }

        /* Make contact modals wider to match gestores reference */
        #contactDetailsModal .modal-dialog {
            max-width: 1200px !important;
            width: 90vw !important;
        }

        @media (max-width: 1250px) {
            #contactDetailsModal .modal-dialog {
                max-width: 85vw !important;
            }
        }

        @media (max-width: 768px) {
            #contactDetailsModal .modal-dialog {
                max-width: 95vw !important;
                width: 95vw !important;
            }
        }

        /* Ensure all modals are above the header (z-index: 999) */
        #locationSearchModal,
        #locationConfirmedModal,
        #contactConfirmationModal,
        #contactDetailsModal,
        #trajectorySetupModal,
        #helpModal {
            z-index: 1060 !important;
        }

        /* SweetAlert2 z-index configuration - ensure loading modals appear above all Bootstrap modals */
        .swal2-container {
            z-index: 1080 !important; /* Above all Bootstrap modals */
        }

        /* Specific z-index for location loading SweetAlert */
        .swal2-container:has(.swal2-popup) {
            z-index: 1080 !important;
        }

        /* Ensure SweetAlert backdrop doesn't interfere */
        .swal2-backdrop-show {
            z-index: 1079 !important;
        }

        /* Bootstrap modal backdrop z-index - applies to all dynamically created backdrops */
        .modal-backdrop {
            z-index: 1040 !important;
        }

        .modal-backdrop.show {
            z-index: 1040 !important;
        }

        /* Force all modal elements to proper z-index */
        .modal {
            z-index: 1050 !important;
        }

        .modal.show {
            z-index: 1050 !important;
        }

        /* Ensure help modal dialog is properly positioned */
        #helpModal {
            z-index: 1060 !important;
        }

        #helpModal.show {
            z-index: 1060 !important;
        }

        #helpModal .modal-dialog {
            position: relative;
            z-index: 1065 !important;
        }

        /* Additional z-index enforcement for help modal content */
        .help-modal .modal-content {
            position: relative;
            z-index: 1070 !important;
        }

        /* Contact Item Styling - Match tecnicos section design consistency */
        .contact-item {
            background: linear-gradient(135deg, #F0FDF4 0%, #F8FAFC 100%);
            border: 2px solid #D1FAE5 !important;
            border-radius: 12px !important;
            padding: 1rem !important;
            margin-bottom: 1rem !important;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .contact-item:hover {
            background: linear-gradient(135deg, #ECFDF5 0%, #F1F5F9 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            border-color: #BBF7D0 !important;
        }

        .contact-item h6 {
            color: #10b981 !important;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .contact-item h6 i {
            color: #10b981;
            background: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            border: 2px solid #10b981;
        }

        .contact-item .text-muted {
            color: #6b7280 !important;
            font-size: 0.875rem;
        }

        .contact-item .form-label {
            color: #374151;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .contact-item .form-control {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .contact-item .form-control:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        /* Make trajectory setup modal wider */
        #trajectorySetupModal .modal-dialog {
            max-width: 600px !important;
            width: 90vw !important;
        }

        /* Form control styling - Match gestores reference exactly */
        .form-control {
            border: 1px solid #d1d5db !important;
            border-radius: 8px !important;
            padding: 0.75rem !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            background: white !important;
            transition: all 0.2s ease !important;
        }

        .form-control:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25) !important;
        }

        .form-select {
            border: 1px solid #d1d5db !important;
            border-radius: 8px !important;
            padding: 0.75rem !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            background: white !important;
            transition: all 0.2s ease !important;
        }

        .form-select:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25) !important;
        }

        /* Input group styling */
        .input-group .form-control {
            border-radius: 8px 0 0 8px !important;
        }

        .input-group .btn {
            border-radius: 0 8px 8px 0 !important;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%) !important;
            border: none !important;
            color: white !important;
        }

        .input-group .btn:hover {
            background: linear-gradient(135deg, #0891b2 0%, #0a7ea4 100%) !important;
        }

        /* Weather Options - Match gestores reference exactly */
        .weather-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-top: 0.75rem;
        }

        .weather-options .form-check {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            min-height: 52px;
            margin-bottom: 0;
        }

        .weather-options .form-check:hover {
            background: rgba(10, 126, 164, 0.02);
            border-color: rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .weather-options .form-check-input {
            position: relative;
            margin: 0 0.75rem 0 0;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .weather-options .form-check-input:checked {
            background-color: #0a7ea4 !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }

        .weather-options .form-check-input:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }

        .weather-options .form-check-input:checked + .form-check-label {
            color: #0a7ea4 !important;
            font-weight: 600 !important;
        }

        .weather-options .form-check:has(.form-check-input:checked) {
            background: rgba(10, 126, 164, 0.05) !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1) !important;
            transform: translateY(-1px) !important;
        }

        .weather-options .form-check-label {
            cursor: pointer;
            display: flex;
            align-items: center;
            margin-bottom: 0;
            font-size: 0.9rem;
            margin-left: 0;
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .trajectory-setup-modal {
            border-radius: 12px;
            overflow: hidden;
        }
        
        .step-indicator {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .step-number {
            font-weight: 700;
            margin-right: 0.25rem;
        }
        
        .form-check-input:checked {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
        }
        
        .form-check-input:focus {
            border-color: #0891b2;
            box-shadow: 0 0 0 0.25rem rgba(10, 126, 164, 0.25);
        }
        
        .btn-outline-primary:hover {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
        }
        
        #saveTrajetoBtn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
        }

        #addContactBtn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
        }

        #addContactBtn.active {
            background-color: #0a7ea4 !important;
            border-color: #0a7ea4 !important;
            color: white !important;
        }

        .contact-mode-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #0a7ea4;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
            display: none;
        }
        
        .photos-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
            min-height: 20px;
        }
        
        .photo-preview-item {
            position: relative;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .photo-preview-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }
        
        .photo-preview-item img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
        }
        
        .photo-remove-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .photo-remove-btn:hover {
            background: #b91c1c;
        }
        
        .dropzone-container {
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }
        
        .dropzone-container:hover {
            border-color: #0891b2 !important;
            background-color: rgba(10, 126, 164, 0.08) !important;
        }
        
        .dropzone-container.dragover {
            border-color: #0891b2 !important;
            background-color: rgba(10, 126, 164, 0.1) !important;
            transform: scale(1.02);
        }
        
        .dropzone-icon {
            font-size: 3rem;
            color: #94a3b8;
            margin-bottom: 1rem;
        }
        
        .dropzone-text {
            color: #64748b;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .dropzone-subtext {
            color: #94a3b8;
            font-size: 0.875rem;
        }

        /* Statistics Grid Styling - Match GPS reports page exactly */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.2rem;
        }

        @media (max-width: 768px) {
            .stats-grid {
                display: flex;
                overflow-x: auto;
                gap: 1rem;
                padding-bottom: 0.5rem;
                margin: 0 0.5rem 1.5rem 0.5rem;
                padding: 0 0.25rem 0.5rem 0.25rem;
            }
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Responsive stat card adjustments */
        @media (max-width: 768px) {
            .stat-card {
                min-width: 280px;
                flex-shrink: 0;
                padding: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .stat-card {
                min-width: 260px;
                padding: 1rem;
            }

            .stat-card .icon {
                width: 36px;
                height: 36px;
            }

            .stat-card .icon i {
                font-size: 16px;
            }

            .stat-card .title {
                font-size: 0.8rem;
            }

            .stat-card .value {
                font-size: 1.25rem;
            }
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-card .icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-card .icon i {
            font-size: 18px;
            color: white;
        }

        .stat-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-card .title {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 600;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .stat-card .value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #374151;
            margin: 0;
        }

        .bg-blue {
            background-color: #3B82F6;
        }

        .bg-purple {
            background-color: #8B5CF6;
        }

        .bg-green {
            background-color: #10B981;
        }

        .bg-orange {
            background-color: #F59E0B;
        }

        /* Trajectory Options List Styling */
        .reports-table-card {
            background: white;
            border-radius: 12px;
            padding: 0;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        /* Ensure table header has proper flex layout for button positioning */
        .reports-table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .reports-table-header h2 {
            margin: 0;
            display: flex;
            align-items: center;
        }

        .reports-table-body {
            padding: 0;
        }

        .method-icon-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            margin-right: 0.75rem;
            font-size: 0.875rem;
        }

        .method-icon-badge.manual {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
        }

        /* Contact Count Button Styling - exact match to relatorios */
        .contact-count-btn {
            width: auto !important;
            min-width: 70px !important;
            padding: 8px 14px !important;
            font-size: 12px !important;
            background-color: #F0FDF4 !important;
            border: 1px solid #DCFCE7 !important;
            color: #166534 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 6px !important;
            transition: all 0.2s !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            border-radius: 6px !important;
            text-decoration: none !important;
        }

        .contact-count-btn:hover:not([disabled]) {
            background-color: #DCFCE7 !important;
            border-color: #BBF7D0 !important;
            transform: translateY(-1px) !important;
            color: #166534 !important;
        }

        .contact-count-btn[disabled] {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            background-color: #F3F4F6 !important;
            border-color: #E5E7EB !important;
            color: #6B7280 !important;
        }

        .contact-count-btn i {
            margin-right: 0 !important;
            color: inherit !important;
            font-size: 13px !important;
        }

        #trajetosTable {
            margin-bottom: 0 !important;
            width: 100% !important;
            table-layout: fixed !important;
            max-width: 100% !important;
        }

        /* Force exact DataTable styling to match relatorios */

        /* DataTable wrapper and controls - exact match */
        #trajetosTable_wrapper .dataTables_length select,
        #trajetosTable_wrapper .dataTables_filter input {
            border: 1px solid #E5E7EB !important;
            border-radius: 6px !important;
            padding: 8px 12px !important;
            font-size: 14px !important;
            color: #374151 !important;
            background-color: #F9FAFB !important;
            margin-left: 8px !important;
            height: auto !important;
            line-height: 1.5 !important;
        }

        #trajetosTable_wrapper .dataTables_length select {
            min-width: 80px !important;
            padding-right: 40px !important;
            /* Add dropdown arrow icon - EXACT MATCH TO GPS REPORTS */
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
            background-position: right 8px center !important;
            background-repeat: no-repeat !important;
            background-size: 16px 16px !important;
            appearance: none !important;
        }

        #trajetosTable_wrapper .dataTables_filter input {
            min-width: 250px !important;
            /* Add search icon - EXACT MATCH TO GPS REPORTS */
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3e%3c/svg%3e") !important;
            background-position: left 12px center !important;
            background-repeat: no-repeat !important;
            background-size: 16px 16px !important;
            padding-left: 40px !important;
        }

        #trajetosTable_wrapper .dataTables_length label,
        #trajetosTable_wrapper .dataTables_filter label {
            font-size: 14px !important;
            color: #374151 !important;
            font-weight: 500 !important;
            display: flex !important;
            align-items: center !important;
            margin: 0 !important;
        }

        /* Table headers - exact match to relatorios with reduced height */
        #trajetosTable th {
            background-color: #F8F9FB !important;
            padding: 12px 20px !important;
            font-weight: 600 !important;
            color: #6B7280 !important;
            text-transform: uppercase !important;
            font-size: 12px !important;
            letter-spacing: 0.05em !important;
            border-bottom: 1px solid #E5E7EB !important;
            text-align: center !important;
            border-top: none !important;
            height: auto !important;
            line-height: 1.1 !important;
        }

        /* Table cells - exact match to relatorios */
        #trajetosTable td {
            padding: 16px 20px !important;
            vertical-align: middle !important;
            border-bottom: 1px solid #F3F4F6 !important;
            color: #374151 !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            text-align: center !important;
            height: auto !important;
        }

        #trajetosTable tbody tr {
            transition: background-color 0.2s ease !important;
        }

        #trajetosTable tbody tr:hover {
            background-color: #F9FAFB !important;
        }
        
        /* Make buttons bigger and more usable with better icon visibility - EXACT MATCH TO REGULAR REPORTS */
        #trajetosTable .btn {
            padding: 8px 12px !important;
            font-size: 16px !important;
            line-height: 1.4 !important;
            border-radius: 6px !important;
            min-width: auto !important;
        }
        
        #trajetosTable .btn i {
            font-size: 16px !important;
        }
        
        #trajetosTable .btn-sm {
            padding: 8px 12px !important;
            font-size: 16px !important;
        }
        
        /* PDF button styling - Better background with white icon */
        #trajetosTable .btn-outline-secondary {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: white !important;
        }
        
        #trajetosTable .btn-outline-secondary:hover {
            background-color: #5a6268 !important;
            border-color: #5a6268 !important;
            color: white !important;
        }
        
        /* Make PDF icon white for better contrast on gray background */
        #trajetosTable .btn .fa-file-pdf {
            font-size: 17px !important;
            font-weight: bold !important;
            color: white !important;
        }
        
        #trajetosTable .btn-outline-secondary .fa-file-pdf {
            color: white !important;
        }
        
        #trajetosTable .btn-outline-secondary:hover .fa-file-pdf {
            color: white !important;
        }
        
        /* Make action column more compact */
        #trajetosTable .d-flex.gap-2 {
            gap: 2px !important;
            justify-content: center;
            flex-wrap: nowrap;
        }
        
        /* Ensure action column has enough space for bigger buttons */
        #trajetosTable td:nth-child(7) {
            min-width: 140px !important;
            max-width: 160px !important;
        }
        
        /* Custom tooltip styling - EXACT MATCH TO REGULAR REPORTS */
        .custom-tooltip {
            position: absolute;
            background: linear-gradient(135deg, #0a7ea4 0%, #096d8c 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
            z-index: 10001;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease-in-out;
            box-shadow: 0 6px 16px rgba(10, 126, 164, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }
        
        .custom-tooltip.show {
            opacity: 1 !important;
            display: block !important;
        }
        
        .custom-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -6px;
            border: 6px solid transparent;
            border-top-color: #0a7ea4;
        }

        /* First column (trajectory name) should be left-aligned */
        #trajetosTable th:first-child,
        #trajetosTable td:first-child {
            text-align: left !important;
        }

        /* DataTable wrapper spacing - exact match to relatorios */
        #trajetosTable_wrapper {
            padding: 0 !important;
            margin-top: 0 !important;
        }

        #trajetosTable_wrapper .row {
            display: flex !important;
            align-items: center !important;
            margin-bottom: 1rem !important;
        }

        #trajetosTable_wrapper .col-sm-6 {
            flex: 0 0 50% !important;
            display: flex !important;
            align-items: center !important;
        }

        #trajetosTable_wrapper .col-sm-6:last-child {
            justify-content: flex-end !important;
        }

        #trajetosTable_wrapper .dataTables_length {
            margin-right: 2rem !important;
        }

        #trajetosTable_wrapper .dataTables_length,
        #trajetosTable_wrapper .dataTables_filter,
        #trajetosTable_wrapper .dataTables_info,
        #trajetosTable_wrapper .dataTables_paginate {
            padding-top: 0.75rem !important;
        }

        /* Column width specifications - exact match to datatables.css */
        #trajetosTable th:nth-child(1), /* Nome do Trajeto */
        #trajetosTable td:nth-child(1) {
            width: 25% !important;
            min-width: 120px !important;
            text-align: left !important;
        }

        #trajetosTable th:nth-child(2), /* Data de Criação */
        #trajetosTable td:nth-child(2) {
            width: 15% !important;
            min-width: 120px !important;
            text-align: center !important;
        }

        #trajetosTable th:nth-child(3), /* Pontos */
        #trajetosTable td:nth-child(3) {
            width: 10% !important;
            min-width: 80px !important;
            text-align: center !important;
        }

        #trajetosTable th:nth-child(4), /* Distância */
        #trajetosTable td:nth-child(4) {
            width: 10% !important;
            min-width: 80px !important;
            text-align: center !important;
        }

        #trajetosTable th:nth-child(5), /* Contactos */
        #trajetosTable td:nth-child(5) {
            width: 8% !important;
            min-width: 70px !important;
            text-align: center !important;
        }

        #trajetosTable th:nth-child(6), /* Observadores */
        #trajetosTable td:nth-child(6) {
            width: 8% !important;
            min-width: 70px !important;
            text-align: center !important;
        }

        #trajetosTable th:nth-child(7), /* Ações */
        #trajetosTable td:nth-child(7) {
            width: 24% !important;
            min-width: 160px !important;
            text-align: center !important;
        }

        /* Force pagination centering - exact match to datatables.css */
        #trajetosTable_wrapper .row:last-child {
            display: block !important;
            text-align: center !important;
            width: 100% !important;
        }

        #trajetosTable_wrapper .row:last-child .col-sm-5 {
            float: left !important;
            text-align: left !important;
            width: auto !important;
        }

        #trajetosTable_wrapper .row:last-child .col-sm-7 {
            float: none !important;
            text-align: center !important;
            width: 100% !important;
            clear: both !important;
            margin-top: 1rem !important;
        }

        #trajetosTable_paginate {
            text-align: center !important;
            margin: 0 auto !important;
            display: block !important;
            width: 100% !important;
        }

        #trajetosTable_paginate .pagination {
            justify-content: center !important;
            margin: 0 auto !important;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* Additional styling for trajectory options */
        .content {
            padding: 2rem;
        }

        /* Breadcrumb styling */
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #6b7280;
            font-weight: 600;
        }

        .breadcrumb-item.active {
            color: #374151;
            font-weight: 500;
        }

        /* Let datatables.css handle button styling */

        .btn-primary {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border: 1px solid #0a7ea4;
            box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0891b2 0%, #0a7ea4 100%);
            border-color: #0891b2;
            box-shadow: 0 4px 8px rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
            border-color: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
            border-color: #4b5563;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content {
                padding: 1rem;
            }

            .reports-table-header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            #trajectoryOptionsTable td {
                padding: 1rem;
            }

            .method-icon-badge {
                width: 28px;
                height: 28px;
                font-size: 0.75rem;
            }
        }

        /* Loading Spinner */
        .loading-spinner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #0a7ea4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast Container */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }

        /* Help Modal Styling */
        .help-modal .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .help-modal .modal-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            border-bottom: none;
            padding: 1.5rem 2rem;
        }

        .help-modal .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .help-modal .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .help-modal .modal-close:hover {
            opacity: 1;
        }

        .help-modal .modal-body {
            padding: 1.5rem 2rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .help-modal .modal-footer {
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            justify-content: center;
        }

        .help-modal .modal-footer .btn-primary {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border: 1px solid #0a7ea4;
            box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .help-modal .modal-footer .btn-primary:hover {
            background: linear-gradient(135deg, #0891b2 0%, #0a7ea4 100%);
            border-color: #0891b2;
            box-shadow: 0 4px 8px rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
        }

        /* Help Sections */
        .help-section {
            margin-bottom: 2rem;
        }

        .help-section:last-child {
            margin-bottom: 0;
        }

        .help-section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .help-section-header i {
            color: #0a7ea4;
            font-size: 1.125rem;
        }

        .help-section-header h6 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
        }

        .help-description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* Help Steps */
        .help-steps {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .help-step {
            display: flex;
            gap: 1rem;
            align-items: flex-start;
        }

        .step-number {
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .step-content {
            flex: 1;
        }

        .step-content h6 {
            margin: 0 0 0.5rem 0;
            font-size: 0.95rem;
            font-weight: 600;
            color: #374151;
        }

        .step-content p {
            margin: 0;
            color: #6b7280;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        /* Help Controls */
        .help-controls {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .control-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #0a7ea4;
        }

        .control-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .control-icon i {
            color: #0a7ea4;
            font-size: 1rem;
        }

        .control-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .control-content strong {
            color: #374151;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .control-content span {
            color: #6b7280;
            font-size: 0.85rem;
        }

        /* Help Tips */
        .help-tips {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .tip-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.75rem;
            background: #f0f9ff;
            border-radius: 8px;
            border: 1px solid #e0f2fe;
        }

        .tip-item i {
            color: #059669;
            font-size: 1rem;
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        .tip-item span {
            color: #374151;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .help-modal .modal-dialog {
                margin: 1rem;
                max-width: calc(100% - 2rem);
            }

            .help-modal .modal-body {
                padding: 1rem 1.5rem;
                max-height: 55vh;
            }

            .help-modal .modal-header {
                padding: 1rem 1.5rem;
            }

            .help-modal .modal-footer {
                padding: 1rem 1.5rem;
            }

            .help-step {
                gap: 0.75rem;
            }

            .step-number {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .control-item {
                padding: 0.75rem;
                gap: 0.75rem;
            }

            .control-icon {
                width: 36px;
                height: 36px;
            }

            .help-section-header {
                gap: 0.5rem;
            }

            .help-section-header h6 {
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-draw-polygon"></i>
                Relatórios Manuais
            </h1>
        </div>
        <div class="header-actions">
            <button type="button" class="btn btn-help" onclick="showRelatorioHelp()" id="helpButton">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Contact Mode Indicator -->
    <div class="contact-mode-indicator" id="contactModeIndicator">
        <i class="fas fa-dove me-2"></i>
        Modo Contacto Ativo - Clique no mapa para adicionar contactos
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon bg-blue">
                    <i class="fas fa-draw-polygon"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total Trajetos Manuais</p>
                    <p class="value"><?php echo number_format($stats['manualTrajectories'] ?? 0); ?></p>
                </div>
            </div>

            <div class="stat-card">
                <div class="icon bg-green">
                    <i class="fas fa-dove"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total Contactos Manuais</p>
                    <p class="value"><?php echo number_format($stats['manualContacts'] ?? 0); ?></p>
                </div>
            </div>

            <div class="stat-card">
                <div class="icon bg-purple">
                    <i class="fas fa-map-pin"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total Pontos</p>
                    <p class="value"><?php
                        // Calculate total points from manual trajectories
                        $totalPoints = 0;
                        if (isset($stats['manualTrajectoriesData']) && is_array($stats['manualTrajectoriesData'])) {
                            foreach ($stats['manualTrajectoriesData'] as $trajectory) {
                                if (isset($trajectory['pointsCount'])) {
                                    $totalPoints += (int)$trajectory['pointsCount'];
                                } elseif (isset($trajectory['coordinates']) && is_array($trajectory['coordinates'])) {
                                    $totalPoints += count($trajectory['coordinates']);
                                }
                            }
                        }
                        echo number_format($totalPoints);
                    ?></p>
                </div>
            </div>

            <div class="stat-card">
                <div class="icon bg-orange">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total Observadores</p>
                    <p class="value"><?php
                        // Calculate total observers from manual trajectories
                        $totalObservers = 0;
                        if (isset($stats['manualTrajectoriesData']) && is_array($stats['manualTrajectoriesData'])) {
                            foreach ($stats['manualTrajectoriesData'] as $trajectory) {
                                if (isset($trajectory['numberOfObservers'])) {
                                    $totalObservers += (int)$trajectory['numberOfObservers'];
                                }
                            }
                        }
                        echo number_format($totalObservers);
                    ?></p>
                </div>
            </div>
        </div>

        <!-- Trajectories Table -->
        <div class="reports-table-card">
            <div class="reports-table-header">
                <h2><i class="fas fa-draw-polygon" style="color: var(--primary-color); margin-right: 0.5rem;"></i> Lista de Relatórios Manuais</h2>
                <button class="btn btn-primary" onclick="startManualTrajectoryCreation()">
                    <i class="fas fa-plus"></i> Adicionar Relatório
                </button>
            </div>
            <div class="reports-table-body">
                <div class="table-responsive">
                    <table id="trajetosTable" class="table">
                        <thead>
                            <tr>
                                <th>Nome do Relatório</th>
                                <th>Data de Criação</th>
                                <th>Pontos</th>
                                <th>Distância</th>
                                <th>Contactos</th>
                                <th>Observadores</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($trajectories)): ?>
                                <tr>
                                    <td class="text-center text-muted">
                                        <i class="fas fa-route"></i> Nenhum relatório criado ainda
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($trajectories as $id => $trajectory): ?>
                                    <tr>
                                        <td>
                                            <?php echo htmlspecialchars($trajectory['name'] ?? 'Relatório sem nome'); ?>
                                        </td>
                                        <td>
                                            <?php
                                            $date = $trajectory['createdAt'] ?? $trajectory['date'] ?? '';
                                            if ($date) {
                                                try {
                                                    // Try different date formats
                                                    $dateObj = DateTime::createFromFormat('c', $date); // ISO 8601
                                                    if (!$dateObj) {
                                                        $dateObj = DateTime::createFromFormat('Y-m-d\TH:i:s.uP', $date); // With microseconds
                                                    }
                                                    if (!$dateObj) {
                                                        $dateObj = DateTime::createFromFormat('Y-m-d\TH:i:sP', $date); // Without microseconds
                                                    }
                                                    if (!$dateObj) {
                                                        $dateObj = new DateTime($date); // Let PHP try to parse it
                                                    }
                                                    echo $dateObj->format('d/m/Y H:i');
                                                } catch (Exception $e) {
                                                    echo '-';
                                                }
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo intval($trajectory['pointsCount'] ?? count($trajectory['coordinates'] ?? [])); ?></td>
                                        <td><?php echo htmlspecialchars($trajectory['distance'] ?? '0 km'); ?></td>
                                        <td data-order="<?php echo intval($trajectory['contactCount'] ?? 0); ?>">
                                            <button class="contact-count-btn" <?php echo ($trajectory['contactCount'] ?? 0) == 0 ? 'disabled' : ''; ?>>
                                                <i class="fas fa-dove"></i>
                                                <?php echo intval($trajectory['contactCount'] ?? 0); ?>
                                            </button>
                                        </td>
                                        <td><?php echo intval($trajectory['numberOfObservers'] ?? 1); ?></td>
                                        <td class="text-center">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <a href="view-trajectory.php?id=<?php echo htmlspecialchars($id); ?>" class="btn btn-sm btn-primary" data-tooltip="Ver relatório">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="generate-pdf.php?id=<?php echo htmlspecialchars($id); ?>&pdf=false" target="_blank" class="btn btn-sm btn-outline-secondary" data-tooltip="Gerar PDF">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteTrajectory('<?php echo htmlspecialchars($id); ?>')" data-tooltip="Eliminar relatório">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading Spinner (hidden by default) -->
        <div class="loading-spinner" id="loadingSpinner" style="display: none;">
            <div class="spinner"></div>
            <p style="margin-top: 1rem; color: #6b7280;">Processando relatório...</p>
        </div>

        <!-- Toast Container -->
        <div class="toast-container" id="toastContainer"></div>

        <!-- Hidden: Original Trajectory Creation Interface -->
        <div id="trajectoryCreationInterface" style="display: none;">
            <div>

                <!-- Trajeto Creation Form -->
                <div class="row">
                    <!-- Combined Card - Full Width -->
                    <div class="col-12">
                        <div class="trajeto-card">
                            <!-- Stats Header -->
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-route me-2"></i>
                                    Informações do Relatório
                                </h5>
                                <span class="info-badge">
                                    <i class="fas fa-mouse-pointer me-1"></i>
                                    Clique no mapa para criar pontos do trajeto
                                </span>
                            </div>
                        
                        <!-- Stats Section -->
                        <div class="stats-section">
                            <div class="stats-flex-container">
                                <div class="stat-item-wrapper stat-address-wrapper">
                                    <div class="stat-item stat-item-address">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <span class="stat-label">INÍCIO:</span>
                                        <span class="stat-value" id="startingAddress">Não definido</span>
                                    </div>
                                </div>
                                <div class="stat-item-wrapper">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-route me-1"></i>
                                        <span class="stat-label">DISTÂNCIA:</span>
                                        <span class="stat-value" id="routeDistance">0 km</span>

                                    </div>
                                </div>
                                <div class="stat-item-wrapper">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-map-pin me-1"></i>
                                        <span class="stat-label">PONTOS:</span>
                                        <span class="stat-value" id="routePoints">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Map with Floating Controls -->
                        <div class="map-container">
                            <div id="map" class="google-map"></div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="card-actions">
                            <form id="trajetoForm">
                                <input type="hidden" id="routeCoordinates" value="">
                                
                                <div class="form-actions">
                                    <!-- Normal trajectory mode buttons -->
                                    <div id="trajectoryModeButtons">
                                        <button type="button" class="btn btn-outline-secondary" onclick="cancelCreate()">
                                            <i class="fas fa-times me-1"></i>
                                            Cancelar
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="clearRoute()">
                                            <i class="fas fa-eraser me-1"></i>
                                            Limpar Trajeto
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="undoLastPoint()" id="undoBtn" disabled>
                                            <i class="fas fa-undo me-1"></i>
                                            Desfazer Último
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="toggleContactMode()" id="addContactBtn" disabled>
                                            <i class="fas fa-dove me-1"></i>
                                            <span id="contactBtnText">Adicionar Contacto</span>
                                            <span class="badge bg-primary ms-2" id="contactCountBadge" style="display: none;">0</span>
                                        </button>
                                        <button type="submit" class="btn btn-success" id="saveTrajetoBtn" disabled>
                                            <i class="fas fa-save me-1"></i>
                                            Guardar Relatório
                                        </button>
                                    </div>

                                    <!-- Contact mode buttons (hidden by default) -->
                                    <div id="contactModeButtons" style="display: none;">
                                        <button type="button" class="btn btn-outline-secondary" onclick="toggleContactMode()">
                                            <i class="fas fa-route me-1"></i>
                                            Modo Trajeto
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteLastContact()" id="deleteLastContactBtn" disabled>
                                            <i class="fas fa-trash me-1"></i>
                                            Apagar Último
                                        </button>
                                        <!-- Keep Save Trajectory button visible in contact mode -->
                                        <button type="button" class="btn btn-success" id="saveTrajetoContactModeBtn" disabled>
                                            <i class="fas fa-save me-1"></i>
                                            Guardar Relatório
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            </div>
        </div>
        <!-- End Hidden: Original Trajectory Creation Interface -->

    </div>

    <!-- Location Choice Modal -->
    <div class="modal fade" id="locationChoiceModal" tabindex="-1" aria-labelledby="locationChoiceModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content location-choice-modal">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationChoiceModalLabel">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Definir Localização Inicial
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-location-arrow text-primary" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                    <h6 class="mb-3">Como pretende definir a localização inicial do relatório?</h6>
                    <div class="mx-auto mb-4" style="max-width: 500px;">
                        <p class="text-muted small">
                            Escolha uma das opções abaixo para definir onde pretende começar o relatório.
                        </p>
                    </div>

                    <div class="d-grid gap-3 mx-auto" style="max-width: 350px;">
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="useCurrentLocation()">
                            <i class="fas fa-crosshairs me-2"></i>
                            <span>Usar Localização Atual</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="searchLocation()">
                            <i class="fas fa-search me-2"></i>
                            <span>Pesquisar Localização</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="useManualLocation()">
                            <i class="fas fa-map me-2"></i>
                            <span>Definir Manualmente no Mapa</span>
                        </button>
                    </div>

                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Pode sempre ajustar a localização clicando no mapa
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Search Modal -->
    <div class="modal fade" id="locationSearchModal" tabindex="-1" aria-labelledby="locationSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content location-choice-modal">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationSearchModalLabel">
                        <i class="fas fa-search me-2"></i>
                        Pesquisar Localização
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body py-4">
                    <div class="mb-3">
                        <label for="locationSearchInput" class="form-label">Digite o nome da cidade, vila ou localização:</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                            </span>
                            <input type="text" class="form-control form-control-lg" id="locationSearchInput"
                                   placeholder="Ex: Lisboa, Porto, Coimbra..."
                                   onkeypress="handleLocationSearchEnter(event)">
                            <button class="btn btn-primary" type="button" onclick="performLocationSearch()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Pode pesquisar por cidade, código postal ou morada específica
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="performLocationSearch()">
                            <i class="fas fa-search me-2"></i>
                            <span>Pesquisar e Centrar Mapa</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg location-choice-btn" data-bs-dismiss="modal" onclick="showLocationChoiceModal()">
                            <i class="fas fa-arrow-left me-2"></i>
                            <span>Voltar às Opções</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Confirmed Modal -->
    <div class="modal fade" id="locationConfirmedModal" tabindex="-1" aria-labelledby="locationConfirmedModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false" style="z-index: 1060;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content trajectory-setup-modal">
                <div class="modal-header bg-primary text-white" style="flex-shrink: 0;">
                    <h5 class="modal-title" id="locationConfirmedModalLabel">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Localização Definida
                    </h5>
                </div>
                <div class="modal-body text-center" style="padding: 2rem;">
                    <div class="mb-3">
                        <div class="icon-circle mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                    </div>
                    <h6 class="mb-3" style="color: #1f2937; font-weight: 600;">Localização definida!</h6>
                    <p class="text-muted mb-0">O mapa foi centrado na sua localização atual.</p>
                </div>
                <div class="modal-footer" style="flex-shrink: 0; border-top: 1px solid #dee2e6; justify-content: center;">
                    <button type="button" class="btn btn-primary" onclick="closeLocationConfirmedModal()">
                        <i class="fas fa-arrow-right me-1"></i>
                        Continuar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Confirmation Modal -->
    <div class="modal fade contact-modal" id="contactConfirmationModal" tabindex="-1" aria-labelledby="contactConfirmationModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white">
                    <h5 class="modal-title" id="contactConfirmationModalLabel">
                        <i class="fas fa-dove me-2"></i>
                        Adicionar Contacto
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-map-marker-alt mb-3" style="font-size: 2rem; color: #10b981;"></i>
                        <h6 class="mb-3">Confirmar Localização do Contacto</h6>
                        <p class="text-muted mb-0">Deseja adicionar um contacto com rola-brava nesta localização?</p>
                        <small class="text-muted" id="contactCoordinates"></small>
                    </div>
                </div>
                <div class="modal-footer justify-content-center gap-2">
                    <button type="button" class="btn btn-secondary" onclick="cancelContactPlacement()">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmContactPlacement()">
                        <i class="fas fa-check me-1"></i>
                        Sim, Adicionar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Details Modal -->
    <div class="modal fade contact-modal" id="contactDetailsModal" tabindex="-1" aria-labelledby="contactDetailsModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content trajectory-setup-modal" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                <div class="modal-header" style="border-radius: 16px 16px 0 0; border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; display: flex; justify-content: space-between; align-items: center; padding: 1rem 1.5rem; position: relative; margin: 0;">
                    <div class="d-flex align-items-center">
                        <div class="step-indicator me-3" style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3);">
                            <i class="fas fa-dove" style="color: white; font-size: 1.2rem;"></i>
                        </div>
                        <h5 class="modal-title mb-0" style="color: white; font-weight: 600; font-size: 1.25rem;">Detalhes do contacto com rola-brava</h5>
                    </div>
                </div>
                <div class="modal-body" style="padding: 1.5rem; background: white; max-height: 75vh; overflow-y: auto;">
                    <form id="contactDetailsForm">
                        <!-- Time and Coordinates -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="contactTime" class="form-label" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                                    <i class="fas fa-clock me-2" style="color: #0a7ea4;"></i>
                                    Hora
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="contactTime" placeholder="Selecione a hora" readonly required style="border: 2px solid #e5e7eb; border-radius: 8px 0 0 8px; font-weight: 500;">
                                    <button class="btn" type="button" id="contactTimePickerBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; border-radius: 0 8px 8px 0;">
                                        <i class="fas fa-clock"></i>
                                    </button>
                                </div>

                            </div>
                            <div class="col-md-6">
                                <label class="form-label" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                                    <i class="fas fa-map-marker-alt me-2" style="color: #0a7ea4;"></i>
                                    Coordenadas
                                </label>
                                <input type="text" class="form-control" id="contactCoords" readonly style="background-color: #f8fafc; border: 2px solid #e5e7eb; border-radius: 8px; font-family: monospace; font-weight: 500; color: #374151;">
                            </div>
                        </div>

                        <!-- Contact Circumstances Section -->
                        <div class="form-section mb-3">
                            <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.75rem; display: flex; align-items: center; font-size: 1rem;">
                                <i class="fas fa-eye me-2" style="color: #0a7ea4;"></i>
                                Circunstância do contacto
                            </label>
                            <div class="contact-circumstances" style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-top: 0.75rem;">
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_singing" name="contact_circumstances" value="adultSinging" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_singing" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-music me-2" style="color: #f59e0b;"></i>
                                        Rola adulta a cantar
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_display" name="contact_circumstances" value="adultDisplay" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_display" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-heart me-2" style="color: #ef4444;"></i>
                                        Adulto em display
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_flying" name="contact_circumstances" value="flying" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_flying" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-dove me-2" style="color: #0a7ea4;"></i>
                                        Rola em voo
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_empty_nest" name="contact_circumstances" value="emptyNest" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_empty_nest" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-circle me-2" style="color: #6b7280;"></i>
                                        Ninho vazio
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_perched" name="contact_circumstances" value="adultPerched" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_perched" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-feather me-2" style="color: #10b981;"></i>
                                        Adulto pousado
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_occupied_nest" name="contact_circumstances" value="occupiedNest" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_occupied_nest" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-egg me-2" style="color: #06b6d4;"></i>
                                        Ninho ocupado
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_groups" name="contact_circumstances" value="groups" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_groups" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-dove me-2" style="color: #8b5cf6;"></i>
                                        Grupos de Rolas
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="circumstance_other" name="contact_circumstances" value="other" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_other" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-question-circle me-2" style="color: #6b7280;"></i>
                                        Outro. Qual?
                                    </label>
                                </div>
                            </div>
                            <!-- Number input for "Grupos de Rolas" -->
                            <div id="circumstanceGroupsInput" style="display: none; margin-top: 0.75rem;">
                                <input type="number" class="form-control" id="circumstanceGroupsNumber" placeholder="Número de Indivíduos..." min="1" max="999" style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; font-size: 0.9rem;" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                            </div>
                            <!-- Text input for "Outro. Qual?" in circumstances -->
                            <div id="circumstanceOtherInput" style="display: none; margin-top: 0.75rem;">
                                <input type="text" class="form-control" id="circumstanceOtherText" placeholder="Especifique a circunstância..." style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; font-size: 0.9rem;">
                            </div>
                        </div>

                        <!-- Location Details Section -->
                        <div class="form-section mb-3">
                            <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.75rem; display: flex; align-items: center; font-size: 1rem;">
                                <i class="fas fa-tree me-2" style="color: #0a7ea4;"></i>
                                Local do contacto
                            </label>
                            <div class="contact-location" style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-top: 0.75rem;">
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="location_tree" name="contact_location" value="tree" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_tree" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-tree me-2" style="color: #10b981;"></i>
                                        Árvore
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="location_water" name="contact_location" value="waterPoint" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_water" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-tint me-2" style="color: #0a7ea4;"></i>
                                        Ponto de água
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="location_shrub" name="contact_location" value="shrub" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_shrub" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-seedling me-2" style="color: #10b981;"></i>
                                        Arbusto
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="location_clearing" name="contact_location" value="clearing" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_clearing" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-circle me-2" style="color: #f59e0b;"></i>
                                        Clareira
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                    <input class="form-check-input" type="radio" id="location_other" name="contact_location" value="other" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_other" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-question-circle me-2" style="color: #6b7280;"></i>
                                        Outro. Qual?
                                    </label>
                                </div>
                            </div>
                            <!-- Text input for "Outro. Qual?" in location -->
                            <div id="locationOtherInput" style="display: none; margin-top: 0.75rem;">
                                <input type="text" class="form-control" id="locationOtherText" placeholder="Especifique o local..." style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; font-size: 0.9rem;">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: white; display: flex; gap: 1rem; justify-content: center;">
                    <button type="button" class="btn" onclick="cancelContactDetails()" style="background-color: #6b7280; border-color: #6b7280; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn" onclick="saveContactDetails()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-save"></i>
                        Guardar Contacto
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Trajectory Setup Modal -->
    <div class="modal fade" id="trajectorySetupModal" tabindex="-1" aria-labelledby="trajectorySetupModalLabel" aria-hidden="true" data-bs-backdrop="false" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content trajectory-setup-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="trajectorySetupModalLabel">
                        <i class="fas fa-clipboard-list me-2"></i>
                        Informações do Relatório
                    </h5>
                </div>
                <div class="modal-body">
                    <form id="trajectorySetupForm">
                        <!-- Trajectory Name -->
                        <div class="mb-3">
                            <label for="trajectoryName" class="form-label">
                                <i class="fas fa-route me-1"></i>
                                Nome do Relatório
                            </label>
                            <input type="text" class="form-control" id="trajectoryName" placeholder="Digite o nome do relatório" required>
                            <div class="form-text text-muted" style="font-size: 0.75rem;">
                                <i class="fas fa-info-circle me-1"></i>
                                Dê um nome descritivo ao seu relatório
                            </div>
                        </div>

                        <div class="row">
                            <!-- Date and Time -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trajectoryDate" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>
                                        Data
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="trajectoryDate" placeholder="Selecione a data" readonly required>
                                        <button class="btn btn-outline-secondary" type="button" id="datePickerBtn">
                                            <i class="fas fa-calendar"></i>
                                        </button>
                                    </div>
                                    <div class="form-text text-muted" style="font-size: 0.75rem;">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Clique em qualquer parte do campo para selecionar a data
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trajectoryStartTime" class="form-label">
                                        <i class="fas fa-clock me-1"></i>
                                        Hora de Início
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="trajectoryStartTime" placeholder="Selecione a hora" readonly required>
                                        <button class="btn btn-outline-secondary" type="button" id="timePickerBtn">
                                            <i class="fas fa-clock"></i>
                                        </button>
                                    </div>
                                    <div class="form-text text-muted" style="font-size: 0.75rem;">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Clique em qualquer parte do campo para selecionar a hora
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Weather Conditions -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-cloud-sun me-1"></i>
                                Indique as condições meteorológicas
                            </label>
                            <div class="weather-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_clear" name="weather_conditions" value="clear">
                                    <label class="form-check-label" for="weather_clear">
                                        <i class="fas fa-sun text-warning me-2"></i>
                                        Céu limpo
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_cloudy" name="weather_conditions" value="cloudy">
                                    <label class="form-check-label" for="weather_cloudy">
                                        <i class="fas fa-cloud text-secondary me-2"></i>
                                        Nublado
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_rain" name="weather_conditions" value="rain">
                                    <label class="form-check-label" for="weather_rain">
                                        <i class="fas fa-cloud-rain text-primary me-2"></i>
                                        Chuva
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_wind" name="weather_conditions" value="wind">
                                    <label class="form-check-label" for="weather_wind">
                                        <i class="fas fa-wind text-info me-2"></i>
                                        Vento moderado a forte
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_other" name="weather_conditions" value="other">
                                    <label class="form-check-label" for="weather_other">
                                        <i class="fas fa-question-circle text-muted me-2"></i>
                                        Outro. Qual?
                                    </label>
                                </div>
                                <div class="mt-2" id="weatherOtherInput" style="display: none;">
                                    <input type="text" class="form-control" id="weatherOtherText" placeholder="Especifique as condições meteorológicas...">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Number of Observers -->
                        <div class="mb-3">
                            <label for="numberOfObservers" class="form-label">
                                <i class="fas fa-users me-1"></i>
                                Indique quantos observadores
                            </label>
                            <input type="number" class="form-control" id="numberOfObservers" min="1" max="20" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="cancelTrajectorySetup()">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="proceedToMap()">
                        <i class="fas fa-map me-1"></i>
                        Continuar para Mapa
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- Contact List Modal -->
    <div class="modal fade" id="contactListModal" tabindex="-1" aria-labelledby="contactListModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactListModalLabel">
                        <i class="fas fa-dove me-2"></i>
                        Adicionar Contactos
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Clique no mapa para adicionar pontos onde teve contacto com rola-brava
                    </div>
                    
                    <div class="contact-stats mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-dove me-1"></i> Contactos adicionados: <strong id="contactCount">0</strong></span>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllContacts()" id="clearContactsBtn" disabled>
                                <i class="fas fa-trash me-1"></i>
                                Limpar Todos
                            </button>
                        </div>
                    </div>
                    
                    <div id="contactsList" class="contacts-list"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="backToTrajectory()">
                        <i class="fas fa-arrow-left me-1"></i>
                        Voltar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="proceedToFinalize()" id="proceedToFinalizeBtn" disabled>
                        <i class="fas fa-arrow-right me-1"></i>
                        Finalizar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Trajectory Details Modal -->
    <div class="modal fade" id="trajectoryDetailsModal" tabindex="-1" aria-labelledby="trajectoryDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="trajectoryDetailsModalLabel">
                        <i class="fas fa-check-circle me-2"></i>
                        Finalizar Relatório
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Trajectory Summary -->
                        <div class="col-md-6">
                            <h6><i class="fas fa-route me-2"></i>Resumo do Relatório</h6>
                            <div class="trajectory-summary">
                                <p><strong>Data:</strong> <span id="summaryDate"></span></p>
                                <p><strong>Hora:</strong> <span id="summaryTime"></span></p>
                                <p><strong>Distância:</strong> <span id="summaryDistance"></span></p>
                                <p><strong>Pontos:</strong> <span id="summaryPoints"></span></p>
                                <p><strong>Observadores:</strong> <span id="summaryObservers"></span></p>
                                <p><strong>Condições:</strong> <span id="summaryWeather"></span></p>
                                <p><strong>Contactos:</strong> <span id="summaryContacts"></span></p>
                            </div>
                        </div>
                        
                        <!-- Photo Upload -->
                        <div class="col-md-6">
                            <h6><i class="fas fa-camera me-2"></i>Fotos do Relatório</h6>
                            <div class="dropzone-container" id="photosDropzone">
                                <div class="dropzone-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="dropzone-text">Arraste fotos aqui ou clique para selecionar</div>
                                <div class="dropzone-subtext">Máximo 6 fotos, 5MB cada</div>
                            </div>
                            <div class="photos-preview" id="photosPreview"></div>
                            <input type="file" id="photosInput" accept="image/*" multiple style="display: none;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="backToContacts()">
                        <i class="fas fa-arrow-left me-1"></i>
                        Voltar
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveTrajectoryDetails()" id="saveDetailsBtn">
                        <i class="fas fa-check-circle me-1"></i>
                        Concluir Relatório
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="successModalLabel">
                        <i class="fas fa-check-circle me-2"></i>
                        Relatório Criado com Sucesso
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle" style="font-size: 4rem; color: #16a34a; margin-bottom: 1rem;"></i>
                        <h6>Relatório guardado com sucesso!</h6>
                        <p class="text-muted">O seu relatório foi criado e está disponível no sistema.</p>
                    </div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-info" onclick="goToTrajectories()">
                        <i class="fas fa-list me-1"></i>
                        Ver Relatórios
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="createAnother()">
                        <i class="fas fa-plus me-1"></i>
                        Criar Outro
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Time Picker Modal -->
    <div class="modal fade" id="timePickerModal" tabindex="-1" aria-labelledby="timePickerModalLabel" aria-hidden="true" data-bs-backdrop="static" style="z-index: 1080;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="timePickerModalLabel">
                        <i class="fas fa-clock me-2"></i>Selecionar Hora
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label text-center d-block">Horas</label>
                            <select class="form-select" id="hourSelect" size="8">
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label text-center d-block">Minutos</label>
                            <select class="form-select" id="minuteSelect" size="8">
                                <option value="00">00</option>
                                <option value="05">05</option>
                                <option value="10">10</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                                <option value="25">25</option>
                                <option value="30">30</option>
                                <option value="35">35</option>
                                <option value="40">40</option>
                                <option value="45">45</option>
                                <option value="50">50</option>
                                <option value="55">55</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>
                            <span id="selectedTimeDisplay">--:--</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" id="cancelTimeBtn" onclick="cancelTimeSelection()" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmTimeBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Date Picker Modal -->
    <div class="modal fade" id="datePickerModal" tabindex="-1" aria-labelledby="datePickerModalLabel" aria-hidden="true" data-bs-backdrop="false" style="z-index: 1070;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="datePickerModalLabel">
                        <i class="fas fa-calendar me-2"></i>Selecionar Data
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-4">
                            <label class="form-label text-center d-block">Dia</label>
                            <select class="form-select" id="daySelect" size="8">
                                <!-- Days will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="col-4">
                            <label class="form-label text-center d-block">Mês</label>
                            <select class="form-select" id="monthSelect" size="8">
                                <option value="01">Janeiro</option>
                                <option value="02">Fevereiro</option>
                                <option value="03">Março</option>
                                <option value="04">Abril</option>
                                <option value="05">Maio</option>
                                <option value="06">Junho</option>
                                <option value="07">Julho</option>
                                <option value="08">Agosto</option>
                                <option value="09">Setembro</option>
                                <option value="10">Outubro</option>
                                <option value="11">Novembro</option>
                                <option value="12">Dezembro</option>
                            </select>
                        </div>
                        <div class="col-4">
                            <label class="form-label text-center d-block">Ano</label>
                            <select class="form-select" id="yearSelect" size="8">
                                <!-- Years will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-calendar me-2"></i>
                            <span id="selectedDateDisplay">--/--/----</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" id="cancelDateBtn" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmDateBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered" style="max-width: 900px; max-height: 80vh;">
            <div class="modal-content help-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel">
                        <i class="fas fa-question-circle me-2"></i>
                        Guia de Criação de Relatórios
                    </h5>
                    <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close">&times;</button>
                </div>
                <div class="modal-body">

                    <!-- Step by Step Guide -->
                    <div class="help-section">
                        <div class="help-section-header">
                            <i class="fas fa-list-ol"></i>
                            <h6>Como Criar um Relatório</h6>
                        </div>
                        <div class="help-steps">
                            <div class="help-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h6><i class="fas fa-calendar-alt me-2"></i>Informações Básicas</h6>
                                    <p>Preencha a data, hora de início, condições meteorológicas e número de observadores.</p>
                                </div>
                            </div>
                            <div class="help-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h6><i class="fas fa-map-marker-alt me-2"></i>Definir Localização</h6>
                                    <p>Escolha o ponto de partida usando a localização atual, pesquisa ou seleção manual no mapa.</p>
                                </div>
                            </div>
                            <div class="help-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h6><i class="fas fa-route me-2"></i>Criar Trajeto</h6>
                                    <p>Clique no mapa para adicionar pontos e criar o percurso realizado durante a observação.</p>
                                </div>
                            </div>
                            <div class="help-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h6><i class="fas fa-dove me-2"></i>Adicionar Contactos</h6>
                                    <p>Marque os locais onde teve contacto visual ou auditivo com rola-brava (opcional).</p>
                                </div>
                            </div>
                            <div class="help-step">
                                <div class="step-number">5</div>
                                <div class="step-content">
                                    <h6><i class="fas fa-camera me-2"></i>Finalizar</h6>
                                    <p>Adicione fotos e observações finais antes de guardar o relatório (opcional).</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Map Controls -->
                    <div class="help-section">
                        <div class="help-section-header">
                            <i class="fas fa-mouse-pointer"></i>
                            <h6>Controles do Mapa</h6>
                        </div>
                        <div class="help-controls">
                            <div class="control-item">
                                <div class="control-icon">
                                    <i class="fas fa-mouse-pointer"></i>
                                </div>
                                <div class="control-content">
                                    <strong>Clique no Mapa</strong>
                                    <span>Adiciona um novo ponto ao trajeto</span>
                                </div>
                            </div>
                            <div class="control-item">
                                <div class="control-icon">
                                    <i class="fas fa-undo"></i>
                                </div>
                                <div class="control-content">
                                    <strong>Botão Desfazer</strong>
                                    <span>Remove o último ponto adicionado</span>
                                </div>
                            </div>
                            <div class="control-item">
                                <div class="control-icon">
                                    <i class="fas fa-eraser"></i>
                                </div>
                                <div class="control-content">
                                    <strong>Botão Limpar</strong>
                                    <span>Remove todos os pontos do trajeto</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tips Section -->
                    <div class="help-section">
                        <div class="help-section-header">
                            <i class="fas fa-lightbulb"></i>
                            <h6>Dicas Importantes</h6>
                        </div>
                        <div class="help-tips">
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Certifique-se de que o trajeto representa fielmente o percurso realizado</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Adicione contactos apenas nos locais onde observou rola-brava</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Use o zoom do mapa para maior precisão na marcação de pontos</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Pode cancelar a criação a qualquer momento sem perder dados</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-1"></i>
                        Compreendi
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <?php include '../../includes/profile_modal.php'; ?>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <!-- Google Maps API - Load conditionally -->
    <script>
        // Define initMap function BEFORE loading Google Maps API
        window.initMap = function() {
            console.log('🗺️ initMap called by Google Maps API');

            // Check if the actual initMap function is defined
            if (typeof window.actualInitMap === 'function') {
                window.actualInitMap();
            } else {
                console.error('❌ actualInitMap function not found');
            }
        };

        // Only load Google Maps API if it's not already loaded
        if (typeof google === 'undefined' || !google.maps) {
            console.log('🗺️ Loading Google Maps API...');
            const script = document.createElement('script');
            script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap&libraries=geometry';
            script.async = true;
            script.defer = true;

            // Add error handling
            script.onerror = function() {
                console.error('❌ Failed to load Google Maps API');
            };

            document.head.appendChild(script);
        } else {
            console.log('🗺️ Google Maps API already loaded');
            // Call initMap directly if already loaded
            setTimeout(() => {
                if (typeof window.initMap === 'function') {
                    window.initMap();
                }
            }, 100);
        }
    </script>
    <!-- Date Time Pickers JS -->
    <script src="../../assets/js/date-time-pickers.js"></script>

    <script>
        // IMMEDIATE TEST - This should appear in console if JavaScript is running
        console.log('🚀 JAVASCRIPT IS RUNNING - Main script loaded');

        // Global variables
        let map;
        let routeCoordinates = [];
        let routePath;
        let contactMarkers = [];
        let trajectorySetupData = {};
        let currentTrajectoryId = null;
        let addingContact = false;
        let selectedPhotos = [];
        let markers = [];
        let pendingContactLocation = null;
        let isContactMode = false;
        let contactCount = 0;
        let mapInitialized = false; // Flag to track map initialization status

        // Define the actual map initialization function
        window.actualInitMap = function actualInitMap() {
            console.log('🗺️ actualInitMap called - initializing map...');
            // Default center (Portugal)
            const center = { lat: 39.5, lng: -8.0 };

            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 7,
                center: center,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_RIGHT
                },
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_CENTER
                },
                fullscreenControl: false,
                streetViewControl: false,
                scrollwheel: true,
                gestureHandling: 'auto',
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            // Initialize route path with shadow effect
            // Shadow path (darker, thicker line underneath)
            window.shadowPath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#000000',
                strokeOpacity: 0.4,
                strokeWeight: 6,
                zIndex: 1
            });
            window.shadowPath.setMap(map);

            // Main route path (colored line on top)
            routePath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#0a7ea4',
                strokeOpacity: 1.0,
                strokeWeight: 4,
                zIndex: 2
            });
            routePath.setMap(map);

            // Add click listener for adding trajectory points
            map.addListener('click', function(event) {
                console.log('🗺️ Map clicked - Contact mode active:', isContactModeActive);

                if (isContactModeActive) {
                    // Contact mode - add contact point with confirmation
                    console.log('🗺️ Contact mode click detected');
                    handleContactMapClick(event);
                } else if (isContactMode) {
                    // Legacy contact mode (for post-save workflow)
                    handleContactMapClick(event);
                } else if (addingContact) {
                    // Legacy contact addition (for post-save workflow)
                    addContactPoint(event.latLng);
                } else {
                    // Normal trajectory mode - add trajectory point
                    console.log('🗺️ Trajectory mode click detected');
                    addTrajectoryPoint(event.latLng);
                }
            });

            // Map is ready - set initialization flag
            mapInitialized = true;
            console.log('🗺️ Map initialized and ready for trajectory creation - mapInitialized flag set to true');
        }

        // Utility function to properly close modals and clean up backdrops
        function closeModalWithCleanup(modalId) {
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            if (modal) {
                modal.hide();

                // Ensure backdrop is properly removed after modal is hidden
                setTimeout(() => {
                    // Remove any orphaned modal backdrops
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => {
                        if (backdrop && backdrop.parentNode) {
                            backdrop.parentNode.removeChild(backdrop);
                        }
                    });

                    // Ensure body classes are cleaned up
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                }, 300);
            }
        }

        // Location services functions
        function showLocationChoiceModal() {
            const locationModal = new bootstrap.Modal(document.getElementById('locationChoiceModal'));
            locationModal.show();
        }

        function handleLocationSearchEnter(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                performLocationSearch();
            }
        }

        window.useCurrentLocation = function useCurrentLocation() {
            if (navigator.geolocation) {
                // Show loading state with exact gestores styling
                Swal.fire({
                    title: 'A obter localização...',
                    html: '<div class="text-center">' +
                          '<p class="mb-3">Por favor <strong>permita o acesso à localização</strong> quando o navegador solicitar.</p>' +
                          '<div class="alert alert-info d-inline-block" style="font-size: 0.9rem;">' +
                          '<i class="fas fa-info-circle me-2"></i>' +
                          'Procure por um ícone de localização na barra de endereços do navegador' +
                          '</div>' +
                          '</div>',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                        // Ensure SweetAlert2 is above all Bootstrap modals
                        const swalContainer = document.querySelector('.swal2-container');
                        if (swalContainer) {
                            swalContainer.style.zIndex = '1080';
                            console.log('✅ Location loading modal z-index set to 1080');
                        }
                    }
                });

                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };

                        Swal.close();

                        // Hide location choice modal
                        const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
                        locationModal.hide();

                        // Animate map to user location with zoom out/in effect, then show confirmation modal
                        animateMapToLocation(userLocation, 15).then(() => {
                            showLocationConfirmedModal();
                        });
                    },
                    function(error) {
                        Swal.close();
                        console.error('Geolocation error:', error);

                        let errorMessage = 'Não foi possível obter a sua localização.';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'Permissão de localização negada. Por favor, permita o acesso à localização nas definições do navegador.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'Informação de localização não disponível.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'Tempo limite excedido ao obter localização.';
                                break;
                        }

                        Swal.fire({
                            title: 'Erro de Localização',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#0a7ea4'
                        });
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 300000
                    }
                );
            } else {
                Swal.fire({
                    title: 'Geolocalização não suportada',
                    text: 'O seu navegador não suporta geolocalização. Pode definir a localização manualmente no mapa.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
            }
        }

        window.useManualLocation = function useManualLocation() {
            const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
            locationModal.hide();

            // For manual location, show confirmation modal immediately (user will click on map)
            setTimeout(() => {
                showLocationConfirmedModal();
            }, 300);
        }

        window.searchLocation = function searchLocation() {
            const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
            locationModal.hide();

            setTimeout(() => {
                const searchModal = new bootstrap.Modal(document.getElementById('locationSearchModal'));
                searchModal.show();

                // Focus on search input
                setTimeout(() => {
                    document.getElementById('locationSearchInput').focus();
                }, 300);
            }, 300);
        }

        window.performLocationSearch = function performLocationSearch() {
            const query = document.getElementById('locationSearchInput').value.trim();
            if (!query) {
                Swal.fire({
                    title: 'Campo obrigatório',
                    text: 'Por favor, digite o nome da localização.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            // Show loading
            Swal.fire({
                title: 'A pesquisar...',
                text: 'Por favor aguarde enquanto pesquisamos a localização.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Store original map position for animation
            const originalCenter = map.getCenter();
            const originalZoom = map.getZoom();

            // Use Google Geocoding API
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                address: query + ', Portugal',
                region: 'PT'
            }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    const location = results[0].geometry.location;
                    const address = results[0].formatted_address;

                    // Hide search modal
                    const searchModal = bootstrap.Modal.getInstance(document.getElementById('locationSearchModal'));
                    searchModal.hide();

                    Swal.close();

                    // Animate from current position to found location with cinematic effect
                    animateMapToLocation(location, 15, true).then(() => {
                        showLocationConfirmedModal();
                    });
                } else {
                    Swal.fire({
                        title: 'Localização não encontrada',
                        text: 'Não foi possível encontrar a localização especificada. Tente com um nome diferente.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4',
                        didOpen: () => {
                            // Ensure SweetAlert2 is above all Bootstrap modals
                            const swalContainer = document.querySelector('.swal2-container');
                            if (swalContainer) {
                                swalContainer.style.zIndex = '1080';
                                console.log('✅ Error modal z-index set to 1080');
                            }
                        }
                    });
                }
            });
        }

        function showLocationConfirmedModal() {
            const modal = new bootstrap.Modal(document.getElementById('locationConfirmedModal'));
            modal.show();
        }

        window.closeLocationConfirmedModal = function closeLocationConfirmedModal() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('locationConfirmedModal'));
            modal.hide();

            // Show trajectory setup modal after location confirmation (animation already happened)
            setTimeout(() => {
                showTrajectorySetupModal();
            }, 300);
        }

        // Function alias for backward compatibility
        function showTrajectorySetup() {
            showTrajectorySetupModal();
        }

        function showTrajectorySetupModal() {
            // Set current date in Portuguese format (DD-MM-YYYY)
            const dateInput = document.getElementById('trajectoryDate');
            if (dateInput) {
                const today = new Date();
                const year = today.getFullYear();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                dateInput.value = `${day}-${month}-${year}`;
            }

            // Set current time rounded to nearest 5 minutes
            const timeInput = document.getElementById('trajectoryStartTime');
            if (timeInput) {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0');
                timeInput.value = `${hours}:${minutes}`;
            }

            // Set default number of observers to 1
            const observersInput = document.getElementById('numberOfObservers');
            if (observersInput) {
                observersInput.value = 1;
            }

            const modal = new bootstrap.Modal(document.getElementById('trajectorySetupModal'));
            modal.show();
        }

        // Helper function for smooth zoom animations - Match gestores reference exactly
        function smoothZoomTo(targetZoom, duration = 1000) {
            return new Promise((resolve) => {
                const startZoom = map.getZoom();
                const zoomDifference = targetZoom - startZoom;
                const steps = 20; // Number of animation steps
                const stepDuration = duration / steps;
                const zoomStep = zoomDifference / steps;

                let currentStep = 0;

                const animateStep = () => {
                    if (currentStep >= steps) {
                        map.setZoom(targetZoom); // Ensure exact final zoom
                        resolve();
                        return;
                    }

                    const newZoom = startZoom + (zoomStep * currentStep);
                    map.setZoom(newZoom);
                    currentStep++;

                    setTimeout(animateStep, stepDuration);
                };

                animateStep();
            });
        }

        // Function to animate map to a specific location with smooth cinematic effect - Match gestores reference exactly
        function animateMapToLocation(targetLocation, targetZoom = 15, fromCurrentPosition = false) {
            return new Promise((resolve) => {
                if (!map) {
                    resolve();
                    return;
                }

                const cinematicZoomOut = 8; // Zoom out to level 8 for subtle effect
                const intermediateZoom = 12; // Intermediate zoom level

                if (fromCurrentPosition) {
                    // For search locations: start from current map position, zoom out, then travel to target
                    const currentPosition = map.getCenter();

                    // Step 1: Zoom out from current position
                    smoothZoomTo(cinematicZoomOut, 800).then(() => {
                        // Step 2: Pan from current position to target location (journey effect)
                        map.panTo(targetLocation);

                        setTimeout(() => {
                            // Step 3: Zoom to intermediate level
                            smoothZoomTo(intermediateZoom, 600).then(() => {
                                // Step 4: Final zoom to target level
                                smoothZoomTo(targetZoom, 800).then(() => {
                                    // Final centering
                                    setTimeout(() => {
                                        map.panTo(targetLocation);
                                        setTimeout(() => {
                                            resolve();
                                        }, 300);
                                    }, 200);
                                });
                            });
                        }, 1200); // Wait for pan animation to complete
                    });
                } else {
                    // For GPS locations: center on target first, then zoom out and back in
                    // Step 1: Instantly center on target location (for GPS we want to focus on user location)
                    map.setCenter(targetLocation);

                    // Step 2: Zoom out from target location
                    smoothZoomTo(cinematicZoomOut, 800).then(() => {
                        // Step 3: Small re-center during zoom out
                        map.panTo(targetLocation);

                        setTimeout(() => {
                            // Step 4: Zoom to intermediate level
                            smoothZoomTo(intermediateZoom, 600).then(() => {
                                // Step 5: Final zoom to target level
                                smoothZoomTo(targetZoom, 800).then(() => {
                                    // Final centering
                                    setTimeout(() => {
                                        map.panTo(targetLocation);
                                        setTimeout(() => {
                                            resolve();
                                        }, 300);
                                    }, 200);
                                });
                            });
                        }, 400); // Shorter wait since no long pan needed
                    });
                }
            });
        }



        // Add trajectory point
        function addTrajectoryPoint(latLng) {
            const pointIndex = routeCoordinates.length;
            routeCoordinates.push(latLng);
            routePath.setPath(routeCoordinates);

            // Update shadow path
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Determine marker icon based on position
            let markerIcon;
            if (pointIndex === 0) {
                // Start point - green with floating label
                markerIcon = {
                    path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                    fillColor: '#22c55e',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 0.7,
                    anchor: new google.maps.Point(0, 8),
                    labelOrigin: new google.maps.Point(0, -40)
                };
            } else {
                // Regular point - blue circle with number
                markerIcon = {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 9,
                    fillColor: '#0a7ea4',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 3
                };
            }

            // Add marker
            const marker = new google.maps.Marker({
                position: latLng,
                map: map,
                title: pointIndex === 0 ? 'Início' : `Ponto ${pointIndex + 1}`,
                icon: markerIcon,
                label: pointIndex === 0 ? {
                    text: 'INÍCIO',
                    fontSize: '9px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                } : {
                    text: (pointIndex + 1).toString(),
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                },
                zIndex: 100
            });

            // Add marker to markers array
            markers.push(marker);

            // Add ground marker for start point (numbered circle on ground)
            if (pointIndex === 0) {
                const groundMarker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#22c55e',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    },
                    label: {
                        text: '1',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    },
                    zIndex: 50
                });
                markers.push(groundMarker);
            } else if (routeCoordinates.length > 1) {
                // Find and remove previous floating finish marker if it exists
                for (let i = markers.length - 1; i >= 0; i--) {
                    if (markers[i].getTitle() === 'Fim') {
                        markers[i].setMap(null);
                        markers.splice(i, 1);
                        break;
                    }
                }

                // Change previous final ground point back to blue (if it exists)
                for (let i = markers.length - 1; i >= 0; i--) {
                    const marker = markers[i];
                    // Only change orange markers (not green INÍCIO markers) back to blue
                    if (marker.getIcon && marker.getIcon().fillColor === '#ea580c') {
                        marker.setIcon({
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: '#0a7ea4',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2
                        });
                        break;
                    }
                }

                // Add ground point for finish (orange for final point)
                const finishGroundMarker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#ea580c',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    },
                    label: {
                        text: (routeCoordinates.length).toString(),
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    },
                    zIndex: 50
                });
                markers.push(finishGroundMarker);

                // Update current marker to floating finish
                marker.setIcon({
                    path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                    fillColor: '#ea580c',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 0.7,
                    anchor: new google.maps.Point(0, 8),
                    labelOrigin: new google.maps.Point(0, -40)
                });
                marker.setLabel({
                    text: 'FIM',
                    fontSize: '9px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                });
                marker.setTitle('Fim');
                marker.setZIndex(1000);
            }

            updateRouteStats();
            updateButtons();

            // Get address for first point
            if (routeCoordinates.length === 1) {
                getAddressFromLatLng(latLng);
            }
        }

        // Add contact point
        function addContactPoint(latLng) {
            createContactMarkerIcon(function(iconConfig) {
                const marker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    title: 'Contacto com Rola-Brava',
                    icon: iconConfig
                });

                const contactData = {
                    marker: marker,
                    position: latLng,
                    time: '',
                    circumstance: '',
                    location: ''
                };

                contactMarkers.push(contactData);
                updateContactsList();
                updateContactButtons();
            });
        }

        // Update statistics
        function updateStats() {
            document.getElementById('routePoints').textContent = routeCoordinates.length;

            if (routeCoordinates.length > 1) {
                const distance = calculateTotalDistance();
                document.getElementById('routeDistance').textContent = distance.toFixed(2) + ' km';
            } else {
                document.getElementById('routeDistance').textContent = '0 km';
            }
        }

        // Calculate total distance
        function calculateTotalDistance() {
            let totalDistance = 0;
            for (let i = 0; i < routeCoordinates.length - 1; i++) {
                totalDistance += google.maps.geometry.spherical.computeDistanceBetween(
                    routeCoordinates[i], 
                    routeCoordinates[i + 1]
                );
            }
            return totalDistance / 1000; // Convert to kilometers
        }

        // Update buttons
        function updateButtons() {
            const undoBtn = document.getElementById('undoBtn');
            const saveBtn = document.getElementById('saveTrajetoBtn');
            const contactBtn = document.getElementById('addContactBtn');
            const saveContactModeBtn = document.getElementById('saveTrajetoContactModeBtn');

            // Don't update button states if currently saving
            if (isSaving) {
                return;
            }

            undoBtn.disabled = routeCoordinates.length === 0;
            saveBtn.disabled = routeCoordinates.length < 2;

            // Enable contact button when we have at least one trajectory point
            contactBtn.disabled = routeCoordinates.length === 0;

            // Keep contact mode save button in sync with trajectory mode save button
            if (saveContactModeBtn) {
                saveContactModeBtn.disabled = routeCoordinates.length < 2;
            }
        }

        // Get address from coordinates
        function getAddressFromLatLng(latLng) {
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                location: latLng,
                language: 'pt',  // Request results in Portuguese
                region: 'PT'     // Bias results towards Portugal
            }, (results, status) => {
                if (status === 'OK') {
                    if (results[0]) {
                        // Get a more readable address format
                        let address = results[0].formatted_address;

                        // Try to get a shorter, more relevant address
                        for (let i = 0; i < results.length; i++) {
                            const result = results[i];
                            // Look for route, locality, or administrative_area_level_3 for better context
                            if (result.types.includes('route') ||
                                result.types.includes('locality') ||
                                result.types.includes('administrative_area_level_3')) {
                                address = result.formatted_address;
                                break;
                            }
                        }

                        // Store the full address for saving to database
                        window.fullStartingAddress = address;

                        // Use full address for display since we have enough space
                        document.getElementById('startingAddress').textContent = address;
                    } else {
                        document.getElementById('startingAddress').textContent = 'Endereço não encontrado';
                    }
                } else {
                    console.log('Geocoder failed due to: ' + status);
                    document.getElementById('startingAddress').textContent = 'Erro ao obter endereço';
                }
            });
        }

        // Clear route function - matches gestores implementation
        window.clearRoute = function clearRoute() {
            console.log('🧹 clearRoute called');

            // CRITICAL FIX: Check if map objects are initialized before using them
            // This prevents "routePath is undefined" errors when button is clicked before map loads
            if (!routePath) {
                console.log('⚠️ routePath not initialized yet - skipping route path clearing');
            } else {
                // Clear route path if initialized
                routePath.setPath([]);
                console.log('✅ Route path cleared');
            }

            if (!window.shadowPath) {
                console.log('⚠️ shadowPath not initialized yet - skipping shadow path clearing');
            } else {
                // Clear shadow path if initialized
                window.shadowPath.setPath([]);
                console.log('✅ Shadow path cleared');
            }

            // Clear all markers (including ground points and floating labels)
            if (markers && markers.length > 0) {
                markers.forEach(marker => marker.setMap(null));
                markers = [];
                console.log('✅ Markers cleared');
            }

            // Clear route coordinates (always safe to clear)
            routeCoordinates = [];

            // Reset starting address (always safe to reset)
            const startingAddressElement = document.getElementById('startingAddress');
            if (startingAddressElement) {
                startingAddressElement.textContent = 'Não definido';
            }
            window.fullStartingAddress = null;

            // Update stats
            updateRouteStats();

            // Disable buttons
            document.getElementById('saveTrajetoBtn').disabled = true;
            document.getElementById('undoBtn').disabled = true;
            document.getElementById('addContactBtn').disabled = true;

            // Reset contact mode
            if (isContactModeActive) {
                toggleContactMode();
            }

            // Clear any contacts added during trajectory creation
            contactMarkers.forEach(contact => {
                contact.marker.setMap(null);
            });
            contactMarkers = [];
            updateContactCountBadge();

            // Re-enable sidebar since trajectory is cleared
            enableSidebar();
        }

        // Alternative clear trajectory function
        window.clearTrajectory = function clearTrajectory() {
            // Clear route coordinates
            routeCoordinates = [];

            // Clear markers
            if (markers && markers.length > 0) {
                markers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                markers = [];
            }

            // Clear route path
            if (routePath) {
                routePath.setPath([]);
            }
            if (window.shadowPath) {
                window.shadowPath.setPath([]);
            }

            // Update UI
            updateTrajectoryUI(false);
        }

        function enableSidebar() {
            // Re-enable sidebar functionality if needed
            // This function can be expanded based on specific sidebar requirements
        }

        // Contact mode management
        let isContactModeActive = false;
        let isSaving = false; // Flag to track save state
        let lastSaveAttempt = 0; // Timestamp of last save attempt

        // DUPLICATE TRAJECTORY BUG FIX:
        // This fix prevents duplicate trajectory creation when Contact Mode is active.
        // The issue was caused by having two save buttons with separate event handlers:
        // 1. saveTrajetoBtn (form submit) - for normal trajectory mode
        // 2. saveTrajetoContactModeBtn (click event) - for contact mode
        // Both could trigger simultaneously, causing duplicate database entries.

        // MAP INITIALIZATION BUG FIX:
        // This fix prevents "routePath is undefined" errors when buttons are clicked
        // before the Google Maps API has fully loaded and initialized all objects.

        // Utility function to check if map is ready for operations
        function isMapReady() {
            return mapInitialized && map && routePath && window.shadowPath;
        }

        function toggleContactMode() {
            console.log('🔄 toggleContactMode called, current state:', isContactModeActive);

            isContactModeActive = !isContactModeActive;

            const contactBtn = document.getElementById('addContactBtn');
            const contactBtnText = document.getElementById('contactBtnText');
            const contactIndicator = document.getElementById('contactModeIndicator');
            const trajectoryModeButtons = document.getElementById('trajectoryModeButtons');
            const contactModeButtons = document.getElementById('contactModeButtons');

            if (isContactModeActive) {
                // Activate contact mode
                console.log('✅ Activating contact mode');
                contactBtn.classList.add('active');
                contactBtnText.textContent = 'Modo Trajeto';
                contactIndicator.style.display = 'block';

                // Hide trajectory mode buttons and show contact mode buttons
                trajectoryModeButtons.style.display = 'none';
                contactModeButtons.style.display = 'flex';

                // Update contact mode buttons state
                updateContactModeButtons();

                // Update icon to show trajectory mode will be next
                contactBtn.innerHTML = '<i class="fas fa-route me-1"></i><span id="contactBtnText">Modo Trajeto</span>';
            } else {
                // Deactivate contact mode
                console.log('❌ Deactivating contact mode');
                contactBtn.classList.remove('active');
                contactBtnText.textContent = 'Adicionar Contacto';
                contactIndicator.style.display = 'none';

                // Show trajectory mode buttons and hide contact mode buttons
                trajectoryModeButtons.style.display = 'flex';
                contactModeButtons.style.display = 'none';

                // Update icon back to contact mode
                contactBtn.innerHTML = '<i class="fas fa-dove me-1"></i><span id="contactBtnText">Adicionar Contacto</span>';
            }

            console.log('🔄 Contact mode is now:', isContactModeActive ? 'ACTIVE' : 'INACTIVE');
        }

        // Create contact marker icon with dove using canvas
        function createContactMarkerIcon(callback) {
            const canvas = document.createElement('canvas');
            canvas.width = 32;
            canvas.height = 32;
            const ctx = canvas.getContext('2d');

            // Draw green circle background
            ctx.beginPath();
            ctx.arc(16, 16, 14, 0, 2 * Math.PI);
            ctx.fillStyle = '#10b981';  // Use the same green as other contact elements
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Load and draw dove icon
            const doveImg = new Image();
            doveImg.onload = function() {
                // Create a temporary canvas to process the dove icon
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = 16;
                tempCanvas.height = 16;
                const tempCtx = tempCanvas.getContext('2d');

                // Draw the dove icon on temp canvas
                tempCtx.drawImage(doveImg, 0, 0, 16, 16);

                // Get image data to process pixels
                const imageData = tempCtx.getImageData(0, 0, 16, 16);
                const data = imageData.data;

                // Convert non-transparent pixels to white
                for (let i = 0; i < data.length; i += 4) {
                    if (data[i + 3] > 0) { // If pixel is not transparent
                        data[i] = 255;     // Red = 255 (white)
                        data[i + 1] = 255; // Green = 255 (white)
                        data[i + 2] = 255; // Blue = 255 (white)
                    }
                }

                // Put the processed image data back
                tempCtx.putImageData(imageData, 0, 0);

                // Draw the white dove icon on the main canvas
                ctx.drawImage(tempCanvas, 8, 8);

                // Return the icon configuration
                callback({
                    url: canvas.toDataURL(),
                    scaledSize: new google.maps.Size(32, 32),
                    anchor: new google.maps.Point(16, 16)
                });
            };

            doveImg.onerror = function() {
                console.error('Failed to load dove icon, using fallback');
                // Fallback to simple green circle
                callback({
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 10,
                    fillColor: '#10b981',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 3
                });
            };

            // Set the dove icon source
            doveImg.src = '../../assets/images/icons/dove-icon.png';
        }

        // Update contact count badge
        function updateContactCountBadge() {
            const badge = document.getElementById('contactCountBadge');
            const count = contactMarkers.length;

            // Check if badge element exists before accessing its properties
            if (badge) {
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'inline';
                } else {
                    badge.style.display = 'none';
                }
                console.log('📊 Updated contact count badge:', count);
            } else {
                console.warn('⚠️ contactCountBadge element not found in DOM');
            }

            // Update delete last contact button state
            updateContactModeButtons();
        }

        // Delete last contact function
        function deleteLastContact() {
            console.log('🗑️ deleteLastContact called');

            if (contactMarkers.length === 0) {
                console.warn('⚠️ No contacts to delete');
                return;
            }

            // Get the last contact
            const lastContact = contactMarkers[contactMarkers.length - 1];

            // Remove marker from map
            if (lastContact.marker) {
                lastContact.marker.setMap(null);
                console.log('🗑️ Removed last contact marker from map');
            }

            // Remove from array
            contactMarkers.pop();
            contactCount--;

            // Update displays
            const contactCountElement = document.getElementById('contactCount');
            if (contactCountElement) {
                contactCountElement.textContent = contactCount;
            }

            updateContactCountBadge();
            console.log('✅ Last contact deleted. Remaining contacts:', contactMarkers.length);
        }

        // Update contact mode buttons state
        function updateContactModeButtons() {
            const deleteBtn = document.getElementById('deleteLastContactBtn');
            const saveContactModeBtn = document.getElementById('saveTrajetoContactModeBtn');

            // Don't update button states if currently saving
            if (isSaving) {
                return;
            }

            if (deleteBtn) {
                deleteBtn.disabled = contactMarkers.length === 0;
            }

            // Keep save button state consistent with trajectory mode
            if (saveContactModeBtn) {
                saveContactModeBtn.disabled = routeCoordinates.length < 2;
            }
        }

        function updateTrajectoryUI(isActive) {
            // Update UI based on trajectory state
            if (isActive) {
                document.body.classList.add('trajectory-active');
            } else {
                document.body.classList.remove('trajectory-active');
            }
        }

        // Update route statistics - matches gestores implementation
        function updateRouteStats() {
            const pointsElement = document.getElementById('routePoints');
            const distanceElement = document.getElementById('routeDistance');

            if (pointsElement) {
                pointsElement.textContent = routeCoordinates.length;
            }

            if (routeCoordinates.length > 1) {
                const distance = calculateTotalDistance();
                if (distanceElement) {
                    distanceElement.textContent = distance.toFixed(2) + ' km';
                }
            } else {
                if (distanceElement) {
                    distanceElement.textContent = '0 km';
                }
            }
        }

        // Contact handling functions
        function handleContactMapClick(event) {
            console.log('🗺️ Contact map clicked at:', event.latLng.lat(), event.latLng.lng());

            pendingContactLocation = {
                lat: event.latLng.lat(),
                lng: event.latLng.lng()
            };

            console.log('📍 pendingContactLocation set to:', pendingContactLocation);
            console.log('📍 Latitude:', pendingContactLocation.lat);
            console.log('📍 Longitude:', pendingContactLocation.lng);
            console.log('📍 Formatted coordinates:', `${pendingContactLocation.lat.toFixed(6)}, ${pendingContactLocation.lng.toFixed(6)}`);

            // Skip confirmation modal and go directly to contact details
            console.log('🚀 Bypassing confirmation modal, going directly to contact details');

            // Create contact marker with dove icon
            createContactMarkerIcon(function(iconConfig) {
                const marker = new google.maps.Marker({
                    position: pendingContactLocation,
                    map: map,
                    title: 'Contacto com Rola-Brava',
                    icon: iconConfig
                });

                // Store contact data
                const contactData = {
                    marker: marker,
                    location: pendingContactLocation,
                    details: null
                };

                contactMarkers.push(contactData);
                contactCount++;

                // Update contact count display
                const contactCountElement = document.getElementById('contactCount');
                if (contactCountElement) {
                    contactCountElement.textContent = contactCount;
                }

                // Update contact count badge
                updateContactCountBadge();

                // Show contact details modal directly
                console.log('📱 Opening contact details modal directly...');
                showContactDetailsModal();
            });
        }

        function showContactConfirmationModal() {
            console.log('📋 showContactConfirmationModal called');

            // Update coordinates display
            if (pendingContactLocation) {
                const coordsElement = document.getElementById('contactCoordinates');
                const coordsText = `${pendingContactLocation.lat.toFixed(6)}, ${pendingContactLocation.lng.toFixed(6)}`;
                console.log('📍 Setting coordinates display to:', coordsText);
                console.log('📍 contactCoordinates element found:', !!coordsElement);

                if (coordsElement) {
                    coordsElement.textContent = coordsText;
                }
            }

            console.log('📋 Showing contact confirmation modal...');
            const modalElement = document.getElementById('contactConfirmationModal');
            console.log('📋 Modal element found:', !!modalElement);

            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                console.log('✅ Contact confirmation modal shown');
            } else {
                console.error('❌ contactConfirmationModal element not found');
            }
        }

        window.cancelContactPlacement = function cancelContactPlacement() {
            console.log('Contact placement cancelled');
            pendingContactLocation = null;

            closeModalWithCleanup('contactConfirmationModal');
        }

        window.confirmContactPlacement = function confirmContactPlacement() {
            console.log('🎯 confirmContactPlacement called');
            console.log('📍 pendingContactLocation:', pendingContactLocation);

            if (!pendingContactLocation) {
                console.error('❌ No pendingContactLocation - returning');
                return;
            }

            // Create contact marker with consistent green styling and dove icon
            const marker = new google.maps.Marker({
                position: pendingContactLocation,
                map: map,
                title: 'Contacto com Rola-Brava',
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 10,
                    fillColor: '#10b981',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 3
                }
            });

            // Store contact data
            const contactData = {
                marker: marker,
                location: pendingContactLocation,
                details: null
            };

            contactMarkers.push(contactData);
            contactCount++;

            // Update contact count display
            const contactCountElement = document.getElementById('contactCount');
            if (contactCountElement) {
                contactCountElement.textContent = contactCount;
            }

            // Update contact count badge
            updateContactCountBadge();

            // Hide confirmation modal
            closeModalWithCleanup('contactConfirmationModal');

            // Show contact details modal
            console.log('⏰ Setting timeout to call showContactDetailsModal...');
            setTimeout(() => {
                console.log('⏰ Timeout executing - calling showContactDetailsModal');
                showContactDetailsModal();
            }, 300);
        }

        function showContactDetailsModal() {
            console.log('🏠 showContactDetailsModal called');
            console.log('📍 pendingContactLocation:', pendingContactLocation);

            // Ensure any leftover modal backdrops are cleaned up before opening new modal
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                if (backdrop && backdrop.parentNode) {
                    backdrop.parentNode.removeChild(backdrop);
                }
            });

            // Ensure body classes are cleaned up
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Store coordinates before form reset
            const coordsValue = pendingContactLocation ?
                `${pendingContactLocation.lat.toFixed(6)}, ${pendingContactLocation.lng.toFixed(6)}` : '';

            console.log('📊 Calculated coordsValue:', coordsValue);
            console.log('📊 coordsValue length:', coordsValue.length);
            console.log('📊 coordsValue type:', typeof coordsValue);

            // Clear form first
            console.log('🧹 Resetting form...');
            const form = document.getElementById('contactDetailsForm');
            if (form) {
                form.reset();
                console.log('✅ Form reset completed');
            } else {
                console.error('❌ contactDetailsForm not found');
            }

            const timeField = document.getElementById('contactTime');
            if (timeField) {
                timeField.value = '';
                console.log('✅ Time field cleared');
            } else {
                console.error('❌ contactTime field not found');
            }

            // Set contact coordinates (readonly) after form reset - use setTimeout to ensure it happens after reset
            console.log('⏰ Setting up coordinates setTimeout...');
            setTimeout(() => {
                console.log('⏰ setTimeout callback executing');
                console.log('📍 pendingContactLocation in setTimeout:', pendingContactLocation);
                console.log('📊 coordsValue in setTimeout:', coordsValue);

                const coordsField = document.getElementById('contactCoords');
                console.log('🔍 coordsField found:', !!coordsField);
                console.log('🔍 coordsField element:', coordsField);

                if (coordsField) {
                    console.log('📊 coordsField current value before setting:', coordsField.value);
                    console.log('📊 coordsField readonly:', coordsField.readOnly);
                    console.log('📊 coordsField disabled:', coordsField.disabled);

                    if (pendingContactLocation && coordsValue) {
                        coordsField.value = coordsValue;
                        console.log('✅ Contact coordinates set to:', coordsValue);
                        console.log('✅ coordsField.value after setting:', coordsField.value);

                        // Force a visual update
                        coordsField.dispatchEvent(new Event('input', { bubbles: true }));
                        coordsField.dispatchEvent(new Event('change', { bubbles: true }));
                    } else {
                        console.log('❌ No coordinates to set - pendingContactLocation or coordsValue is empty');
                        console.log('❌ pendingContactLocation exists:', !!pendingContactLocation);
                        console.log('❌ coordsValue exists:', !!coordsValue);
                    }
                } else {
                    console.error('❌ contactCoords field not found in DOM');
                }
            }, 100); // Increased timeout to 100ms

            // Setup event listeners
            setupContactFormListeners();

            // Debug all modal elements
            debugModalElements();

            const modalElement = document.getElementById('contactDetailsModal');
            console.log('📱 Opening contact details modal...');
            console.log('📱 Modal element found:', !!modalElement);
            console.log('📱 Modal element classes:', modalElement ? modalElement.className : 'N/A');

            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('✅ Contact details modal shown');
        }

        function debugModalElements() {
            console.log('🔍 === MODAL ELEMENTS DEBUG ===');

            // Check time picker elements
            const timeInput = document.getElementById('contactTime');
            const timePickerBtn = document.getElementById('contactTimePickerBtn');
            const timePickerModal = document.getElementById('timePickerModal');
            const hourSelect = document.getElementById('hourSelect');
            const minuteSelect = document.getElementById('minuteSelect');

            console.log('⏰ Time picker elements:', {
                timeInput: !!timeInput,
                timePickerBtn: !!timePickerBtn,
                timePickerModal: !!timePickerModal,
                hourSelect: !!hourSelect,
                minuteSelect: !!minuteSelect
            });

            // Check coordinates elements
            const coordsField = document.getElementById('contactCoords');
            console.log('📍 Coordinates elements:', {
                coordsField: !!coordsField,
                coordsFieldValue: coordsField ? coordsField.value : 'N/A'
            });

            // Check location radio buttons and input
            const locationRadios = document.querySelectorAll('input[name="contact_location"]');
            const locationOtherInput = document.getElementById('locationOtherInput');
            const locationOtherText = document.getElementById('locationOtherText');

            console.log('🔘 Location elements:', {
                locationRadiosCount: locationRadios.length,
                locationOtherInput: !!locationOtherInput,
                locationOtherText: !!locationOtherText,
                locationOtherInputDisplay: locationOtherInput ? locationOtherInput.style.display : 'N/A'
            });

            // List all location radio values
            locationRadios.forEach((radio, index) => {
                console.log(`🔘 Radio ${index}:`, {
                    id: radio.id,
                    value: radio.value,
                    checked: radio.checked
                });
            });

            console.log('🔍 === END MODAL ELEMENTS DEBUG ===');
        }

        // Global debugging functions for browser console testing
        window.debugContactModal = function() {
            console.log('🧪 === MANUAL DEBUG TEST ===');
            debugModalElements();

            console.log('🧪 Testing time picker function...');
            if (typeof window.showContactTimePicker === 'function') {
                console.log('✅ showContactTimePicker function exists');
            } else {
                console.error('❌ showContactTimePicker function not found');
            }

            console.log('🧪 Testing coordinates...');
            console.log('📍 pendingContactLocation:', window.pendingContactLocation || 'Not set');

            console.log('🧪 === END MANUAL DEBUG TEST ===');
        };

        window.testTimePicker = function() {
            console.log('🧪 Testing time picker manually...');
            if (typeof window.showContactTimePicker === 'function') {
                window.showContactTimePicker();
            } else {
                console.error('❌ showContactTimePicker function not available');
            }
        };

        window.testCoordinates = function(lat, lng) {
            console.log('🧪 Testing coordinates manually...');
            window.pendingContactLocation = {
                lat: lat || 38.7223,
                lng: lng || -9.1393
            };
            console.log('📍 Set pendingContactLocation to:', window.pendingContactLocation);
            window.showContactDetailsModal();
        };

        // Simple test to open contact details modal directly
        window.openContactModal = function() {
            console.log('🧪 Opening contact modal directly...');
            // Set dummy coordinates
            window.pendingContactLocation = {
                lat: 38.7223,
                lng: -9.1393
            };
            // Call the modal function directly
            window.showContactDetailsModal();
        };

        function setupContactFormListeners() {
            console.log('🔧 setupContactFormListeners called');
            // Groups number visibility for circumstances
            document.querySelectorAll('input[name="contact_circumstances"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const groupsInput = document.getElementById('circumstanceGroupsInput');
                    const otherInput = document.getElementById('circumstanceOtherInput');

                    if (this.value === 'groups') {
                        groupsInput.style.display = 'block';
                        otherInput.style.display = 'none';
                    } else if (this.value === 'other') {
                        groupsInput.style.display = 'none';
                        otherInput.style.display = 'block';
                    } else {
                        groupsInput.style.display = 'none';
                        otherInput.style.display = 'none';
                    }
                });
            });

            // Other location visibility
            const locationRadios = document.querySelectorAll('input[name="contact_location"]');
            console.log('🔘 Found location radio buttons:', locationRadios.length);

            locationRadios.forEach((radio, index) => {
                console.log(`🔘 Radio ${index}:`, {
                    id: radio.id,
                    value: radio.value,
                    name: radio.name
                });

                radio.addEventListener('change', function() {
                    const otherLocationInput = document.getElementById('locationOtherInput');
                    console.log('🔘 Location radio changed:', this.value);
                    console.log('🔘 Radio ID:', this.id);
                    console.log('🔘 otherLocationInput found:', !!otherLocationInput);
                    console.log('🔘 otherLocationInput element:', otherLocationInput);

                    if (this.value === 'other') {
                        if (otherLocationInput) {
                            console.log('🔘 Current display style before showing:', otherLocationInput.style.display);
                            otherLocationInput.style.display = 'block';
                            console.log('✅ Showing other location input');
                            console.log('🔘 Display style after showing:', otherLocationInput.style.display);
                        } else {
                            console.error('❌ otherLocationInput not found when trying to show');
                        }
                    } else {
                        if (otherLocationInput) {
                            console.log('🔘 Current display style before hiding:', otherLocationInput.style.display);
                            otherLocationInput.style.display = 'none';
                            console.log('✅ Hiding other location input');
                            console.log('🔘 Display style after hiding:', otherLocationInput.style.display);
                        } else {
                            console.error('❌ otherLocationInput not found when trying to hide');
                        }
                    }
                });
            });

            // Time picker functionality - use existing Bootstrap modal
            const timeInput = document.getElementById('contactTime');
            const timePickerBtn = document.getElementById('contactTimePickerBtn');

            console.log('🔍 Looking for time picker elements:', {
                timeInput: !!timeInput,
                timePickerBtn: !!timePickerBtn,
                timeInputId: timeInput ? timeInput.id : 'not found',
                timePickerBtnId: timePickerBtn ? timePickerBtn.id : 'not found',
                showContactTimePickerExists: typeof window.showContactTimePicker === 'function'
            });

            if (timeInput && timePickerBtn) {
                console.log('Setting up contact time picker event listeners');

                // Remove any existing event listeners to prevent duplicates
                timeInput.removeEventListener('click', window.showContactTimePicker);
                timePickerBtn.removeEventListener('click', window.showContactTimePicker);

                // Add event listeners
                timeInput.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('⏰ Time input clicked');
                    console.log('⏰ Calling showContactTimePicker...');
                    try {
                        if (typeof window.showContactTimePicker === 'function') {
                            window.showContactTimePicker();
                        } else {
                            console.error('❌ showContactTimePicker is not a function:', typeof window.showContactTimePicker);
                        }
                    } catch (error) {
                        console.error('❌ Error calling showContactTimePicker:', error);
                    }
                });

                timePickerBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('⏰ Time picker button clicked');
                    console.log('⏰ Calling showContactTimePicker...');
                    try {
                        if (typeof window.showContactTimePicker === 'function') {
                            window.showContactTimePicker();
                        } else {
                            console.error('❌ showContactTimePicker is not a function:', typeof window.showContactTimePicker);
                        }
                    } catch (error) {
                        console.error('❌ Error calling showContactTimePicker:', error);
                    }
                });

                console.log('Contact time picker event listeners attached');
            } else {
                console.error('Contact time input or button not found', {
                    timeInput: timeInput,
                    timePickerBtn: timePickerBtn
                });
            }
        }

        window.showContactTimePicker = function showContactTimePicker() {
            console.log('🚀 showContactTimePicker function called');

            // Set the target for contact time
            window.currentTimeTarget = 'contactTime';
            console.log('🎯 Set currentTimeTarget to:', window.currentTimeTarget);

            // Get current contact time value if any
            const currentTime = document.getElementById('contactTime').value;
            console.log('⏰ Current contact time value:', currentTime);

            const hourSelect = document.getElementById('hourSelect');
            const minuteSelect = document.getElementById('minuteSelect');
            const timePickerModal = document.getElementById('timePickerModal');

            console.log('🔍 Time picker elements check:', {
                hourSelect: !!hourSelect,
                minuteSelect: !!minuteSelect,
                timePickerModal: !!timePickerModal
            });

            if (!hourSelect || !minuteSelect) {
                console.error('❌ Hour or minute select elements not found');
                return;
            }

            if (!timePickerModal) {
                console.error('❌ Time picker modal not found');
                return;
            }

            if (currentTime && currentTime.includes(':')) {
                const [hours, minutes] = currentTime.split(':');
                hourSelect.value = hours.padStart(2, '0');
                minuteSelect.value = minutes.padStart(2, '0');
                updateTimeDisplay();
                console.log('Set time picker to existing contact time:', currentTime);
            } else {
                // Default to current time
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0'); // Round to nearest 5 minutes
                hourSelect.value = hours;
                minuteSelect.value = minutes;
                updateTimeDisplay();
                console.log('Set contact time picker to current time:', `${hours}:${minutes}`);
            }

            // Show the time picker modal
            console.log('📱 Attempting to show time picker modal...');
            try {
                const timePickerModalElement = document.getElementById('timePickerModal');
                if (timePickerModalElement) {
                    const timePickerModal = new bootstrap.Modal(timePickerModalElement);
                    timePickerModal.show();
                    console.log('✅ Contact time picker modal shown successfully');
                } else {
                    console.error('❌ timePickerModal element not found in DOM');
                }
            } catch (error) {
                console.error('❌ Error showing time picker modal:', error);
            }

            // Set up custom confirm handler for contact time
            console.log('🔧 Setting up confirm handler...');
            setupContactTimeConfirmHandler();
        }

        window.setupContactTimeConfirmHandler = function setupContactTimeConfirmHandler() {
            const confirmBtn = document.getElementById('confirmTimeBtn');
            if (confirmBtn) {
                console.log('Setting up contact time confirm handler');
                // Remove existing event listeners
                confirmBtn.removeEventListener('click', window.confirmContactTimeSelection, true);
                // Add new event listener for contact time with capture
                confirmBtn.addEventListener('click', function(e) {
                    if (window.currentTimeTarget === 'contactTime') {
                        e.preventDefault();
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        window.confirmContactTimeSelection();
                    }
                }, true);
                console.log('Contact time confirm handler set up');
            }
        }

        window.confirmContactTimeSelection = function confirmContactTimeSelection() {
            console.log('Contact time confirm clicked, currentTimeTarget:', window.currentTimeTarget);

            const hourSelect = document.getElementById('hourSelect');
            const minuteSelect = document.getElementById('minuteSelect');

            if (!hourSelect || !minuteSelect) {
                console.error('Contact time picker: Hour or minute select not found');
                return;
            }

            const hour = hourSelect.value;
            const minute = minuteSelect.value;
            const timeString = `${hour}:${minute}`;

            // Update the contact time input field
            const contactTimeInput = document.getElementById('contactTime');
            if (contactTimeInput) {
                contactTimeInput.value = timeString;
                console.log('Contact time set to:', timeString);
            } else {
                console.error('Contact time input not found');
                return;
            }

            // Close the time picker modal
            const timeModalElement = document.getElementById('timePickerModal');
            if (timeModalElement) {
                const timeModal = bootstrap.Modal.getInstance(timeModalElement);
                if (timeModal) {
                    timeModal.hide();
                }
            }

            // Reset the target
            window.currentTimeTarget = null;
        }

        window.cancelTimeSelection = function cancelTimeSelection() {
            console.log('⏰ Time selection cancelled');

            // Close the time picker modal
            const timeModalElement = document.getElementById('timePickerModal');
            if (timeModalElement) {
                const timeModal = bootstrap.Modal.getInstance(timeModalElement);
                if (timeModal) {
                    timeModal.hide();
                }
            }

            // Reset the target
            window.currentTimeTarget = null;

            // If we're in contact mode, don't navigate away - just close the time picker
            // The contact details modal should remain open
            console.log('✅ Time picker cancelled - staying in contact mode');
        }

        window.cancelContactDetails = function cancelContactDetails() {
            // Remove the last contact marker if it was just added
            if (contactMarkers.length > 0) {
                const lastContact = contactMarkers[contactMarkers.length - 1];
                if (!lastContact.details) {
                    lastContact.marker.setMap(null);
                    contactMarkers.pop();
                    contactCount--;

                    // Update contact count display
                    const contactCountElement = document.getElementById('contactCount');
                    if (contactCountElement) {
                        contactCountElement.textContent = contactCount;
                    }

                    // Update contact count badge
                    updateContactCountBadge();
                }
            }

            pendingContactLocation = null;

            closeModalWithCleanup('contactDetailsModal');
        }

        window.saveContactDetails = function saveContactDetails() {
            // Validate required fields
            const timeValue = document.getElementById('contactTime').value;
            const selectedCircumstance = document.querySelector('input[name="contact_circumstances"]:checked');
            const selectedLocation = document.querySelector('input[name="contact_location"]:checked');

            if (!timeValue) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, especifique a hora do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            if (!selectedCircumstance) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione a circunstância do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            if (!selectedLocation) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione o local do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            // Build circumstance value
            let circumstanceValue = '';
            switch(selectedCircumstance.value) {
                case 'adultSinging':
                    circumstanceValue = 'Rola adulta a cantar';
                    break;
                case 'adultDisplay':
                    circumstanceValue = 'Adulto em display';
                    break;
                case 'flying':
                    circumstanceValue = 'Rola em voo';
                    break;
                case 'emptyNest':
                    circumstanceValue = 'Ninho vazio';
                    break;
                case 'adultPerched':
                    circumstanceValue = 'Adulto pousado';
                    break;
                case 'occupiedNest':
                    circumstanceValue = 'Ninho ocupado';
                    break;
                case 'groups':
                    const groupsNumber = document.getElementById('circumstanceGroupsNumber').value;
                    if (!groupsNumber || groupsNumber < 1) {
                        Swal.fire({
                            title: 'Campo Obrigatório',
                            text: 'Por favor, especifique o número de indivíduos no grupo.',
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#0a7ea4'
                        });
                        return;
                    }
                    circumstanceValue = `Grupos de Rolas (${groupsNumber} indivíduos)`;
                    break;
                case 'other':
                    const otherCircumstanceText = document.getElementById('circumstanceOtherText').value.trim();
                    if (!otherCircumstanceText) {
                        Swal.fire({
                            title: 'Campo Obrigatório',
                            text: 'Por favor, especifique a circunstância do contacto.',
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#0a7ea4'
                        });
                        return;
                    }
                    circumstanceValue = `Outro: ${otherCircumstanceText}`;
                    break;
            }

            // Build location value
            let locationValue = '';
            switch(selectedLocation.value) {
                case 'tree':
                    locationValue = 'Árvore';
                    break;
                case 'waterPoint':
                    locationValue = 'Ponto de água';
                    break;
                case 'shrub':
                    locationValue = 'Arbusto';
                    break;
                case 'clearing':
                    locationValue = 'Clareira';
                    break;
                case 'other':
                    const otherLocationText = document.getElementById('locationOtherText').value.trim();
                    if (!otherLocationText) {
                        Swal.fire({
                            title: 'Campo Obrigatório',
                            text: 'Por favor, especifique o local do contacto.',
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#0a7ea4'
                        });
                        return;
                    }
                    locationValue = `Outro: ${otherLocationText}`;
                    break;
                    break;
            }

            // Collect form data
            const formData = {
                time: timeValue,
                location: pendingContactLocation,
                circumstance: circumstanceValue,
                locationDetail: locationValue
            };

            // Store details in the last contact marker
            if (contactMarkers.length > 0) {
                contactMarkers[contactMarkers.length - 1].details = formData;
            }

            console.log('Contact saved:', formData);

            // Update contact count badge
            updateContactCountBadge();

            // Reset form and pending location
            document.getElementById('contactDetailsForm').reset();
            pendingContactLocation = null;

            // Close modal with proper backdrop cleanup
            closeModalWithCleanup('contactDetailsModal');

            // Success message suppressed during trajectory creation workflow
            // Contact will be saved automatically when trajectory is saved
            console.log('✅ Contact details saved successfully - no success modal shown during trajectory creation');
        }

        // Undo last point
        function undoLastPoint() {
            if (routeCoordinates.length > 0) {
                // Remove last coordinate
                routeCoordinates.pop();
                routePath.setPath(routeCoordinates);

                // Update shadow path
                if (window.shadowPath) {
                    window.shadowPath.setPath(routeCoordinates);
                }

                // Clear all markers
                markers.forEach(marker => marker.setMap(null));
                markers = [];

                // Rebuild markers for remaining coordinates
                if (routeCoordinates.length > 0) {
                    routeCoordinates.forEach((coord, index) => {
                        let markerIcon;
                        if (index === 0) {
                            // Start point - green with floating label
                            markerIcon = {
                                path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                                fillColor: '#22c55e',
                                fillOpacity: 1,
                                strokeColor: '#ffffff',
                                strokeWeight: 2,
                                scale: 0.7,
                                anchor: new google.maps.Point(0, 8),
                                labelOrigin: new google.maps.Point(0, -40)
                            };
                        } else if (index === routeCoordinates.length - 1 && routeCoordinates.length > 1) {
                            // End point - orange with floating label
                            markerIcon = {
                                path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                                fillColor: '#ea580c',
                                fillOpacity: 1,
                                strokeColor: '#ffffff',
                                strokeWeight: 2,
                                scale: 0.7,
                                anchor: new google.maps.Point(0, 8),
                                labelOrigin: new google.maps.Point(0, -40)
                            };
                        } else {
                            // Regular point - blue circle
                            markerIcon = {
                                path: google.maps.SymbolPath.CIRCLE,
                                scale: 9,
                                fillColor: '#0a7ea4',
                                fillOpacity: 1,
                                strokeColor: '#ffffff',
                                strokeWeight: 3
                            };
                        }

                        const marker = new google.maps.Marker({
                            position: { lat: coord.lat(), lng: coord.lng() },
                            map: map,
                            title: index === 0 ? 'Início' : (index === routeCoordinates.length - 1 && routeCoordinates.length > 1 ? 'Fim' : `Ponto ${index + 1}`),
                            icon: markerIcon,
                            label: index === 0 ? {
                                text: 'INÍCIO',
                                fontSize: '9px',
                                fontWeight: 'bold',
                                color: '#ffffff'
                            } : (index === routeCoordinates.length - 1 && routeCoordinates.length > 1 ? {
                                text: 'FIM',
                                fontSize: '9px',
                                fontWeight: 'bold',
                                color: '#ffffff'
                            } : {
                                text: (index + 1).toString(),
                                fontSize: '12px',
                                fontWeight: 'bold',
                                color: '#ffffff'
                            }),
                            zIndex: 100
                        });
                        markers.push(marker);

                        // Add ground markers for start and end points
                        if (index === 0 || (index === routeCoordinates.length - 1 && routeCoordinates.length > 1)) {
                            const groundMarker = new google.maps.Marker({
                                position: { lat: coord.lat(), lng: coord.lng() },
                                map: map,
                                icon: {
                                    path: google.maps.SymbolPath.CIRCLE,
                                    scale: 9,
                                    fillColor: index === 0 ? '#22c55e' : '#ea580c',
                                    fillOpacity: 1,
                                    strokeColor: '#ffffff',
                                    strokeWeight: 2
                                },
                                label: {
                                    text: (index + 1).toString(),
                                    fontSize: '12px',
                                    fontWeight: 'bold',
                                    color: '#ffffff'
                                },
                                zIndex: 50
                            });
                            markers.push(groundMarker);
                        }
                    });
                }

                if (routeCoordinates.length === 0) {
                    document.getElementById('startingAddress').textContent = 'Não definido';
                    window.fullStartingAddress = null;
                }

                updateRouteStats();
                updateButtons();
            }
        }

        // Cancel creation
        function cancelCreate() {
            Swal.fire({
                title: 'Cancelar Criação',
                text: 'Tem certeza que deseja cancelar a criação do relatório? Todos os dados serão perdidos.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sim, cancelar',
                cancelButtonText: 'Continuar criando',
                confirmButtonColor: '#ea580c',
                didOpen: () => {
                    // Ensure SweetAlert2 is above all Bootstrap modals
                    const swalContainer = document.querySelector('.swal2-container');
                    if (swalContainer) {
                        swalContainer.style.zIndex = '1080';
                        console.log('✅ Cancel confirmation modal z-index set to 1080');
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Return to trajectory list instead of redirecting to criar-kmz
                    backToTrajectoryOptions();
                }
            });
        }



        // Proceed to map
        function proceedToMap() {
            // Validate form
            const name = document.getElementById('trajectoryName').value;
            const date = document.getElementById('trajectoryDate').value;
            const time = document.getElementById('trajectoryStartTime').value;
            const observers = document.getElementById('numberOfObservers').value;
            const weather = document.querySelector('input[name="weather_conditions"]:checked');

            // Check for missing fields and build specific error message
            const missingFields = [];
            if (!name) missingFields.push('Nome');
            if (!date) missingFields.push('Data');
            if (!time) missingFields.push('Hora de Início');
            if (!observers) missingFields.push('Número de Observadores');
            if (!weather) missingFields.push('Condições Meteorológicas');

            if (missingFields.length > 0) {
                const fieldsList = missingFields.join(', ');
                Swal.fire({
                    title: 'Campos Obrigatórios',
                    text: `Por favor preencha: ${fieldsList}`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#10b981'
                });
                return;
            }
            
            let weatherCondition = weather.value;
            if (weather.value === 'other') {
                const otherText = document.getElementById('weatherOtherText').value;
                if (!otherText) {
                    Swal.fire({
                        title: 'Especificar Condição',
                        text: 'Por favor especifique a condição meteorológica.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#10b981'
                    });
                    return;
                }
                weatherCondition = `Outro: ${otherText}`;
            }

            // Store setup data
            trajectorySetupData = {
                name: name,
                date: date,
                startTime: time,
                numberOfObservers: parseInt(observers),
                weatherCondition: weatherCondition
            };
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('trajectorySetupModal'));
            modal.hide();
        }

        // Cancel trajectory setup
        function cancelTrajectorySetup() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('trajectorySetupModal'));
            modal.hide();

            // Return to trajectory list instead of redirecting to criar-kmz
            setTimeout(() => {
                backToTrajectoryOptions();
            }, 300);
        }

        // Handle weather condition change
        document.addEventListener('DOMContentLoaded', function() {
            const weatherRadios = document.querySelectorAll('input[name="weather_conditions"]');
            const otherContainer = document.getElementById('weatherOtherInput');

            weatherRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'other') {
                        otherContainer.style.display = 'block';
                    } else {
                        otherContainer.style.display = 'none';
                    }
                });
            });
        });

        // Form submission
        document.getElementById('trajetoForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // CRITICAL FIX: Prevent form submission when Contact Mode is active
            // This prevents duplicate saves when both form submit and contact mode button are triggered
            if (isContactModeActive) {
                console.log('⚠️ Contact Mode is active - form submission blocked to prevent duplicates');
                return;
            }

            // Prevent double submissions
            if (isSaving) {
                console.log('⚠️ Save already in progress, ignoring form submission');
                return;
            }

            // Debounce rapid successive submissions
            const now = Date.now();
            if (now - lastSaveAttempt < 1000) {
                console.log('⚠️ Form submission too soon after previous attempt, ignoring');
                return;
            }

            if (routeCoordinates.length < 2) {
                Swal.fire({
                    title: 'Erro',
                    text: 'É necessário marcar pelo menos 2 pontos para criar um relatório.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#10b981'
                });
                return;
            }

            // Check if trajectory setup data exists (user should have completed setup first)
            if (!trajectorySetupData || !trajectorySetupData.name || !trajectorySetupData.date || !trajectorySetupData.startTime) {
                Swal.fire({
                    title: 'Configuração Necessária',
                    text: 'Por favor configure primeiro as informações do relatório.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#10b981'
                }).then(() => {
                    // Show setup modal if data is missing
                    showTrajectorySetupModal();
                });
                return;
            }

            // Proceed with saving the trajectory
            console.log('📝 Form submission proceeding - Contact Mode is inactive');
            saveTrajectory();
        });

        // Save trajectory
        function saveTrajectory() {
            // CRITICAL FIX: Prevent double submissions with immediate flag setting
            if (isSaving) {
                console.log('⚠️ Save already in progress, ignoring duplicate call');
                return;
            }

            // Debounce rapid successive calls (prevent clicks within 1 second)
            const now = Date.now();
            if (now - lastSaveAttempt < 1000) {
                console.log('⚠️ Save attempt too soon after previous attempt, ignoring');
                return;
            }
            lastSaveAttempt = now;

            // IMMEDIATELY set saving flag to prevent any race conditions
            isSaving = true;
            console.log('🔒 Save operation started - isSaving flag set to true');

            const formData = {
                name: trajectorySetupData.name,
                description: `${trajectorySetupData.name} - Criado em ${trajectorySetupData.date} às ${trajectorySetupData.startTime}`,
                coordinates: routeCoordinates.map(coord => ({
                    lat: coord.lat(),
                    lng: coord.lng()
                })),
                distance: calculateTotalDistance().toFixed(2) + ' km',
                pointsCount: routeCoordinates.length,
                startingAddress: document.getElementById('startingAddress').textContent,
                status: 'draft',
                createdAt: new Date().toISOString(),
                createdBy: '<?php echo $_SESSION["user"]["id"] ?? ""; ?>',
                date: trajectorySetupData.date,
                startTime: trajectorySetupData.startTime,
                weatherCondition: trajectorySetupData.weatherCondition,
                numberOfObservers: trajectorySetupData.numberOfObservers
            };

            // Show loading on both trajectory mode and contact mode buttons
            const saveBtn = document.getElementById('saveTrajetoBtn');
            const saveContactModeBtn = document.getElementById('saveTrajetoContactModeBtn');
            const originalText = saveBtn.innerHTML;
            const originalContactModeText = saveContactModeBtn ? saveContactModeBtn.innerHTML : '';

            // Note: isSaving flag already set at function start to prevent race conditions

            // Disable and show loading animation on both buttons
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> A guardar...';
            saveBtn.style.pointerEvents = 'none'; // Prevent any clicks during save
            saveBtn.style.opacity = '0.8'; // Visual feedback that button is processing

            if (saveContactModeBtn) {
                saveContactModeBtn.disabled = true;
                saveContactModeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> A guardar...';
                saveContactModeBtn.style.pointerEvents = 'none'; // Prevent any clicks during save
                saveContactModeBtn.style.opacity = '0.8'; // Visual feedback that button is processing
            }

            fetch('save_trajectory.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTrajectoryId = data.documentId;
                    console.log('✅ Trajectory saved with ID:', currentTrajectoryId);

                    // Check if contacts were added during trajectory creation
                    if (contactMarkers.length > 0) {
                        console.log('📍 Found', contactMarkers.length, 'contacts added during creation - saving both trajectory and contacts');
                        // Save contacts automatically and finalize
                        saveContactsAndFinalize();
                    } else {
                        console.log('📍 No contacts found - trajectory saved successfully, going directly to success');
                        // No contacts added during creation, go directly to success
                        showSuccessModal();
                    }
                } else {
                    throw new Error(data.message || 'Failed to save trajectory');
                }
            })
            .catch(error => {
                console.error('Error saving trajectory:', error);
                Swal.fire({
                    title: 'Erro',
                    text: 'Erro ao guardar o relatório. Tente novamente.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#10b981'
                });
            })
            .finally(() => {
                // Reset saving state
                isSaving = false;

                // Restore both buttons to original state
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                saveBtn.style.pointerEvents = 'auto'; // Re-enable clicks
                saveBtn.style.opacity = '1'; // Restore full opacity

                if (saveContactModeBtn) {
                    saveContactModeBtn.disabled = false;
                    saveContactModeBtn.innerHTML = originalContactModeText;
                    saveContactModeBtn.style.pointerEvents = 'auto'; // Re-enable clicks
                    saveContactModeBtn.style.opacity = '1'; // Restore full opacity
                }

                // Update button states based on current trajectory state
                updateButtons();
            });
        }

        // Save contacts and finalize (for contacts added during trajectory creation)
        function saveContactsAndFinalize() {
            console.log('💾 Saving contacts added during trajectory creation...');

            const contactsData = contactMarkers.map(contact => {
                // Handle both new contact format (with details) and legacy format
                const details = contact.details || {};
                return {
                    lat: contact.position ? contact.position.lat() : contact.location.lat,
                    lng: contact.position ? contact.position.lng() : contact.location.lng,
                    time: details.time || contact.time || '',
                    circumstance: details.circumstance || contact.circumstance || '',
                    location: details.locationDetail || contact.location || ''
                };
            });

            fetch('save_contacts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    trajectoryId: currentTrajectoryId,
                    contacts: contactsData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Contacts saved successfully');
                    showSuccessModal();
                } else {
                    throw new Error(data.message || 'Failed to save contacts');
                }
            })
            .catch(error => {
                console.error('Error saving contacts:', error);
                Swal.fire({
                    title: 'Erro',
                    text: 'Relatório salvo, mas erro ao guardar os contactos. Tente novamente.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#10b981'
                });
            });
        }



        // Update contacts list
        function updateContactsList() {
            const contactsList = document.getElementById('contactsList');
            const contactCount = document.getElementById('contactCount');
            
            contactCount.textContent = contactMarkers.length;
            
            if (contactMarkers.length === 0) {
                contactsList.innerHTML = '<p class="text-muted">Nenhum contacto adicionado. Clique no mapa para adicionar.</p>';
            } else {
                contactsList.innerHTML = contactMarkers.map((contact, index) => `
                    <div class="contact-item mb-3 p-3 border rounded">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6><i class="fas fa-dove me-1"></i> Contacto ${index + 1}</h6>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    ${contact.position.lat().toFixed(6)}, ${contact.position.lng().toFixed(6)}
                                </p>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeContact(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Hora</label>
                                <input type="time" class="form-control form-control-sm" 
                                       value="${contact.time}" 
                                       onchange="updateContactData(${index}, 'time', this.value)">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Circunstância</label>
                                <select class="form-select form-select-sm" 
                                        onchange="updateContactData(${index}, 'circumstance', this.value)">
                                    <option value="">Selecionar...</option>
                                    <option value="visual" ${contact.circumstance === 'visual' ? 'selected' : ''}>Visual</option>
                                    <option value="auditivo" ${contact.circumstance === 'auditivo' ? 'selected' : ''}>Auditivo</option>
                                    <option value="ambos" ${contact.circumstance === 'ambos' ? 'selected' : ''}>Ambos</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Local</label>
                                <input type="text" class="form-control form-control-sm" 
                                       placeholder="Descrição do local"
                                       value="${contact.location}" 
                                       onchange="updateContactData(${index}, 'location', this.value)">
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }

        // Update contact data
        function updateContactData(index, field, value) {
            if (contactMarkers[index]) {
                contactMarkers[index][field] = value;
            }
        }

        // Remove contact
        function removeContact(index) {
            if (contactMarkers[index]) {
                contactMarkers[index].marker.setMap(null);
                contactMarkers.splice(index, 1);
                updateContactsList();
                updateContactButtons();
            }
        }

        // Clear all contacts
        function clearAllContacts() {
            Swal.fire({
                title: 'Limpar Contactos',
                text: 'Tem certeza que deseja remover todos os contactos?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sim, limpar',
                cancelButtonText: 'Cancelar',
                confirmButtonColor: '#ea580c'
            }).then((result) => {
                if (result.isConfirmed) {
                    contactMarkers.forEach(contact => {
                        contact.marker.setMap(null);
                    });
                    contactMarkers = [];
                    updateContactsList();
                    updateContactButtons();
                }
            });
        }

        // Update contact buttons
        function updateContactButtons() {
            const clearBtn = document.getElementById('clearContactsBtn');
            const proceedBtn = document.getElementById('proceedToFinalizeBtn');
            
            clearBtn.disabled = contactMarkers.length === 0;
            proceedBtn.disabled = false; // Can proceed with or without contacts
        }

        // Proceed to finalize
        function proceedToFinalize() {
            addingContact = false;

            const modal = bootstrap.Modal.getInstance(document.getElementById('contactListModal'));
            modal.hide();
            
            // Update summary
            document.getElementById('summaryDate').textContent = trajectorySetupData.date;
            document.getElementById('summaryTime').textContent = trajectorySetupData.startTime;
            document.getElementById('summaryDistance').textContent = calculateTotalDistance().toFixed(2) + ' km';
            document.getElementById('summaryPoints').textContent = routeCoordinates.length;
            document.getElementById('summaryObservers').textContent = trajectorySetupData.numberOfObservers;
            document.getElementById('summaryWeather').textContent = trajectorySetupData.weatherCondition;
            document.getElementById('summaryContacts').textContent = contactMarkers.length;
            
            const detailsModal = new bootstrap.Modal(document.getElementById('trajectoryDetailsModal'));
            detailsModal.show();
            
            setupPhotoUpload();
        }

        // Back to trajectory
        function backToTrajectory() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactListModal'));
            modal.hide();
            showSuccessModal();
        }

        // Back to contacts
        function backToContacts() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('trajectoryDetailsModal'));
            modal.hide();
            
            addingContact = true;
            const contactModal = new bootstrap.Modal(document.getElementById('contactDetailsModal'));
            contactModal.show();
        }

        // Setup photo upload
        function setupPhotoUpload() {
            const dropzone = document.getElementById('photosDropzone');
            const input = document.getElementById('photosInput');
            const preview = document.getElementById('photosPreview');
            
            // Click to select files
            dropzone.addEventListener('click', () => {
                input.click();
            });
            
            // File input change
            input.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
            
            // Drag and drop
            dropzone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropzone.classList.add('dragover');
            });
            
            dropzone.addEventListener('dragleave', () => {
                dropzone.classList.remove('dragover');
            });
            
            dropzone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropzone.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        }

        // Handle files
        function handleFiles(files) {
            const maxFiles = 6;
            const maxSize = 5 * 1024 * 1024; // 5MB
            
            if (selectedPhotos.length + files.length > maxFiles) {
                Swal.fire({
                    title: 'Limite de Fotos',
                    text: `Máximo ${maxFiles} fotos permitidas.`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#10b981'
                });
                return;
            }
            
            Array.from(files).forEach(file => {
                if (file.size > maxSize) {
                    Swal.fire({
                        title: 'Arquivo Muito Grande',
                        text: `A foto "${file.name}" excede o limite de 5MB.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#10b981'
                    });
                    return;
                }
                
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Tipo de Arquivo Inválido',
                        text: `"${file.name}" não é uma imagem válida.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#10b981'
                    });
                    return;
                }
                
                selectedPhotos.push(file);
                displayPhoto(file, selectedPhotos.length - 1);
            });
        }

        // Display photo
        function displayPhoto(file, index) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('photosPreview');
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-preview-item';
                photoDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Foto ${index + 1}">
                    <button type="button" class="photo-remove-btn" onclick="removePhoto(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                preview.appendChild(photoDiv);
            };
            reader.readAsDataURL(file);
        }

        // Remove photo
        function removePhoto(index) {
            selectedPhotos.splice(index, 1);
            const preview = document.getElementById('photosPreview');
            preview.innerHTML = '';
            selectedPhotos.forEach((file, i) => {
                displayPhoto(file, i);
            });
        }

        // Save trajectory details
        function saveTrajectoryDetails() {
            const saveBtn = document.getElementById('saveDetailsBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> A guardar...';
            
            // Save contacts first if any
            if (contactMarkers.length > 0) {
                const contactsData = contactMarkers.map(contact => ({
                    lat: contact.position.lat(),
                    lng: contact.position.lng(),
                    time: contact.time || '',
                    circumstance: contact.circumstance || '',
                    location: contact.location || ''
                }));
                
                fetch('save_contacts.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        trajectoryId: currentTrajectoryId,
                        contacts: contactsData
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        finalizeSave();
                    } else {
                        throw new Error(data.message || 'Failed to save contacts');
                    }
                })
                .catch(error => {
                    console.error('Error saving contacts:', error);
                    Swal.fire({
                        title: 'Erro',
                        text: 'Erro ao guardar os contactos. Tente novamente.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#10b981',
                        didOpen: () => {
                            // Ensure SweetAlert2 is above all Bootstrap modals
                            const swalContainer = document.querySelector('.swal2-container');
                            if (swalContainer) {
                                swalContainer.style.zIndex = '1080';
                                console.log('✅ Contact save error modal z-index set to 1080');
                            }
                        }
                    });
                })
                .finally(() => {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = originalText;
                });
            } else {
                finalizeSave();
            }
        }

        // Finalize save
        function finalizeSave() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('trajectoryDetailsModal'));
            modal.hide();
            showSuccessModal();
        }

        // Show success modal
        function showSuccessModal() {
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
        }

        // Go to trajectories
        function goToTrajectories() {
            // Close the success modal and return to trajectory list
            const modal = bootstrap.Modal.getInstance(document.getElementById('successModal'));
            if (modal) {
                modal.hide();
            }

            setTimeout(() => {
                backToTrajectoryOptions();
                // Reload the page to refresh the trajectory list with the new trajectory
                // Add cache-busting parameter to ensure fresh data load
                // Increased timeout to ensure cache invalidation completes
                window.location.href = window.location.pathname + '?refresh=' + Date.now();
            }, 500);
        }

        // Create another
        function createAnother() {
            // Add cache-busting parameter to ensure fresh data load
            window.location.href = window.location.pathname + '?refresh=' + Date.now();
        }

        // Show help
        function showRelatorioHelp() {
            const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
            helpModal.show();
            console.log('📖 Help modal opened - DOM placement fix applied');
        }

        // Function to clear all modal form data
        function clearAllModalData() {
            console.log('🧹 Clearing all modal form data...');

            // Clear trajectory setup modal
            const trajectoryNameInput = document.getElementById('trajectoryName');
            if (trajectoryNameInput) {
                trajectoryNameInput.value = '';
            }

            // Clear date and time inputs (they will be reset to current values when modal opens)
            const dateInput = document.getElementById('trajectoryDate');
            if (dateInput) {
                dateInput.value = '';
            }

            const timeInput = document.getElementById('trajectoryStartTime');
            if (timeInput) {
                timeInput.value = '';
            }

            // Reset weather conditions to default (none selected)
            const weatherRadios = document.querySelectorAll('input[name="weather_conditions"]');
            weatherRadios.forEach(radio => {
                radio.checked = false;
            });

            // Hide and clear the "other" weather input
            const otherContainer = document.getElementById('weatherOtherInput');
            const otherInput = document.getElementById('weatherOtherText');
            if (otherContainer) {
                otherContainer.style.display = 'none';
            }
            if (otherInput) {
                otherInput.value = '';
            }

            // Reset number of observers to default (will be set to 1 when modal opens)
            const observersInput = document.getElementById('numberOfObservers');
            if (observersInput) {
                observersInput.value = '';
            }

            console.log('✅ All modal form data cleared');
        }

        // Navigation function to start manual trajectory creation
        function startManualTrajectoryCreation() {
            console.log('🚀 startManualTrajectoryCreation called');

            // Find the button to provide user feedback
            const addButton = document.querySelector('button[onclick="startManualTrajectoryCreation()"]');

            // CRITICAL FIX: Check if map is fully initialized before proceeding
            if (!isMapReady()) {
                console.log('⚠️ Map not fully initialized yet - waiting for map to load...');
                console.log('  - mapInitialized:', mapInitialized);
                console.log('  - map:', !!map);
                console.log('  - routePath:', !!routePath);

                // Show loading state on button
                if (addButton) {
                    const originalText = addButton.innerHTML;
                    addButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A carregar mapa...';
                    addButton.disabled = true;

                    // Wait for map to initialize, then try again
                    setTimeout(() => {
                        if (isMapReady()) {
                            console.log('✅ Map now fully available - proceeding with trajectory creation');
                            addButton.innerHTML = originalText;
                            addButton.disabled = false;
                            startManualTrajectoryCreation();
                        } else {
                            console.error('❌ Map still not fully available after timeout');
                            addButton.innerHTML = originalText;
                            addButton.disabled = false;
                            Swal.fire({
                                title: 'Erro',
                                text: 'O mapa ainda está a carregar. Tente novamente em alguns segundos.',
                                icon: 'warning',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#10b981'
                            });
                        }
                    }, 1000);
                } else {
                    // Fallback without button feedback
                    setTimeout(() => {
                        if (isMapReady()) {
                            console.log('✅ Map now fully available - proceeding with trajectory creation');
                            startManualTrajectoryCreation();
                        } else {
                            console.error('❌ Map still not fully available after timeout');
                            Swal.fire({
                                title: 'Erro',
                                text: 'O mapa ainda está a carregar. Tente novamente em alguns segundos.',
                                icon: 'warning',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#10b981'
                            });
                        }
                    }, 1000);
                }
                return;
            }

            // Clear any previous trajectory data to ensure clean slate
            console.log('🧹 Clearing previous trajectory data...');
            clearRoute();

            // Clear all modal form data
            clearAllModalData();

            // Reset all trajectory-related variables
            routeCoordinates = [];
            contactMarkers = [];
            trajectorySetupData = {};
            currentTrajectoryId = null;
            selectedPhotos = [];
            pendingContactLocation = null;
            contactCount = 0;

            // Ensure contact mode is disabled
            if (isContactModeActive) {
                isContactModeActive = false;
                const contactIndicator = document.getElementById('contactModeIndicator');
                if (contactIndicator) {
                    contactIndicator.style.display = 'none';
                }
            }

            console.log('✅ Previous trajectory data cleared - starting fresh');

            // Destroy DataTables before hiding the list
            if ($.fn.DataTable.isDataTable('#trajetosTable')) {
                $('#trajetosTable').DataTable().destroy();
                console.log('🗂️ DataTables destroyed before switching to creation interface');
            }

            // Add fade out effect to trajectory list and stats grid
            const trajectoryList = document.querySelector('.reports-table-card');
            const statsGrid = document.querySelector('.stats-grid');

            if (trajectoryList) {
                trajectoryList.style.transition = 'opacity 0.3s ease-out';
                trajectoryList.style.opacity = '0';
            }

            if (statsGrid) {
                statsGrid.style.transition = 'opacity 0.3s ease-out';
                statsGrid.style.opacity = '0';
            }

            setTimeout(() => {
                if (trajectoryList) {
                    trajectoryList.style.display = 'none';
                }

                if (statsGrid) {
                    statsGrid.style.display = 'none';
                }

                // Show the trajectory creation interface with fade in
                const creationInterface = document.getElementById('trajectoryCreationInterface');
                if (creationInterface) {
                    creationInterface.style.display = 'block';
                    creationInterface.style.opacity = '0';
                    creationInterface.style.transition = 'opacity 0.3s ease-in';

                    // Prevent body scroll when trajectory creation is active
                    document.body.style.overflow = 'hidden';

                    // Adjust content padding to remove bottom padding
                    const contentElement = document.querySelector('.content');
                    if (contentElement) {
                        contentElement.style.paddingBottom = '0';
                    }

                    setTimeout(() => {
                        creationInterface.style.opacity = '1';

                        // Trigger map resize after interface is visible to ensure proper height calculation
                        if (map && typeof google !== 'undefined' && google.maps) {
                            setTimeout(() => {
                                google.maps.event.trigger(map, 'resize');
                                console.log('🗺️ Map resize triggered for dynamic height');
                            }, 100);
                        }
                    }, 50);
                }

                // Update page title
                const pageTitle = document.querySelector('.page-title');
                if (pageTitle) {
                    pageTitle.innerHTML = '<i class="fas fa-route"></i> Criar Relatório Manual';
                }

                // Initialize the map and show location choice modal
                setTimeout(() => {
                    showLocationChoiceModal();
                }, 300);
            }, 300);
        }

        // Make function globally accessible
        window.startManualTrajectoryCreation = startManualTrajectoryCreation;

        // Function to go back to trajectory list
        function backToTrajectoryOptions() {
            // Clear contact mode state when returning to trajectory list
            if (isContactModeActive) {
                isContactModeActive = false;
                const contactIndicator = document.getElementById('contactModeIndicator');
                if (contactIndicator) {
                    contactIndicator.style.display = 'none';
                }
                console.log('✅ Contact mode cleared when returning to trajectory list');
            }

            // Reset other contact-related variables
            addingContact = false;
            isContactMode = false;

            // Clear all modal form data to ensure clean state for next creation
            clearAllModalData();

            // Add fade out effect to creation interface
            const creationInterface = document.getElementById('trajectoryCreationInterface');
            if (creationInterface) {
                creationInterface.style.transition = 'opacity 0.3s ease-out';
                creationInterface.style.opacity = '0';

                setTimeout(() => {
                    creationInterface.style.display = 'none';

                    // Restore body scroll when returning to list
                    document.body.style.overflow = '';

                    // Restore content padding
                    const contentElement = document.querySelector('.content');
                    if (contentElement) {
                        contentElement.style.paddingBottom = '';
                    }

                    // Show the trajectory list and stats grid with fade in
                    const trajectoryList = document.querySelector('.reports-table-card');
                    const statsGrid = document.querySelector('.stats-grid');

                    if (trajectoryList) {
                        trajectoryList.style.display = 'block';
                        trajectoryList.style.opacity = '0';
                        trajectoryList.style.transition = 'opacity 0.3s ease-in';

                        setTimeout(() => {
                            trajectoryList.style.opacity = '1';

                            // Reinitialize DataTables after showing the list
                            setTimeout(() => {
                                initializeDataTables();
                            }, 100);
                        }, 50);
                    }

                    if (statsGrid) {
                        statsGrid.style.display = 'grid';
                        statsGrid.style.opacity = '0';
                        statsGrid.style.transition = 'opacity 0.3s ease-in';

                        setTimeout(() => {
                            statsGrid.style.opacity = '1';
                        }, 50);
                    }

                    // Update page title back to original
                    const pageTitle = document.querySelector('.page-title');
                    if (pageTitle) {
                        pageTitle.innerHTML = '<i class="fas fa-plus-circle"></i> Relatórios Manuais';
                    }
                }, 300);
            }
        }

        // Make function globally accessible
        window.backToTrajectoryOptions = backToTrajectoryOptions;

        // Make other key functions globally accessible
        window.showRelatorioHelp = showRelatorioHelp;
        window.viewTrajectory = viewTrajectory;
        window.deleteTrajectory = deleteTrajectory;
        window.toggleContactMode = toggleContactMode;
        window.undoLastPoint = undoLastPoint;
        window.cancelCreate = cancelCreate;
        window.showLocationChoiceModal = showLocationChoiceModal;
        window.proceedToMap = proceedToMap;
        window.clearAllModalData = clearAllModalData;
        window.cancelTrajectorySetup = cancelTrajectorySetup;

        // Add other functions that are called from onclick handlers
        window.deleteLastContact = deleteLastContact;
        window.cancelContactPlacement = cancelContactPlacement;
        window.confirmContactPlacement = confirmContactPlacement;
        window.cancelContactDetails = cancelContactDetails;
        window.saveContactDetails = saveContactDetails;
        window.clearAllContacts = clearAllContacts;
        window.backToTrajectory = backToTrajectory;
        window.proceedToFinalize = proceedToFinalize;
        window.backToContacts = backToContacts;
        window.saveTrajectoryDetails = saveTrajectoryDetails;
        window.goToTrajectories = goToTrajectories;
        window.createAnother = createAnother;
        window.cancelTimeSelection = cancelTimeSelection;
        window.removeContact = removeContact;
        window.removePhoto = removePhoto;
        window.handleLocationSearchEnter = handleLocationSearchEnter;

        // Function to redirect to KMZ creation page
        function redirectToKmzCreation() {
            window.location.href = '../criar-kmz/';
        }

        // Function to view trajectory details
        function viewTrajectory(trajectoryId) {
            console.log('Viewing trajectory:', trajectoryId);
            // Redirect to trajectory viewing page in the same directory
            window.location.href = 'view-trajectory.php?id=' + encodeURIComponent(trajectoryId);
        }

        // Function to delete trajectory
        function deleteTrajectory(trajectoryId) {
            Swal.fire({
                title: 'Eliminar Relatório',
                text: 'Tem a certeza que pretende eliminar este relatório? Esta ação não pode ser desfeita.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Sim, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'A eliminar...',
                        text: 'Por favor aguarde enquanto o relatório é eliminado.',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    console.log('Deleting trajectory:', trajectoryId);

                    // Send delete request to server
                    fetch('delete_trajectory.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ documentId: trajectoryId })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'Eliminado!',
                                text: data.message || 'O relatório foi eliminado com sucesso.',
                                icon: 'success',
                                confirmButtonColor: '#0a7ea4',
                                timer: 2000, // Auto-close after 2 seconds
                                timerProgressBar: true, // Show progress bar
                                showConfirmButton: true, // Keep OK button for manual close
                                allowOutsideClick: false // Prevent closing by clicking outside
                            }).then(() => {
                                // Reload page to refresh the list
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Erro',
                                text: data.message || 'Ocorreu um erro ao eliminar o relatório.',
                                icon: 'error',
                                confirmButtonColor: '#dc3545'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting trajectory:', error);
                        Swal.fire({
                            title: 'Erro',
                            text: 'Ocorreu um erro ao eliminar o relatório. Por favor tente novamente.',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    });
                }
            });
        }

        // Date picker functionality
        function initializeDatePicker() {
            const dateInput = document.getElementById('trajectoryDate');
            const datePickerBtn = document.getElementById('datePickerBtn');
            const datePickerModal = new bootstrap.Modal(document.getElementById('datePickerModal'));
            const daySelect = document.getElementById('daySelect');
            const monthSelect = document.getElementById('monthSelect');
            const yearSelect = document.getElementById('yearSelect');
            const selectedDateDisplay = document.getElementById('selectedDateDisplay');
            const confirmDateBtn = document.getElementById('confirmDateBtn');
            const cancelDateBtn = document.getElementById('cancelDateBtn');

            // Populate day options
            for (let i = 1; i <= 31; i++) {
                const option = document.createElement('option');
                option.value = String(i).padStart(2, '0');
                option.textContent = String(i).padStart(2, '0');
                daySelect.appendChild(option);
            }

            // Populate year options (current year ± 5 years)
            const currentYear = new Date().getFullYear();
            for (let i = currentYear - 5; i <= currentYear + 5; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                yearSelect.appendChild(option);
            }

            // Set current date as default
            const today = new Date();
            daySelect.value = String(today.getDate()).padStart(2, '0');
            monthSelect.value = String(today.getMonth() + 1).padStart(2, '0');
            yearSelect.value = today.getFullYear();
            updateSelectedDateDisplay();

            // Event listeners for date picker
            if (dateInput) {
                dateInput.addEventListener('click', () => datePickerModal.show());
            }
            if (datePickerBtn) {
                datePickerBtn.addEventListener('click', () => datePickerModal.show());
            }

            // Update display when selections change
            [daySelect, monthSelect, yearSelect].forEach(select => {
                select.addEventListener('change', updateSelectedDateDisplay);
            });

            // Confirm date selection
            if (confirmDateBtn) {
                confirmDateBtn.addEventListener('click', () => {
                    const day = daySelect.value;
                    const month = monthSelect.value;
                    const year = yearSelect.value;

                    if (day && month && year) {
                        dateInput.value = `${day}-${month}-${year}`;
                        datePickerModal.hide();
                    }
                });
            }

            // Cancel date selection
            if (cancelDateBtn) {
                cancelDateBtn.addEventListener('click', () => {
                    datePickerModal.hide();
                });
            }

            function updateSelectedDateDisplay() {
                const day = daySelect.value || '--';
                const month = monthSelect.value || '--';
                const year = yearSelect.value || '----';
                selectedDateDisplay.textContent = `${day}/${month}/${year}`;
            }
        }

        // Initialize DataTables and page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize date picker
            initializeDatePicker();

            // Initialize DataTables for trajectory list with error handling
            initializeDataTables();

            // Add event listener for contact mode save button to prevent double submissions
            const saveContactModeBtn = document.getElementById('saveTrajetoContactModeBtn');
            if (saveContactModeBtn) {
                saveContactModeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // CRITICAL FIX: Only allow contact mode button when Contact Mode is actually active
                    if (!isContactModeActive) {
                        console.log('⚠️ Contact Mode is not active - contact mode button click blocked');
                        return;
                    }

                    // Prevent double submissions
                    if (isSaving) {
                        console.log('⚠️ Save already in progress, ignoring contact mode button click');
                        return;
                    }

                    // Debounce rapid successive clicks
                    const now = Date.now();
                    if (now - lastSaveAttempt < 1000) {
                        console.log('⚠️ Contact mode button clicked too soon after previous attempt, ignoring');
                        return;
                    }

                    // Immediately disable both buttons to prevent race conditions
                    const mainSaveBtn = document.getElementById('saveTrajetoBtn');
                    if (mainSaveBtn) mainSaveBtn.disabled = true;
                    saveContactModeBtn.disabled = true;

                    console.log('📝 Contact mode button proceeding - Contact Mode is active');
                    saveTrajectory();
                });
            }

            console.log('🚀 Page loaded - trajectory list initialized');
        });

        // Function to validate table structure
        function validateTableStructure() {
            const table = document.getElementById('trajetosTable');
            if (!table) {
                console.warn('⚠️ Table element not found');
                return false;
            }

            const thead = table.querySelector('thead');
            const tbody = table.querySelector('tbody');

            if (!thead || !tbody) {
                console.warn('⚠️ Table missing thead or tbody');
                return false;
            }

            const headerCells = thead.querySelectorAll('th').length;
            const bodyRows = tbody.querySelectorAll('tr');

            console.log(`📊 Table validation: ${headerCells} header cells, ${bodyRows.length} body rows`);

            // Check if all body rows have the correct number of cells
            for (let i = 0; i < bodyRows.length; i++) {
                const row = bodyRows[i];
                const cells = row.querySelectorAll('td');

                console.log(`📊 Row ${i + 1}: ${cells.length} cells`);

                if (cells.length !== headerCells) {
                    console.warn(`⚠️ Row ${i + 1} has ${cells.length} cells but header has ${headerCells} cells`);
                    return false;
                }
            }

            console.log('✅ Table structure validation passed');
            return true;
        }

        // Function to initialize DataTables with proper error handling
        function initializeDataTables() {
            const table = document.getElementById('trajetosTable');
            if (!table || table.style.display === 'none') {
                console.log('📋 Table not visible, skipping DataTables initialization');
                return;
            }

            try {
                // Check if table has actual data (not just empty state)
                const tbody = table.querySelector('tbody');
                const rows = tbody.querySelectorAll('tr');
                const hasData = rows.length > 0 && !rows[0].querySelector('td').textContent.includes('Nenhum relatório criado ainda');

                if (!hasData) {
                    console.log('📋 Table is empty, skipping DataTables initialization');
                    return;
                }

                // Validate table structure
                if (!validateTableStructure()) {
                    console.warn('⚠️ Table structure validation failed - skipping DataTables initialization');
                    return;
                }

                // Check if DataTable is already initialized
                if ($.fn.DataTable.isDataTable('#trajetosTable')) {
                    $('#trajetosTable').DataTable().destroy();
                    console.log('🗂️ Existing DataTable destroyed');
                }

                // Initialize DataTable with loading system
                initializeDataTableWithLoading('#trajetosTable', {
                    order: [[1, 'desc']], // Sort by date column in descending order
                    columnDefs: [
                        {
                            className: 'dt-center',
                            targets: '_all'
                        },
                        {
                            orderable: false,
                            targets: [-1] // Make only actions column non-sortable
                        }
                    ],
                    drawCallback: function(settings) {
                        $(this).find('td, th').addClass('dt-center');
                        console.log('✅ DataTables draw completed successfully');
                    }
                });

                console.log('✅ DataTables initialized successfully');
            } catch (error) {
                console.error('❌ DataTables initialization failed:', error);
                console.log('📋 Showing table without DataTables features');
            }
        }


        
        // Custom tooltip functionality - EXACT MATCH TO REGULAR REPORTS
        let tooltip = null;
        
        function showTooltip(element, text) {
            let tooltip = document.querySelector('.custom-tooltip');
            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip';
                document.body.appendChild(tooltip);
            }
            
            // Set text
            tooltip.textContent = text;
            
            // Get button position
            const rect = element.getBoundingClientRect();
            const scrollTop = window.pageYOffset;
            const scrollLeft = window.pageXOffset;
            
            // Simple positioning - above the button
            const left = rect.left + scrollLeft + (rect.width / 2) - 50; // Assume 100px tooltip width
            const top = rect.top + scrollTop - 40; // 40px above button
            
            // Set position
            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
            tooltip.style.visibility = 'visible';
            tooltip.style.display = 'block';
            
            // Force show
            setTimeout(() => {
                tooltip.classList.add('show');
            }, 10);
        }
        
        function hideTooltip() {
            const tooltip = document.querySelector('.custom-tooltip');
            if (tooltip) {
                tooltip.classList.remove('show');
            }
        }
        
        // Handle PDF confirmation
        document.addEventListener('DOMContentLoaded', function() {

            
            // Initialize custom tooltips after DataTable is ready - EXACT MATCH TO REGULAR REPORTS
            setTimeout(function() {
                // Add tooltip event listeners ONLY to table buttons with data-tooltip
                $(document).on('mouseenter', '#trajetosTable [data-tooltip]', function() {
                    const tooltipText = $(this).attr('data-tooltip');
                    if (tooltipText) {
                        showTooltip(this, tooltipText);
                    }
                });
                
                $(document).on('mouseleave', '#trajetosTable [data-tooltip]', function() {
                    hideTooltip();
                });
            }, 500);
        });

        // FINAL TEST - This should appear if the entire script loads successfully
        console.log('🎉 SCRIPT LOADED COMPLETELY - All functions should be available');
        console.log('🧪 Available test functions: openContactModal(), debugContactModal(), testTimePicker()');

        // Test when page is fully loaded
        window.addEventListener('load', function() {
            console.log('🌐 PAGE FULLY LOADED - DOM and all resources ready');
            console.log('🧪 You can now test: openContactModal()');

            // Check for modal ID conflicts
            const contactDetailsModals = document.querySelectorAll('#contactDetailsModal');
            const contactListModals = document.querySelectorAll('#contactListModal');
            console.log('🔍 Modal ID check:');
            console.log('  - contactDetailsModal elements found:', contactDetailsModals.length);
            console.log('  - contactListModal elements found:', contactListModals.length);

            if (contactDetailsModals.length === 1 && contactListModals.length === 1) {
                console.log('✅ Modal ID conflict resolved!');
            } else {
                console.error('❌ Modal ID conflict still exists!');
            }

            // Test contact mode functionality
            console.log('🧪 Testing contact mode functions...');
            console.log('  - toggleContactMode:', typeof toggleContactMode);
            console.log('  - updateContactCountBadge:', typeof updateContactCountBadge);
            console.log('  - saveContactsAndFinalize:', typeof saveContactsAndFinalize);
            console.log('  - isContactModeActive:', isContactModeActive);
            console.log('  - contactMarkers array:', contactMarkers.length, 'contacts');
        });
    </script>
</body>
</html> 