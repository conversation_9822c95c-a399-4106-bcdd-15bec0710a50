#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  function calculateSize(currentPath) {
    const stats = fs.lstatSync(currentPath);
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    } else {
      totalSize += stats.size;
    }
  }
  
  try {
    calculateSize(dirPath);
  } catch (error) {
    // Skip directories we can't access
  }
  
  return totalSize;
}

function formatBytes(bytes) {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function analyzeNodeModules() {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('No node_modules folder found');
    return;
  }
  
  console.log('🔍 Analyzing node_modules sizes...\n');
  
  const directories = fs.readdirSync(nodeModulesPath)
    .filter(item => {
      const itemPath = path.join(nodeModulesPath, item);
      return fs.lstatSync(itemPath).isDirectory();
    })
    .map(dir => {
      const dirPath = path.join(nodeModulesPath, dir);
      const size = getDirectorySize(dirPath);
      return { name: dir, size, formattedSize: formatBytes(size) };
    })
    .sort((a, b) => b.size - a.size);
  
  console.log('📊 Top 20 largest packages:');
  console.log('=' .repeat(60));
  
  directories.slice(0, 20).forEach((dir, index) => {
    console.log(`${(index + 1).toString().padStart(2)}. ${dir.name.padEnd(40)} ${dir.formattedSize.padStart(10)}`);
  });
  
  const totalSize = directories.reduce((sum, dir) => sum + dir.size, 0);
  console.log('\n' + '='.repeat(60));
  console.log(`📦 Total node_modules size: ${formatBytes(totalSize)}`);
  
  // Find potential duplicates
  console.log('\n🔍 Potential optimizations:');
  console.log('=' .repeat(60));
  
  const largePackages = directories.filter(dir => dir.size > 50 * 1024 * 1024); // > 50MB
  if (largePackages.length > 0) {
    console.log('📈 Packages larger than 50MB:');
    largePackages.forEach(pkg => {
      console.log(`   • ${pkg.name}: ${pkg.formattedSize}`);
    });
  }
}

if (require.main === module) {
  analyzeNodeModules();
}

module.exports = { analyzeNodeModules }; 