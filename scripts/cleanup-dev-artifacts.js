#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function deleteDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    return true;
  }
  return false;
}

function deleteFile(filePath) {
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    return true;
  }
  return false;
}

function cleanupNodeModules() {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('No node_modules folder found');
    return;
  }
  
  console.log('🧹 Cleaning up node_modules...\n');
  
  let totalCleaned = 0;
  
  // Directories to remove (common dev artifacts)
  const dirsToRemove = [
    'test',
    'tests',
    '__tests__',
    'spec',
    'specs',
    'example',
    'examples',
    'demo',
    'demos',
    'docs',
    'doc',
    'documentation',
    'website',
    'bench',
    'benchmark',
    'benchmarks',
    '.github',
    '.vscode',
    'coverage',
    '.nyc_output'
  ];
  
  // Files to remove
  const filesToRemove = [
    'README.md',
    'CHANGELOG.md',
    'HISTORY.md',
    'LICENSE',
    'LICENSE.txt',
    'CONTRIBUTING.md',
    '.eslintrc.js',
    '.eslintrc.json',
    '.prettierrc',
    'jest.config.js',
    'webpack.config.js',
    'rollup.config.js',
    'tsconfig.json'
  ];
  
  function walkDirectory(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stats = fs.lstatSync(itemPath);
        
        if (stats.isDirectory()) {
          // Remove specific dev directories
          if (dirsToRemove.includes(item)) {
            if (deleteDirectory(itemPath)) {
              console.log(`🗑️  Removed directory: ${itemPath.replace(process.cwd(), '.')}`);
              totalCleaned++;
            }
          } else {
            // Recursively walk other directories
            walkDirectory(itemPath);
          }
        } else if (stats.isFile()) {
          // Remove specific dev files
          if (filesToRemove.includes(item)) {
            if (deleteFile(itemPath)) {
              console.log(`🗑️  Removed file: ${itemPath.replace(process.cwd(), '.')}`);
              totalCleaned++;
            }
          }
        }
      });
    } catch (error) {
      // Skip directories we can't access
    }
  }
  
  walkDirectory(nodeModulesPath);
  
  console.log(`\n✅ Cleanup complete! Removed ${totalCleaned} items.`);
  console.log('💡 Tip: Run "npm run analyze-size" to see the new size.');
}

if (require.main === module) {
  cleanupNodeModules();
}

module.exports = { cleanupNodeModules }; 