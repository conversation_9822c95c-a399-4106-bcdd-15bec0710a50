<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Não autorizado']);
    exit();
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // Basic validation
    if (empty($name)) {
        http_response_code(400);
        header('Content-Type: application/json');
        echo json_encode(['error' => 'O nome é obrigatório']);
        exit();
    }
    
    // Password validation if changing password
    if (!empty($newPassword)) {
        if (empty($currentPassword)) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode(['error' => 'A palavra-passe atual é obrigatória']);
            exit();
        }
        
        if (strlen($newPassword) < 6) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode(['error' => 'A nova palavra-passe deve ter pelo menos 6 caracteres']);
            exit();
        }
        
        if ($newPassword !== $confirmPassword) {
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode(['error' => 'As palavras-passe não coincidem']);
            exit();
        }
    }
    
    try {
        // For now, just simulate success and update session
        $_SESSION['user']['name'] = $name;
        
        // In a real implementation, you would:
        // 1. Verify current password against Firebase/database
        // 2. Update user profile in Firebase/database
        // 3. Update password if provided
        
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Perfil atualizado com sucesso']);
        
    } catch (Exception $e) {
        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Erro interno do servidor']);
    }
} else {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Método não permitido']);
}
?> 