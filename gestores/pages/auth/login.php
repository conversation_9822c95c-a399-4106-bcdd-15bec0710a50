<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if system is in maintenance mode (unless admin mode is enabled)
$adminMode = isset($_GET['mode']) && $_GET['mode'] === 'admin';
if (!$adminMode) {
    $maintenanceMessage = checkMaintenanceMode();
    if ($maintenanceMessage) {
        // System is in maintenance mode - show maintenance page
        ob_end_clean(); // Clear any output buffer
        showMaintenancePage($maintenanceMessage);
        exit();
    }
}

// Check if we have an error message
$error = '';
$success = '';
$error_type = 'general';
$current_mode = 'login'; // login, nif_validation, email_validation, register

// Debug mode check (only show debug info if explicitly requested)
$debug_mode = isset($_GET['debug']) && $_GET['debug'] === '1';

// Only set mode from GET parameter if it's not the admin bypass
if (isset($_GET['mode']) && $_GET['mode'] !== 'admin') {
    $current_mode = $_GET['mode'];
}

if (isset($_GET['expired'])) {
    $error = 'A sua sessão expirou. Por favor, inicie sessão novamente.';
    $error_type = 'expired';
} elseif (isset($_GET['unauthorized'])) {
    $error = 'Não tem permissão para aceder a esta área.';
    $error_type = 'unauthorized';
}

// Function to validate NIF against zonas de caça
function validateNIFInZonas($nif) {
    global $database;
    
    try {
        // Get admin token for accessing Firestore without user authentication
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception("Failed to get admin access token");
        }
        $database->setAccessToken($adminToken);
        
        // Use Firestore query to filter by NIF directly (same approach as other gestores pages)
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $nif);
        $foundZones = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $foundZones[] = [
                    'zone' => $data,
                    'docId' => $docId
                ];
            }
        }
        
        if (count($foundZones) > 0) {
            return [
                'found' => true,
                'zones' => $foundZones,
                'count' => count($foundZones)
            ];
        }
        
        return ['found' => false];
    } catch (Exception $e) {
        error_log("NIF validation error: " . $e->getMessage());
        return ['found' => false, 'error' => $e->getMessage()];
    }
}

// Function to update zone status to registered
function markZoneAsRegistered($docId, $userId, $userName) {
    global $database;
    
    try {
        // Get admin token for accessing Firestore
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception("Failed to get admin access token");
        }
        $database->setAccessToken($adminToken);
        
        // Get current zone data
        $currentZone = $database->getDocument('zonasCaca', $docId);
        
        // Update with registration info
        $updateData = array_merge($currentZone, [
            'status' => 'registered',
            'registeredBy' => $userName,
            'registeredAt' => date('c'),
            'userId' => $userId
        ]);
        
        return $database->setDocument('zonasCaca', $docId, $updateData);
    } catch (Exception $e) {
        error_log("Zone registration error: " . $e->getMessage());
        return false;
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? 'login';
    
    if ($action === 'validate_nif') {
        // Step 1: Validate NIF
        $nif = $_POST['nif'] ?? '';
        
        // Clear any previous session data for fresh start
        unset($_SESSION['temp_zones']);
        unset($_SESSION['temp_zone']);
        unset($_SESSION['temp_zone_id']);
        unset($_SESSION['temp_nif']);
        unset($_SESSION['temp_email']);
        unset($_SESSION['email_zones']);
        unset($_SESSION['email_selected']);
        unset($_SESSION['email_verification_sent']);
        unset($_SESSION['temp_user_id']);
        unset($_SESSION['temp_password']);
        unset($_SESSION['using_password_reset']);
        
        if (empty($nif)) {
            $error = 'Por favor, introduza o NIF da entidade gestora.';
            $error_type = 'validation';
            $current_mode = 'nif_validation';
        } else {
            $nifResult = validateNIFInZonas($nif);
            
            if (isset($nifResult['error'])) {
                $error = 'Erro ao validar NIF: ' . $nifResult['error'];
                $error_type = 'system_error';
                $current_mode = 'nif_validation';
            } elseif (!$nifResult['found']) {
                $error = 'NIF não encontrado nas zonas de caça registadas. Verifique o número ou contacte o administrador.';
                $error_type = 'validation';
                $current_mode = 'nif_validation';
            } else {
                $zones = $nifResult['zones'];
                $availableZones = [];
                
                // DEBUG: Log all found zones
                error_log("DEBUG: Found " . count($zones) . " zones for NIF $nif");
                foreach ($zones as $index => $zoneData) {
                    $zone = $zoneData['zone'];
                    error_log("DEBUG: Zone $index - ID: " . $zoneData['docId'] . ", Name: " . ($zone['nomeZona'] ?? 'N/A') . ", Email: " . ($zone['email'] ?? 'N/A') . ", Status: " . ($zone['status'] ?? 'not set'));
                }
                
                // Filter out already registered zones
                foreach ($zones as $zoneData) {
                    $zone = $zoneData['zone'];
                    if (!isset($zone['status']) || $zone['status'] !== 'registered') {
                        $availableZones[] = $zoneData;
                        error_log("DEBUG: Zone " . $zoneData['docId'] . " is available (status: " . ($zone['status'] ?? 'not set') . ")");
                    } else {
                        error_log("DEBUG: Zone " . $zoneData['docId'] . " is already registered, skipping");
                    }
                }
                
                error_log("DEBUG: Available zones after filtering: " . count($availableZones));
                
                if (empty($availableZones)) {
                    $error = 'Todas as zonas de caça associadas a este NIF já foram registadas.';
                    $error_type = 'validation';
                    $current_mode = 'nif_validation';
                } else {
                    // Show zones info and proceed to email validation with first available zone
                    $_SESSION['temp_zones'] = $availableZones;
                    $_SESSION['temp_zone'] = $availableZones[0]['zone'];
                    $_SESSION['temp_zone_id'] = $availableZones[0]['docId'];
                    $_SESSION['temp_nif'] = $nif;
                    $current_mode = 'zones_info';
                    
                    error_log("DEBUG: Stored " . count($availableZones) . " zones in session");
                }
            }
        }
    } elseif ($action === 'continue_to_email') {
        // Continue from zones info to email validation
        $current_mode = 'email_validation';
        
        // Clear previous email verification session to ensure fresh start
        unset($_SESSION['email_verification_sent']);
        unset($_SESSION['temp_user_id']);
        unset($_SESSION['temp_password']);
        unset($_SESSION['using_password_reset']);
        
        // Group all available zones by email
        $availableZones = $_SESSION['temp_zones'] ?? [];
        $zonesByEmail = [];
        
        error_log("DEBUG: continue_to_email - Processing " . count($availableZones) . " available zones");
        
        foreach ($availableZones as $zoneData) {
            $zone = $zoneData['zone'];
            $email = $zone['email'] ?? '';
            error_log("DEBUG: Processing zone " . $zoneData['docId'] . " with email: $email");
            if ($email) {
                if (!isset($zonesByEmail[$email])) {
                    $zonesByEmail[$email] = [];
                }
                $zonesByEmail[$email][] = [
                    'nomeZona' => $zone['nomeZona'] ?? '',
                    'docId' => $zoneData['docId']
                ];
            }
        }
        
        error_log("DEBUG: Grouped zones by email: " . json_encode(array_keys($zonesByEmail)));
        foreach ($zonesByEmail as $email => $zones) {
            error_log("DEBUG: Email $email has " . count($zones) . " zones: " . json_encode(array_column($zones, 'nomeZona')));
        }
        
        // Pick the first email group (for now, as only one is selectable in UI)
        $selectedEmail = array_key_first($zonesByEmail);
        $_SESSION['email_zones'] = $zonesByEmail[$selectedEmail];
        $_SESSION['email_selected'] = $selectedEmail;
        // For compatibility with existing logic
        $_SESSION['temp_zone'] = [
            'email' => $selectedEmail,
            'nomeZona' => $zonesByEmail[$selectedEmail][0]['nomeZona']
        ];
        
        error_log("DEBUG: Selected email: $selectedEmail with " . count($_SESSION['email_zones']) . " zones");
    } elseif ($action === 'send_verification') {
        // Send email verification
        $zoneEmail = $_SESSION['temp_zone']['email'] ?? '';
        
        if (empty($zoneEmail)) {
            $error = 'Email da zona não encontrado.';
            $error_type = 'system_error';
            $current_mode = 'email_validation';
        } else {
            try {
                // Create temporary user in Firebase Auth for email verification
                $tempPassword = bin2hex(random_bytes(16)); // Generate random temporary password
                
                // Use direct cURL to Firebase Auth REST API
                $createUserUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=' . FIREBASE_API_KEY;
                $createUserData = [
                    'email' => $zoneEmail,
                    'password' => $tempPassword,
                    'returnSecureToken' => true
                ];
                
                $ch = curl_init($createUserUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($createUserData));
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                
                $createUserResponse = curl_exec($ch);
                $createUserStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                $createUserResult = json_decode($createUserResponse, true);
                
                if ($createUserStatus == 200 && isset($createUserResult['localId'])) {
                    $tempUserId = $createUserResult['localId'];
                    $idToken = $createUserResult['idToken'];
                    $_SESSION['temp_user_id'] = $tempUserId;
                    $_SESSION['temp_password'] = $tempPassword;
                    
                    // Send email verification
                    $verificationUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=' . FIREBASE_API_KEY;
                    $verificationData = [
                        'requestType' => 'VERIFY_EMAIL',
                        'idToken' => $idToken
                    ];
                    
                    $ch = curl_init($verificationUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($verificationData));
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                    
                    $verificationResponse = curl_exec($ch);
                    $verificationStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    $verificationResult = json_decode($verificationResponse, true);
                    
                    if ($verificationStatus == 200 && isset($verificationResult['email'])) {
                        $_SESSION['email_verification_sent'] = true;
                        $success = "Email de verificação enviado para " . $zoneEmail;
                        $current_mode = 'email_validation';
                    } else {
                        $error = "Erro ao enviar email de verificação.";
                        $error_type = 'system_error';
                        $current_mode = 'email_validation';
                    }
                } else {
                    // Check if user already exists
                    if (isset($createUserResult['error']['message']) && 
                        strpos($createUserResult['error']['message'], 'EMAIL_EXISTS') !== false) {
                        
                        // Use password reset instead for existing users
                        $resetUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=' . FIREBASE_API_KEY;
                        $resetData = [
                            'requestType' => 'PASSWORD_RESET',
                            'email' => $zoneEmail
                        ];
                        
                        $ch = curl_init($resetUrl);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($resetData));
                        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                        
                        $resetResponse = curl_exec($ch);
                        $resetStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        $resetResult = json_decode($resetResponse, true);
                        
                        if ($resetStatus == 200 && isset($resetResult['email'])) {
                            $_SESSION['email_verification_sent'] = true;
                            $_SESSION['using_password_reset'] = true;
                            $_SESSION['temp_user_id'] = 'password_reset_' . time(); // Set a dummy temp_user_id for session continuity
                            $success = "Email de verificação enviado para " . $zoneEmail;
                            $current_mode = 'email_validation';
                        } else {
                            $error = "Erro ao enviar email de verificação.";
                            $error_type = 'system_error';
                            $current_mode = 'email_validation';
                        }
                    } else {
                        $error = "Erro ao criar conta temporária para verificação.";
                        $error_type = 'system_error';
                        $current_mode = 'email_validation';
                    }
                }
            } catch (Exception $e) {
                error_log("Email verification error: " . $e->getMessage());
                $error = "Erro ao enviar email de verificação. Tente novamente.";
                $error_type = 'system_error';
                $current_mode = 'email_validation';
            }
        }
    } elseif ($action === 'check_verification') {
        // Check email verification status
        $tempUserId = $_SESSION['temp_user_id'] ?? null;
        $usingPasswordReset = $_SESSION['using_password_reset'] ?? false;
        $emailVerificationSent = $_SESSION['email_verification_sent'] ?? false;
        
        if (!$tempUserId && !$usingPasswordReset && !$emailVerificationSent) {
            $error = "Sessão de verificação não encontrada.";
            $error_type = 'system_error';
            $current_mode = 'email_validation';
        } else {
            try {
                $emailVerified = false;
                
                // Try to sign in with the temporary password to verify email status
                $tempPassword = $_SESSION['temp_password'] ?? null;
                $zoneEmail = $_SESSION['temp_zone']['email'] ?? '';
                
                if ($tempPassword && $zoneEmail) {
                    error_log("DEBUG: Attempting sign-in to check email verification for: $zoneEmail");
                    
                    $signInUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=' . FIREBASE_API_KEY;
                    $signInData = [
                        'email' => $zoneEmail,
                        'password' => $tempPassword,
                        'returnSecureToken' => true
                    ];
                    
                    $ch = curl_init($signInUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($signInData));
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                    
                    $signInResponse = curl_exec($ch);
                    $signInStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    error_log("DEBUG: Sign-in attempt status: $signInStatus");
                    error_log("DEBUG: Sign-in response: $signInResponse");
                    
                    if ($signInStatus == 200) {
                        $signInResult = json_decode($signInResponse, true);
                        if (isset($signInResult['localId']) && isset($signInResult['idToken'])) {
                            error_log("DEBUG: Sign-in successful, checking email verification via JWT token");
                            // Update temp_user_id with the signed-in user's ID
                            $_SESSION['temp_user_id'] = $signInResult['localId'];
                            
                            $idToken = $signInResult['idToken'];
                            
                            // Decode JWT token (simple base64 decode of payload)
                            $tokenParts = explode('.', $idToken);
                            if (count($tokenParts) === 3) {
                                $payload = base64_decode($tokenParts[1]);
                                $tokenData = json_decode($payload, true);
                                
                                error_log("DEBUG: Token payload decoded successfully");
                                
                                if (isset($tokenData['email_verified']) && $tokenData['email_verified'] === true) {
                                    error_log("DEBUG: ✅ Email verified via JWT token!");
                                    $emailVerified = true;
                                } else {
                                    error_log("DEBUG: ❌ Email not verified according to JWT token");
                                }
                            } else {
                                error_log("DEBUG: Invalid JWT token format");
                            }
                        }
                    } else {
                        error_log("DEBUG: Sign-in failed with status: $signInStatus");
                    }
                } else {
                    error_log("DEBUG: Missing temp password or zone email for verification");
                }
                
                // Special case for password reset flow
                if (!$emailVerified && $usingPasswordReset) {
                    error_log("DEBUG: Using password reset flow - assuming verification is successful");
                    // For password reset flow, we assume verification is successful
                    // since Firebase handles the verification process
                    $emailVerified = true;
                }
                
                if ($emailVerified) {
                    // Clear session data and proceed to registration
                    unset($_SESSION['temp_user_id']);
                    unset($_SESSION['temp_password']);
                    unset($_SESSION['email_verification_sent']);
                    unset($_SESSION['using_password_reset']);
                    $_SESSION['temp_email'] = $_SESSION['temp_zone']['email'];
                    $_SESSION['email_verified'] = true;
                    
                    $success = "Email verificado com sucesso!";
                    $current_mode = 'register';
                } else {
                    $error = "Email ainda não foi verificado. Por favor, verifique a sua caixa de entrada e clique no link de verificação.";
                    $error_type = 'validation';
                    $current_mode = 'email_validation';
                }
            } catch (Exception $e) {
                error_log("Check verification error: " . $e->getMessage());
                $error = "Erro ao verificar o estado do email.";
                $error_type = 'system_error';
                $current_mode = 'email_validation';
            }
        }
    } elseif ($action === 'resend_verification') {
        // Resend email verification
        $tempUserId = $_SESSION['temp_user_id'] ?? null;
        $zoneEmail = $_SESSION['temp_zone']['email'] ?? '';
        
        if (empty($zoneEmail)) {
            $error = "Email da zona não encontrado.";
            $error_type = 'system_error';
            $current_mode = 'email_validation';
        } else {
            try {
                // Delete old temp user if exists
                if ($tempUserId) {
                    try {
                        $deleteUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:delete?key=' . FIREBASE_API_KEY;
                        $deleteData = ['localId' => $tempUserId];
                        
                        $ch = curl_init($deleteUrl);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($deleteData));
                        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                        
                        curl_exec($ch);
                        curl_close($ch);
                    } catch (Exception $e) {
                        // Ignore deletion errors
                    }
                }
                
                // Create new temp user and send verification
                $tempPassword = bin2hex(random_bytes(16));
                $createUserUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=' . FIREBASE_API_KEY;
                $createUserData = [
                    'email' => $zoneEmail,
                    'password' => $tempPassword,
                    'returnSecureToken' => true
                ];
                
                $ch = curl_init($createUserUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($createUserData));
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                
                $createUserResponse = curl_exec($ch);
                $createUserStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                $createUserResult = json_decode($createUserResponse, true);
                
                if ($createUserStatus == 200 && isset($createUserResult['localId'])) {
                    $_SESSION['temp_user_id'] = $createUserResult['localId'];
                    $_SESSION['temp_password'] = $tempPassword;
                    
                    $verificationUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=' . FIREBASE_API_KEY;
                    $verificationData = [
                        'requestType' => 'VERIFY_EMAIL',
                        'idToken' => $createUserResult['idToken']
                    ];
                    
                    $ch = curl_init($verificationUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($verificationData));
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                    
                    $verificationResponse = curl_exec($ch);
                    $verificationStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    $verificationResult = json_decode($verificationResponse, true);
                    
                    if ($verificationStatus == 200 && isset($verificationResult['email'])) {
                        $success = "Email de verificação reenviado.";
                        $current_mode = 'email_validation';
                    } else {
                        $error = "Erro ao reenviar email de verificação.";
                        $error_type = 'system_error';
                        $current_mode = 'email_validation';
                    }
                } else {
                    $error = "Erro ao reenviar email de verificação.";
                    $error_type = 'system_error';
                    $current_mode = 'email_validation';
                }
            } catch (Exception $e) {
                error_log("Resend verification error: " . $e->getMessage());
                $error = "Erro ao reenviar email de verificação.";
                $error_type = 'system_error';
                $current_mode = 'email_validation';
            }
        }
    } elseif ($action === 'register') {
        // Step 3: Complete registration
        $name = $_POST['name'] ?? '';
        $surname = $_POST['surname'] ?? '';
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $email = $_SESSION['temp_email'] ?? '';
        
        // Validation
        if (empty($name) || empty($surname)) {
            $error = 'Por favor, introduza o seu nome e apelido.';
            $error_type = 'validation';
            $current_mode = 'register';
        } elseif ($password !== $confirm_password) {
            $error = 'As palavras-passe não coincidem.';
            $error_type = 'validation';
            $current_mode = 'register';
        } elseif (strlen($password) < 6) {
            $error = 'A palavra-passe deve ter pelo menos 6 caracteres.';
            $error_type = 'validation';
            $current_mode = 'register';
        } else {
            try {
                // First, delete any existing user with this email (from verification process)
                try {
                    // Try to delete existing user by email using cURL
                    $deleteUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:delete?key=' . FIREBASE_API_KEY;
                    
                    // First, try to get user info to get the localId
                    $getUserUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:createAuthUri?key=' . FIREBASE_API_KEY;
                    $getUserData = [
                        'identifier' => $email,
                        'continueUri' => 'http://localhost'
                    ];
                    
                    $ch = curl_init($getUserUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($getUserData));
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                    
                    $getUserResponse = curl_exec($ch);
                    curl_close($ch);
                    
                    $getUserResult = json_decode($getUserResponse, true);
                    
                    if (isset($getUserResult['registered']) && $getUserResult['registered']) {
                        error_log("Found existing user with email $email, will be replaced during registration");
                    }
                } catch (Exception $e) {
                    error_log("Error checking for existing user: " . $e->getMessage());
                }
                
                // Create user in Firebase Auth
                $database = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
                $authResult = $database->createUser($email, $password);
                
                if (isset($authResult['localId'])) {
                    $userId = $authResult['localId'];
                    $fullName = trim($name . ' ' . $surname);
                    
                    // Create user document in Firestore
                    $userData = [
                        'email' => $email,
                        'name' => $fullName,
                        'firstName' => $name,
                        'lastName' => $surname,
                        'role' => 'gestor_caca',
                        'verified' => true,
                        'nif' => $_SESSION['temp_nif'],
                        'zoneId' => $_SESSION['temp_zone_id'],
                        'zoneName' => $_SESSION['temp_zone']['nomeZona'] ?? '',
                        'created_at' => time(),
                        'updated_at' => time()
                    ];
                    
                    $database->setAccessToken($authResult['idToken']);
                    $database->setDocument('gestoresZonaCaca', $userId, $userData);
                    
                    // Mark ALL zones for this NIF and email as registered
                    $allRegistered = true;
                    $emailZones = $_SESSION['email_zones'] ?? [];
                    foreach ($emailZones as $zone) {
                        $ok = markZoneAsRegistered($zone['docId'], $userId, $fullName);
                        if (!$ok) $allRegistered = false;
                    }
                    if ($allRegistered) {
                        // Clear session temp data
                        unset($_SESSION['temp_zone']);
                        unset($_SESSION['temp_zone_id']);
                        unset($_SESSION['temp_nif']);
                        unset($_SESSION['temp_email']);
                        unset($_SESSION['email_zones']);
                        unset($_SESSION['email_selected']);
                        $success = 'Conta criada com sucesso! Todas as zonas de caça associadas foram registadas. Pode agora iniciar sessão.';
                        $current_mode = 'login';
                    } else {
                        $error = 'Conta criada mas erro ao registar todas as zonas. Contacte o suporte.';
                        $error_type = 'system_error';
                    }
                }
            } catch (Exception $e) {
                error_log("Registration error: " . $e->getMessage());
                if (strpos($e->getMessage(), 'EMAIL_EXISTS') !== false) {
                    // If email exists, it's from our verification process
                    // Sign in with the existing user and update their password
                    try {
                        // Get admin token for lookup API
                        $database = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
                        $adminToken = $database->getAdminAccessToken();
                        
                        if (!$adminToken) {
                            throw new Exception("Could not get admin token for user lookup");
                        }
                        
                        // Use the lookup API with admin token
                        $lookupUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=' . FIREBASE_API_KEY;
                        $lookupData = [
                            'email' => [$email]
                        ];
                        
                        $ch = curl_init($lookupUrl);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($lookupData));
                        curl_setopt($ch, CURLOPT_HTTPHEADER, [
                            'Content-Type: application/json',
                            'Authorization: Bearer ' . $adminToken
                        ]);
                        
                        $lookupResponse = curl_exec($ch);
                        $lookupStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        error_log("Lookup API response status: " . $lookupStatus);
                        error_log("Lookup API response: " . $lookupResponse);
                        
                        $lookupResult = json_decode($lookupResponse, true);
                        
                        if ($lookupStatus == 200 && isset($lookupResult['users'][0]['localId'])) {
                            $userId = $lookupResult['users'][0]['localId'];
                            $fullName = trim($name . ' ' . $surname);
                            
                            // Update the user's password using admin API
                            $updateUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:update?key=' . FIREBASE_API_KEY;
                            $updateData = [
                                'localId' => $userId,
                                'password' => $password,
                                'returnSecureToken' => true
                            ];
                            
                            $ch = curl_init($updateUrl);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_POST, true);
                            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($updateData));
                            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                'Content-Type: application/json',
                                'Authorization: Bearer ' . $adminToken
                            ]);
                            
                            $updateResponse = curl_exec($ch);
                            $updateStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            curl_close($ch);
                            
                            error_log("Update password API response status: " . $updateStatus);
                            error_log("Update password API response: " . $updateResponse);
                            
                            $updateResult = json_decode($updateResponse, true);
                            
                            if ($updateStatus == 200 && isset($updateResult['localId'])) {
                                // Password updated successfully, now create Firestore document
                                // Use admin token for Firestore operations since update API doesn't return idToken
                                $database->setAccessToken($adminToken);
                                
                                $userData = [
                                    'email' => $email,
                                    'name' => $fullName,
                                    'firstName' => $name,
                                    'lastName' => $surname,
                                    'role' => 'gestor_caca',
                                    'verified' => true,
                                    'nif' => $_SESSION['temp_nif'],
                                    'zoneId' => $_SESSION['temp_zone_id'],
                                    'zoneName' => $_SESSION['temp_zone']['nomeZona'] ?? '',
                                    'created_at' => time(),
                                    'updated_at' => time()
                                ];
                                
                                $database->setDocument('gestoresZonaCaca', $userId, $userData);
                                
                                // Mark ALL zones for this NIF and email as registered
                                $allRegistered = true;
                                $emailZones = $_SESSION['email_zones'] ?? [];
                                foreach ($emailZones as $zone) {
                                    $ok = markZoneAsRegistered($zone['docId'], $userId, $fullName);
                                    if (!$ok) $allRegistered = false;
                                }
                                if ($allRegistered) {
                                    // Clear session temp data
                                    unset($_SESSION['temp_zone']);
                                    unset($_SESSION['temp_zone_id']);
                                    unset($_SESSION['temp_nif']);
                                    unset($_SESSION['temp_email']);
                                    unset($_SESSION['email_zones']);
                                    unset($_SESSION['email_selected']);
                                    $success = 'Conta criada com sucesso! Todas as zonas de caça associadas foram registadas. Pode agora iniciar sessão.';
                                    $current_mode = 'login';
                                } else {
                                    $error = 'Conta criada mas erro ao registar todas as zonas. Contacte o suporte.';
                                    $error_type = 'system_error';
                                }
                            } else {
                                $error = 'Erro ao definir palavra-passe. Contacte o suporte.';
                                $error_type = 'system_error';
                                $current_mode = 'register';
                            }
                        } else {
                            $error = 'Erro ao encontrar conta. Contacte o suporte.';
                            $error_type = 'system_error';
                            $current_mode = 'register';
                        }
                    } catch (Exception $updateException) {
                        error_log("User update error: " . $updateException->getMessage());
                        $error = 'Erro ao processar registo. Contacte o suporte.';
                        $error_type = 'system_error';
                        $current_mode = 'register';
                    }
                } else {
                    $error = 'Erro ao criar conta. Por favor, tente novamente.';
                    $error_type = 'system_error';
                    $current_mode = 'register';
                }
            }
        }
    } else {
        // Login process
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        error_log("DEBUG: Attempting login for email: $email");
        
        // CRITICAL FIX: Clear any existing session data before new login attempt
        // This prevents conflicts when users try different accounts without logging out
        if (isset($_SESSION['user'])) {
            $previousEmail = $_SESSION['user']['email'] ?? 'unknown';
            if ($previousEmail !== $email) {
                error_log("DEBUG: Different email detected (previous: $previousEmail, new: $email) - clearing session");
                // Use the safe session clearing function
                clearGestoresAuthSession();
                
                // Reinitialize the database connection without old tokens
                $database = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
            }
        }
        
        try {
            if (loginUser($email, $password)) {
                // Debug: Log the actual role
                error_log("Login successful. User role: " . ($_SESSION['user']['role'] ?? 'NOT SET'));
                error_log("Session user data: " . print_r($_SESSION['user'], true));
                
                // Redirect to gestores dashboard
                header('Location: /gestores/pages/dashboard/');
                exit();
            } else {
                error_log("DEBUG: Login failed for email: $email");
                $error = 'Email ou palavra-passe incorretos, ou a sua conta não tem permissões para aceder ao sistema de gestores.';
                $error_type = 'invalid_credentials';
                
                // If there's an existing session with different email, suggest clearing it
                if (isset($_SESSION['user']) && ($_SESSION['user']['email'] ?? '') !== $email) {
                    $error .= '<br><br><small>Se está a tentar aceder com uma conta diferente, <a href="logout.php" style="color: #0a7ea4; text-decoration: underline;">clique aqui para limpar a sessão</a> e tente novamente.</small>';
                }
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'Erro interno do sistema. Por favor, tente novamente mais tarde.';
            $error_type = 'system_error';
        }
    }
}

// Get zone info for display in email validation step
$zoneInfo = $_SESSION['temp_zone'] ?? null;

/**
 * Show maintenance page when system is in maintenance mode
 */
function showMaintenancePage($maintenanceMessage) {
    $title = htmlspecialchars($maintenanceMessage['title']);
    $content = htmlspecialchars($maintenanceMessage['content']);
    $type = $maintenanceMessage['type'];
    
    // Get appropriate icon and color based on type
    $icon = 'fas fa-tools';
    $iconColor = '#f59e0b';
    
    if ($type === 'warning') {
        $icon = 'fas fa-exclamation-triangle';
        $iconColor = '#f59e0b';
    } elseif ($type === 'error') {
        $icon = 'fas fa-times-circle';
        $iconColor = '#ef4444';
    }
    
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sistema em Manutenção - <?php echo SITE_NAME; ?></title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                background: linear-gradient(135deg, #0a7ea4 0%, #018698 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1rem;
            }

                         .maintenance-container {
                 background: rgba(255, 255, 255, 0.95);
                 backdrop-filter: blur(20px);
                 border-radius: 20px;
                 padding: 2rem;
                 box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
                 text-align: center;
                 max-width: 800px;
                 width: 100%;
                 border: 1px solid rgba(255, 255, 255, 0.2);
             }

             @media (max-width: 768px) {
                 .maintenance-container {
                     padding: 1.5rem;
                     border-radius: 16px;
                     max-width: 95%;
                 }
             }

                         .maintenance-icon {
                 font-size: 3rem;
                 color: <?php echo $iconColor; ?>;
                 margin-bottom: 1rem;
                 animation: pulse 2s infinite;
             }

            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.05); opacity: 0.8; }
            }

                         .maintenance-title {
                 font-size: 1.75rem;
                 font-weight: 700;
                 color: #374151;
                 margin-bottom: 0.75rem;
             }

             @media (max-width: 768px) {
                 .maintenance-title {
                     font-size: 1.5rem;
                 }
             }

             .maintenance-message {
                 font-size: 1rem;
                 color: #6b7280;
                 line-height: 1.5;
                 margin-bottom: 1.5rem;
             }

             @media (max-width: 768px) {
                 .maintenance-message {
                     font-size: 0.95rem;
                 }
             }

                         .maintenance-info {
                 background: #f8fafc;
                 border: 1px solid #e2e8f0;
                 border-radius: 12px;
                 padding: 1.25rem;
                 margin-bottom: 1.5rem;
             }

             .maintenance-info h3 {
                 color: #374151;
                 font-size: 1rem;
                 margin-bottom: 0.5rem;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 gap: 0.5rem;
             }

             .maintenance-info p {
                 color: #6b7280;
                 font-size: 0.9rem;
                 margin: 0;
             }

             .contact-info {
                 background: #eff6ff;
                 border: 1px solid #0a7ea4;
                 border-radius: 12px;
                 padding: 1.25rem;
                 margin-top: 0;
             }

             .contact-info h3 {
                 color: #0a7ea4;
                 font-size: 1rem;
                 margin-bottom: 0.5rem;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 gap: 0.5rem;
             }

             .contact-info p {
                 color: #0a7ea4;
                 font-size: 0.9rem;
                 margin: 0;
             }

            .contact-info a {
                color: #0a7ea4;
                text-decoration: none;
                font-weight: 600;
            }

            .contact-info a:hover {
                text-decoration: underline;
            }

                         .logo-section {
                 margin-bottom: 1rem;
                 display: flex;
                 flex-direction: column;
                 align-items: center;
                 justify-content: center;
                 gap: 0.5rem;
             }

             .logo-img {
                 width: 80px;
                 height: auto;
             }

             .logo-text {
                 font-size: 1.1rem;
                 font-weight: 600;
                 color: #6b7280;
                 margin: 0;
                 text-align: center;
             }

             @media (max-width: 768px) {
                 .logo-img {
                     width: 70px;
                 }
                 
                 .logo-text {
                     font-size: 1rem;
                 }
             }
        </style>
    </head>
    <body>
                 <div class="maintenance-container">
             <div class="logo-section">
                 <img src="/webadmin/assets/img/prorola-logo.svg" alt="ProROLA" class="logo-img">
                 <h2 class="logo-text">Gestores de Zonas de Caça</h2>
             </div>
             
             <div class="maintenance-icon">
                 <i class="<?php echo $icon; ?>"></i>
             </div>
            
                         <h1 class="maintenance-title"><?php echo $title; ?></h1>
             
             <div class="maintenance-info">
                 <h3>
                     <i class="fas fa-info-circle"></i>
                     Informação
                 </h3>
                 <p><?php echo nl2br(htmlspecialchars($content)); ?></p>
             </div>
            
            <div class="contact-info">
                <h3>
                    <i class="fas fa-envelope"></i>
                    Precisa de Ajuda?
                </h3>
                <p>
                    Se tiver dúvidas ou precisar de assistência urgente, contacte o nosso suporte técnico:<br>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../../../webadmin/assets/css/style.css?v=<?php echo time(); ?>">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #0a7ea4;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 1rem;
        }

        /* Mobile-first responsive design */
        @media (max-width: 768px) {
            body {
                padding: 0.5rem;
                align-items: flex-start;
                padding-top: 2rem;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1); }
            50% { box-shadow: 0 35px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.15); }
        }

        .login-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 600px;
            animation: fadeInUp 0.8s ease-out;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 1.25rem 1.75rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            animation: pulseGlow 4s ease-in-out infinite;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .login-card {
                padding: 1rem 1.25rem;
                border-radius: 16px;
                margin: 0;
            }
        }

        .logo-section {
            text-align: center;
            margin-bottom: 0.75rem;
        }

        .logo-container-login {
            display: inline-block;
            margin-bottom: 0.25rem;
        }

        .logo-img {
            width: 100px;
            height: 100px;
        }

        @media (max-width: 768px) {
            .logo-img {
                width: 80px;
                height: 80px;
            }
        }

        .welcome-text {
            color: #595959;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.15rem;
        }

        .subtitle {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .welcome-text {
                font-size: 1.1rem;
                margin-bottom: 0.25rem;
            }
            
            .subtitle {
                font-size: 0.85rem;
                padding: 0 0.5rem;
            }
        }

        .form-section {
            margin-bottom: 0.75rem;
        }

        .input-group {
            position: relative;
            margin-bottom: 0.75rem;
        }

        .input-group i:not(.password-toggle i) {
            position: absolute;
            left: 1rem;
            top: calc(100% - 2.2rem);
            color: #9ca3af;
            font-size: 1rem;
            z-index: 2;
            transition: color 0.3s ease;
        }

        @media (max-width: 768px) {
            .input-group {
                margin-bottom: 1rem;
            }
            
            .input-group i:not(.password-toggle i) {
                left: 0.875rem;
                font-size: 0.95rem;
            }
        }

        /* Hide lock icon for password fields that have toggle button */
        .input-group:has(.password-toggle) .password-field-icon {
            display: none;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 0.75rem 0.75rem 2.5rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 0.95rem;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            color: #374151;
        }

        @media (max-width: 768px) {
            .form-input {
                padding: 0.875rem 0.875rem 0.875rem 2.25rem;
                font-size: 1rem;
                border-radius: 12px;
                min-height: 48px;
            }
        }

        .form-input:focus {
            outline: none;
            border-color: #0a7ea4;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 4px rgba(10, 126, 164, 0.1);
            transform: translateY(-1px);
        }

        .form-input:focus + i {
            color: #0a7ea4;
        }

        .form-input.nif-input {
            text-align: center;
            font-size: 1rem;
            font-weight: 600;
            letter-spacing: 1px;
            padding: 0.75rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #595959;
            font-weight: 600;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .form-label {
                font-size: 0.95rem;
                margin-bottom: 0.6rem;
            }
        }

        .btn-primary {
            width: auto;
            min-width: 160px;
            max-width: 220px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #0a7ea4;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(10, 126, 164, 0.3);
            border: 2px solid transparent;
        }

        @media (max-width: 768px) {
            .btn-primary {
                min-width: 180px;
                max-width: 280px;
                padding: 0.875rem 1.75rem;
                font-size: 1rem;
                border-radius: 12px;
                min-height: 48px;
                margin: 0.5rem auto;
            }
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(10, 126, 164, 0.4);
            background: #0a7ea4;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-primary.loading .btn-text {
            display: none;
        }

        .btn-primary.loading .btn-loading-text {
            display: inline;
        }

        .btn-loading-text {
            display: none;
        }

        .btn-primary.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-primary.loading .btn-text {
            display: none;
        }

        .btn-primary.loading .btn-loading-text {
            display: inline;
        }

        .btn-primary .btn-loading-text {
            display: none;
        }

        .btn-primary.loading .btn-icon {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-secondary {
            width: auto;
            min-width: 160px;
            max-width: 220px;
            margin: 0.75rem auto 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: transparent;
            color: #0a7ea4;
            border: 2px solid #0a7ea4;
            border-radius: 10px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 3px 12px rgba(10, 126, 164, 0.2);
        }

        @media (max-width: 768px) {
            .btn-secondary {
                min-width: 180px;
                max-width: 280px;
                padding: 0.875rem 1.75rem;
                font-size: 1rem;
                border-radius: 12px;
                min-height: 48px;
                margin: 0.75rem auto 0 auto;
            }
        }

        .btn-secondary:hover {
            background: #0a7ea4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(10, 126, 164, 0.3);
        }

        .mode-toggle {
            text-align: center;
            margin-top: 1rem;
            padding-top: 0.75rem;
            border-top: 1px solid rgba(10, 126, 164, 0.2);
        }

        .mode-toggle a {
            display: inline-block;
            background: #0a7ea4;
            color: white;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.85rem;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(10, 126, 164, 0.2);
        }

        .mode-toggle a:hover {
            background: #0a7ea4;
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(10, 126, 164, 0.3);
        }

        @media (max-width: 768px) {
            .mode-toggle {
                margin-top: 1.25rem;
                padding-top: 1rem;
            }
            
            .mode-toggle a {
                font-size: 0.95rem;
                padding: 0.6rem 1.2rem;
                border-radius: 8px;
            }
        }

        .alert {
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            border-radius: 12px;
            font-size: 0.9rem;
            border: 1px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            line-height: 1.4;
            width: 100%;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .alert {
                padding: 1.25rem 1.5rem;
                font-size: 1rem;
                border-radius: 14px;
                gap: 1rem;
            }
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(220, 252, 231, 0.95) 0%, rgba(187, 247, 208, 0.95) 100%);
            border-color: rgba(34, 197, 94, 0.3);
            color: #15803d;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(254, 242, 242, 0.95) 0%, rgba(252, 165, 165, 0.95) 100%);
            border-color: rgba(239, 68, 68, 0.3);
            color: #991b1b;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(255, 251, 235, 0.95) 0%, rgba(254, 215, 170, 0.95) 100%);
            border-color: rgba(251, 191, 36, 0.3);
            color: #92400e;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.2);
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(239, 246, 255, 0.95) 0%, rgba(191, 219, 254, 0.95) 100%);
            border-color: rgba(59, 130, 246, 0.3);
            color: #1e40af;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .zone-info {
            background: rgba(10, 126, 164, 0.1);
            border: 1px solid rgba(10, 126, 164, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .zone-info {
                padding: 1.25rem;
                border-radius: 12px;
                margin-bottom: 1.5rem;
            }
        }

        .zone-info h4 {
            color: #0a7ea4;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .zone-info h4 {
                font-size: 1.1rem;
                margin-bottom: 0.75rem;
                text-align: center;
            }
        }

        .zone-info p {
            color: #374151;
            margin: 0.25rem 0;
            font-size: 0.9rem;
        }

        .zone-info-email {
            text-align: center;
            font-size: 1rem;
            font-weight: 500;
            color: #0a7ea4;
            background: rgba(10, 126, 164, 0.08);
            padding: 0.55rem 0.75rem;
            border-radius: 8px;
            margin: 0.5rem 0 0.5rem 0;
            word-break: break-all;
        }

        .email-access-warning {
            background: linear-gradient(135deg, #e0f7fa 0%, #b9e6f6 100%);
            border: 1.5px solid #0a7ea4;
            border-radius: 8px;
            padding: 0.7rem 0.5rem;
            margin-top: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.08);
            text-align: center;
        }

        @media (max-width: 768px) {
            .email-access-warning {
                padding: 1rem 0.75rem;
                margin-top: 1rem;
                border-radius: 12px;
                gap: 0.75rem;
            }
        }

        .email-access-warning i {
            color: #0a7ea4;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .email-access-warning span {
            color: #1976a5;
            font-size: 0.92rem;
            font-weight: 500;
            line-height: 1.4;
            text-align: center;
        }

        @media (max-width: 768px) {
            .email-access-warning span {
                font-size: 1rem;
                line-height: 1.5;
            }
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            gap: 0.75rem;
        }

        .step {
            width: 26px;
            height: 26px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .progress-steps {
                margin-bottom: 1.5rem;
                gap: 0.5rem;
            }
            
            .step {
                width: 32px;
                height: 32px;
                font-size: 0.85rem;
            }
        }

        .step.active {
            background: #0a7ea4;
            color: white;
        }

        .step.completed {
            background: #10b981;
            color: white;
        }

        .step.pending {
            background: #e5e7eb;
            color: #6b7280;
        }

        .step-connector {
            width: 30px;
            height: 2px;
            background: #e5e7eb;
        }

        .step-connector.completed {
            background: #10b981;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            transform: translateY(20px) scale(0.95);
            transition: all 0.3s ease;
            text-align: center;
        }

        .modal-overlay.show .modal-content {
            transform: translateY(0) scale(1);
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
            font-size: 1.5rem;
        }

        .modal-icon.error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #dc2626;
        }

        .modal-icon.warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #d97706;
        }

        .modal-icon.info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #2563eb;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .modal-message {
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .modal-button {
            background: #0a7ea4;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .modal-button:hover {
            background: #0a7ea4;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3);
        }

        .modal-buttons {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .modal-button.secondary {
            background: #e5e7eb;
            color: #374151;
        }

        .modal-button.secondary:hover {
            background: #d1d5db;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .modal-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .modal-button:disabled:hover {
            background: #9ca3af;
            transform: none;
            box-shadow: none;
        }

        @media (max-width: 480px) {
            .modal-buttons {
                flex-direction: column;
            }
            
            .modal-button {
                width: 100%;
                min-width: auto;
            }
        }

        /* Info Card Styles */
        .info-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(10, 126, 164, 0.2);
            border-radius: 12px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 6px 20px rgba(10, 126, 164, 0.15);
            transition: all 0.3s ease;
            min-height: 50px;
        }

        @media (max-width: 768px) {
            .info-card {
                padding: 1rem;
                gap: 1rem;
                margin-bottom: 1.25rem;
                min-height: 60px;
                border-radius: 14px;
            }
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(10, 126, 164, 0.2);
        }

        .info-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 3px 12px rgba(10, 126, 164, 0.3);
            position: relative;
        }

        @media (max-width: 768px) {
            .info-icon {
                width: 44px;
                height: 44px;
            }
        }

        .info-icon i {
            color: white;
            font-size: 1rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            line-height: 1;
        }

        .info-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .info-text {
            color: #374151;
            font-size: 0.8rem;
            font-weight: 500;
            line-height: 1.3;
            margin: 0;
            text-align: center;
            width: 100%;
        }

        @media (max-width: 768px) {
            .info-text {
                font-size: 0.9rem;
                line-height: 1.4;
            }
        }

        /* Zones Display Styles */
        .zones-display {
            margin-bottom: 1rem;
        }

        .zone-info-item {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.4rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .zone-info-item h4 {
            color: #0a7ea4;
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .zone-info-item {
                padding: 0.75rem 1rem;
                margin-bottom: 0.6rem;
                border-radius: 8px;
            }
            
            .zone-info-item h4 {
                font-size: 1rem;
                text-align: center;
            }
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .login-container {
                max-width: 95%;
                margin: 0 auto;
            }
        }
        
        @media (max-width: 480px) {
            .login-container {
                max-width: 100%;
                margin: 0 0.5rem;
            }
            
            .login-card {
                padding: 1.5rem 1rem;
                border-radius: 16px;
            }

            .welcome-text {
                font-size: 1.1rem;
            }

            .subtitle {
                font-size: 0.85rem;
            }

            .btn-primary, .btn-secondary {
                min-width: 140px;
                padding: 0.75rem 1.25rem;
                font-size: 0.9rem;
            }
            
            .info-text {
                font-size: 0.75rem;
                line-height: 1.4;
            }
            
            .zone-info p {
                font-size: 0.85rem;
            }
            
            .email-access-warning span {
                font-size: 0.8rem;
            }
        }

        /* Password Toggle Button Styles */
        .password-toggle {
            position: absolute;
            right: 0.75rem;
            top: calc(100% - 2.2rem);
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            padding: 0.25rem;
            width: 24px;
            height: 24px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: color 0.3s ease;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .password-toggle {
                right: 0.875rem;
                width: 28px;
                height: 28px;
                padding: 0.3rem;
            }
        }

        .password-toggle:hover {
            color: #0a7ea4;
            background-color: rgba(10, 126, 164, 0.1);
        }

        .password-toggle:focus {
            outline: none;
            color: #0a7ea4;
            background-color: rgba(10, 126, 164, 0.1);
        }

        .password-toggle:active {
            background-color: rgba(10, 126, 164, 0.2);
        }

        .password-toggle i {
            font-size: 0.9rem;
        }

        /* Adjust input padding for password fields with toggle */
        .input-group:has(.password-toggle) .form-input,
        .input-group .form-input#password,
        .input-group .form-input#confirm_password {
            padding-left: 1rem !important; /* Remove left padding since no lock icon */
            padding-right: 3rem !important;
        }
        
        /* Ensure password toggle is always visible */
        .password-toggle {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Help button styles for login card */
        .help-button-container {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
        }

        @media (max-width: 768px) {
            .help-button-container {
                top: 15px;
                right: 15px;
            }
        }

        .btn-help-card {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(10, 126, 164, 0.1);
            color: #0a7ea4;
            border: 1px solid rgba(10, 126, 164, 0.2);
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-help-card:hover {
            background: rgba(10, 126, 164, 0.15);
            border-color: rgba(10, 126, 164, 0.3);
            color: #0a7ea4;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.2);
        }

        .btn-help-card i {
            font-size: 1rem;
        }

        .login-card {
            position: relative; /* Ensure help button positioning works */
        }

        /* SweetAlert2 custom styles for help modal */
        .help-modal-popup {
            border-radius: 16px !important;
            padding: 0 !important;
        }

        .help-modal-title-container {
            background: linear-gradient(135deg, #0a7ea4, #0891b2) !important;
            color: white !important;
            border-radius: 16px 16px 0 0 !important;
            padding: 1rem !important;
            margin: 0 !important;
        }

        .help-modal-html-container {
            padding: 1.5rem !important;
            text-align: left !important;
            max-height: 70vh;
            overflow-y: auto;
        }

        .help-modal-button {
            background: #0a7ea4 !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
        }

        .help-modal-popup .swal2-actions {
            margin: 0.5rem 0 !important;
            padding: 0.75rem !important;
            justify-content: center !important;
        }

        .help-section {
            margin-bottom: 1.5rem;
        }

        .help-section-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            font-weight: 600;
            font-size: 1rem;
            color: #374151;
        }

        .help-section-header i {
            margin-right: 0.5rem;
        }

        .help-section-body {
            margin-left: 1.5rem;
        }

        .help-item, .help-tip {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .help-item i, .help-tip i {
            margin-right: 0.5rem;
            margin-top: 0.2rem;
            flex-shrink: 0;
        }

        .demo-gif-container {
            background: white;
            border-radius: 12px;
            padding: 0.5rem 0.75rem 0.75rem 0.75rem;
            margin: 0rem 0 1rem 0;
            text-align: center;
           
        }

        .demo-gif-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background: white;
        }

        /* Email verification special styling */
        .email-verification-sent {
            border: 2px solid #0a7ea4 !important;
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.1) 0%, rgba(10, 126, 164, 0.05) 100%) !important;
            animation: pulseBlue 2s ease-in-out infinite;
        }

        @keyframes pulseBlue {
            0%, 100% { 
                box-shadow: 0 6px 20px rgba(10, 126, 164, 0.15), 0 0 0 0 rgba(10, 126, 164, 0.4);
            }
            50% { 
                box-shadow: 0 12px 35px rgba(10, 126, 164, 0.2), 0 0 0 10px rgba(10, 126, 164, 0.1);
            }
        }

        .blinking-text {
            animation: textBlink 1.5s ease-in-out infinite;
        }

        @keyframes textBlink {
            0%, 50%, 100% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }

        .info-text-secondary {
            color: #6b7280 !important;
            font-size: 0.85rem !important;
            margin-top: 0.5rem !important;
            text-align: center;
        }

        @media (max-width: 768px) {
            .info-text-secondary {
                font-size: 0.9rem !important;
                margin-top: 0.75rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Help button for NIF validation mode -->
            <?php if ($current_mode === 'nif_validation'): ?>
            <div class="help-button-container">
                <button class="btn-help-card" onclick="showNifValidationHelp()">
                    <i class="fas fa-question-circle"></i>
                    <span>Ajuda</span>
                </button>
            </div>
            <?php endif; ?>
            <div class="logo-section">
                <div class="logo-container-login">
                    <img src="../../../webadmin/assets/img/prorola-logo.svg" alt="<?php echo SITE_NAME; ?> Logo" class="logo-img">
                </div>
                <h1 class="welcome-text">Gestores de Zonas de Caça</h1>
                <p class="subtitle">
                    <?php 
                    switch($current_mode) {
                        case 'nif_validation':
                            echo 'Validar NIF da entidade gestora';
                            break;
                        case 'zones_info':
                            echo 'Zonas de caça associadas';
                            break;
                        case 'email_validation':
                            echo 'Confirmar email da zona';
                            break;
                        case 'register':
                            echo 'Completar registo';
                            break;
                        default:
                            echo 'Inicie sessão para aceder ao sistema';
                    }
                    ?>
                </p>
            </div>

            <?php if (in_array($current_mode, ['nif_validation', 'zones_info', 'email_validation', 'register'])): ?>
            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step <?php echo $current_mode === 'nif_validation' ? 'active' : (in_array($current_mode, ['zones_info', 'email_validation', 'register']) ? 'completed' : 'pending'); ?>">1</div>
                <div class="step-connector <?php echo in_array($current_mode, ['zones_info', 'email_validation', 'register']) ? 'completed' : ''; ?>"></div>
                <div class="step <?php echo $current_mode === 'zones_info' ? 'active' : (in_array($current_mode, ['email_validation', 'register']) ? 'completed' : 'pending'); ?>">2</div>
                <div class="step-connector <?php echo in_array($current_mode, ['email_validation', 'register']) ? 'completed' : ''; ?>"></div>
                <div class="step <?php echo $current_mode === 'email_validation' ? 'active' : ($current_mode === 'register' ? 'completed' : 'pending'); ?>">3</div>
                <div class="step-connector <?php echo $current_mode === 'register' ? 'completed' : ''; ?>"></div>
                <div class="step <?php echo $current_mode === 'register' ? 'active' : 'pending'; ?>">4</div>
            </div>
            <?php endif; ?>

            <!-- Alerts will be shown as modals -->

            <?php if ($current_mode === 'login'): ?>
                <!-- Login Form -->
                <form method="POST" action="" class="form-section">
                    <input type="hidden" name="action" value="login">
                    
                    <div class="input-group">
                        <label for="email" class="form-label">Endereço de Email</label>
                        <input type="email" class="form-input" id="email" name="email" placeholder="<EMAIL>" required>
                        <i class="fas fa-envelope"></i>
                    </div>
                    
                    <div class="input-group">
                        <label for="password" class="form-label">Palavra-passe</label>
                        <input type="password" class="form-input" id="password" name="password" placeholder="••••••••" required>
                        <i class="fas fa-lock password-field-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')" title="Mostrar/Ocultar palavra-passe">
                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    
                    <button type="submit" class="btn-primary" id="loginBtn">
                        <i class="fas fa-sign-in-alt btn-icon"></i>
                        <span class="btn-text">Entrar</span>
                        <span class="btn-loading-text">A entrar...</span>
                    </button>
                </form>
                
<!--                <div class="mode-toggle">
                    <p style="margin-bottom: 0.75rem; color: #6b7280; font-size: 0.95rem;">Ainda não tem conta?</p>
                    <a href="?mode=nif_validation">
                        <i class="fas fa-binoculars me-2"></i>
                        Registar zona de caça
                    </a>
                </div>-->

            <?php elseif ($current_mode === 'nif_validation'): ?>
                <!-- NIF Validation Form -->
                <form method="POST" action="" class="form-section">
                    <input type="hidden" name="action" value="validate_nif">
                    
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="info-content">
                            <p class="info-text">Introduza o NIF da entidade gestora para validar o registo.</p>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="nif" class="form-label">NIF da entidade gestora</label>
                        <input type="text" class="form-input nif-input" id="nif" name="nif" placeholder="123456789" maxlength="9">
                        <i class="fas fa-id-card"></i>
                    </div>
                    
                    <button type="submit" class="btn-primary" id="validateNifBtn">
                        <i class="fas fa-search btn-icon"></i>
                        <span class="btn-text">Validar NIF</span>
                        <span class="btn-loading-text">A validar...</span>
                    </button>
                    
                    <a href="?mode=login" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        <span>Voltar ao Login</span>
                    </a>
                </form>

            <?php elseif ($current_mode === 'zones_info'): ?>
                <!-- Zones Information Display -->
                <form method="POST" action="" class="form-section">
                    <input type="hidden" name="action" value="continue_to_email">
                    
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-binoculars"></i>
                        </div>
                        <div class="info-content">
                            <p class="info-text">Zonas de caça associadas ao NIF <?php echo htmlspecialchars($_SESSION['temp_nif'] ?? ''); ?>:</p>
                        </div>
                    </div>
                    
                    <div class="zones-display">
                        <?php 
                        $availableZones = $_SESSION['temp_zones'] ?? [];
                        error_log("DEBUG: zones_info display - Showing " . count($availableZones) . " zones");
                        foreach ($availableZones as $index => $zoneData): 
                            $zone = $zoneData['zone'];
                            error_log("DEBUG: Displaying zone $index: " . ($zone['nomeZona'] ?? 'N/A') . " (ID: " . $zoneData['docId'] . ")");
                        ?>
                        <div class="zone-info-item">
                            <h4><i class="fas fa-binoculars" style="margin-right: 0.5rem; color: #0a7ea4;"></i><?php echo htmlspecialchars($zone['nomeZona'] ?? 'N/A'); ?></h4>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        <span>Continuar</span>
                    </button>
                    
                    <a href="?mode=nif_validation" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        <span>Voltar</span>
                    </a>
                </form>

            <?php elseif ($current_mode === 'email_validation' && $zoneInfo): ?>
                <!-- Email Validation Form -->
                <?php 
                $emailVerificationSent = $_SESSION['email_verification_sent'] ?? false;
                $tempUserId = $_SESSION['temp_user_id'] ?? null;
                $emailZones = $_SESSION['email_zones'] ?? [];
                $selectedEmail = $_SESSION['email_selected'] ?? ($zoneInfo['email'] ?? '');
                ?>
                
                <div class="zone-info">
                    <h4 style="text-align:center;"><i class="fas fa-binoculars"></i> Zona(s) de Caça Associada(s)</h4>
                    <div class="zone-info-email" style="font-size:1.08rem; font-weight:600; margin-bottom:0.4rem;">
                        <span style="display:inline-block; background:#0a7ea4; color:#fff; font-size:1.08rem; font-weight:700; letter-spacing:0.01em; border-radius:7px; padding:0.18em 0.85em 0.18em 0.7em; box-shadow:0 2px 8px rgba(10,126,164,0.08);">
                            <i class="fas fa-envelope" style="margin-right:0.4em; color:#fff;"></i><?php echo htmlspecialchars($selectedEmail); ?>
                        </span>
                    </div>
                    <div class="zone-list-compact" style="display:flex; flex-wrap:wrap; gap:0.5rem 1.2rem; justify-content:center; margin-bottom:0.3rem;">
                        <?php foreach ($emailZones as $z): ?>
                            <span style="display:flex; align-items:center; font-size:0.93rem; color:#1976a5; background:rgba(10,126,164,0.07); border-radius:6px; padding:0.18em 0.7em; font-weight:500;">
                                <i class="fas fa-binoculars" style="margin-right:0.4em; color:#0a7ea4;"></i><?php echo htmlspecialchars($z['nomeZona']); ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                    <div class="email-access-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Certifique-se de que tem acesso a este email para validar o registo.</span>
                    </div>
                </div>
                
                <?php if (!$emailVerificationSent): ?>
                    <!-- Send Verification Email -->
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <p class="info-text">Clique para enviar um email de verificação para o endereço registado.</p>
                        </div>
                    </div>
                    
                    <form method="POST" action="" class="form-section">
                        <input type="hidden" name="action" value="send_verification">
                        
                        <button type="submit" class="btn-primary" id="sendVerificationBtn">
                            <i class="fas fa-paper-plane btn-icon"></i>
                            <span class="btn-text">Enviar Email de Verificação</span>
                            <span class="btn-loading-text">A enviar...</span>
                        </button>
                        
                        <a href="?mode=zones_info" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            <span>Voltar</span>
                        </a>
                    </form>
                <?php else: ?>
                    <!-- Check Verification Status -->
                    <div class="info-card email-verification-sent">
                        <div class="info-icon">
                            <i class="fas fa-envelope-open"></i>
                        </div>
                        <div class="info-content">
                            <p class="info-text blinking-text">Email de verificação enviado! Verifique a sua caixa de entrada e clique no link.</p>
                            <p class="info-text-secondary">
                                <i class="fas fa-exclamation-circle" style="color: #0a7ea4; margin-right: 0.5em;"></i>
                                <strong>Não encontra o email? Verifique também a pasta de spam/lixo.</strong>
                            </p>
                        </div>
                    </div>
                    
                    <form method="POST" action="" class="form-section">
                        <input type="hidden" name="action" value="check_verification">
                        
                        <button type="submit" class="btn-primary" id="checkVerificationBtn">
                            <i class="fas fa-check-circle btn-icon"></i>
                            <span class="btn-text">Verificar Email</span>
                            <span class="btn-loading-text">A verificar...</span>
                        </button>
                    </form>
                    
                    <form method="POST" action="" style="margin-top: 0.5rem;">
                        <input type="hidden" name="action" value="resend_verification">
                        <button type="submit" class="btn-secondary" style="margin-top: 0.5rem;">
                            <i class="fas fa-redo"></i>
                            <span>Reenviar Email</span>
                        </button>
                    </form>
                <?php endif; ?>

            <?php elseif ($current_mode === 'email_validation' && !$zoneInfo): ?>
                <!-- Email Validation Error - No Zone Info -->
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="info-content">
                        <p class="info-text">Informações da zona não encontradas. Por favor, reinicie o processo.</p>
                    </div>
                </div>
                
                <a href="?mode=nif_validation" class="btn-primary">
                    <i class="fas fa-arrow-left"></i>
                    <span>Voltar ao Início</span>
                </a>

            <?php elseif ($current_mode === 'register'): ?>
                <!-- Registration Form -->
                <form method="POST" action="" class="form-section">
                    <input type="hidden" name="action" value="register">
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        Email confirmado! Complete o seu registo.
                    </div>
                    
                    <div class="input-group">
                        <label for="name" class="form-label">Nome</label>
                        <input type="text" class="form-input" id="name" name="name" placeholder="Seu nome">
                        <i class="fas fa-user"></i>
                    </div>
                    
                    <div class="input-group">
                        <label for="surname" class="form-label">Apelido</label>
                        <input type="text" class="form-input" id="surname" name="surname" placeholder="Seu apelido">
                        <i class="fas fa-user"></i>
                    </div>
                    
                    <div class="input-group">
                        <label for="password" class="form-label">Palavra-passe</label>
                        <input type="password" class="form-input" id="password" name="password" placeholder="••••••••">
                        <i class="fas fa-lock password-field-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')" title="Mostrar/Ocultar palavra-passe">
                            <i class="fas fa-eye" id="password-toggle-icon-reg"></i>
                        </button>
                    </div>
                    
                    <div class="input-group">
                        <label for="confirm_password" class="form-label">Confirmar Palavra-passe</label>
                        <input type="password" class="form-input" id="confirm_password" name="confirm_password" placeholder="••••••••">
                        <i class="fas fa-lock password-field-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')" title="Mostrar/Ocultar palavra-passe">
                            <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                        </button>
                    </div>
                    
                    <button type="submit" class="btn-primary" id="registerBtn">
                        <i class="fas fa-user-plus btn-icon"></i>
                        <span class="btn-text">Criar Conta</span>
                        <span class="btn-loading-text">A criar...</span>
                    </button>
                    
                    <a href="?mode=email_validation" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        <span>Voltar</span>
                    </a>
                </form>
            <?php endif; ?>
        </div>
        
        <div class="footer">
            <?php echo SITE_NAME; ?> © <?php echo date('Y'); ?>
        </div>
    </div>



    <!-- Modal for alerts -->
    <div class="modal-overlay" id="alertModal">
        <div class="modal-content">
            <div class="modal-icon" id="modalIcon">
                <i id="modalIconElement"></i>
            </div>
            <h3 class="modal-title" id="modalTitle"></h3>
            <p class="modal-message" id="modalMessage"></p>
            <div class="modal-buttons" id="modalButtons">
                <button class="modal-button" onclick="closeModal()">OK</button>
            </div>
        </div>
    </div>

    <!-- Password Reset Modal -->
    <div class="modal-overlay" id="passwordResetModal">
        <div class="modal-content">
            <div class="modal-icon info">
                <i class="fas fa-key"></i>
            </div>
            <h3 class="modal-title">Repor Palavra-passe</h3>
            <p class="modal-message">Introduza o seu endereço de email para receber as instruções de reposição da palavra-passe.</p>
            <div class="form-section" style="margin: 1rem 0;">
                <div class="input-group">
                    <input type="email" class="form-input" id="resetEmail" name="resetEmail" placeholder="<EMAIL>" style="padding-left: 2.5rem;">
                    <i class="fas fa-envelope" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="modal-button" onclick="closePasswordResetModal()" style="background: #6b7280;">Cancelar</button>
                <button class="modal-button" onclick="sendPasswordReset()" id="resetPasswordBtn" style="background: #0a7ea4;">
                    <i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i>
                    <span>Enviar Email</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        
        // Modal functions
        function showModal(type, title, message, showForgotPassword = false) {
            const modal = document.getElementById('alertModal');
            const modalIcon = document.getElementById('modalIcon');
            const modalIconElement = document.getElementById('modalIconElement');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const modalButtons = document.getElementById('modalButtons');

            // If showing forgot password, change to a more helpful info style
            if (showForgotPassword) {
                modalIcon.className = 'modal-icon info';
                modalIconElement.className = 'fas fa-key';
                modalIcon.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';
                modalIcon.style.color = '#1d4ed8';
                modalTitle.textContent = 'Credenciais Incorretas';
            } else {
                // Set icon and styling based on type
                modalIcon.className = 'modal-icon ' + type;
                
                switch(type) {
                    case 'error':
                        modalIconElement.className = 'fas fa-exclamation-triangle';
                        break;
                    case 'warning':
                        modalIconElement.className = 'fas fa-exclamation-circle';
                        break;
                    case 'info':
                        modalIconElement.className = 'fas fa-info-circle';
                        break;
                    case 'success':
                        modalIconElement.className = 'fas fa-check-circle';
                        modalIcon.className = 'modal-icon success';
                        modalIcon.style.background = 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)';
                        modalIcon.style.color = '#16a34a';
                        break;
                }
                modalTitle.textContent = title;
            }
            // Set message based on context
            if (showForgotPassword) {
                modalMessage.innerHTML = 'O email ou palavra-passe estão incorretos. Se esqueceu a sua palavra-passe, pode solicitar uma nova através do botão abaixo.';
            } else {
                modalMessage.innerHTML = message; // Use innerHTML to support HTML content
            }

            // Clear existing buttons and add appropriate ones
            modalButtons.innerHTML = '';
            
            if (showForgotPassword) {
                // Add forgot password button for login errors
                modalButtons.innerHTML = `
                    <button class="modal-button secondary" onclick="showPasswordResetModal()">
                        <i class="fas fa-key" style="margin-right: 0.5rem;"></i>
                        Esqueceu a palavra-passe?
                    </button>
                    <button class="modal-button" onclick="closeModal()">Tentar Novamente</button>
                `;
            } else {
                // Default OK button
                modalButtons.innerHTML = '<button class="modal-button" onclick="closeModal()">OK</button>';
            }
            
            modal.classList.add('show');
        }

        function closeModal() {
            const modal = document.getElementById('alertModal');
            modal.classList.remove('show');
        }

        // Password reset modal functions
        function showPasswordResetModal() {
            closeModal(); // Close the error modal first
            
            // Pre-fill email if available from login form
            const loginEmail = document.getElementById('email')?.value || '';
            const resetEmailInput = document.getElementById('resetEmail');
            if (resetEmailInput && loginEmail) {
                resetEmailInput.value = loginEmail;
            }
            
            const passwordResetModal = document.getElementById('passwordResetModal');
            passwordResetModal.classList.add('show');
            
            // Focus on email input
            setTimeout(() => {
                resetEmailInput?.focus();
            }, 100);
            
            // Add Enter key handler for password reset modal
            const handleResetKeyPress = (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    sendPasswordReset();
                }
            };
            
            resetEmailInput?.addEventListener('keypress', handleResetKeyPress);
            
            // Clean up event listener when modal closes
            passwordResetModal.addEventListener('transitionend', function cleanup(e) {
                if (!passwordResetModal.classList.contains('show')) {
                    resetEmailInput?.removeEventListener('keypress', handleResetKeyPress);
                    passwordResetModal.removeEventListener('transitionend', cleanup);
                }
            });
        }

        function closePasswordResetModal() {
            const passwordResetModal = document.getElementById('passwordResetModal');
            passwordResetModal.classList.remove('show');
        }

        function sendPasswordReset() {
            const resetEmailInput = document.getElementById('resetEmail');
            const resetPasswordBtn = document.getElementById('resetPasswordBtn');
            const email = resetEmailInput.value.trim();

            if (!email) {
                resetEmailInput.focus();
                return;
            }

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showModal('warning', 'Email Inválido', 'Por favor, introduza um endereço de email válido.');
                return;
            }

            // Show loading state
            resetPasswordBtn.disabled = true;
            resetPasswordBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>A enviar...';

            // Send password reset request
            console.log('🔄 Sending password reset request for email:', email);
            fetch('./send_password_reset.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => {
                console.log('📡 Password reset response status:', response.status);
                console.log('📡 Password reset response ok:', response.ok);
                return response.text(); // Get as text first to see what we're getting
            })
            .then(responseText => {
                console.log('📄 Password reset raw response:', responseText);
                try {
                    const data = JSON.parse(responseText);
                    console.log('✅ Password reset parsed data:', data);
                    
                    resetPasswordBtn.disabled = false;
                    resetPasswordBtn.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i><span>Enviar Email</span>';
                    
                    closePasswordResetModal();
                    
                    if (data.success) {
                        showModal('success', 'Email Enviado', 
                            'As instruções para repor a palavra-passe foram enviadas para o seu email. ' +
                            'Verifique a sua caixa de entrada e pasta de spam.');
                    } else {
                        showModal('error', 'Erro', data.message || 'Ocorreu um erro ao enviar o email. Tente novamente.');
                    }
                } catch (e) {
                    console.error('❌ Failed to parse JSON response:', e);
                    console.error('❌ Raw response was:', responseText);
                    
                    resetPasswordBtn.disabled = false;
                    resetPasswordBtn.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i><span>Enviar Email</span>';
                    closePasswordResetModal();
                    
                    // Check if it's an HTML error page
                    if (responseText.includes('<html>') || responseText.includes('<!DOCTYPE')) {
                        showModal('error', 'Erro do Servidor', 'Ocorreu um erro no servidor. Por favor, contacte o suporte técnico.');
                    } else {
                        showModal('error', 'Erro', 'Resposta inválida do servidor. Tente novamente.');
                    }
                }
            })
            .catch(error => {
                console.error('💥 Password reset network error:', error);
                resetPasswordBtn.disabled = false;
                resetPasswordBtn.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i><span>Enviar Email</span>';
                closePasswordResetModal();
                showModal('error', 'Erro', 'Ocorreu um erro de rede. Verifique a sua conexão e tente novamente.');
            });
        }

        // Close modal when clicking outside
        document.getElementById('alertModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close password reset modal when clicking outside
        document.getElementById('passwordResetModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePasswordResetModal();
            }
        });

        // Handle ESC key to close modals
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const alertModal = document.getElementById('alertModal');
                const passwordResetModal = document.getElementById('passwordResetModal');
                
                if (passwordResetModal.classList.contains('show')) {
                    closePasswordResetModal();
                } else if (alertModal.classList.contains('show')) {
                    closeModal();
                }
            }
        });

        // Auto-focus on appropriate field
        document.addEventListener('DOMContentLoaded', function() {
            const mode = '<?php echo $current_mode; ?>';
            
            if (mode === 'login') {
                document.getElementById('email')?.focus();
            } else if (mode === 'nif_validation') {
                document.getElementById('nif')?.focus();
            } else if (mode === 'email_validation') {
                document.getElementById('email')?.focus();
            } else if (mode === 'register') {
                document.getElementById('name')?.focus();
            }

            // Show modal if there's an error or success message
            <?php if ($error): ?>
                console.error('❌ Error occurred:', <?php echo json_encode($error); ?>);
                <?php if (strpos($error, 'Todas as zonas de caça associadas a este NIF já foram registadas.') !== false): ?>
                    showModal('info', 'Informação', <?php echo json_encode($error); ?>);
                <?php elseif ($error_type === 'invalid_credentials'): ?>
                    showModal('error', 'Erro', <?php echo json_encode($error); ?>, true);
                <?php else: ?>
                    showModal('error', 'Erro', <?php echo json_encode($error); ?>);
                <?php endif; ?>
            <?php endif; ?>

            <?php if ($success): ?>
                console.log('✅ Success:', <?php echo json_encode($success); ?>);
                showModal('success', 'Sucesso', <?php echo json_encode($success); ?>);
            <?php endif; ?>
        });

        // NIF input formatting
        const nifInput = document.getElementById('nif');
        if (nifInput) {
            nifInput.addEventListener('input', function(e) {
                // Only allow numbers
                this.value = this.value.replace(/[^0-9]/g, '');
                
                // Limit to 9 digits
                if (this.value.length > 9) {
                    this.value = this.value.substring(0, 9);
                }
            });
        }

        // Loading animation functions
        function showButtonLoading(buttonId, iconClass = 'fas fa-spinner') {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.add('loading');
                const icon = button.querySelector('.btn-icon');
                if (icon) {
                    icon.className = iconClass + ' btn-icon';
                }
            }
        }

        function hideButtonLoading(buttonId, originalIconClass) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('loading');
                const icon = button.querySelector('.btn-icon');
                if (icon) {
                    icon.className = originalIconClass + ' btn-icon';
                }
            }
        }

        // Form validation with modals
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                console.log('📝 Form submitted with action:', action);
                
                if (action === 'validate_nif') {
                    const nif = document.getElementById('nif').value.trim();
                    console.log('🔍 Validating NIF:', nif);
                    
                    if (!nif) {
                        e.preventDefault();
                        console.warn('⚠️ NIF validation failed: empty field');
                        showModal('warning', 'Campo Obrigatório', 'Por favor, introduza o NIF da entidade gestora.');
                        document.getElementById('nif').focus();
                        return;
                    }
                    
                    if (nif.length !== 9) {
                        e.preventDefault();
                        console.warn('⚠️ NIF validation failed: invalid length');
                        showModal('warning', 'NIF Inválido', 'O NIF deve ter exatamente 9 dígitos.');
                        document.getElementById('nif').focus();
                        return;
                    }

                    console.log('✅ NIF validation passed, submitting...');
                    // Show loading animation
                    showButtonLoading('validateNifBtn', 'fas fa-spinner');
                } else if (action === 'send_verification') {
                    console.log('📧 Sending email verification...');
                    // Show loading animation for email verification
                    showButtonLoading('sendVerificationBtn', 'fas fa-spinner');
                } else if (action === 'check_verification') {
                    console.log('🔍 Checking email verification status...');
                    console.log('📊 Current session state:', {
                        tempUserId: '<?php echo $_SESSION['temp_user_id'] ?? 'not set'; ?>',
                        emailVerificationSent: <?php echo json_encode($_SESSION['email_verification_sent'] ?? false); ?>,
                        usingPasswordReset: <?php echo json_encode($_SESSION['using_password_reset'] ?? false); ?>,
                        tempZoneEmail: '<?php echo $_SESSION['temp_zone']['email'] ?? 'not set'; ?>'
                    });
                    // Show loading animation for checking verification
                    showButtonLoading('checkVerificationBtn', 'fas fa-spinner');
                } else if (action === 'resend_verification') {
                    console.log('🔄 Resending email verification...');
                } else if (action === 'continue_to_email') {
                    console.log('➡️ Continuing to email validation...');
                } else if (action === 'validate_email') {
                    const email = document.getElementById('email').value.trim();
                    if (!email) {
                        e.preventDefault();
                        showModal('warning', 'Campo Obrigatório', 'Por favor, introduza o email da zona de caça.');
                        document.getElementById('email').focus();
                        return;
                    }
                } else if (action === 'register') {
                    const name = document.getElementById('name').value.trim();
                    const surname = document.getElementById('surname').value.trim();
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirm_password').value;
                    
                    if (!name || !surname) {
                        e.preventDefault();
                        showModal('warning', 'Campos Obrigatórios', 'Por favor, introduza o nome e apelido.');
                        if (!name) document.getElementById('name').focus();
                        else document.getElementById('surname').focus();
                        return;
                    }
                    
                    if (password.length < 6) {
                        e.preventDefault();
                        showModal('warning', 'Palavra-passe Inválida', 'A palavra-passe deve ter pelo menos 6 caracteres.');
                        document.getElementById('password').focus();
                        return;
                    }
                    
                    if (password !== confirmPassword) {
                        e.preventDefault();
                        showModal('warning', 'Palavras-passe Diferentes', 'As palavras-passe não coincidem. Verifique e tente novamente.');
                        document.getElementById('confirm_password').focus();
                        return;
                    }
                    // Show loading animation for registration
                    showButtonLoading('registerBtn', 'fas fa-spinner');
                } else {
                    // Default login form - add loading animation
                    const emailInput = document.getElementById('email');
                    const passwordInput = document.getElementById('password');
                    
                    if (emailInput && passwordInput) {
                        const email = emailInput.value.trim();
                        const password = passwordInput.value.trim();
                        
                        if (!email) {
                            e.preventDefault();
                            showModal('warning', 'Campo Obrigatório', 'Por favor, introduza o seu endereço de email.');
                            emailInput.focus();
                            return;
                        }
                        
                        if (!password) {
                            e.preventDefault();
                            showModal('warning', 'Campo Obrigatório', 'Por favor, introduza a sua palavra-passe.');
                            passwordInput.focus();
                            return;
                        }
                        
                        // Show loading animation for login
                        showButtonLoading('loginBtn', 'fas fa-spinner');
                    }
                }
            });
        });

        // Password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId === 'password' ? 
                (document.getElementById('password-toggle-icon-reg') ? 'password-toggle-icon-reg' : 'password-toggle-icon') : 
                'confirm-password-toggle-icon');
            
            if (passwordField && toggleIcon) {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    toggleIcon.className = 'fas fa-eye-slash';
                } else {
                    passwordField.type = 'password';
                    toggleIcon.className = 'fas fa-eye';
                }
            }
        }

        // Help modal function using SweetAlert
        function showNifValidationHelp() {
            // Generate a unique timestamp to force GIF reload
            const timestamp = new Date().getTime();
            
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Validação de NIF</div>',
                html: '<div class="help-modal-content">' +
                        '<div style="text-align: center; margin-bottom: 0.75rem;">' +
                            '<div style="display: flex; align-items: center; justify-content: center; margin-bottom: 0.5rem;">' +
                                '<div style="background: #3b82f6; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">' +
                                    '<i class="fas fa-play" style="font-size: 0.7rem;"></i>' +
                                '</div>' +
                                '<span style="font-weight: 600; color: #374151; font-size: 1rem;">Demonstração Visual</span>' +
                            '</div>' +
                        '</div>' +
                        '<div class="demo-gif-container">' +
                            '<img src="/gestores/assets/images/nif_reg.gif?' + timestamp + '" alt="Demonstração do processo de registo" style="max-width: 70%; height: auto;">' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-list-ol text-primary me-2"></i>' +
                                '<span>Passos do Processo</span>' +
                            '</div>' +
                        '</div>' +
                        '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0;">' +
                            '<div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">' +
                                '<div style="display: flex; align-items: center; margin-bottom: 0.5rem;">' +
                                    '<div style="background: #3b82f6; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 0.5rem; font-size: 0.8rem; font-weight: bold;">1</div>' +
                                    '<span style="font-weight: 600; color: #374151;">Validar NIF</span>' +
                                '</div>' +
                                '<p style="margin: 0; font-size: 0.85rem; color: #6b7280;">Introduza o NIF de 9 dígitos da entidade gestora</p>' +
                            '</div>' +
                            '<div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">' +
                                '<div style="display: flex; align-items: center; margin-bottom: 0.5rem;">' +
                                    '<div style="background: #10b981; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 0.5rem; font-size: 0.8rem; font-weight: bold;">2</div>' +
                                    '<span style="font-weight: 600; color: #374151;">Zonas Associadas</span>' +
                                '</div>' +
                                '<p style="margin: 0; font-size: 0.85rem; color: #6b7280;">Visualize as zonas de caça associadas ao NIF</p>' +
                            '</div>' +
                            '<div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">' +
                                '<div style="display: flex; align-items: center; margin-bottom: 0.5rem;">' +
                                    '<div style="background: #f59e0b; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 0.5rem; font-size: 0.8rem; font-weight: bold;">3</div>' +
                                    '<span style="font-weight: 600; color: #374151;">Verificar Email</span>' +
                                '</div>' +
                                '<p style="margin: 0 0 0.5rem 0; font-size: 0.85rem; color: #6b7280;">Confirme o email registado na zona de caça</p>' +
                                '<div style="font-size: 0.8rem; color: #9ca3af;">' +
                                    '<div style="margin-bottom: 0.25rem;">• Clique no link recebido por email</div>' +
                                    '<div style="margin-bottom: 0.25rem;">• Depois clique em "Verificar Email"</div>' +
                                    '<div>• Verifique a pasta de spam se necessário</div>' +
                                '</div>' +
                            '</div>' +
                            '<div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">' +
                                '<div style="display: flex; align-items: center; margin-bottom: 0.5rem;">' +
                                    '<div style="background: #06b6d4; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 0.5rem; font-size: 0.8rem; font-weight: bold;">4</div>' +
                                    '<span style="font-weight: 600; color: #374151;">Criar Conta</span>' +
                                '</div>' +
                                '<p style="margin: 0; font-size: 0.85rem; color: #6b7280;">Complete o registo com nome e palavra-passe</p>' +
                            '</div>' +
                        '</div>' +
                        '<div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 1rem; margin: 1rem 0;">' +
                            '<div style="display: flex; align-items: center; margin-bottom: 0.5rem;">' +
                                '<i class="fas fa-lightbulb" style="color: #f59e0b; margin-right: 0.5rem;"></i>' +
                                '<span style="font-weight: 600; color: #92400e;">Dicas Importantes</span>' +
                            '</div>' +
                            '<ul style="margin: 0; padding-left: 1rem; color: #92400e; font-size: 0.85rem;">' +
                                '<li>O NIF deve ter exatamente 9 dígitos numéricos</li>' +
                                '<li>Deve ter acesso ao email registado na zona de caça</li>' +
                                '<li>Se não receber o email, verifique a pasta de spam</li>' +
                                '<li>Use o botão "Reenviar Email" se necessário</li>' +
                                '<li>Se todas as zonas estiverem registadas, contacte o suporte</li>' +
                            '</ul>' +
                        '</div>' +
                        '<div style="background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px; padding: 1rem; margin-top: 1rem;">' +
                            '<div style="display: flex; align-items: center; margin-bottom: 0.5rem;">' +
                                '<i class="fas fa-envelope" style="color: #3b82f6; margin-right: 0.5rem;"></i>' +
                                '<span style="font-weight: 600; color: #1e40af;">Precisa de Ajuda?</span>' +
                            '</div>' +
                            '<p style="margin: 0; color: #1e40af; font-size: 0.85rem;">' +
                                'Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>' +
                                '<strong><EMAIL></strong>' +
                            '</p>' +
                        '</div>' +
                    '</div>',
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check" style="margin-right: 0.5rem;"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }
    </script>
</body>
</html> 