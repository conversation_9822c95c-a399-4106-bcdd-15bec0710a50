<?php
// Prevent any output before headers
ob_start();

session_start();
require_once '../../includes/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Log the start of the request
error_log("Password reset request started for IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['email'])) {
        echo json_encode(['success' => false, 'message' => 'Email é obrigatório']);
        exit;
    }
    
    $email = trim($input['email']);
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Formato de email inválido']);
        exit;
    }
    
    // Get admin access token first for database operations
    error_log("Getting admin access token for database operations");
    
    try {
        // Check if database is initialized (gestores uses $database, not $firestore)
        if (!isset($database) || !$database) {
            throw new Exception("Database not initialized");
        }
        
        // Get admin access token for database queries
        $adminAccessToken = $database->getAdminAccessToken();
        if (!$adminAccessToken) {
            throw new Exception("Failed to get admin access token for database operations");
        }
        
        // Set the admin token for database operations
        $database->setAccessToken($adminAccessToken);
        error_log("Admin access token set for database operations");
        
    } catch (Exception $e) {
        error_log("Error getting admin access token: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Erro interno do sistema (token)']);
        exit;
    }
    
    // Check if user exists in gestoresZonaCaca collection
    $userExists = false;
    $userName = '';
    
    error_log("Checking if user exists in gestoresZonaCaca for email: $email");
    
    try {
        // Use the gestores database query method (now with admin token)
        // Note: Firestore REST API uses 'EQUAL' instead of '=='
        $documents = $database->queryDocuments('gestoresZonaCaca', 'email', 'EQUAL', $email);
        
        if (!empty($documents)) {
            foreach ($documents as $userData) {
                if ($userData && isset($userData['email']) && $userData['email'] === $email) {
                    $userExists = true;
                    $userName = trim(($userData['nome'] ?? '') . ' ' . ($userData['apelido'] ?? ''));
                    error_log("User found in gestoresZonaCaca: $userName");
                    break;
                }
            }
        }
        
        if (!$userExists) {
            error_log("User not found in gestoresZonaCaca for email: $email");
        }
    } catch (Exception $e) {
        error_log("Error checking user in gestoresZonaCaca: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Erro ao verificar utilizador: ' . $e->getMessage()]);
        exit;
    }
    
    if (!$userExists) {
        echo json_encode(['success' => false, 'message' => 'Email não encontrado no sistema de gestores']);
        exit;
    }
    
    // Admin access token already obtained above, reuse it for Firebase Auth operations
    error_log("Using admin access token for Firebase Auth operations");
    
    // Check if user exists in Firebase Auth
    $firebaseUser = null;
    try {
        $lookupUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=' . FIREBASE_API_KEY;
        $lookupData = json_encode([
            'email' => [$email]
        ]);
        
        $lookupContext = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $adminAccessToken
                ],
                'content' => $lookupData,
                'timeout' => 30
            ]
        ]);
        
        $lookupResponse = file_get_contents($lookupUrl, false, $lookupContext);
        
        if ($lookupResponse !== false) {
            $lookupResult = json_decode($lookupResponse, true);
            
            if (isset($lookupResult['users']) && count($lookupResult['users']) > 0) {
                $firebaseUser = $lookupResult['users'][0];
            }
        }
    } catch (Exception $e) {
        error_log("Error looking up Firebase user: " . $e->getMessage());
    }
    
    // If user doesn't exist in Firebase Auth, create them first
    if (!$firebaseUser) {
        try {
            $createUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=' . FIREBASE_API_KEY;
            $createData = json_encode([
                'email' => $email,
                'password' => bin2hex(random_bytes(16)), // Temporary password
                'displayName' => $userName,
                'emailVerified' => false
            ]);
            
            $createContext = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => [
                        'Content-Type: application/json',
                        'Authorization: Bearer ' . $adminAccessToken
                    ],
                    'content' => $createData,
                    'timeout' => 30
                ]
            ]);
            
            $createResponse = file_get_contents($createUrl, false, $createContext);
            
            if ($createResponse === false) {
                throw new Exception("Failed to create Firebase Auth user");
            }
            
            $createResult = json_decode($createResponse, true);
            
            if (isset($createResult['error'])) {
                throw new Exception("Firebase Auth error: " . $createResult['error']['message']);
            }
            
            error_log("Created Firebase Auth user for gestor: $email");
            
        } catch (Exception $e) {
            error_log("Error creating Firebase Auth user: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Erro ao preparar conta para reset']);
            exit;
        }
    }
    
    // Send password reset email
    try {
        $resetUrl = 'https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=' . FIREBASE_API_KEY;
        $resetData = json_encode([
            'requestType' => 'PASSWORD_RESET',
            'email' => $email
        ]);
        
        $resetContext = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $adminAccessToken
                ],
                'content' => $resetData,
                'timeout' => 30
            ]
        ]);
        
        $resetResponse = file_get_contents($resetUrl, false, $resetContext);
        
        if ($resetResponse === false) {
            throw new Exception("Failed to send password reset email");
        }
        
        $resetResult = json_decode($resetResponse, true);
        
        if (isset($resetResult['error'])) {
            $errorMessage = $resetResult['error']['message'] ?? 'Unknown error';
            
            // Handle specific Firebase errors
            if (strpos($errorMessage, 'EMAIL_NOT_FOUND') !== false) {
                throw new Exception("Email não encontrado no sistema");
            } elseif (strpos($errorMessage, 'TOO_MANY_ATTEMPTS_TRY_LATER') !== false) {
                throw new Exception("Muitas tentativas. Tente novamente mais tarde");
            } else {
                throw new Exception("Erro ao enviar email: " . $errorMessage);
            }
        }
        
        // Log successful password reset request
        error_log("Password reset email sent successfully to gestor: $email");
        
        echo json_encode([
            'success' => true,
            'message' => 'Email de reposição de palavra-passe enviado com sucesso'
        ]);
        
    } catch (Exception $e) {
        error_log("Password reset error for $email: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    
} catch (Exception $e) {
    error_log("General password reset error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do sistema']);
}

// Flush output buffer to send response
ob_end_flush();
?> 