<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Clean any previous output
ob_end_clean();

http_response_code(404);
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>gina Não Encontrada - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, <PERSON>xy<PERSON>, <PERSON>bu<PERSON><PERSON>, <PERSON><PERSON>ell, sans-serif;
            background: #0a7ea4;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .error-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-icon {
            font-size: 4rem;
            color: #0a7ea4;
            margin-bottom: 1.5rem;
            opacity: 0.8;
        }

        .error-title {
            font-size: 3rem;
            font-weight: 700;
            color: #374151;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.5;
        }

        .btn-home {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 2rem;
            background: #0a7ea4;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(10, 126, 164, 0.3);
        }

        .btn-home:hover {
            background: #0969a3;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(10, 126, 164, 0.4);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .logo-container {
            display: inline-block;
            margin-bottom: 0.5rem;
        }

        .logo-img {
            width: 80px;
            height: 80px;
        }

        .error-subtitle {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .error-container {
                max-width: 100%;
                margin: 0 1rem;
            }
            
            .error-card {
                padding: 2rem 1.5rem;
                border-radius: 15px;
            }

            .error-title {
                font-size: 2.5rem;
            }

            .error-icon {
                font-size: 3rem;
            }

            .btn-home {
                padding: 1rem 1.5rem;
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../../../webadmin/assets/img/prorola-logo.svg" alt="<?php echo SITE_NAME; ?> Logo" class="logo-img">
                </div>
                <p class="error-subtitle">Gestores de Zonas de Caça</p>
            </div>
            
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="error-title">404</h1>
            
            <p class="error-message">
                A página que procura não foi encontrada.<br>
                Verifique o endereço ou volte ao início.
            </p>
            
            <?php if (isGestorUser()): ?>
                <a href="../dashboard/" class="btn-home">
                    <i class="fas fa-tachometer-alt"></i>
                    Voltar ao Painel
                </a>
            <?php else: ?>
                <a href="../auth/login.php" class="btn-home">
                    <i class="fas fa-sign-in-alt"></i>
                    Iniciar Sessão
                </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html> 