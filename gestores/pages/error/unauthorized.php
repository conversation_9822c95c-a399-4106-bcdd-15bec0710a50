<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Get current user info
$currentEmail = isset($_SESSION['user']['email']) ? $_SESSION['user']['email'] : 'não autenticado';
$currentRole = isset($_SESSION['user']['role']) ? $_SESSION['user']['role'] : 'não autenticado';

// Clean any previous output
ob_end_clean();

http_response_code(403);
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acesso Não Autorizado - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #dc2626;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .error-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 600px;
            text-align: center;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-icon {
            font-size: 4rem;
            color: #dc2626;
            margin-bottom: 1.5rem;
            opacity: 0.8;
        }

        .error-title {
            font-size: 3rem;
            font-weight: 700;
            color: #374151;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.5;
        }

        .error-details {
            background: #f3f4f6;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
            border-left: 4px solid #dc2626;
        }

        .error-details h4 {
            color: #374151;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .error-details p {
            margin: 0.5rem 0;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .error-details strong {
            color: #374151;
            font-weight: 600;
        }

        .btn-home {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 2rem;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.3);
        }

        .btn-home:hover {
            background: #b91c1c;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .logo-container {
            display: inline-block;
            margin-bottom: 0.5rem;
        }

        .logo-img {
            width: 80px;
            height: 80px;
        }

        .error-subtitle {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .error-container {
                max-width: 100%;
                margin: 0 1rem;
            }
            
            .error-card {
                padding: 2rem 1.5rem;
                border-radius: 15px;
            }

            .error-title {
                font-size: 2.5rem;
            }

            .error-icon {
                font-size: 3rem;
            }

            .btn-home {
                padding: 1rem 1.5rem;
                font-size: 0.95rem;
            }

            .error-details {
                padding: 1rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../../../webadmin/assets/img/prorola-logo.svg" alt="<?php echo SITE_NAME; ?> Logo" class="logo-img">
                </div>
                <p class="error-subtitle">Gestores de Zonas de Caça</p>
            </div>
            
            <div class="error-icon">
                <i class="fas fa-lock"></i>
            </div>
            
            <h1 class="error-title">403</h1>
            
            <p class="error-message">
                Não tem permissão para aceder a esta área.<br>
                Esta secção é restrita a gestores de zonas de caça autorizados.
            </p>
            
            <div class="error-details">
                <h4>
                    <i class="fas fa-info-circle"></i>
                    Detalhes do Acesso
                </h4>
                <p><strong>Email:</strong> <?php echo htmlspecialchars($currentEmail); ?></p>
                <p><strong>Função atual:</strong> <?php echo htmlspecialchars($currentRole); ?></p>
                <p><strong>Função necessária:</strong> Gestor de Zona de Caça</p>
                <p><strong>Área solicitada:</strong> Sistema de Gestores</p>
            </div>
            
            <?php if (isGestorUser()): ?>
                <a href="../dashboard/" class="btn-home">
                    <i class="fas fa-tachometer-alt"></i>
                    Voltar ao Painel
                </a>
            <?php else: ?>
                <a href="../auth/login.php" class="btn-home" onclick="clearSessionAndRedirect(event)">
                    <i class="fas fa-sign-in-alt"></i>
                    Voltar ao Login
                </a>
            <?php endif; ?>
        </div>
    </div>

    <script>
    function clearSessionAndRedirect(event) {
        event.preventDefault();
        
        // Clear any stored session data
        document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
        });
        
        // Redirect to gestores login
        window.location.href = '../auth/login.php';
    }
    </script>
</body>
</html> 