<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Clean any previous output
ob_end_clean();

http_response_code(500);
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erro Interno do Servidor - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f59e0b;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .error-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 600px;
            text-align: center;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-icon {
            font-size: 4rem;
            color: #f59e0b;
            margin-bottom: 1.5rem;
            opacity: 0.8;
        }

        .error-title {
            font-size: 3rem;
            font-weight: 700;
            color: #374151;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.5;
        }

        .error-details {
            background: #fef3c7;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
            border-left: 4px solid #f59e0b;
        }

        .error-details h4 {
            color: #92400e;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .error-details ul {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
            list-style-type: disc;
        }

        .error-details li {
            margin: 0.25rem 0;
            font-size: 0.9rem;
            color: #92400e;
        }

        .btn-home {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 2rem;
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            margin-right: 1rem;
        }

        .btn-home:hover {
            background: #d97706;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        }

        .btn-secondary {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 2rem;
            background: transparent;
            color: #f59e0b;
            border: 2px solid #f59e0b;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #f59e0b;
            color: white;
            transform: translateY(-2px);
        }

        .buttons-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            align-items: center;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .logo-container {
            display: inline-block;
            margin-bottom: 0.5rem;
        }

        .logo-img {
            width: 80px;
            height: 80px;
        }

        .error-subtitle {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .error-container {
                max-width: 100%;
                margin: 0 1rem;
            }
            
            .error-card {
                padding: 2rem 1.5rem;
                border-radius: 15px;
            }

            .error-title {
                font-size: 2.5rem;
            }

            .error-icon {
                font-size: 3rem;
            }

            .btn-home, .btn-secondary {
                padding: 1rem 1.5rem;
                font-size: 0.95rem;
            }

            .buttons-container {
                flex-direction: column;
                width: 100%;
            }

            .buttons-container .btn-home,
            .buttons-container .btn-secondary {
                width: 100%;
                margin: 0;
            }

            .error-details {
                padding: 1rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="../../../webadmin/assets/img/prorola-logo.svg" alt="<?php echo SITE_NAME; ?> Logo" class="logo-img">
                </div>
                <p class="error-subtitle">Gestores de Zonas de Caça</p>
            </div>
            
            <div class="error-icon">
                <i class="fas fa-server"></i>
            </div>
            
            <h1 class="error-title">500</h1>
            
            <p class="error-message">
                Ocorreu um erro interno no servidor.<br>
                Estamos a trabalhar para resolver o problema.
            </p>
            
            <div class="error-details">
                <h4>
                    <i class="fas fa-tools"></i>
                    O que pode fazer:
                </h4>
                <ul>
                    <li>Aguarde alguns minutos e tente novamente</li>
                    <li>Verifique a sua ligação à internet</li>
                    <li>Contacte o suporte técnico se o problema persistir</li>
                    <li>Volte à página anterior ou ao início</li>
                </ul>
            </div>
            

        </div>
    </div>
</body>
</html> 