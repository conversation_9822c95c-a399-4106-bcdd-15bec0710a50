<?php
// Start output buffering to prevent header issues  
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Clean any previous output
ob_end_clean();

header('Content-Type: application/json');

try {
    // Validate required fields
    if (!isset($_FILES['photo']) || !isset($_POST['trajectoryId']) || !isset($_POST['type']) || !isset($_POST['index'])) {
        throw new Exception('Missing required fields');
    }
    
    $trajectoryId = $_POST['trajectoryId'];
    $type = $_POST['type']; // 'maps' or 'other'
    $index = $_POST['index'];
    $file = $_FILES['photo'];
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $file['error']);
    }
    
    // Validate file size (5MB max)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('File size exceeds 5MB limit');
    }
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Invalid file type. Only JPG, PNG, and GIF are allowed.');
    }
    
    // Initialize Firebase using the custom implementation
    $firebase = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
    
    // Get admin access token for server-side operations
    $adminToken = $firebase->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception('Failed to get admin access token');
    }
    
    error_log("Admin token length: " . strlen($adminToken));
    error_log("Admin token starts with: " . substr($adminToken, 0, 50) . "...");
    
    // Test token validity by making a simple API call
    $testUrl = "https://storage.googleapis.com/storage/v1/b/" . str_replace('.firebasestorage.app', '', FIREBASE_STORAGE_BUCKET);
    $ch = curl_init($testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken
    ]);
    $testResponse = curl_exec($ch);
    $testStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    error_log("Token test - Status: " . $testStatus . ", Response: " . $testResponse);
    
    // Generate unique filename
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = $trajectoryId . '_' . $type . '_' . $index . '_' . time() . '.' . $fileExtension;
            $storagePath = 'zonas/' . $trajectoryId . '/photos/' . $fileName;
    
    // Read file content
    $fileContent = file_get_contents($file['tmp_name']);
    if ($fileContent === false) {
        throw new Exception('Failed to read uploaded file');
    }
    
    // Try Google Cloud Storage API first, then fallback to Firebase Storage
    $bucketName = str_replace('.firebasestorage.app', '', FIREBASE_STORAGE_BUCKET);
    
    // Method 1: Google Cloud Storage API
    $uploadUrl = "https://storage.googleapis.com/upload/storage/v1/b/{$bucketName}/o?uploadType=media&name=" . urlencode($storagePath);
    
    error_log("Trying Google Cloud Storage API");
    error_log("Upload URL: " . $uploadUrl);
    error_log("Bucket name: " . $bucketName);
    error_log("Storage path: " . $storagePath);
    error_log("File size: " . strlen($fileContent));
    error_log("File type: " . $file['type']);
    
    $ch = curl_init($uploadUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContent);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken,
        'Content-Type: ' . $file['type']
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    error_log("GCS Upload response status: " . $status);
    error_log("GCS Upload response: " . $response);
    if ($curlError) {
        error_log("GCS cURL error: " . $curlError);
    }
    
    // If Google Cloud Storage fails, try Firebase Storage API
    if ($status >= 400) {
        error_log("Google Cloud Storage failed, trying Firebase Storage API");
        
        $uploadUrl = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o?name=" . urlencode($storagePath) . "&uploadType=media";
        
        error_log("Firebase Storage URL: " . $uploadUrl);
        
        $ch = curl_init($uploadUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContent);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken,
            'Content-Type: ' . $file['type']
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        error_log("Firebase Storage response status: " . $status);
        error_log("Firebase Storage response: " . $response);
        if ($curlError) {
            error_log("Firebase Storage cURL error: " . $curlError);
        }
    }
    
    if ($status >= 400) {
        $errorResponse = json_decode($response, true);
        $errorMessage = isset($errorResponse['error']['message']) ? $errorResponse['error']['message'] : 'Unknown error';
        error_log("Storage upload error details: " . print_r($errorResponse, true));
        
        // If Firebase Storage fails, save locally as fallback
        error_log("Firebase Storage failed, saving locally as fallback");
        
        // Create local directory structure
        $localDir = dirname(dirname(__DIR__)) . '/uploads/zonas/' . $trajectoryId . '/photos';
        if (!is_dir($localDir)) {
            mkdir($localDir, 0755, true);
        }
        
        // Save file locally
        $localPath = $localDir . '/' . $fileName;
        if (file_put_contents($localPath, $fileContent) === false) {
            throw new Exception("Failed to save file locally after Firebase Storage failure");
        }
        
        // Create local download URL
        $downloadUrl = GESTORES_SITE_URL . '/uploads/zonas/' . $trajectoryId . '/photos/' . $fileName;
        
        error_log("File saved locally: " . $localPath);
        error_log("Local download URL: " . $downloadUrl);
        
        // Continue with Firestore update using local URL
        $uploadResult = ['name' => $storagePath, 'bucket' => 'local'];
    } else {
        $uploadResult = json_decode($response, true);
        
        // Get download URL - use the correct format
        $downloadUrl = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/" . urlencode($storagePath) . "?alt=media";
    }
    
    // Prepare photo data for Firestore
    $photoData = [
        'fileName' => ['stringValue' => $fileName],
        'originalName' => ['stringValue' => $file['name']],
        'storagePath' => ['stringValue' => $storagePath],
        'downloadUrl' => ['stringValue' => $downloadUrl],
        'type' => ['stringValue' => $type],
        'index' => ['integerValue' => (string)$index],
        'size' => ['integerValue' => (string)$file['size']],
        'mimeType' => ['stringValue' => $file['type']],
        'uploadedAt' => ['timestampValue' => date('c')],
        'uploadedBy' => ['stringValue' => $_SESSION['user']['email'] ?? 'unknown']
    ];
    
    // Add photo to Firestore subcollection using REST API
    $firestoreUrl = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/zonas/{$trajectoryId}/photos/{$fileName}";
    
    $postData = json_encode(['fields' => $photoData]);
    
    $ch = curl_init($firestoreUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($status >= 400) {
        $errorResponse = json_decode($response, true);
        $errorMessage = isset($errorResponse['error']['message']) ? $errorResponse['error']['message'] : 'Unknown error';
        throw new Exception("Firestore update failed: {$errorMessage} (Status: {$status})");
    }
    
    $isLocal = isset($uploadResult['bucket']) && $uploadResult['bucket'] === 'local';
    
    echo json_encode([
        'success' => true,
        'message' => $isLocal ? 'Photo saved locally (Firebase Storage unavailable)' : 'Photo uploaded successfully',
        'fileName' => $fileName,
        'downloadUrl' => $downloadUrl,
        'storagePath' => $storagePath,
        'type' => $type,
        'index' => $index,
        'storage' => $isLocal ? 'local' : 'firebase'
    ]);
    
} catch (Exception $e) {
    error_log('Error uploading trajectory photo: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error uploading photo: ' . $e->getMessage()
    ]);
}
?> 