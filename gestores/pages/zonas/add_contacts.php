<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Clean any previous output
ob_end_clean();

// Get trajectory ID from URL
$trajectory_id = $_GET['trajectory_id'] ?? '';

if (empty($trajectory_id)) {
    ob_end_clean();
    header('Location: index.php');
    exit();
}

// Load trajectory data from Firestore
$trajectory_data = null;
try {
    $firebase = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
    
    // Get user's access token from session
    if (isset($_SESSION['user']) && isset($_SESSION['user']['auth_token'])) {
        $firebase->setAccessToken($_SESSION['user']['auth_token']);
        
        // Fetch trajectory document
        $trajectory_doc = $firebase->getDocument('zonas', $trajectory_id);
        
        if ($trajectory_doc) {
            $trajectory_data = [
                'id' => $trajectory_id,
                'name' => $trajectory_doc['name'] ?? 'Trajeto - ' . date('d/m/Y'),
                'distance' => $trajectory_doc['distance'] ?? '0 km',
                'points' => $trajectory_doc['pointsCount'] ?? 0,
                'coordinates' => $trajectory_doc['coordinates'] ?? [],
                'startingAddress' => $trajectory_doc['startingAddress'] ?? 'Localização não definida'
            ];
        }
    }
} catch (Exception $e) {
    error_log("Error loading trajectory data: " . $e->getMessage());
}

// If trajectory data couldn't be loaded, redirect back
if (!$trajectory_data) {
    ob_end_clean();
    header('Location: index.php?error=trajectory_not_found');
    exit();
}
?>

<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="language" content="pt-PT">
    <meta http-equiv="Content-Language" content="pt-PT">
    <title>Adicionar Contactos - <?php echo htmlspecialchars($trajectory_data['name']); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    <!-- Google Maps API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap&libraries=geometry"></script>
    
    <style>
        /* Contact-specific styles - now integrated into layout */
        
        /* Ensure we have the same styling as trajectory creation */
        .google-map {
            width: 100%;
            height: 500px;
            border-radius: 8px;
        }
        
        .trajeto-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .info-badge {
            background: #0a7ea4;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .stats-section {
            padding: 1rem 1.5rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-item-address {
            flex-wrap: wrap;
        }
        
        .stat-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .stat-value {
            font-size: 0.9rem;
            font-weight: 500;
            color: #374151;
        }
        
        .map-container {
            padding: 1.5rem;
            background: #f9fafb;
        }
        
        .card-actions {
            padding: 1rem 1.5rem;
            background: white;
            border-top: 1px solid #e2e8f0;
        }
        
        .form-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
            align-items: center;
        }

        /* Modal styling */
        .contact-modal .modal-content {
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .contact-modal .modal-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 16px 16px 0 0;
            border: none;
        }

        .contact-modal .modal-body {
            padding: 2rem;
        }

        .contact-modal .modal-footer {
            border-top: 1px solid #dee2e6;
            padding: 1rem 2rem;
        }

        /* Contact form styling */
        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h6 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-section h6 i {
            color: #0a7ea4;
        }

        .contact-circumstances {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
        }

        .circumstance-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .circumstance-item:last-child {
            border-bottom: none;
        }

        .circumstance-item label {
            margin: 0;
            color: #374151;
            font-size: 0.9rem;
        }

        .circumstance-item input[type="number"] {
            width: 60px;
            text-align: center;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 0.25rem;
        }

        .location-circumstances {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
        }

        .other-input {
            margin-top: 0.5rem;
            width: 100%;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 0.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .form-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .form-actions .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-dove"></i>
                Adicionar Contactos
                <span class="zone-name"><?php echo htmlspecialchars($trajectory_data['name']); ?></span>
            </h1>
        </div>
        <div class="header-actions">
            <button type="button" class="btn btn-outline-light btn-sm" onclick="window.history.back()">
                <i class="fas fa-arrow-left me-1"></i>
                Voltar
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div style="padding-top:20px">
            
            <!-- Contact Addition Form -->
            <div class="row">
                <!-- Combined Card - Full Width -->
                <div class="col-12">
                    <div class="trajeto-card">
                        <!-- Stats Header -->
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-route me-2"></i>
                                Informações do Trajeto
                            </h5>
                            <span class="info-badge">
                                <i class="fas fa-mouse-pointer me-1"></i>
                                Clique no mapa para adicionar contactos com rola-brava
                            </span>
                        </div>
                        
                        <!-- Stats Section -->
                        <div class="stats-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="stat-item stat-item-address">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <span class="stat-label">LOCALIZAÇÃO:</span>
                                        <span class="stat-value"><?php echo htmlspecialchars($trajectory_data['startingAddress']); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-route me-1"></i>
                                        <span class="stat-label">DISTÂNCIA:</span>
                                        <span class="stat-value"><?php echo htmlspecialchars($trajectory_data['distance']); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-map-pin me-1"></i>
                                        <span class="stat-label">PONTOS:</span>
                                        <span class="stat-value"><?php echo $trajectory_data['points']; ?></span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-dove me-1"></i>
                                        <span class="stat-label">CONTACTOS:</span>
                                        <span class="stat-value" id="contactCount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Map with Floating Controls -->
                        <div class="map-container">
                            <div id="map" class="google-map"></div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="card-actions">
                            <form id="contactsForm">
                                <input type="hidden" id="trajectoryId" value="<?php echo htmlspecialchars($trajectory_data['id']); ?>">
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn-outline-secondary" onclick="cancelContactAddition()">
                                        <i class="fas fa-times me-1"></i>
                                        Cancelar
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="clearContactsBtn" disabled onclick="clearAllContacts()">
                                        <i class="fas fa-eraser me-1"></i>
                                        Limpar Contactos
                                    </button>
                                    <button type="button" class="btn btn-warning" id="undoContactBtn" disabled onclick="undoLastContact()">
                                        <i class="fas fa-undo me-1"></i>
                                        Desfazer Último
                                    </button>
                                    <button type="submit" class="btn btn-success" id="saveContactsBtn" disabled>
                                        <i class="fas fa-save me-1"></i>
                                        Guardar Contactos
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Contact Confirmation Modal -->
    <div class="modal fade contact-modal" id="contactConfirmationModal" tabindex="-1" aria-labelledby="contactConfirmationModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white">
                    <h5 class="modal-title" id="contactConfirmationModalLabel">
                        <i class="fas fa-dove me-2"></i>
                        Adicionar Contacto
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-map-marker-alt text-primary mb-3" style="font-size: 2rem;"></i>
                        <h6 class="mb-3">Confirmar Localização do Contacto</h6>
                        <p class="text-muted mb-0">Deseja adicionar um contacto com rola-brava nesta localização?</p>
                        <small class="text-muted" id="contactCoordinates"></small>
                    </div>
                </div>
                <div class="modal-footer justify-content-center gap-2">
                    <button type="button" class="btn btn-secondary" onclick="cancelContactPlacement()">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmContactPlacement()">
                        <i class="fas fa-check me-1"></i>
                        Sim, Adicionar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Details Modal -->
    <div class="modal fade contact-modal" id="contactDetailsModal" tabindex="-1" aria-labelledby="contactDetailsModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header text-white">
                    <h5 class="modal-title" id="contactDetailsModalLabel">
                        <i class="fas fa-dove me-2"></i>
                        Detalhes do Contacto
                    </h5>
                </div>
                <div class="modal-body">
                    <form id="contactDetailsForm">
                        <!-- Contact Location Info -->
                        <div class="form-section">
                            <h6><i class="fas fa-map-marker-alt"></i>Localização do contacto com rola-brava</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Hora:</label>
                                    <input type="time" class="form-control" id="contactTime" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Coordenadas do início do trajeto:</label>
                                    <input type="text" class="form-control" id="trajectoryCoordinates" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Circumstances -->
                        <div class="form-section">
                            <h6><i class="fas fa-eye"></i>Indique em que circunstância se deu o contacto com rola-brava</h6>
                            <div class="contact-circumstances">
                                <div class="circumstance-item">
                                    <label>Rola adulta a cantar</label>
                                    <input type="number" min="0" value="0" id="adultSinging">
                                </div>
                                <div class="circumstance-item">
                                    <label>Rola em voo</label>
                                    <input type="number" min="0" value="0" id="flying">
                                </div>
                                <div class="circumstance-item">
                                    <label>Adulto pousado</label>
                                    <input type="number" min="0" value="0" id="adultPerched">
                                </div>
                                <div class="circumstance-item">
                                    <label>Adulto em display</label>
                                    <input type="number" min="0" value="0" id="adultDisplay">
                                </div>
                                <div class="circumstance-item">
                                    <label>Ninho vazio</label>
                                    <input type="number" min="0" value="0" id="emptyNest">
                                </div>
                                <div class="circumstance-item">
                                    <label>Nicho ocupado</label>
                                    <input type="number" min="0" value="0" id="occupiedNest">
                                </div>
                                <div class="circumstance-item">
                                    <label>Ovos</label>
                                    <input type="number" min="0" value="0" id="eggs">
                                </div>
                                <div class="circumstance-item">
                                    <label>Adulto a incubar</label>
                                    <input type="number" min="0" value="0" id="adultIncubating">
                                </div>
                                <div class="circumstance-item">
                                    <label>Crias</label>
                                    <input type="number" min="0" value="0" id="chicks">
                                </div>
                                <div class="circumstance-item">
                                    <label>Juvenil</label>
                                    <input type="number" min="0" value="0" id="juvenile">
                                </div>
                                <div class="circumstance-item">
                                    <label>Outra. Qual?</label>
                                    <input type="number" min="0" value="0" id="other">
                                </div>
                            </div>
                            <input type="text" class="other-input" id="otherDescription" placeholder="Descreva a outra circunstância...">
                        </div>

                        <!-- Contact Location -->
                        <div class="form-section">
                            <h6><i class="fas fa-tree"></i>Indique onde se deu contacto</h6>
                            <div class="location-circumstances">
                                <div class="circumstance-item">
                                    <label>Árvore</label>
                                    <input type="number" min="0" value="0" id="tree">
                                </div>
                                <div class="circumstance-item">
                                    <label>Arbusto</label>
                                    <input type="number" min="0" value="0" id="shrub">
                                </div>
                                <div class="circumstance-item">
                                    <label>Ponto de Água</label>
                                    <input type="number" min="0" value="0" id="waterPoint">
                                </div>
                                <div class="circumstance-item">
                                    <label>Clareira</label>
                                    <input type="number" min="0" value="0" id="clearing">
                                </div>
                                <div class="circumstance-item">
                                    <label>Parcela Agrícola</label>
                                    <input type="number" min="0" value="0" id="agriculturalPlot">
                                </div>
                                <div class="circumstance-item">
                                    <label>Outro. Qual?</label>
                                    <input type="number" min="0" value="0" id="otherLocation">
                                </div>
                            </div>
                            <input type="text" class="other-input" id="otherLocationDescription" placeholder="Descreva o outro local...">
                        </div>
                    </form>
                </div>
                <div class="modal-footer justify-content-center gap-2">
                    <button type="button" class="btn btn-secondary" onclick="cancelContactDetails()">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveContactDetails()">
                        <i class="fas fa-save me-1"></i>
                        Guardar Contacto
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let map;
        let trajectoryPath;
        let contactMarkers = [];
        let pendingContactLocation = null;
        let contactCount = 0;

        // Trajectory coordinates from PHP
        const trajectoryCoordinates = <?php echo json_encode($trajectory_data['coordinates']); ?>;

        // Initialize Google Map
        function initMap() {
            // Calculate center of trajectory
            const center = calculateCenter(trajectoryCoordinates);
            
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 16,
                center: center,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_RIGHT
                },
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_CENTER
                },
                fullscreenControl: false,
                streetViewControl: false
            });

            // Draw trajectory path
            drawTrajectoryPath();

            // Add click listener for adding contacts
            map.addListener('click', function(event) {
                handleMapClick(event.latLng);
            });
        }

        function calculateCenter(coordinates) {
            let lat = 0, lng = 0;
            coordinates.forEach(coord => {
                lat += coord.lat;
                lng += coord.lng;
            });
            return {
                lat: lat / coordinates.length,
                lng: lng / coordinates.length
            };
        }

        function drawTrajectoryPath() {
            // Draw the main trajectory path
            trajectoryPath = new google.maps.Polyline({
                path: trajectoryCoordinates,
                geodesic: true,
                strokeColor: '#0a7ea4',
                strokeOpacity: 1.0,
                strokeWeight: 4
            });
            trajectoryPath.setMap(map);

            // Add trajectory markers
            trajectoryCoordinates.forEach((coord, index) => {
                let markerIcon, label;
                
                if (index === 0) {
                    // Start marker
                    markerIcon = {
                        path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                        fillColor: '#22c55e',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                        scale: 0.7,
                        anchor: new google.maps.Point(0, 8),
                        labelOrigin: new google.maps.Point(0, -40)
                    };
                    label = {
                        text: 'INÍCIO',
                        fontSize: '9px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    };
                } else if (index === trajectoryCoordinates.length - 1) {
                    // End marker
                    markerIcon = {
                        path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                        fillColor: '#dc2626',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                        scale: 0.7,
                        anchor: new google.maps.Point(0, 8),
                        labelOrigin: new google.maps.Point(0, -40)
                    };
                    label = {
                        text: 'FIM',
                        fontSize: '9px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    };
                } else {
                    // Regular point
                    markerIcon = {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: '#0a7ea4',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    };
                    label = {
                        text: (index + 1).toString(),
                        fontSize: '11px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    };
                }

                new google.maps.Marker({
                    position: coord,
                    map: map,
                    icon: markerIcon,
                    label: label,
                    title: index === 0 ? 'Início' : (index === trajectoryCoordinates.length - 1 ? 'Fim' : `Ponto ${index + 1}`),
                    zIndex: 100
                });
            });
        }

        function handleMapClick(latLng) {
            console.log('Map clicked at:', latLng.lat(), latLng.lng());
            
            // Store the pending contact location
            pendingContactLocation = {
                lat: latLng.lat(),
                lng: latLng.lng()
            };

            // Update coordinates in confirmation modal
            document.getElementById('contactCoordinates').textContent = 
                `Lat: ${latLng.lat().toFixed(6)}, Lng: ${latLng.lng().toFixed(6)}`;

            // Show confirmation modal
            const modal = new bootstrap.Modal(document.getElementById('contactConfirmationModal'));
            modal.show();
        }

        function cancelContactPlacement() {
            console.log('Contact placement cancelled');
            pendingContactLocation = null;
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactConfirmationModal'));
            modal.hide();
        }

        function confirmContactPlacement() {
            console.log('Contact placement confirmed');
            
            if (!pendingContactLocation) return;

            // Close confirmation modal
            const confirmModal = bootstrap.Modal.getInstance(document.getElementById('contactConfirmationModal'));
            confirmModal.hide();

            // Add temporary contact marker
            addContactMarker(pendingContactLocation);

            // Show contact details modal after a short delay
            setTimeout(() => {
                showContactDetailsModal();
            }, 300);
        }

        function addContactMarker(location) {
            // Create dove/rola marker using Font Awesome dove icon path
            const marker = new google.maps.Marker({
                position: location,
                map: map,
                icon: {
                    path: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
                    fillColor: '#10b981',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 1.2,
                    anchor: new google.maps.Point(12, 12)
                },
                title: `Contacto ${contactCount + 1}`,
                zIndex: 200
            });

            // Store marker data
            contactMarkers.push({
                marker: marker,
                location: location,
                details: null // Will be filled when form is submitted
            });

            updateContactCounter();
        }

        function showContactDetailsModal() {
            // Set current time as default
            const now = new Date();
            const timeString = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0');
            document.getElementById('contactTime').value = timeString;

            // Set trajectory coordinates
            const startCoord = trajectoryCoordinates[0];
            document.getElementById('trajectoryCoordinates').value = 
                `${startCoord.lat.toFixed(6)}, ${startCoord.lng.toFixed(6)}`;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('contactDetailsModal'));
            modal.show();
        }

        function cancelContactDetails() {
            console.log('Contact details cancelled - removing marker');
            
            // Remove the last added marker
            if (contactMarkers.length > 0) {
                const lastContact = contactMarkers.pop();
                lastContact.marker.setMap(null);
                updateContactCounter();
            }

            pendingContactLocation = null;
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactDetailsModal'));
            modal.hide();
        }

        function saveContactDetails() {
            console.log('Saving contact details');
            
            // Collect form data
            const formData = {
                time: document.getElementById('contactTime').value,
                location: pendingContactLocation,
                circumstances: {
                    adultSinging: parseInt(document.getElementById('adultSinging').value) || 0,
                    flying: parseInt(document.getElementById('flying').value) || 0,
                    adultPerched: parseInt(document.getElementById('adultPerched').value) || 0,
                    adultDisplay: parseInt(document.getElementById('adultDisplay').value) || 0,
                    emptyNest: parseInt(document.getElementById('emptyNest').value) || 0,
                    occupiedNest: parseInt(document.getElementById('occupiedNest').value) || 0,
                    eggs: parseInt(document.getElementById('eggs').value) || 0,
                    adultIncubating: parseInt(document.getElementById('adultIncubating').value) || 0,
                    chicks: parseInt(document.getElementById('chicks').value) || 0,
                    juvenile: parseInt(document.getElementById('juvenile').value) || 0,
                    other: parseInt(document.getElementById('other').value) || 0,
                    otherDescription: document.getElementById('otherDescription').value
                },
                locationDetails: {
                    tree: parseInt(document.getElementById('tree').value) || 0,
                    shrub: parseInt(document.getElementById('shrub').value) || 0,
                    waterPoint: parseInt(document.getElementById('waterPoint').value) || 0,
                    clearing: parseInt(document.getElementById('clearing').value) || 0,
                    agriculturalPlot: parseInt(document.getElementById('agriculturalPlot').value) || 0,
                    otherLocation: parseInt(document.getElementById('otherLocation').value) || 0,
                    otherLocationDescription: document.getElementById('otherLocationDescription').value
                }
            };

            // Validate that at least one field has a value > 0
            const hasCircumstances = Object.values(formData.circumstances).some(val => 
                typeof val === 'number' && val > 0
            );
            const hasLocationDetails = Object.values(formData.locationDetails).some(val => 
                typeof val === 'number' && val > 0
            );

            if (!hasCircumstances || !hasLocationDetails) {
                alert('Por favor, preencha pelo menos uma circunstância e um local de contacto.');
                return;
            }

            // Store details in the last contact marker
            if (contactMarkers.length > 0) {
                contactMarkers[contactMarkers.length - 1].details = formData;
            }

            console.log('Contact saved:', formData);

            // Reset form
            document.getElementById('contactDetailsForm').reset();
            pendingContactLocation = null;

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactDetailsModal'));
            modal.hide();

            // Show success message
            alert('Contacto adicionado com sucesso!');
        }

        function updateContactCounter() {
            contactCount = contactMarkers.length;
            document.getElementById('contactCount').textContent = contactCount;

            // Enable/disable buttons
            const saveBtn = document.getElementById('saveContactsBtn');
            const clearBtn = document.getElementById('clearContactsBtn');
            const undoBtn = document.getElementById('undoContactBtn');
            
            if (contactCount > 0) {
                saveBtn.disabled = false;
                clearBtn.disabled = false;
                undoBtn.disabled = false;
            } else {
                saveBtn.disabled = true;
                clearBtn.disabled = true;
                undoBtn.disabled = true;
            }
        }

        function clearAllContacts() {
            if (confirm('Tem a certeza que deseja remover todos os contactos?')) {
                contactMarkers.forEach(contact => {
                    contact.marker.setMap(null);
                });
                contactMarkers = [];
                updateContactCounter();
            }
        }

        function cancelContactAddition() {
            if (contactMarkers.length > 0) {
                if (confirm('Tem contactos não guardados. Tem a certeza que deseja cancelar?')) {
                    unblockSidebar();
                    window.history.back();
                }
            } else {
                unblockSidebar();
                window.history.back();
            }
        }

        function undoLastContact() {
            if (contactMarkers.length > 0) {
                const lastContact = contactMarkers.pop();
                lastContact.marker.setMap(null);
                updateContactCounter();
                console.log('Removed last contact, remaining:', contactMarkers.length);
            }
        }

        function saveContacts() {
            if (contactMarkers.length === 0) {
                alert('Não há contactos para guardar.');
                return;
            }

            console.log('Saving all contacts:', contactMarkers);
            
            // Here you would send the data to the server
            // For now, just show a success message and redirect
            alert(`${contactCount} contacto(s) guardado(s) com sucesso!`);
            
            // Unblock sidebar before redirecting
            unblockSidebar();
            
            // Redirect back to trajectory list or completion page
            window.location.href = 'index.php';
        }

        // Map will be initialized via callback when Google Maps API loads

        // Sidebar blocking functionality - block navigation during contact addition
        let sidebarBlocked = false;
        let originalSidebarAttributes = new Map();

        function blockSidebar() {
            if (sidebarBlocked) return;
            
            console.log('Blocking sidebar navigation during contact addition');
            sidebarBlocked = true;

            // Create and show warning banner
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                const warningBanner = document.createElement('div');
                warningBanner.id = 'sidebar-warning-banner';
                warningBanner.className = 'sidebar-warning-banner';
                warningBanner.innerHTML = `
                    <div class="warning-content">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span>Navegação bloqueada durante adição de contactos</span>
                    </div>
                `;
                
                // Add styles
                warningBanner.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: ${sidebar.offsetWidth}px;
                    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                    color: white;
                    padding: 0.75rem 1rem;
                    font-size: 0.875rem;
                    font-weight: 500;
                    z-index: 1060;
                    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
                    backdrop-filter: blur(8px);
                    transform: translateY(-100%);
                    transition: transform 0.3s ease-in-out;
                `;

                document.body.appendChild(warningBanner);
                
                // Trigger animation
                setTimeout(() => {
                    warningBanner.style.transform = 'translateY(0)';
                }, 10);
            }

            // Disable all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar a, .sidebar button');
            sidebarLinks.forEach(link => {
                // Store original attributes
                const key = link.outerHTML;
                originalSidebarAttributes.set(link, {
                    href: link.getAttribute('href'),
                    onclick: link.getAttribute('onclick'),
                    style: link.getAttribute('style') || ''
                });

                // Disable link
                if (link.getAttribute('href')) {
                    link.removeAttribute('href');
                }
                if (link.getAttribute('onclick')) {
                    link.removeAttribute('onclick');
                }
                
                // Add disabled styling
                const currentStyle = link.getAttribute('style') || '';
                link.setAttribute('style', currentStyle + '; opacity: 0.5; cursor: not-allowed; pointer-events: none;');
            });

            // Add click interceptor
            document.addEventListener('click', sidebarClickInterceptor, true);
        }

        function unblockSidebar() {
            if (!sidebarBlocked) return;
            
            console.log('Unblocking sidebar navigation');
            sidebarBlocked = false;

            // Remove warning banner
            const warningBanner = document.getElementById('sidebar-warning-banner');
            if (warningBanner) {
                warningBanner.style.transform = 'translateY(-100%)';
                setTimeout(() => {
                    warningBanner.remove();
                }, 300);
            }

            // Restore all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar a, .sidebar button');
            sidebarLinks.forEach(link => {
                const original = originalSidebarAttributes.get(link);
                if (original) {
                    // Restore attributes
                    if (original.href) {
                        link.setAttribute('href', original.href);
                    }
                    if (original.onclick) {
                        link.setAttribute('onclick', original.onclick);
                    }
                    link.setAttribute('style', original.style);
                }
            });

            // Clear stored attributes
            originalSidebarAttributes.clear();

            // Remove click interceptor
            document.removeEventListener('click', sidebarClickInterceptor, true);
        }

        function sidebarClickInterceptor(event) {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar && sidebar.contains(event.target)) {
                event.preventDefault();
                event.stopPropagation();
                showNavigationBlockedModal();
                return false;
            }
        }

        function showNavigationBlockedModal() {
            // Create modal if it doesn't exist
            let modal = document.getElementById('navigationBlockedModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'navigationBlockedModal';
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered modal-sm">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Navegação Bloqueada
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <p class="mb-0">A navegação está bloqueada durante a adição de contactos.</p>
                                <small class="text-muted">Termine ou cancele a operação atual primeiro.</small>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // Auto-hide after 3 seconds
            setTimeout(() => {
                bsModal.hide();
            }, 3000);
        }

        // Block sidebar when page loads (since we're in contact addition mode)
        document.addEventListener('DOMContentLoaded', function() {
            blockSidebar();
            
            // Handle form submission
            document.getElementById('contactsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveContacts();
            });
        });

        // Unblock sidebar when leaving the page
        window.addEventListener('beforeunload', function() {
            unblockSidebar();
        });

        // Override the back button to unblock sidebar
        const originalHistoryBack = window.history.back;
        window.history.back = function() {
            unblockSidebar();
            originalHistoryBack.call(window.history);
        };
    </script>
</body>
</html> 