<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Clean any previous output
ob_end_clean();

header('Content-Type: application/json');

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate required fields
    if (empty($data['trajectoryId'])) {
        throw new Exception('Trajectory ID is required');
    }
    
    $trajectoryId = $data['trajectoryId'];
    $middleTime = $data['middleTime'] ?? '';
    $endTime = $data['endTime'] ?? '';
    $hasMapPhotos = $data['hasMapPhotos'] ?? false;
    $hasOtherPhotos = $data['hasOtherPhotos'] ?? false;
    $mapPhotosCount = $data['mapPhotosCount'] ?? 0;
    $otherPhotosCount = $data['otherPhotosCount'] ?? 0;
    
    // Initialize Firebase using the custom implementation
    $firebase = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
    
    // Get admin access token for server-side operations
    $adminToken = $firebase->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception('Failed to get admin access token');
    }
    
    // Prepare update data for Firestore
    $updateData = [
        'middleTime' => ['stringValue' => $middleTime],
        'endTime' => ['stringValue' => $endTime],
        'hasMapPhotos' => ['booleanValue' => $hasMapPhotos],
        'hasOtherPhotos' => ['booleanValue' => $hasOtherPhotos],
        'mapPhotosCount' => ['integerValue' => (string)$mapPhotosCount],
        'otherPhotosCount' => ['integerValue' => (string)$otherPhotosCount],
        'detailsUpdatedAt' => ['timestampValue' => date('c')]
    ];
    
    // Remove empty string values
    if (empty($middleTime)) unset($updateData['middleTime']);
    if (empty($endTime)) unset($updateData['endTime']);
    
    // Update the trajectory document using REST API
    $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/zonas/{$trajectoryId}?updateMask.fieldPaths=" . implode('&updateMask.fieldPaths=', array_keys($updateData));
    
    $postData = json_encode(['fields' => $updateData]);
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($status >= 400) {
        $errorResponse = json_decode($response, true);
        $errorMessage = isset($errorResponse['error']['message']) ? $errorResponse['error']['message'] : 'Unknown error';
        throw new Exception("Firestore update failed: {$errorMessage} (Status: {$status})");
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Trajectory details saved successfully',
        'trajectoryId' => $trajectoryId,
        'updatedFields' => array_keys($updateData)
    ]);
    
} catch (Exception $e) {
    error_log('Error saving trajectory details: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error saving trajectory details: ' . $e->getMessage()
    ]);
}
?> 