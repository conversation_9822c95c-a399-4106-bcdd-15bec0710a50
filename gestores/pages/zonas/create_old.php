<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Get zone information from URL parameters
$zoneId = $_GET['zoneId'] ?? '';
$zoneName = $_GET['zoneName'] ?? '';

if (empty($zoneId)) {
    ob_end_clean();
    header('Location: index.php');
    exit();
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="language" content="pt-PT">
    <meta http-equiv="Content-Language" content="pt-PT">
    <title>Criar Trajeto - <?php echo htmlspecialchars($zoneName); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <!-- Additional CSS for Photo Upload -->
    <style>
        .photos-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .photo-preview-item {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .photo-preview-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }
        
        .dropzone-container:hover {
            border-color: #0891b2 !important;
            background-color: rgba(10, 126, 164, 0.08) !important;
        }
        
        .dropzone-container.dragover {
            border-color: #0891b2 !important;
            background-color: rgba(10, 126, 164, 0.1) !important;
            transform: scale(1.02);
        }
        
        .form-check-container {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .form-check-inline {
            margin-right: 0;
        }
        
        .form-check-input:checked {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
        }
        
        .form-check-input:focus {
            border-color: #0891b2;
            box-shadow: 0 0 0 0.25rem rgba(10, 126, 164, 0.25);
        }
        
        .btn-outline-primary:hover {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
        }
        
        .alert .form-label {
            font-size: 0.95rem;
            margin-bottom: 0.75rem;
        }
        
        .time-input-card:hover {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.08) 0%, rgba(8, 145, 178, 0.06) 100%) !important;
            border-color: rgba(10, 126, 164, 0.25) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.15);
        }
        
        .photo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #d1d5db;
        }
        
        .photo-card button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        #saveDetailsBtn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(10, 126, 164, 0.4) !important;
        }
        
        .photos-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
            min-height: 20px;
        }

        @media (max-width: 768px) {
            .modal-xl {
                max-width: 95vw;
            }
            
            .photos-preview {
                justify-content: flex-start;
            }
            
            .photo-preview-item img {
                width: 80px !important;
                height: 80px !important;
            }
        }
        
        /* Distance validation styles */
        .distance-insufficient {
            color: #dc2626 !important;
            font-weight: 600 !important;
        }
        
        .distance-sufficient {
            color: #16a34a !important;
            font-weight: 600 !important;
        }
        
        .distance-warning {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border: 1px solid #f87171;
            color: #dc2626;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.15rem;
            white-space: nowrap;
            line-height: 1;
            margin-left: 0.4rem;
            vertical-align: text-top;
            height: 18px;
            transform: translateY(1px);
            animation: flashWarning 1.5s infinite;
        }
        
        @keyframes flashWarning {
            0%, 100% { 
                opacity: 1;
                background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
                border-color: #f87171;
            }
            50% { 
                opacity: 0.7;
                background: linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%);
                border-color: #ef4444;
            }
        }
        
        .distance-success {
            background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
            border: 1px solid #86efac;
            color: #16a34a;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.15rem;
            white-space: nowrap;
            line-height: 1;
            margin-left: 0.4rem;
            vertical-align: text-top;
            height: 18px;
            transform: translateY(1px);
        }
        
        /* Distance validation popup styling */
        .distance-validation-popup {
            border-radius: 16px !important;
            overflow: hidden !important;
        }
        
        .distance-validation-popup .swal2-html-container {
            padding: 0 !important;
            margin: 0 !important;
        }
        
        /* Override webadmin modal styles to prevent dimmed overlay */
        .modal {
            background-color: transparent !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }
        
        /* Ensure modal backdrop doesn't interfere with page interaction */
        .modal-backdrop {
            display: none !important;
        }
        
        /* Override any potential overlay effects */
        body.modal-open {
            overflow: auto !important;
        }
        
        /* Fix modal z-index stacking issues */
        #locationChoiceModal {
            z-index: 1055 !important;
        }
        
        #locationChoiceModal .modal-dialog {
            z-index: 1056 !important;
        }
        
        /* Make Finalizar Trajeto modal wider */
        #trajectoryDetailsModal .modal-dialog {
            max-width: 1400px !important;
            width: 95vw !important;
        }
        
        @media (max-width: 1450px) {
            #trajectoryDetailsModal .modal-dialog {
                max-width: 90vw !important;
            }
        }
        
        @media (max-width: 768px) {
            #trajectoryDetailsModal .modal-dialog {
                max-width: 95vw !important;
                width: 95vw !important;
            }
        }
        
        /* Make header text white for location choice modal */
        #locationChoiceModal .modal-header,
        #locationChoiceModal .modal-header .modal-title,
        #locationChoiceModal .modal-header h5 {
            color: white !important;
        }
        
        /* Make header text white for location search modal */
        #locationSearchModal .modal-header,
        #locationSearchModal .modal-header .modal-title,
        #locationSearchModal .modal-header h5 {
            color: white !important;
        }
        
        /* Fix z-index for location search modal */
        #locationSearchModal {
            z-index: 1056 !important;
        }
        
        #locationSearchModal .modal-dialog {
            z-index: 1057 !important;
        }
        
        /* Save button disabled state for distance validation */
        #saveTrajetoBtn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            position: relative;
        }
        
        #saveTrajetoBtn:disabled:hover::after {
            content: "Trajeto deve ter pelo menos 2.5 km";
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 0.25rem;
        }
        
        #saveTrajetoBtn:disabled:hover::before {
            content: "";
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            margin-bottom: 0.125rem;
        }
    </style>
</head>
<body>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-route"></i>
                Criar Trajeto
                <span class="zone-name"><?php echo htmlspecialchars($zoneName); ?></span>
            </h1>
        </div>
        <div class="header-actions">
            <button type="button" class="btn btn-help" onclick="showTrajetoHelp()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div style="padding-top:20px">
            
            <!-- Trajeto Creation Form -->
            <div class="row">
                <!-- Combined Card - Full Width -->
                <div class="col-12">
                    <div class="trajeto-card">
                        <!-- Stats Header -->
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-route me-2"></i>
                                Informações do Trajeto
                            </h5>
                            <span class="info-badge">
                                <i class="fas fa-mouse-pointer me-1"></i>
                                Clique no mapa para criar pontos do trajeto
                            </span>
                        </div>
                        
                        <!-- Stats Section -->
                        <div class="stats-section">
                            <div class="stats-flex-container">
                                <div class="stat-item-wrapper stat-address-wrapper">
                                    <div class="stat-item stat-item-address">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <span class="stat-label">INÍCIO:</span>
                                        <span class="stat-value" id="startingAddress">Não definido</span>
                                    </div>
                                </div>
                                <div class="stat-item-wrapper stat-distance-wrapper">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-route me-1"></i>
                                        <span class="stat-label">DISTÂNCIA:</span>
                                        <span class="stat-value" id="routeDistance">0 km</span>
                                        <div id="distanceValidation" style="display: none;"></div>
                                    </div>
                                </div>
                                <div class="stat-item-wrapper stat-points-wrapper">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-map-pin me-1"></i>
                                        <span class="stat-label">PONTOS:</span>
                                        <span class="stat-value" id="routePoints">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Map Header -->
                        <!-- Removed separate map header to save space -->
                        
                        <!-- Map with Floating Controls -->
                        <div class="map-container">
                            <div id="map" class="google-map"></div>
                        </div>
                        

                        
                        <!-- Action Buttons -->
                        <div class="card-actions">
                            <form id="trajetoForm">
                                <input type="hidden" id="zoneId" value="<?php echo htmlspecialchars($zoneId); ?>">
                                <input type="hidden" id="routeCoordinates" value="">
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn-outline-secondary" onclick="cancelCreate()">
                                        <i class="fas fa-times me-1"></i>
                                        Cancelar
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearRoute()">
                                        <i class="fas fa-eraser me-1"></i>
                                        Limpar Trajeto
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="undoLastPoint()" id="undoBtn" disabled>
                                        <i class="fas fa-undo me-1"></i>
                                        Desfazer Último
                                    </button>
                                    <button type="submit" class="btn btn-success" id="saveTrajetoBtn" disabled>
                                        <i class="fas fa-save me-1"></i>
                                        Guardar Trajeto
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Step 1: Trajectory Setup Modal -->
    <div class="modal fade" id="trajectorySetupModal" tabindex="-1" aria-labelledby="trajectorySetupModalLabel" aria-hidden="true" data-bs-backdrop="false" data-bs-keyboard="false" style="z-index: 1050;">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="position: relative; z-index: 1052;">
            <div class="modal-content trajectory-setup-modal" style="position: relative; z-index: 1053; pointer-events: all;">
                <div class="modal-header">
                    <h5 class="modal-title" id="trajectorySetupModalLabel">
                        <i class="fas fa-clipboard-list me-2"></i>
                        Informações do Trajeto
                    </h5>
                    <div class="step-indicator">
                        <span class="step-number">2</span>
                        <span class="step-text">de 5</span>
                    </div>
                </div>
                <div class="modal-body">
                    <form id="trajectorySetupForm">
                        <div class="row">
                            <!-- Date and Time -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trajectoryDate" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>
                                        Data
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="trajectoryDate" placeholder="Selecione a data" readonly required>
                                        <button class="btn btn-outline-secondary" type="button" id="datePickerBtn">
                                            <i class="fas fa-calendar"></i>
                                        </button>
                                    </div>
                                    <div class="form-text text-muted" style="font-size: 0.75rem;">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Clique em qualquer parte do campo para selecionar a data
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trajectoryStartTime" class="form-label">
                                        <i class="fas fa-clock me-1"></i>
                                        Hora de Início
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="trajectoryStartTime" placeholder="Selecione a hora" readonly required>
                                        <button class="btn btn-outline-secondary" type="button" id="timePickerBtn">
                                            <i class="fas fa-clock"></i>
                                        </button>
                                    </div>
                                    <div class="form-text text-muted" style="font-size: 0.75rem;">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Clique em qualquer parte do campo para selecionar a hora
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Weather Conditions -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-cloud-sun me-1"></i>
                                Indique as condições meteorológicas
                            </label>
                            <div class="weather-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_clear" name="weather_conditions" value="clear">
                                    <label class="form-check-label" for="weather_clear">
                                        <i class="fas fa-sun text-warning me-2"></i>
                                        Céu limpo
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_cloudy" name="weather_conditions" value="cloudy">
                                    <label class="form-check-label" for="weather_cloudy">
                                        <i class="fas fa-cloud text-secondary me-2"></i>
                                        Nublado
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_rain" name="weather_conditions" value="rain">
                                    <label class="form-check-label" for="weather_rain">
                                        <i class="fas fa-cloud-rain text-primary me-2"></i>
                                        Chuva
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_wind" name="weather_conditions" value="wind">
                                    <label class="form-check-label" for="weather_wind">
                                        <i class="fas fa-wind text-info me-2"></i>
                                        Vento moderado a forte
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="weather_other" name="weather_conditions" value="other">
                                    <label class="form-check-label" for="weather_other">
                                        <i class="fas fa-question-circle text-muted me-2"></i>
                                        Outro. Qual?
                                    </label>
                                </div>
                                <div class="mt-2" id="weatherOtherInput" style="display: none;">
                                    <input type="text" class="form-control" id="weatherOtherText" placeholder="Especifique as condições meteorológicas...">
                                </div>
                            </div>
                        </div>

                        <!-- Number of Observers -->
                        <div class="mb-3">
                            <label for="numberOfObservers" class="form-label">
                                <i class="fas fa-users me-1"></i>
                                Indique quantos observadores
                            </label>
                            <input type="number" class="form-control" id="numberOfObservers" min="1" max="20" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary trajectory-cancel-btn" onclick="cancelTrajectoryCreation()">
                        <i class="fas fa-times me-2"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary trajectory-next-btn" onclick="proceedToLocationChoice()">
                        <i class="fas fa-arrow-right me-2"></i>
                        Próximo Passo
                    </button>
                </div>
            </div>
            </div>
</div>

<!-- Custom Time Picker Modal -->
<div class="modal fade" id="timePickerModal" tabindex="-1" aria-labelledby="timePickerModalLabel" aria-hidden="true" data-bs-backdrop="static" style="z-index: 1080;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white; border-radius: 16px 16px 0 0;">
                <h5 class="modal-title" id="timePickerModalLabel">
                    <i class="fas fa-clock me-2"></i>Selecionar Hora
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <label class="form-label text-center d-block">Horas</label>
                        <select class="form-select" id="hourSelect" size="8">
                            <option value="00">00</option>
                            <option value="01">01</option>
                            <option value="02">02</option>
                            <option value="03">03</option>
                            <option value="04">04</option>
                            <option value="05">05</option>
                            <option value="06">06</option>
                            <option value="07">07</option>
                            <option value="08">08</option>
                            <option value="09">09</option>
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12">12</option>
                            <option value="13">13</option>
                            <option value="14">14</option>
                            <option value="15">15</option>
                            <option value="16">16</option>
                            <option value="17">17</option>
                            <option value="18">18</option>
                            <option value="19">19</option>
                            <option value="20">20</option>
                            <option value="21">21</option>
                            <option value="22">22</option>
                            <option value="23">23</option>
                        </select>
                    </div>
                    <div class="col-6">
                        <label class="form-label text-center d-block">Minutos</label>
                        <select class="form-select" id="minuteSelect" size="8">
                            <option value="00">00</option>
                            <option value="05">05</option>
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="20">20</option>
                            <option value="25">25</option>
                            <option value="30">30</option>
                            <option value="35">35</option>
                            <option value="40">40</option>
                            <option value="45">45</option>
                            <option value="50">50</option>
                            <option value="55">55</option>
                        </select>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-clock me-2"></i>
                        <span id="selectedTimeDisplay">--:--</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="confirmTimeBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                    <i class="fas fa-check me-2"></i>Confirmar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Date Picker Modal -->
<div class="modal fade" id="datePickerModal" tabindex="-1" aria-labelledby="datePickerModalLabel" aria-hidden="true" data-bs-backdrop="false" style="z-index: 1070;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white; border-radius: 16px 16px 0 0;">
                <h5 class="modal-title" id="datePickerModalLabel">
                    <i class="fas fa-calendar me-2"></i>Selecionar Data
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-4">
                        <label class="form-label text-center d-block">Dia</label>
                        <select class="form-select" id="daySelect" size="8">
                            <!-- Days will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="col-4">
                        <label class="form-label text-center d-block">Mês</label>
                        <select class="form-select" id="monthSelect" size="8">
                            <option value="01">Janeiro</option>
                            <option value="02">Fevereiro</option>
                            <option value="03">Março</option>
                            <option value="04">Abril</option>
                            <option value="05">Maio</option>
                            <option value="06">Junho</option>
                            <option value="07">Julho</option>
                            <option value="08">Agosto</option>
                            <option value="09">Setembro</option>
                            <option value="10">Outubro</option>
                            <option value="11">Novembro</option>
                            <option value="12">Dezembro</option>
                        </select>
                    </div>
                    <div class="col-4">
                        <label class="form-label text-center d-block">Ano</label>
                        <select class="form-select" id="yearSelect" size="8">
                            <!-- Years will be populated by JavaScript -->
                        </select>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-calendar me-2"></i>
                        <span id="selectedDateDisplay">--/--/----</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="confirmDateBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                    <i class="fas fa-check me-2"></i>Confirmar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Trajectory Tutorial Modal -->
    <div class="modal fade" id="trajectoryTutorialModal" tabindex="-1" aria-labelledby="trajectoryTutorialModalLabel" aria-hidden="true" data-bs-backdrop="false" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered" style="height: 90vh; max-height: 90vh;">
        <div class="modal-content" style="height: 100%; display: flex; flex-direction: column;">
            <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white; flex-shrink: 0;">
                <h5 class="modal-title" id="trajectoryTutorialModalLabel">
                    <i class="fas fa-route me-2"></i>Como Criar o Trajeto
                </h5>
                <div class="step-indicator">
                    <span class="step-number">3</span>
                    <span class="step-text">de 5</span>
                </div>
            </div>
                            <div class="modal-body text-center" style="flex: 1; overflow-y: auto; padding: 1rem;">
                  <div class="tutorial-content">
                      <div class="tutorial-intro mb-1">
                          <h4 class="tutorial-title mb-1">Criar o Seu Trajeto</h4>
                          <p class="tutorial-subtitle text-muted mb-0">
                              Siga estes passos simples para registar o trajeto
                          </p>
                      </div>
                    
                    <!-- Animated GIF Tutorial -->
                    <div class="tutorial-video-section mb-1">
                        <div class="video-header mb-1">
                            <h5 class="section-title">
                                <i class="fas fa-play-circle me-2 text-primary"></i>
                                Demonstração Visual
                            </h5>
                            <p class="section-subtitle text-muted mb-0">
                                Veja como criar um trajeto passo a passo
                            </p>
                        </div>
                        <div class="gif-container">
                            <div class="gif-frame">
                                <img src="https://prorola.app/gestores/assets/images/trajeto1.gif" 
                                     alt="Tutorial de criação de trajeto" 
                                     class="tutorial-gif"
                                     onload=""
                                     onerror="console.error('Failed to load GIF:', this.src); this.style.display='none'; document.getElementById('gif-error').style.display='block';"
                                     crossorigin="anonymous">
                                <div id="gif-error" class="gif-error" style="display: none;">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    <span>Tutorial em vídeo não disponível</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step-by-step instructions -->
                    <div class="tutorial-steps-section mb-1">
                        <div class="steps-header mb-1">
                            <h5 class="section-title">
                                <i class="fas fa-list-ol me-2 text-primary"></i>
                                Passos do Processo
                            </h5>
                            <p class="section-subtitle text-muted mb-0">
                                Cada passo é identificado por cores e símbolos específicos
                            </p>
                        </div>
                        <div class="steps-grid">
                            <div class="row">
                                <div class="col-md-6 mb-1">
                                <div class="tutorial-step">
                                    <div class="step-icon">
                                        <div style="width: 24px; height: 24px; background: #22c55e; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 12px;">INÍCIO</div>
                                    </div>
                                    <div class="step-content">
                                        <h6 class="step-title">1. Ponto de Início (Verde)</h6>
                                        <p class="step-description">Clique no mapa para marcar o primeiro ponto com marcador verde "INÍCIO"</p>
                                    </div>
                                </div>
                                                          </div>
                            <div class="col-md-6 mb-1">
                                <div class="tutorial-step">
                                    <div class="step-icon">
                                        <div style="width: 24px; height: 24px; background: #3b82f6; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px;">2</div>
                                    </div>
                                    <div class="step-content">
                                        <h6 class="step-title">2. Pontos Numerados (Azul)</h6>
                                        <p class="step-description">Continue clicando para adicionar pontos numerados (2, 3, 4...) em azul</p>
                                    </div>
                                </div>
                                                          </div>
                            <div class="col-md-6 mb-1">
                                <div class="tutorial-step">
                                    <div class="step-icon">
                                        <div style="width: 24px; height: 24px; background: #ef4444; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 12px;">FIM</div>
                                    </div>
                                    <div class="step-content">
                                        <h6 class="step-title">3. Ponto Final (Vermelho)</h6>
                                        <p class="step-description">O último ponto fica vermelho com "FIM" quando terminar o trajeto</p>
                                    </div>
                                </div>
                                                          </div>
                            <div class="col-md-6 mb-1">
                                <div class="tutorial-step">
                                    <div class="step-icon">
                                        <i class="fas fa-minus" style="color: #0ea5e9; font-size: 1.5rem; font-weight: bold;"></i>
                                    </div>
                                    <div class="step-content">
                                        <h6 class="step-title">4. Linha Azul Conecta</h6>
                                        <p class="step-description">Uma linha azul liga automaticamente todos os pontos do trajeto</p>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tips -->
                    <div class="tutorial-tips-section">
                        <div class="tips-header mb-1">
                            <h5 class="section-title">
                                <i class="fas fa-lightbulb me-2 text-warning"></i>
                                Dicas Importantes
                            </h5>
                        </div>
                        <div class="tips-content">
                            <div class="tip-item">
                                <i class="fas fa-undo text-warning me-2"></i>
                                <span>Use o botão <strong>"Desfazer Último"</strong> para remover pontos indesejados</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-link text-primary me-2"></i>
                                <span>O trajeto é <strong>ligado automaticamente</strong> entre todos os pontos</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-save text-success me-2"></i>
                                <span>Precisa de <strong>mínimo 2 pontos</strong> para guardar o trajeto</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="flex-shrink: 0; border-top: 1px solid #dee2e6; display: flex; justify-content: center; align-items: center;">
                <button type="button" class="btn btn-primary" onclick="proceedFromTutorialToMap()" style="background-color: #0a7ea4; border-color: #0a7ea4;">
                    <i class="fas fa-arrow-right me-1"></i>
                    Entendi, Continuar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Location Choice Modal -->
    <div class="modal fade" id="locationChoiceModal" tabindex="-1" aria-labelledby="locationChoiceModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content location-choice-modal">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationChoiceModalLabel">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Definir Localização Inicial
                    </h5>
                    <div class="step-indicator">
                        <span class="step-number">1</span>
                        <span class="step-text">de 5</span>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-location-arrow text-primary" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                    <h6 class="mb-3">Como pretende definir a localização inicial do trajeto?</h6>
                    <div class="mx-auto mb-4" style="max-width: 500px;">
                        <p class="text-muted small">
                            Escolha uma das opções abaixo para definir onde pretende começar o trajeto.
                        </p>
                    </div>
                    
                    <div class="d-grid gap-3 mx-auto" style="max-width: 350px;">
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="useCurrentLocation()">
                            <i class="fas fa-crosshairs me-2"></i>
                            <span>Usar Localização Atual</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="showLocationSearch()">
                            <i class="fas fa-search me-2"></i>
                            <span>Pesquisar Localização</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="useManualLocation()">
                            <i class="fas fa-map me-2"></i>
                            <span>Definir Manualmente no Mapa</span>
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Pode sempre ajustar a localização clicando no mapa
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Search Modal -->
    <div class="modal fade" id="locationSearchModal" tabindex="-1" aria-labelledby="locationSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content location-choice-modal">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationSearchModalLabel">
                        <i class="fas fa-search me-2"></i>
                        Pesquisar Localização
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body py-4">
                    <div class="mb-3">
                        <label for="locationSearchInput" class="form-label">Digite o nome da cidade, vila ou localização:</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                            </span>
                            <input type="text" class="form-control form-control-lg" id="locationSearchInput" 
                                   placeholder="Ex: Lisboa, Porto, Coimbra..." 
                                   onkeypress="handleLocationSearchEnter(event)">
                            <button class="btn btn-primary" type="button" onclick="searchLocation()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Pode pesquisar por cidade, código postal ou morada específica
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="searchLocation()">
                            <i class="fas fa-search me-2"></i>
                            <span>Pesquisar e Centrar Mapa</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg location-choice-btn" data-bs-dismiss="modal" onclick="showLocationChoiceModal()">
                            <i class="fas fa-arrow-left me-2"></i>
                            <span>Voltar às Opções</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Profile Modal -->
    <?php include '../../includes/profile_modal.php'; ?>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Firebase v8 Compatibility -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    
    <!-- Google Maps API - Load conditionally -->
    <script>
        // Only load Google Maps API if it's not already loaded
        if (typeof google === 'undefined' || !google.maps) {

            const script = document.createElement('script');
            script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap&libraries=geometry';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        } else {

        }
    </script>

    <style>
        /* CSS Variables for dynamic calculations and theme */
        :root {
            --header-height: 48px;
            --content-padding: 60px;
            --card-header-height: 65px;
            --stats-section-height: 70px;
            --card-actions-height: 100px;
            --available-map-height: calc(100vh - var(--header-height) - var(--content-padding) - var(--card-header-height) - var(--stats-section-height) - var(--card-actions-height));
            
            /* Theme Colors */
            --primary-blue: #0a7ea4;
            --primary-blue-light: rgba(10, 126, 164, 0.1);
            --primary-blue-border: rgba(10, 126, 164, 0.2);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-white: #ffffff;
            --bg-gray-50: #f9fafb;
            --bg-gray-100: #f3f4f6;
            --border-gray: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Header and Content Layout */
        .header {
            background-color: #fff;
            padding: 0.5rem 1rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            left: 220px !important;
            z-index: 999 !important;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header.sidebar-collapsed {
            left: 60px !important;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-title {
            color: #374151;
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-title .zone-name {
            color: #0a7ea4;
            font-size: 1rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .content {
            margin-left: 220px !important;
            padding: 60px 24px 24px 24px !important;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 100vh !important;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            width: calc(100% - 220px) !important;
            position: relative !important;
            overflow-x: hidden !important;
        }

        .content.sidebar-collapsed {
            margin-left: 60px !important;
            width: calc(100% - 60px) !important;
        }

        /* Help Button */
        .btn-help {
            background: rgba(10, 126, 164, 0.08);
            color: #0a7ea4;
            border: 1px solid rgba(10, 126, 164, 0.15);
            padding: 0.375rem 0.75rem;
            font-size: 0.813rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
            height: 32px;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .btn-help:hover {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Modern Card Container */
        .trajeto-card {
            background: var(--bg-white);
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(229, 231, 235, 0.8);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .trajeto-card:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(10, 126, 164, 0.1);
            border-color: rgba(10, 126, 164, 0.2);
        }

        /* Breadcrumb */
        .breadcrumb {
            background: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            margin-bottom: 0;
        }

        .breadcrumb-item a {
            color: #0a7ea4;
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            text-decoration: underline;
        }

        /* Form Card */
        .trajeto-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .stats-section {
            padding: 0.5rem;
            background: linear-gradient(135deg, var(--bg-gray-50) 0%, var(--bg-white) 100%);
            border-bottom: 1px solid var(--border-gray);
        }

        .stats-flex-container {
            display: flex;
            gap: 0.5rem;
            align-items: stretch;
        }

        .stat-item-wrapper {
            display: flex;
            flex-direction: column;
        }

        .stat-address-wrapper {
            flex: 1;
            min-width: 0;
        }

        .stat-distance-wrapper {
            flex: 0 0 auto;
            min-width: fit-content;
        }

        .stat-points-wrapper {
            flex: 0 0 auto;
            min-width: fit-content;
            width: auto;
        }

        .stat-item {
            display: flex;
            align-items: center;
            padding: 0.35rem 0.75rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 1px solid rgba(229, 231, 235, 0.6);
            height: 32px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: visible;
            backdrop-filter: blur(8px);
        }

        .stat-address-wrapper .stat-item {
            width: 100%;
        }

        .stat-distance-wrapper .stat-item {
            width: auto;
            white-space: nowrap;
            min-width: fit-content;
            padding-right: 0.75rem;
        }
        
        .stat-points-wrapper .stat-item {
            width: fit-content;
            white-space: nowrap;
        }

        .stat-item i {
            color: var(--primary-blue);
            font-size: 0.8rem;
            margin-right: 0.25rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            margin-right: 0.2rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
            margin-top: 0.08rem !important;
        }

        .stat-value {
            font-weight: 700;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
        }

        /* Address stat item */
        .stat-item-address .stat-label {
            font-size: 0.7rem;
            white-space: nowrap;
            font-weight: 600;
        }

        .stat-item-address .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
            min-width: 0;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.3;
        }

        /* Compact stat items for distance and points */
        .stat-item-compact .stat-label {
            font-size: 0.7rem;
            white-space: nowrap;
        }

        .stat-item-compact .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            white-space: nowrap;
        }

        /* Special styling for address - allow natural wrapping */
        #startingAddress {
            width: 100% !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            overflow: visible !important;
            text-overflow: unset !important;
            white-space: normal !important;
            max-width: none !important;
        }

        /* Map Container - Dynamic Height */
        .map-container {
            position: relative;
            height: var(--available-map-height);
            width: 100%;
            border: none;
            min-height: 400px;
            max-height: calc(100vh - 200px);
        }

        .google-map {
            height: 100%;
            width: 100%;
            border: none;
        }

        .btn-warning {
            background-color: #f59e0b;
            border-color: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
            border-color: #d97706;
        }

        /* Card Header */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, var(--bg-white) 0%, var(--bg-gray-50) 100%);
            border-bottom: 1px solid var(--border-gray);
            position: relative;
        }

        .card-header h5 {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.125rem;
            margin: 0;
            display: flex;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            letter-spacing: -0.025em;
        }

        .card-header h5 i {
            color: var(--primary-blue);
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        .info-badge {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.08) 0%, rgba(10, 126, 164, 0.03) 100%);
            color: var(--primary-blue);
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.2rem 0.875rem;
            border-radius: 20px;
            border: 1px solid rgba(10, 126, 164, 0.15);
            display: flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 1px 3px 0 rgba(10, 126, 164, 0.1), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(4px);
        }

        .info-badge i {
            color: inherit;
            font-size: 0.7rem;
        }

        /* Card Actions */
        .card-actions {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--bg-gray-50) 0%, var(--bg-white) 100%);
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--border-gray);
        }

        /* Form Actions */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
        }

        .form-actions .btn {
            min-width: 140px;
            height: 40px;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        /* Button Styles */
        .btn {
            padding: 0.625rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Modal Centering Fix - Ensure proper vertical centering */
        #trajectorySetupModal {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        #trajectorySetupModal .modal-dialog-centered {
            display: flex !important;
            align-items: center !important;
            min-height: calc(100vh - 1rem) !important;
            justify-content: center !important;
            margin: 0 !important;
        }

        #trajectorySetupModal .modal-dialog {
            margin: 0 auto !important;
            position: relative !important;
            width: auto !important;
            pointer-events: none !important;
            transform: none !important;
        }

        #trajectorySetupModal .modal-content {
            pointer-events: auto !important;
            position: relative !important;
            display: flex !important;
            flex-direction: column !important;
            width: 100% !important;
            background-color: #fff !important;
            background-clip: padding-box !important;
            border: 1px solid rgba(0,0,0,.2) !important;
            border-radius: 0.375rem !important;
            outline: 0 !important;
        }

        @media (min-width: 576px) {
            #trajectorySetupModal .modal-dialog-centered {
                min-height: calc(100vh - 3.5rem) !important;
            }
        }

        /* Override Bootstrap modal positioning */
        #trajectorySetupModal.modal.show {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        #trajectorySetupModal.modal.show .modal-dialog {
            transform: none !important;
        }

        /* Custom SweetAlert2 Styling to match app theme */
        .custom-swal-popup {
            border-radius: 12px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
            font-family: inherit !important;
        }

        .custom-swal-title {
            color: #374151 !important;
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
        }

        .custom-swal-content {
            color: #6b7280 !important;
            font-size: 0.95rem !important;
            line-height: 1.5 !important;
        }

        .custom-swal-confirm {
            background-color: #0a7ea4 !important;
            border: none !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 0.625rem 1.25rem !important;
            font-size: 0.875rem !important;
        }

        .custom-swal-confirm:hover {
            background-color: #0891b2 !important;
        }

        .custom-swal-cancel {
            background-color: #6b7280 !important;
            border: none !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 0.625rem 1.25rem !important;
            font-size: 0.875rem !important;
        }

        .custom-swal-cancel:hover {
            background-color: #4b5563 !important;
        }

        .btn-primary {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0891b2;
            border-color: #0891b2;
        }

        .btn-success {
            background-color: #16a34a;
            border: 1px solid #16a34a;
            color: white;
        }

        .btn-success:hover {
            background-color: #15803d;
            border-color: #15803d;
            color: white;
        }

        .btn-secondary {
            background-color: #6b7280;
            border: 1px solid #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
            border-color: #4b5563;
            color: white;
        }

        .btn-warning {
            background-color: #f59e0b;
            border: 1px solid #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
            border-color: #d97706;
            color: white;
        }

        .btn-outline-secondary {
            background-color: transparent;
            border: 1px solid #9ca3af;
            color: #9ca3af;
        }

        .btn-outline-secondary:hover {
            background-color: #e5e7eb;
            border-color: #6b7280;
            color: #374151;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Trajectory Setup Modal */
        .trajectory-setup-modal {
            border: none !important;
            border-radius: 16px !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden !important;
            position: relative;
            z-index: 1051;
        }

        /* Cancel Confirmation Modal */
        #cancelConfirmationModal {
            z-index: 1055 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            pointer-events: auto !important;
        }

        #cancelConfirmationModal .modal-dialog {
            z-index: 1056 !important;
            pointer-events: auto !important;
        }

        #cancelConfirmationModal .modal-content {
            z-index: 1057 !important;
            pointer-events: auto !important;
        }

        #cancelConfirmationModal .modal-header,
        #cancelConfirmationModal .modal-body,
        #cancelConfirmationModal .modal-footer,
        #cancelConfirmationModal button {
            pointer-events: auto !important;
            z-index: 1058 !important;
        }

        /* Contact Details Modal - Match Weather Options Styling */
        #contactDetailsModal .contact-circumstances .form-check,
        #contactDetailsModal .contact-location .form-check {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            min-height: 52px;
        }
        
        #contactDetailsModal .contact-circumstances .form-check:hover,
        #contactDetailsModal .contact-location .form-check:hover {
            background: rgba(10, 126, 164, 0.02);
            border-color: rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        #contactDetailsModal .contact-circumstances .form-check-input,
        #contactDetailsModal .contact-location .form-check-input {
            position: relative;
            margin: 0 0.75rem 0 0;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        #contactDetailsModal .contact-circumstances .form-check-input:checked,
        #contactDetailsModal .contact-location .form-check-input:checked {
            background-color: #0a7ea4 !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }
        
        #contactDetailsModal .contact-circumstances .form-check-input:checked + .form-check-label,
        #contactDetailsModal .contact-location .form-check-input:checked + .form-check-label {
            color: #0a7ea4 !important;
            font-weight: 600 !important;
        }
        
        #contactDetailsModal .contact-circumstances .form-check:has(.form-check-input:checked),
        #contactDetailsModal .contact-location .form-check:has(.form-check-input:checked) {
            background: rgba(10, 126, 164, 0.05) !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1) !important;
            transform: translateY(-1px) !important;
        }
        
        #contactDetailsModal .contact-circumstances .form-check-input:focus,
        #contactDetailsModal .contact-location .form-check-input:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }
        
        #contactDetailsModal .contact-circumstances .form-check-label,
        #contactDetailsModal .contact-location .form-check-label {
            cursor: pointer;
            display: flex;
            align-items: center;
            margin-bottom: 0;
            font-size: 0.9rem;
            margin-left: 0;
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        /* Ensure modal content has proper rounded corners */
        #trajectorySetupModal .modal-content {
            border-radius: 16px !important;
            overflow: hidden !important;
            border: none !important;
        }
        
        .trajectory-setup-modal .modal-header {
            border-radius: 16px 16px 0 0 !important;
            border-bottom: none !important;
            border-top: none !important;
            border-left: none !important;
            border-right: none !important;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%) !important;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem !important;
            position: relative;
            margin: 0 !important;
        }
        
        .trajectory-setup-modal .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }
        
        .trajectory-setup-modal .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .trajectory-setup-modal .modal-body {
            background: #fafafa;
        }
        
        .trajectory-setup-modal .modal-footer {
            border-radius: 0 0 16px 16px;
            border-top: 1px solid #e5e7eb;
            padding: 1.5rem 2rem;
            background: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }
        
        /* Enhanced Modal Sizing - Always show buttons */
        #trajectorySetupModal .modal-dialog {
            height: auto;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
        }
        
        #trajectorySetupModal .modal-content {
            height: 100%;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
        }
        
        #trajectorySetupModal .modal-body {
            flex: 1;
            overflow-y: auto;
            max-height: calc(90vh - 140px); /* Always leave space for header and footer */
            padding: 1.5rem 2rem;
        }
        
        #trajectorySetupModal .modal-header {
            flex-shrink: 0;
        }
        
        #trajectorySetupModal .modal-footer {
            flex-shrink: 0;
        }
        
        /* Ensure modal works on very small screens */
        @media (max-height: 500px) {
            #trajectorySetupModal .modal-dialog {
                max-height: 95vh;
            }
            #trajectorySetupModal .modal-content {
                max-height: 95vh;
            }
            #trajectorySetupModal .modal-body {
                max-height: calc(95vh - 120px);
                padding: 1rem 1.5rem;
            }
        }

        /* Step Indicator */
        .step-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.95);
            font-size: 0.875rem;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .step-number {
            background: rgba(255, 255, 255, 0.25);
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.75rem;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .step-text {
            font-weight: 500;
            font-size: 0.85rem;
        }

        /* Enhanced Form Input Styling */
        .trajectory-setup-modal .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .trajectory-setup-modal .form-control:focus {
            border-color: #0a7ea4;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
            outline: none;
        }
        
        .trajectory-setup-modal .form-control:hover {
            border-color: rgba(10, 126, 164, 0.3);
        }
        
        .trajectory-setup-modal .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .trajectory-setup-modal .input-group-text,
        .trajectory-setup-modal .btn-outline-secondary {
            border: 2px solid #e5e7eb;
            background: #f9fafb;
            color: #6b7280;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .trajectory-setup-modal .btn-outline-secondary:hover {
            background: #0a7ea4;
            border-color: #0a7ea4;
            color: white;
        }
        
        .trajectory-setup-modal .form-text {
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        /* Enhanced Footer Buttons */
        .trajectory-cancel-btn {
            border: 2px solid #e5e7eb !important;
            color: #6b7280 !important;
            background: white !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 8px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
        }
        
        .trajectory-cancel-btn:hover {
            background: #f3f4f6 !important;
            border-color: #9ca3af !important;
            color: #374151 !important;
        }
        
        .trajectory-next-btn {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%) !important;
            border: 2px solid #0a7ea4 !important;
            color: white !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2) !important;
        }
        
        .trajectory-next-btn:hover {
            background: linear-gradient(135deg, #065a82 0%, #0c7490 100%) !important;
            border-color: #065a82 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(10, 126, 164, 0.3) !important;
        }
        
        /* Ensure Modal Content is Interactive */
        #trajectorySetupModal .modal-content,
        #trajectorySetupModal .modal-content * {
            pointer-events: auto !important;
        }
        
        #trajectorySetupModal {
            pointer-events: none;
        }
        
        #trajectorySetupModal .modal-backdrop {
            pointer-events: auto;
        }
        
        #trajectorySetupModal .modal-dialog {
            pointer-events: auto !important;
            position: relative !important;
            z-index: 1052 !important;
        }
        
        #trajectorySetupModal .modal-content {
            pointer-events: auto !important;
            position: relative !important;
            z-index: 1053 !important;
        }
        
        /* Force interactivity on all form elements */
        #trajectorySetupModal input,
        #trajectorySetupModal button,
        #trajectorySetupModal select,
        #trajectorySetupModal textarea,
        #trajectorySetupModal .form-check-input,
        #trajectorySetupModal .form-check-label {
            pointer-events: auto !important;
            position: relative !important;
            z-index: 1054 !important;
        }
        
        /* Date and Time Picker Modal Styling */
        .modal-content {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        #timePickerModal .modal-body,
        #datePickerModal .modal-body {
            background: #fafafa;
            padding: 1.5rem;
        }
        
        #timePickerModal .form-select,
        #datePickerModal .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.5rem;
            font-size: 0.9rem;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        #timePickerModal .form-select:focus,
        #datePickerModal .form-select:focus {
            border-color: #0a7ea4;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
            outline: none;
        }
        
        #timePickerModal .form-label,
        #datePickerModal .form-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }
        
        #timePickerModal .alert,
        #datePickerModal .alert {
            background: rgba(10, 126, 164, 0.05);
            border: 2px solid rgba(10, 126, 164, 0.1);
            color: #0a7ea4;
            border-radius: 8px;
            font-weight: 500;
        }
        
        /* Date and Time Input Styling */
        .date-input, .time-input {
            cursor: pointer;
            position: relative;
            background: white;
        }
        
        .date-input::-webkit-calendar-picker-indicator,
        .time-input::-webkit-calendar-picker-indicator {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            color: transparent;
            background: transparent;
            cursor: pointer;
            opacity: 0;
            z-index: 2;
        }
        
        .date-input::-webkit-inner-spin-button,
        .time-input::-webkit-inner-spin-button {
            display: none;
        }
        
        .date-input::-webkit-clear-button,
        .time-input::-webkit-clear-button {
            display: none;
        }
        
        /* Add a visual indicator that the whole field is clickable */
        .date-input:hover,
        .time-input:hover {
            border-color: #0a7ea4;
            box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25);
        }
        
        /* Fix for WebKit browsers */
        .date-input::-webkit-datetime-edit,
        .time-input::-webkit-datetime-edit {
            cursor: pointer;
        }
        
        .date-input::-webkit-datetime-edit-fields-wrapper,
        .time-input::-webkit-datetime-edit-fields-wrapper {
            cursor: pointer;
        }
        
        /* Additional time input styling */
        .time-input {
            cursor: pointer !important;
        }
        
        .time-input::-webkit-datetime-edit-hour-field,
        .time-input::-webkit-datetime-edit-minute-field {
            cursor: pointer;
        }
        
        /* Firefox time input support */
        .time-input[type="time"]::-moz-focus-inner {
            border: 0;
        }

        /* Weather Options */
        .weather-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-top: 0.75rem;
        }
        
        .weather-options .form-check {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            min-height: 52px;
        }
        
        .weather-options .form-check:hover {
            background: rgba(10, 126, 164, 0.02);
            border-color: rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .weather-options .form-check-input {
            position: relative;
            margin: 0 0.75rem 0 0;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .weather-options .form-check-input:checked {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
        }
        
        .weather-options .form-check-input:checked + .form-check-label {
            color: #0a7ea4 !important;
            font-weight: 600 !important;
        }
        
        .weather-options .form-check:has(.form-check-input:checked) {
            background: rgba(10, 126, 164, 0.05) !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1) !important;
            transform: translateY(-1px) !important;
        }
        
        /* Enhanced radio button styling */
        .weather-options .form-check-input:checked {
            background-color: #0a7ea4 !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }
        
        .weather-options .form-check-input:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }
        
        /* Compact form sections */
        #trajectorySetupModal .mb-4 {
            margin-bottom: 1.5rem !important;
        }
        
        #trajectorySetupModal .mb-3 {
            margin-bottom: 1rem !important;
        }
        
        #trajectorySetupModal .form-label {
            margin-bottom: 0.5rem !important;
        }
        
        .weather-options .form-check-label {
            cursor: pointer;
            display: flex;
            align-items: center;
            margin-bottom: 0;
            font-size: 0.9rem;
            margin-left: 0;
            flex: 1;
            font-weight: 500;
            color: #374151;
        }

        /* Tutorial Modal Styles */
        .tutorial-intro {
            padding: 0.25rem 0;
        }

        .intro-icon .icon-circle {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.3);
        }

        .intro-icon .icon-circle i {
            font-size: 1rem;
            color: white;
        }

        .tutorial-title {
            color: #1f2937;
            font-weight: 700;
            font-size: 1.25rem;
        }

        .tutorial-subtitle {
            font-size: 0.85rem;
            line-height: 1.3;
        }

        .section-title {
            color: #374151;
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.125rem;
        }

        .section-subtitle {
            font-size: 0.8rem;
            line-height: 1.2;
        }

        /* Video Section */
        .tutorial-video-section {
            padding: 0.25rem 0;
        }

        .gif-container {
            position: relative;
            max-width: 450px;
            margin: 0 auto;
        }

        .gif-frame {
            position: relative;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 0.25rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(10, 126, 164, 0.1);
        }

        .tutorial-gif {
            width: 100%;
            height: auto;
            border-radius: 0.75rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .gif-error {
            padding: 2rem;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 0.75rem;
            color: #6b7280;
        }

        /* Steps Section */
        .tutorial-steps-section {
            padding: 0.25rem 0;
        }

        .tutorial-step {
            display: flex;
            align-items: flex-start;
            text-align: left;
            padding: 0.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 0.375rem;
            height: 100%;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .tutorial-step:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-color: rgba(10, 126, 164, 0.2);
        }

        .tutorial-step .step-icon {
            margin-right: 0.5rem;
            margin-top: 0.125rem;
        }

        .tutorial-step .step-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 0.125rem;
            font-size: 0.85rem;
        }

        .tutorial-step .step-description {
            color: #6b7280;
            margin-bottom: 0;
            font-size: 0.75rem;
            line-height: 1.2;
        }

        /* Tips Section */
        .tutorial-tips-section {
            padding: 0.25rem 0;
        }

        .tips-content {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            padding: 0.5rem;
            border-radius: 0.375rem;
            border: 1px solid #fbbf24;
        }

        .tip-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.25rem;
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .tip-item:last-child {
            margin-bottom: 0;
        }

        .tip-item i {
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        .tip-item span {
            color: #92400e;
        }

        /* Date and Time Picker Modal Z-Index */
        #datePickerModal,
        #timePickerModal {
            z-index: 1060; /* Higher than main modal (1055) */
        }

        #datePickerModal .modal-backdrop,
        #timePickerModal .modal-backdrop {
            z-index: 1055;
            background-color: rgba(0, 0, 0, 0.7) !important;
        }

        #datePickerModal .modal-dialog,
        #timePickerModal .modal-dialog {
            z-index: 1065;
        }

        /* Force remove any lingering overlays */
        body.trajectory-active {
            overflow: auto !important;
            padding-right: 0 !important;
            position: static !important;
        }

        body.trajectory-active::before,
        body.trajectory-active::after {
            display: none !important;
        }

        .trajectory-active .swal2-backdrop,
        .trajectory-active .modal-backdrop,
        .trajectory-active [class*="backdrop"],
        .trajectory-active [class*="overlay"] {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
        
        /* Exception: Allow SweetAlert2 modals to show after trajectory save */
        .swal2-container {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 10000 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            justify-content: center !important;
            align-items: center !important;
        }
        
        .swal2-popup {
            visibility: visible !important;
            opacity: 1 !important;
            display: grid !important;
            position: relative !important;
            margin: auto !important;
        }
        
        .swal2-backdrop {
            display: block !important;
            opacity: 0.4 !important;
            visibility: visible !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
        }
        
        /* Trajectory completion modal styling */
        .trajectory-completion-modal {
            border-radius: 16px !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
        }
        
        /* Modal layering and backdrop fixes */
        #locationConfirmedModal {
            z-index: 1060 !important;
        }
        
        #locationConfirmedModal .modal-backdrop {
            z-index: 1059 !important;
            background-color: rgba(0, 0, 0, 0.6) !important;
        }
        
        /* Ensure trajectory setup modal is dimmed when location modal is shown */
        #trajectorySetupModal.modal-backdrop-dimmed {
            opacity: 0.3 !important;
        }
        
        /* Trajectory saved modal styling */
        #trajectorySavedModal {
            z-index: 1055 !important;
        }
        
        #trajectorySavedModal .modal-backdrop {
            z-index: 1054 !important;
            background-color: rgba(0, 0, 0, 0.7) !important;
        }
        
        /* Ensure proper backdrop for trajectory saved modal */
        .modal-backdrop.show {
            opacity: 0.7 !important;
        }
        
        /* Override any trajectory-active styles that might interfere with modals */
        body:not(.trajectory-active) .modal-backdrop {
            display: block !important;
            opacity: 0.7 !important;
            background-color: rgba(0, 0, 0, 0.7) !important;
        }
        
        /* Ensure trajectory saved modal is always visible */
        #trajectorySavedModal.show {
            display: block !important;
        }
        
        #trajectorySavedModal .modal-dialog {
            transform: none !important;
        }
        
        /* Button hover effects for contact response */
        #trajectorySavedModal .btn-secondary:hover {
            background-color: #5a6268 !important;
            border-color: #5a6268 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        #trajectorySavedModal .btn-primary:hover {
            background-color: #0891b2 !important;
            border-color: #0891b2 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(10, 126, 164, 0.3);
        }

        /* Ensure map is always interactive when trajectory is active */
        .trajectory-active #map {
            pointer-events: auto !important;
            position: relative !important;
            z-index: 1 !important;
            opacity: 1 !important;
        }

        /* Hide all modals when trajectory is active to prevent click blocking */
        .trajectory-active .modal:not(#trajectorySavedModal) {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
            z-index: -1 !important;
        }

        /* Ensure trajectory setup modal specifically doesn't block clicks */
        .trajectory-active #trajectorySetupModal {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
            z-index: -1 !important;
            position: absolute !important;
            top: -9999px !important;
            left: -9999px !important;
        }

        /* Location Choice Modal */
        .location-choice-modal {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .location-choice-modal .modal-header {
            border-radius: 16px 16px 0 0;
            border-bottom: none;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .location-choice-modal .modal-body {
            border-radius: 0 0 16px 16px;
        }
        
        /* Ensure modal is properly centered */
        .modal-dialog-centered {
            display: flex;
            align-items: center;
            min-height: calc(100vh - 1rem);
        }
        
        @media (min-width: 576px) {
            .modal-dialog-centered {
                min-height: calc(100vh - 3.5rem);
            }
        }
        
        /* Location choice button styling */
        .location-choice-btn {
            display: flex !important;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .location-choice-btn i {
            flex-shrink: 0;
        }
        
        .location-choice-btn span {
            flex: 1;
            text-align: center;
        }

        /* Sidebar disabled state */
        .sidebar-disabled {
            position: relative;
        }
        
        .sidebar-disabled::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: 1;
            pointer-events: none;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .header {
                left: 60px !important;
            }
            
            .content {
                margin-left: 60px !important;
                width: calc(100% - 60px) !important;
            }
        }

        @media (max-width: 768px) {
            .zone-info-card {
                flex-direction: column;
                text-align: center;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .map-container {
                height: calc(100vh - 48px - 40px - 100px - 100px);
                min-height: 300px;
            }
        }

        @media (max-width: 576px) {
            .map-container {
                height: calc(100vh - 48px - 40px - 80px - 120px);
                min-height: 250px;
            }
            
            .form-actions .btn {
                min-width: 120px;
                font-size: 0.8rem;
            }
        }
        /* Contact Mode Styles */
        .step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .demo-placeholder {
            min-height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    </style>

    <!-- Location Confirmed Modal -->
    <div class="modal fade" id="locationConfirmedModal" tabindex="-1" aria-labelledby="locationConfirmedModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false" style="z-index: 1060;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content trajectory-setup-modal">
                <div class="modal-header bg-primary text-white" style="flex-shrink: 0;">
                    <h5 class="modal-title" id="locationConfirmedModalLabel">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Localização Definida
                    </h5>
                    <div class="step-indicator">
                        <span class="step-number">1</span>
                        <span class="step-text">de 5</span>
                    </div>
                </div>
                <div class="modal-body text-center" style="padding: 2rem;">
                    <div class="mb-3">
                        <div class="icon-circle mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                    </div>
                    <h6 class="mb-3" style="color: #1f2937; font-weight: 600;">Localização definida!</h6>
                    <p class="text-muted mb-0">O mapa foi centrado na sua localização atual.</p>
                </div>
                <div class="modal-footer" style="flex-shrink: 0; border-top: 1px solid #dee2e6; justify-content: center;">
                    <button type="button" class="btn btn-primary" onclick="closeLocationConfirmedModal()">
                        <i class="fas fa-arrow-right me-1"></i>
                        Continuar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Trajectory Saved Modal -->
    <div class="modal fade" id="trajectorySavedModal" tabindex="-1" aria-labelledby="trajectorySavedModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false" style="z-index: 1055;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content trajectory-setup-modal">
                <div class="modal-header bg-primary text-white" style="flex-shrink: 0;">
                    <h5 class="modal-title" id="trajectorySavedModalLabel">
                        <i class="fas fa-save me-2"></i>
                        Trajeto Guardado
                    </h5>
                    <div class="step-indicator">
                        <span class="step-number">5</span>
                        <span class="step-text">de 5</span>
                    </div>
                </div>
                <div class="modal-body text-center" style="padding: 2rem;">
                    <div class="mb-3">
                        <div class="icon-circle mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                    </div>
                    <h6 class="mb-3" style="color: #1f2937; font-weight: 600;">O seu trajeto foi guardado com sucesso.</h6>
                    <div class="mb-4">
                        <div style="background: rgba(10, 126, 164, 0.1); border: 1px solid rgba(10, 126, 164, 0.2); border-radius: 8px; padding: 1rem;">
                            <i class="fas fa-dove me-2" style="color: #0a7ea4;"></i>
                            <strong style="color: #0a7ea4;">Durante este trajeto, teve algum contacto<br>com uma rola-brava?</strong>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="flex-shrink: 0; border-top: 1px solid #dee2e6; justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-secondary" onclick="handleContactResponse(false)" style="background-color: #6b7280; border-color: #6b7280; color: white;">
                        <i class="fas fa-times me-1"></i>
                        Não, não tive contacto
                    </button>
                    <button type="button" class="btn btn-primary" onclick="handleContactResponse(true)">
                        <i class="fas fa-check me-1"></i>
                        Sim, tive contacto
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Trajectory Completed Modal -->
    <div class="modal fade" id="trajectoryCompletedModal" tabindex="-1" aria-labelledby="trajectoryCompletedModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content trajectory-setup-modal">
                <div class="modal-header bg-primary text-white" style="flex-shrink: 0;">
                    <h5 class="modal-title" id="trajectoryCompletedModalLabel">
                        <i class="fas fa-check-circle me-2"></i>
                        Trajeto Finalizado
                    </h5>
                    <div class="step-indicator">
                        <span class="step-number">✓</span>
                        <span class="step-text">Concluído</span>
                    </div>
                </div>
                <div class="modal-body text-center" style="padding: 2rem;">
                    <div class="mb-3">
                        <div class="icon-circle mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                    </div>
                    <h6 class="mb-3" style="color: #1f2937; font-weight: 600;">Trajeto Finalizado!</h6>
                    <p class="text-muted mb-0">O trajeto foi criado e finalizado com sucesso!</p>
                </div>
                <div class="modal-footer" style="flex-shrink: 0; border-top: 1px solid #dee2e6; justify-content: center;">
                    <button type="button" class="btn btn-primary" onclick="redirectToTrajectoryList()">
                        <i class="fas fa-arrow-left me-1"></i>
                        Voltar à Lista
                    </button>
                </div>
            </div>
        </div>
    </div>



    <script>
        // Check if Google Maps API is already loaded (from previous page navigation)
        const isGoogleMapsLoaded = typeof google !== 'undefined' && google.maps;
        
        // Ensure initMap is available immediately when script starts loading
        window.initMap = window.initMap || function() {
            setTimeout(() => {
                if (typeof window.initMap === 'function' && window.initMap.toString().includes('Default center')) {
                    window.initMap();
                }
            }, 100);
        };

        let map;
        let routePath;
        let routeCoordinates = [];
        let markers = [];

        // Ensure initMap is available immediately when script loads
        function initMap() {
            // Default center (Portugal)
            const center = { lat: 39.5, lng: -8.0 };
            
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 7,
                center: center,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_RIGHT
                },
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_CENTER
                },
                fullscreenControl: false,
                streetViewControl: false,
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            // Initialize route path with shadow effect
            // Shadow path (darker, thicker line underneath)
            const shadowPath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#000000',
                strokeOpacity: 0.4,
                strokeWeight: 6,
                zIndex: 1
            });
            shadowPath.setMap(map);

            // Main route path (colored line on top)
            routePath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#0a7ea4',
                strokeOpacity: 1.0,
                strokeWeight: 4,
                zIndex: 2
            });
            routePath.setMap(map);

            // Store shadow path for updates
            window.shadowPath = shadowPath;

            // Add click listener to map
            map.addListener('click', function(event) {
    
                addRoutePoint(event.latLng);
            });

            // Add double-click listener to finish route
            map.addListener('dblclick', function(event) {
                finishRoute();
            });

            // Show location choice modal after map is initialized (Step 1)
            setTimeout(() => {
                const locationModal = new bootstrap.Modal(document.getElementById('locationChoiceModal'));
                locationModal.show();
            }, 500);
        }

        // Ensure initMap is globally accessible immediately
        window.initMap = initMap;
        
        // If Google Maps API is already loaded, call initMap immediately
        if (typeof google !== 'undefined' && google.maps) {
            setTimeout(() => {
                initMap();
            }, 100);
        }
        
        // Add error handling for Google Maps API loading
        window.gm_authFailure = function() {
            Swal.fire({
                title: 'Erro no Mapa',
                text: 'Falha na autenticação da API do Google Maps. Por favor, recarregue a página.',
                icon: 'error',
                confirmButtonText: 'Recarregar',
                confirmButtonColor: '#0a7ea4'
            }).then(() => {
                window.location.reload();
            });
        };

        // Backup initMap function in case of timing issues
        if (typeof window.initMap !== 'function') {
            window.initMap = function() {
                initMap();
            };
        }

        // Additional check: if Google Maps is loaded but map hasn't been initialized yet
        setTimeout(() => {
            if (typeof google !== 'undefined' && google.maps && !map) {
                initMap();
            }
        }, 500);

        window.addRoutePoint = function addRoutePoint(latLng) {
            const pointIndex = routeCoordinates.length;
            
            // Disable sidebar when first point is added
            if (pointIndex === 0) {
                disableSidebar();
            }
            
            // Determine marker icon based on position
            let markerIcon;
            if (pointIndex === 0) {
                // Start point - green with wider marker for full word, floating above
                markerIcon = {
                    path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                    fillColor: '#22c55e',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 0.7,
                    anchor: new google.maps.Point(0, 8),
                    labelOrigin: new google.maps.Point(0, -40)
                };
            } else {
                // Regular point - blue circle with number
                markerIcon = {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 9,
                    fillColor: '#0a7ea4',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 3
                };
            }

            // Add marker
            const marker = new google.maps.Marker({
                position: latLng,
                map: map,
                title: pointIndex === 0 ? 'Início' : `Ponto ${pointIndex + 1}`,
                icon: markerIcon,
                label: pointIndex === 0 ? {
                    text: 'INÍCIO',
                    fontSize: '9px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                } : {
                    text: (pointIndex + 1).toString(),
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                },
                zIndex: pointIndex === 0 ? 1000 : 100
            });

            // Add a ground point marker for INÍCIO to show connection to the line
            if (pointIndex === 0) {
                const groundMarker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#22c55e',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    },
                    label: {
                        text: '1',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    },
                    title: 'Início Ground',
                    zIndex: 50
                });
                markers.push(groundMarker);
            }

            markers.push(marker);
            routeCoordinates.push({
                lat: latLng.lat(),
                lng: latLng.lng()
            });

            // Get address for the first point (starting location)
            if (pointIndex === 0) {
                getAddressFromCoordinates(latLng.lat(), latLng.lng());
            }

            // Set current marker as finish if it's not the first point
            if (routeCoordinates.length > 1) { // Check actual route points, not marker count
                // Find and remove previous floating finish marker if it exists
                for (let i = markers.length - 1; i >= 0; i--) {
                    if (markers[i].getTitle() === 'Fim') {
                        markers[i].setMap(null);
                        markers.splice(i, 1);
                        break;
                    }
                }
                
                // Change previous final ground point back to blue (if it exists)
                for (let i = markers.length - 1; i >= 0; i--) {
                    const marker = markers[i];
                    // Only change red markers (not green INÍCIO markers) back to blue
                    if (marker.getIcon && marker.getIcon().fillColor === '#dc2626') {
                        marker.setIcon({
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: '#0a7ea4',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2
                        });
                        break;
                    }
                }
                
                // Add ground point for finish (red for final point)
                const finishGroundMarker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#dc2626',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    },
                    label: {
                        text: (routeCoordinates.length).toString(),
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    },
                    zIndex: 50
                });
                markers.push(finishGroundMarker);
                
                // Update current marker to floating finish
                marker.setIcon({
                    path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                    fillColor: '#dc2626',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 0.7,
                    anchor: new google.maps.Point(0, 8),
                    labelOrigin: new google.maps.Point(0, -40)
                });
                marker.setLabel({
                    text: 'FIM',
                    fontSize: '9px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                });
                marker.setTitle('Fim');
                marker.setZIndex(1000);
            }

            // Update route path with shadow effect
            routePath.setPath(routeCoordinates);
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Update stats
            updateRouteStats();

            // Enable save button if we have at least 2 points
            if (routeCoordinates.length >= 2) {
                document.getElementById('saveTrajetoBtn').disabled = false;
            }

            // Enable undo button if we have at least 1 point
            document.getElementById('undoBtn').disabled = routeCoordinates.length === 0;
        }

        window.undoLastPoint = function undoLastPoint() {
            if (routeCoordinates.length === 0) return;

            // Remove last coordinate first
            routeCoordinates.pop();

            // Clear all markers and rebuild them for the remaining coordinates
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Rebuild markers for remaining coordinates
            if (routeCoordinates.length > 0) {
                routeCoordinates.forEach((coord, index) => {
                    let markerIcon;
                    if (index === 0) {
                        // Start point - green with floating label
                        markerIcon = {
                            path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                            fillColor: '#22c55e',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2,
                            scale: 0.7,
                            anchor: new google.maps.Point(0, 8),
                            labelOrigin: new google.maps.Point(0, -40)
                        };
                    } else if (index === routeCoordinates.length - 1 && routeCoordinates.length > 1) {
                        // End point - red with floating label
                        markerIcon = {
                            path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                            fillColor: '#dc2626',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2,
                            scale: 0.7,
                            anchor: new google.maps.Point(0, 8),
                            labelOrigin: new google.maps.Point(0, -40)
                        };
                    } else {
                        // Regular point - blue circle
                        markerIcon = {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: '#0a7ea4',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 3
                        };
                    }

                    const marker = new google.maps.Marker({
                        position: { lat: coord.lat, lng: coord.lng },
                        map: map,
                        title: index === 0 ? 'Início' : (index === routeCoordinates.length - 1 && routeCoordinates.length > 1 ? 'Fim' : `Ponto ${index + 1}`),
                        icon: markerIcon,
                        label: index === 0 ? {
                            text: 'INÍCIO',
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        } : (index === routeCoordinates.length - 1 && routeCoordinates.length > 1 ? {
                            text: 'FIM',
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        } : {
                            text: (index + 1).toString(),
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        }),
                        zIndex: index === 0 || (index === routeCoordinates.length - 1 && routeCoordinates.length > 1) ? 1000 : 100
                    });

                    // Add ground point for start and end
                    if (index === 0 || (index === routeCoordinates.length - 1 && routeCoordinates.length > 1)) {
                        const groundMarker = new google.maps.Marker({
                            position: { lat: coord.lat, lng: coord.lng },
                            map: map,
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                scale: 9,
                                fillColor: index === 0 ? '#22c55e' : '#dc2626',
                                fillOpacity: 1,
                                strokeColor: '#ffffff',
                                strokeWeight: 2
                            },
                            label: {
                                text: (index + 1).toString(),
                                fontSize: '12px',
                                fontWeight: 'bold',
                                color: '#ffffff'
                            },
                            title: index === 0 ? 'Início Ground' : 'Fim Ground',
                            zIndex: 50
                        });
                        markers.push(groundMarker);
                    }

                    markers.push(marker);
                });
            } else {
                // Reset starting address if no points left
                document.getElementById('startingAddress').textContent = 'Não definido';
                window.fullStartingAddress = null;
                
                // Re-enable sidebar when all points are removed
                enableSidebar();
            }

            // Update route path
            routePath.setPath(routeCoordinates);
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Update stats (this also handles button states)
            updateRouteStats();
        }

        function updateRouteStats() {
            const pointsCount = routeCoordinates.length;
            document.getElementById('routePoints').textContent = pointsCount;

            const distanceElement = document.getElementById('routeDistance');
            const validationElement = document.getElementById('distanceValidation');
            const saveBtn = document.getElementById('saveTrajetoBtn');
            
            if (pointsCount >= 2) {
                const distance = google.maps.geometry.spherical.computeLength(routeCoordinates);
                const distanceKm = (distance / 1000).toFixed(2);
                distanceElement.textContent = `${distanceKm} km`;
                
                // Validate distance (minimum 2.5 km) - only for display, not for button state
                validateDistance(parseFloat(distanceKm), distanceElement, validationElement);
                
                // Always enable save button when we have at least 2 points
                saveBtn.disabled = false;
            } else {
                distanceElement.textContent = '0 km';
                distanceElement.className = distanceElement.className.replace(/distance-(insufficient|sufficient)/g, '');
                validationElement.style.display = 'none';
                saveBtn.disabled = true;
            }

            // Update undo button
            document.getElementById('undoBtn').disabled = routeCoordinates.length === 0;

            // Update hidden input with coordinates
            document.getElementById('routeCoordinates').value = JSON.stringify(routeCoordinates);
        }
        
        function validateDistance(distanceKm, distanceElement, validationElement) {
            const minimumDistance = 2.5;
            
            // Remove existing classes
            distanceElement.className = distanceElement.className.replace(/distance-(insufficient|sufficient)/g, '');
            
            if (distanceKm < minimumDistance) {
                distanceElement.classList.add('distance-insufficient');
                validationElement.innerHTML = `
                    <div class="distance-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span style="font-size: 0.8rem;">Faltam ${(minimumDistance - distanceKm).toFixed(2)} km</span>
                    </div>
                `;
                validationElement.style.display = 'block';
                return false; // Distance is insufficient
            } else {
                distanceElement.classList.add('distance-sufficient');
                validationElement.innerHTML = `
                    <div class="distance-success">
                        <i class="fas fa-check-circle"></i>
                        <span style="font-size: 0.8rem;">Válida</span>
                    </div>
                `;
                validationElement.style.display = 'block';
                return true; // Distance is sufficient
            }
        }

        function getAddressFromCoordinates(lat, lng) {
            const geocoder = new google.maps.Geocoder();
            const latlng = { lat: lat, lng: lng };
            
            geocoder.geocode({ 
                location: latlng,
                language: 'pt',  // Request results in Portuguese
                region: 'PT'     // Bias results towards Portugal
            }, (results, status) => {
                if (status === 'OK') {
                    if (results[0]) {
                        // Get a more readable address format
                        let address = results[0].formatted_address;
                        
                        // Try to get a shorter, more relevant address
                        for (let i = 0; i < results.length; i++) {
                            const result = results[i];
                            // Look for route, locality, or administrative_area_level_3 for better context
                            if (result.types.includes('route') || 
                                result.types.includes('locality') || 
                                result.types.includes('administrative_area_level_3')) {
                                address = result.formatted_address;
                                break;
                            }
                        }
                        
                        // Store the full address for saving to database
                        window.fullStartingAddress = address;
                        
                        // Use full address for display since we have enough space
                        document.getElementById('startingAddress').textContent = address;
                    } else {
                        document.getElementById('startingAddress').textContent = 'Endereço não encontrado';
                    }
                } else {
                    console.log('Geocoder failed due to: ' + status);
                    document.getElementById('startingAddress').textContent = 'Erro ao obter endereço';
                }
            });
        }

        window.clearRoute = function clearRoute() {
            // Clear all markers (including ground points and floating labels)
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Clear route coordinates
            routeCoordinates = [];
            routePath.setPath([]);
            if (window.shadowPath) {
                window.shadowPath.setPath([]);
            }

            // Reset starting address
            document.getElementById('startingAddress').textContent = 'Não definida';
            window.fullStartingAddress = null;

            // Update stats
            updateRouteStats();

            // Disable buttons
            document.getElementById('saveTrajetoBtn').disabled = true;
            document.getElementById('undoBtn').disabled = true;
            
            // Re-enable sidebar since trajectory is cleared
            enableSidebar();
        }

        function finishRoute() {
            if (routeCoordinates.length >= 2) {
                Swal.fire({
                    title: 'Rota Criada!',
                    text: `Rota com ${routeCoordinates.length} pontos criada com sucesso.`,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
            }
        }

        // Form submission
        document.getElementById('trajetoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            console.log('🔍 Form submitted. Route coordinates:', routeCoordinates.length, routeCoordinates);
            console.log('🧩 Current routeCoordinates state:', routeCoordinates);
            
            // Clear any existing modal state first
            console.log('🧹 Clearing any existing modal state');
            
            // Close all Bootstrap modals
            const bootstrapModals = document.querySelectorAll('.modal.show');
            bootstrapModals.forEach(modal => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            });
            
            // Remove any existing modal backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            
            // Close any existing SweetAlert2 modals
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
            
            if (routeCoordinates.length < 2) {
                Swal.fire({
                    title: 'Erro',
                    text: 'É necessário marcar pelo menos 2 pontos para criar um trajeto.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }
            
            // Validate distance (minimum 2.5 km) - make sure we have fresh coordinates
            console.log('📏 Calculating distance for coordinates:', routeCoordinates);
            
            // Check if Google Maps geometry library is loaded
            if (!google?.maps?.geometry?.spherical) {
                console.error('❌ Google Maps geometry library not loaded');
                Swal.fire({
                    title: 'Erro',
                    text: 'Erro ao calcular distância. Recarregue a página.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }
            
            // Recalculate distance fresh from current coordinates
            const distance = google.maps.geometry.spherical.computeLength(routeCoordinates);
            const distanceKm = (distance / 1000).toFixed(2);
            const minimumDistance = 2.5;
            
            console.log('📊 FRESH Distance calculated:', distanceKm, 'km | Minimum required:', minimumDistance, 'km');
            console.log('✅ FRESH Distance check:', parseFloat(distanceKm) >= minimumDistance ? 'PASSED' : 'FAILED');
            console.log('🎯 Distance comparison: distanceKm =', distanceKm, 'minimumDistance =', minimumDistance, 'parseFloat(distanceKm) =', parseFloat(distanceKm));
            
            if (parseFloat(distanceKm) < minimumDistance) {
                console.log('❌ Distance insufficient, showing Bootstrap modal');
                
                // Calculate missing distance
                const missingDistance = (minimumDistance - parseFloat(distanceKm)).toFixed(2);
                
                console.log('🚨 Showing insufficient distance modal with distance:', distanceKm, 'km');
                console.log('📍 Missing distance:', missingDistance, 'km');
                
                // Close any existing SweetAlert2 modals first
                if (typeof Swal !== 'undefined') {
                    Swal.close();
                }
                
                // Show custom Bootstrap modal instead of SweetAlert2
                showInsufficientDistanceModal(distanceKm, missingDistance, minimumDistance);
                return;
            }
            
            console.log('✅ Distance sufficient, proceeding with save');
            
            // Prevent any delayed insufficient distance modals from appearing
            setTimeout(() => {
                console.log('🛡️ Closing any delayed insufficient distance modals');
                if (typeof Swal !== 'undefined') {
                    Swal.close();
                }
            }, 200);
            
            // Distance is sufficient, proceed with save
            proceedWithSave();
        });
        
        function showSimpleDistanceModal(distanceKm, missingDistance, minimumDistance) {
            console.log('🎯 Creating simple custom modal');
            
            // Remove any existing modal
            const existingModal = document.getElementById('simpleDistanceModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // Create simple modal with inline styles (no Bootstrap dependencies)
            const modalHTML = `
                <div id="simpleDistanceModal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.7);
                    z-index: 99999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(4px);
                ">
                    <div style="
                        background: white;
                        border-radius: 16px;
                        max-width: 500px;
                        width: 90%;
                        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                        animation: modalFadeIn 0.3s ease-out;
                    ">
                        <div style="
                            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
                            color: white;
                            padding: 1.5rem 2rem;
                            border-radius: 16px 16px 0 0;
                            display: flex;
                            align-items: center;
                        ">
                            <div style="
                                width: 50px;
                                height: 50px;
                                border-radius: 50%;
                                background: rgba(255, 255, 255, 0.2);
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 1rem;
                                border: 1px solid rgba(255, 255, 255, 0.3);
                            ">
                                <i class="fas fa-exclamation-triangle" style="color: white; font-size: 1.5rem;"></i>
                            </div>
                            <h3 style="margin: 0; font-weight: 600; font-size: 1.25rem;">Trajeto Insuficiente</h3>
                        </div>
                        <div style="padding: 2rem;">
                            <div style="text-align: center;">
                                <div style="
                                    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
                                    border-radius: 12px;
                                    padding: 1.5rem;
                                    margin-bottom: 1.5rem;
                                ">
                                    <h4 style="color: #dc2626; font-weight: 600; margin-bottom: 0.5rem; font-size: 1.1rem;">
                                        Distância mínima não atingida
                                    </h4>
                                    <p style="color: #7f1d1d; margin: 0; font-size: 0.95rem;">
                                        O trajeto deve ter <strong>obrigatoriamente pelo menos ${minimumDistance} km</strong>
                                    </p>
                                </div>
                                <div style="
                                    background: #f8fafc;
                                    border: 2px solid #e2e8f0;
                                    border-radius: 8px;
                                    padding: 1.5rem;
                                    margin-bottom: 1.5rem;
                                    font-family: monospace;
                                ">
                                    <div style="color: #64748b; font-size: 0.85rem; margin-bottom: 0.5rem;">Distância atual:</div>
                                    <div style="color: #dc2626; font-size: 1.4rem; font-weight: 600; margin-bottom: 0.5rem;">${distanceKm} km</div>
                                    <div style="color: #64748b; font-size: 0.85rem;">
                                        Faltam: <strong style="color: #dc2626; font-size: 1rem;">${missingDistance} km</strong>
                                    </div>
                                </div>
                                <div style="
                                    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                                    border: 2px solid #60a5fa;
                                    border-radius: 8px;
                                    padding: 1.5rem;
                                    margin-bottom: 1.5rem;
                                ">
                                    <i class="fas fa-info-circle" style="color: #2563eb; margin-right: 0.5rem; font-size: 1.1rem;"></i>
                                    <span style="color: #1e40af; font-size: 0.95rem; font-weight: 500;">
                                        Continue a adicionar pontos ao trajeto para atingir a distância obrigatória
                                    </span>
                                </div>
                            </div>
                            <div style="text-align: center;">
                                <button onclick="closeSimpleDistanceModal()" style="
                                    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
                                    border: none;
                                    color: white;
                                    padding: 0.75rem 2rem;
                                    font-size: 0.95rem;
                                    font-weight: 500;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    transition: all 0.2s;
                                    display: inline-flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-route"></i>
                                    Continuar a Criar Trajeto
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <style>
                    @keyframes modalFadeIn {
                        from {
                            opacity: 0;
                            transform: scale(0.9) translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: scale(1) translateY(0);
                        }
                    }
                </style>
            `;
            
            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            console.log('✅ Simple custom modal created and shown');
        }
        
        window.closeSimpleDistanceModal = function closeSimpleDistanceModal() {
            console.log('🔄 Closing simple distance modal');
            const modal = document.getElementById('simpleDistanceModal');
            if (modal) {
                modal.style.animation = 'modalFadeOut 0.3s ease-in';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }
        
        function showInsufficientDistanceModal(distanceKm, missingDistance, minimumDistance) {
            console.log('🔧 Creating insufficient distance modal');
            
            // Remove any existing insufficient distance modal
            const existingModal = document.getElementById('insufficientDistanceModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // Try simple custom modal first (more reliable)
            try {
                showSimpleDistanceModal(distanceKm, missingDistance, minimumDistance);
                return;
            } catch (error) {
                console.error('❌ Simple modal failed, trying Bootstrap modal:', error);
            }
            
            // Create modal HTML
            const modalHTML = `
                <div class="modal fade" id="insufficientDistanceModal" tabindex="-1" aria-labelledby="insufficientDistanceModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content" style="border-radius: 16px; border: none; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">
                            <div class="modal-header" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; border-radius: 16px 16px 0 0; border: none; padding: 1.5rem 2rem;">
                                <div class="d-flex align-items-center w-100">
                                    <div class="me-3" style="width: 50px; height: 50px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="fas fa-exclamation-triangle" style="color: white; font-size: 1.5rem;"></i>
                                    </div>
                                    <h5 class="modal-title mb-0" id="insufficientDistanceModalLabel" style="color: white; font-weight: 600; font-size: 1.25rem;">
                                        Trajeto Insuficiente
                                    </h5>
                                </div>
                            </div>
                            <div class="modal-body" style="padding: 2rem; background: white;">
                                <div style="text-align: center;">
                                    <div style="background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%); border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
                                        <h6 style="color: #dc2626; font-weight: 600; margin-bottom: 0.5rem; font-size: 1.1rem;">Distância mínima não atingida</h6>
                                        <p style="color: #7f1d1d; margin: 0; font-size: 0.95rem;">O trajeto deve ter <strong>obrigatoriamente pelo menos ${minimumDistance} km</strong></p>
                                    </div>
                                    <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem; font-family: monospace;">
                                        <div style="color: #64748b; font-size: 0.85rem; margin-bottom: 0.5rem;">Distância atual:</div>
                                        <div style="color: #dc2626; font-size: 1.4rem; font-weight: 600; margin-bottom: 0.5rem;">${distanceKm} km</div>
                                        <div style="color: #64748b; font-size: 0.85rem;">Faltam: <strong style="color: #dc2626; font-size: 1rem;">${missingDistance} km</strong></div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); border: 2px solid #60a5fa; border-radius: 8px; padding: 1.5rem;">
                                        <i class="fas fa-info-circle" style="color: #2563eb; margin-right: 0.5rem; font-size: 1.1rem;"></i>
                                        <span style="color: #1e40af; font-size: 0.95rem; font-weight: 500;">Continue a adicionar pontos ao trajeto para atingir a distância obrigatória</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: white; border-radius: 0 0 16px 16px; justify-content: center;">
                                <button type="button" class="btn" onclick="closeInsufficientDistanceModal()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.75rem 2rem; font-size: 0.95rem; font-weight: 500; border-radius: 8px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-route"></i>
                                    Continuar a Criar Trajeto
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // Show modal with debugging
            const modalElement = document.getElementById('insufficientDistanceModal');
            console.log('🔍 Modal element created:', modalElement);
            console.log('🔍 Modal element in DOM:', document.body.contains(modalElement));
            
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
            
            // Force high z-index to ensure visibility
            modalElement.style.zIndex = '9999';
            
            modal.show();
            
            // Check if modal is actually shown
            setTimeout(() => {
                const isShown = modalElement.classList.contains('show');
                const computedStyle = window.getComputedStyle(modalElement);
                console.log('🔍 Modal shown class:', isShown);
                console.log('🔍 Modal display:', computedStyle.display);
                console.log('🔍 Modal visibility:', computedStyle.visibility);
                console.log('🔍 Modal z-index:', computedStyle.zIndex);
                console.log('🔍 Modal position:', computedStyle.position);
                
                // Force visibility if needed
                if (!isShown || computedStyle.display === 'none') {
                    console.log('🚨 Modal not visible, forcing display');
                    modalElement.style.display = 'block';
                    modalElement.classList.add('show');
                    modalElement.style.visibility = 'visible';
                    modalElement.style.opacity = '1';
                }
                
                // Check for backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    console.log('🔍 Backdrop found:', backdrop);
                    backdrop.style.zIndex = '9998';
                } else {
                    console.log('⚠️ No backdrop found');
                }
            }, 100);
            
            console.log('✅ Bootstrap insufficient distance modal triggered');
        }
        
        window.closeInsufficientDistanceModal = function closeInsufficientDistanceModal() {
            console.log('🔄 Closing insufficient distance modal');
            const modal = bootstrap.Modal.getInstance(document.getElementById('insufficientDistanceModal'));
            if (modal) {
                modal.hide();
            }
            
            // Remove modal from DOM after hiding
            setTimeout(() => {
                const modalElement = document.getElementById('insufficientDistanceModal');
                if (modalElement) {
                    modalElement.remove();
                }
            }, 300);
        }
        
        function proceedWithSave() {
            // Generate default values since form fields were removed
            const zoneName = '<?php echo addslashes($zoneName); ?>';
            const currentDate = new Date().toLocaleDateString('pt-PT');
            
            const formData = {
                zoneId: document.getElementById('zoneId').value,
                name: trajectorySetupData?.name || `Trajeto ${zoneName} - ${currentDate}`,
                description: trajectorySetupData?.description || `Trajeto criado para a zona ${zoneName}`,
                difficulty: 'medio',
                coordinates: routeCoordinates,
                distance: document.getElementById('routeDistance').textContent,
                pointsCount: routeCoordinates.length,
                startingAddress: window.fullStartingAddress || document.getElementById('startingAddress').textContent,
                status: 'draft',
                createdAt: new Date().toISOString(),
                createdBy: '<?php echo $_SESSION["user"]["id"] ?? ""; ?>',
                // Add weather setup data if available
                date: trajectorySetupData?.date,
                startTime: trajectorySetupData?.startTime,
                weatherCondition: trajectorySetupData?.weatherCondition,
                numberOfObservers: trajectorySetupData?.numberOfObservers
            };
            
            // Debug: Log the complete form data being sent
            console.log('🚀 Form data being sent to server:', formData);
            console.log('🌤️ Weather condition from setup:', trajectorySetupData?.weatherCondition);
            console.log('👥 Number of observers from setup:', trajectorySetupData?.numberOfObservers);
            console.log('📅 Date from setup:', trajectorySetupData?.date);
            console.log('⏰ Start time from setup:', trajectorySetupData?.startTime);

            // Show loading
            const saveBtn = document.getElementById('saveTrajetoBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> A guardar...';

            // Save to Firestore
            saveTrajetoToFirestore(formData)
                .then((trajectoryId) => {
                    
                    
                    // Store the trajectory ID globally for contact addition
                    window.currentTrajectoryId = trajectoryId;
                    
                    // Re-enable sidebar since trajectory is saved
                    enableSidebar();
                    
                    // Disable map interaction after save
                    disableMapInteraction();
                    
                    // Test if SweetAlert2 is working
                    if (typeof Swal === 'undefined') {
                        console.error('SweetAlert2 is not loaded!');
                        alert('Trajeto guardado! Teve contacto com rola-brava?');
                        return;
                    }
                    
                    // Show trajectory saved modal with contact inquiry
                    // Add a small delay to ensure save operation is complete
                    setTimeout(() => {
                        showTrajectorySavedModal();
                    }, 500);
                })
                .catch(error => {
                    console.error('Error saving trajeto:', error);
                    Swal.fire({
                        title: 'Erro',
                        text: 'Erro ao guardar o trajeto. Tente novamente.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc2626'
                    });
                })
                .finally(() => {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = originalText;
                });
        }

        function saveTrajetoToFirestore(trajetoData) {
            return new Promise((resolve, reject) => {
                // Call PHP endpoint to save trajeto
                fetch('save_trajeto.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(trajetoData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Store the trajectory ID for contact saving
                        window.savedTrajectoryId = data.documentId;
                        
                        resolve(data.documentId);
                    } else {
                        console.error('Error saving trajeto: ', data.message);
                        reject(new Error(data.message));
                    }
                })
                .catch(error => {
                    console.error('Error calling save endpoint: ', error);
                    reject(error);
                });
            });
        }

        window.cancelCreate = function cancelCreate() {
            // Clean up any modal artifacts first
            cleanupAllModalArtifacts();
            
            // Small delay to ensure cleanup is complete before showing modal
            setTimeout(() => {
                showCancelConfirmationModal();
            }, 100);
        }

        function showCancelConfirmationModal() {
            // Create the modal HTML if it doesn't exist
            let modal = document.getElementById('cancelConfirmationModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'cancelConfirmationModal';
                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered" style="margin: 0 auto; position: relative; width: auto; pointer-events: none; max-width: 500px;">
                        <div class="modal-content trajectory-setup-modal" style="pointer-events: auto; position: relative; display: flex; flex-direction: column; width: 100%; background-color: #fff; background-clip: padding-box; border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                            <div class="modal-header" style="border-radius: 16px 16px 0 0; border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; display: flex; justify-content: space-between; align-items: center; padding: 1.5rem 2rem; position: relative; margin: 0;">
                                <div class="d-flex align-items-center">
                                    <div class="step-indicator me-3" style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="fas fa-question-circle" style="color: white; font-size: 1.2rem;"></i>
                                    </div>
                                    <h5 class="modal-title mb-0" style="color: white; font-weight: 600; font-size: 1.25rem;">Cancelar Criação?</h5>
                                </div>
                            </div>
                            <div class="modal-body" style="padding: 2.5rem 2rem; text-align: center; background: white;">
                                <div class="mb-4">
                                    <div class="mb-3" style="font-size: 0.95rem; color: #6b7280; line-height: 1.5;">
                                        Tem a certeza que pretende sair sem criar o trajeto?
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: white; display: flex; gap: 1rem; justify-content: center;">
                                <button type="button" class="btn" onclick="confirmCancelAction()" style="background-color: #6b7280; border-color: #6b7280; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-times"></i>
                                    Sim, Sair
                                </button>
                                <button type="button" class="btn" onclick="continueCancelAction()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-edit"></i>
                                    Continuar Criação
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            // Add custom backdrop
            addCustomBackdrop();

            // Show the modal with direct CSS - no Bootstrap Modal interference
            modal.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                z-index: 1055 !important;
                display: flex !important;
                width: 100% !important;
                height: 100% !important;
                overflow-x: hidden !important;
                overflow-y: auto !important;
                outline: 0 !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
                align-items: center !important;
                justify-content: center !important;
            `;
            
            // Ensure all child elements are interactive
            const dialog = modal.querySelector('.modal-dialog');
            if (dialog) {
                dialog.style.cssText = `
                    position: relative !important;
                    width: auto !important;
                    margin: 0 auto !important;
                    pointer-events: none !important;
                    max-width: 500px !important;
                `;
            }
            
            const content = modal.querySelector('.modal-content');
            if (content) {
                content.style.cssText += `
                    pointer-events: auto !important;
                    position: relative !important;
                    z-index: 1056 !important;
                `;
            }
            
            // Add body class to prevent scrolling
            document.body.classList.add('modal-open');
            

        }

        window.confirmCancelAction = function confirmCancelAction() {
            
            // Hide the modal
            const modal = document.getElementById('cancelConfirmationModal');
            if (modal) {
                modal.style.display = 'none';
                modal.style.visibility = 'hidden';
                modal.style.opacity = '0';
            }
            
            // Remove custom backdrop
            removeCustomBackdrop();
            
            // Remove body class
            document.body.classList.remove('modal-open');
            
            // Re-enable sidebar before leaving
            enableSidebar();
            
            // Redirect to index
            window.location.href = 'index.php';
        }

        window.continueCancelAction = function continueCancelAction() {
            
            // Hide the modal
            const modal = document.getElementById('cancelConfirmationModal');
            if (modal) {
                modal.style.display = 'none';
                modal.style.visibility = 'hidden';
                modal.style.opacity = '0';
            }
            
            // Remove custom backdrop
            removeCustomBackdrop();
            
            // Remove body class
            document.body.classList.remove('modal-open');
        }

        window.useCurrentLocation = function useCurrentLocation() {
            const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
            locationModal.hide();
            
            if (navigator.geolocation) {
                // Show loading state
                Swal.fire({
                    title: 'A obter localização...',
                    html: '<div class="text-center">' +
                          '<p class="mb-3">Por favor <strong>permita o acesso à localização</strong> quando o navegador solicitar.</p>' +
                          '<div class="alert alert-info d-inline-block" style="font-size: 0.9rem;">' +
                          '<i class="fas fa-info-circle me-2"></i>' +
                          'Procure por um ícone de localização na barra de endereços do navegador' +
                          '</div>' +
                          '</div>',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        
                        Swal.close();
                        
                        // Animate map to user location with zoom out/in effect, then show confirmation modal
                        animateMapToLocation(userLocation, 15).then(() => {
                            showLocationConfirmedModal();
                        });
                    },
                    function(error) {
                        Swal.close();
                        
                        let errorMessage = 'Não foi possível obter a sua localização.';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'Acesso à localização foi negado. Pode definir a localização manualmente no mapa.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'Informação de localização não disponível. Pode definir a localização manualmente no mapa.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'Tempo limite excedido ao obter localização. Pode definir a localização manualmente no mapa.';
                                break;
                        }
                        
                        Swal.fire({
                            title: 'Erro de Localização',
                            text: errorMessage,
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#0a7ea4'
                        });
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            } else {
                Swal.fire({
                    title: 'Geolocalização não suportada',
                    text: 'O seu navegador não suporta geolocalização. Pode definir a localização manualmente no mapa.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
            }
        }
        
        window.useManualLocation = function useManualLocation() {
            const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
            locationModal.hide();
            
            // For manual location, show confirmation modal immediately (user will click on map)
            setTimeout(() => {
                showLocationConfirmedModal();
            }, 300);
        }
        
        window.showLocationSearch = function showLocationSearch() {
            const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
            locationModal.hide();
            
            const searchModal = new bootstrap.Modal(document.getElementById('locationSearchModal'));
            searchModal.show();
            
            // Focus on input when modal is shown
            setTimeout(() => {
                document.getElementById('locationSearchInput').focus();
            }, 500);
        }
        
        window.showLocationChoiceModal = function showLocationChoiceModal() {
            const locationModal = new bootstrap.Modal(document.getElementById('locationChoiceModal'));
            locationModal.show();
        }
        
        function handleLocationSearchEnter(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                searchLocation();
            }
        }
        
        window.searchLocation = function searchLocation() {
            const searchInput = document.getElementById('locationSearchInput');
            const query = searchInput.value.trim();
            
            if (!query) {
                Swal.fire({
                    title: 'Campo vazio',
                    text: 'Por favor digite uma localização para pesquisar.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                searchInput.focus();
                return;
            }
            
            // CAPTURE THE ORIGINAL MAP POSITION BEFORE SEARCHING
            const originalCenter = map.getCenter();
            const originalZoom = map.getZoom();
            
            // Show loading
            Swal.fire({
                title: 'A pesquisar...',
                text: `A procurar "${query}"...`,
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Use Google Geocoding API
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({ 
                address: query + ', Portugal',
                region: 'PT'
            }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    const location = results[0].geometry.location;
                    const address = results[0].formatted_address;
                    
                    // Hide search modal
                    const searchModal = bootstrap.Modal.getInstance(document.getElementById('locationSearchModal'));
                    searchModal.hide();
                    
                    Swal.close();
                    
                    // DON'T move the map yet! Animate from original position to found location
                    animateMapFromTo(originalCenter, originalZoom, location, 12).then(() => {
                        showLocationConfirmedModal();
                    });
                } else {
                    Swal.close();
                    
                    let errorMessage = 'Não foi possível encontrar essa localização.';
                    if (status === 'ZERO_RESULTS') {
                        errorMessage = `Não foram encontrados resultados para "${query}". Tente com uma localização diferente.`;
                    } else if (status === 'OVER_QUERY_LIMIT') {
                        errorMessage = 'Limite de pesquisas excedido. Tente novamente mais tarde.';
                    }
                    
                    Swal.fire({
                        title: 'Localização não encontrada',
                        text: errorMessage,
                        icon: 'warning',
                        confirmButtonText: 'Tentar novamente',
                        confirmButtonColor: '#0a7ea4'
                    }).then(() => {
                        // Show search modal again
                        const searchModal = new bootstrap.Modal(document.getElementById('locationSearchModal'));
                        searchModal.show();
                        setTimeout(() => {
                            searchInput.focus();
                            searchInput.select();
                        }, 500);
                    });
                }
            });
        }

        // Global variables to store trajectory setup data
        let trajectorySetupData = {};

        // Handle weather "other" option and date/time inputs
        document.addEventListener('DOMContentLoaded', function() {
            const weatherOtherCheckbox = document.getElementById('weather_other');
            const weatherOtherInput = document.getElementById('weatherOtherInput');
            
            if (weatherOtherCheckbox) {
                weatherOtherCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        weatherOtherInput.style.display = 'block';
                        document.getElementById('weatherOtherText').focus();
                    } else {
                        weatherOtherInput.style.display = 'none';
                        document.getElementById('weatherOtherText').value = '';
                    }
                });
            }
            
            // Set up time input handlers immediately on page load - OUTSIDE any async context
            setupTimeInputOnPageLoad();
        });

        function setupTimeInputOnPageLoad() {

            
            // Handle picker button clicks
            document.addEventListener('click', function(e) {
                // Time picker button
                if (e.target && (e.target.id === 'timePickerBtn' || e.target.closest('#timePickerBtn'))) {
                    e.preventDefault();
                    e.stopPropagation();

                    openCustomTimePicker();
                }
                
                // Date picker button
                if (e.target && (e.target.id === 'datePickerBtn' || e.target.closest('#datePickerBtn'))) {
                    e.preventDefault();
                    e.stopPropagation();

                    openCustomDatePicker();
                }
                
                // Time input click (readonly, so open picker)
                if (e.target && e.target.id === 'trajectoryStartTime') {
                    e.preventDefault();
                    e.stopPropagation();
    
                    openCustomTimePicker();
                }
                
                // Date input click (readonly, so open picker)
                if (e.target && e.target.id === 'trajectoryDate') {
                    e.preventDefault();
                    e.stopPropagation();
    
                    openCustomDatePicker();
                }
            }, true);
            
            // Set up custom picker functionality
            setupCustomTimePicker();
            setupCustomDatePicker();
        }

        function openCustomTimePicker() {
            // Set the target for trajectory time (default behavior)
            window.currentTimeTarget = 'trajectoryStartTime';
            
            // Get current time value if any
            const currentTime = document.getElementById('trajectoryStartTime').value;
            
            if (currentTime) {
                const [hours, minutes] = currentTime.split(':');
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();

            } else {
                // Default to current time
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0'); // Round to nearest 5 minutes
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();

            }
            
            // Temporarily hide the trajectory setup modal to avoid stacking issues
            const setupModal = bootstrap.Modal.getInstance(document.getElementById('trajectorySetupModal'));
            if (setupModal) {
                setupModal.hide();
            }
            
            // Show the time picker modal after a brief delay
            setTimeout(() => {
                const timeModal = new bootstrap.Modal(document.getElementById('timePickerModal'));
                timeModal.show();
            }, 300);
        }

        function setupCustomTimePicker() {
            
            // Update display when selections change
            document.getElementById('hourSelect').addEventListener('change', updateTimeDisplay);
            document.getElementById('minuteSelect').addEventListener('change', updateTimeDisplay);
            
            // Flag to track if time was confirmed (to avoid double-showing setup modal)
            let timeConfirmed = false;
            
            // Handle modal close events (cancel button or X button)
            document.getElementById('timePickerModal').addEventListener('hidden.bs.modal', function() {
                
                
                // Clean up manual backdrop if it exists
                const manualBackdrop = document.getElementById('manual-time-picker-backdrop');
                if (manualBackdrop) {
                    manualBackdrop.remove();

                }
                
                // Only restore modals if time was not confirmed
                if (!timeConfirmed) {
                    const lastTarget = window.currentTimeTarget;
                    
                    if (lastTarget === 'trajectoryStartTime' || !lastTarget) {
                    setTimeout(() => {
                        const setupModal = new bootstrap.Modal(document.getElementById('trajectorySetupModal'));
                        setupModal.show();

                    }, 300);
                    } else if (lastTarget === 'middleTime' || lastTarget === 'endTime') {
                        setTimeout(() => {
                            const detailsModal = new bootstrap.Modal(document.getElementById('trajectoryDetailsModal'));
                            detailsModal.show();

                    }, 300);
                    }
                }
                
                // Reset flags for next time
                timeConfirmed = false;
                window.currentTimeTarget = null;
            });
            
            // Confirm button
            document.getElementById('confirmTimeBtn').addEventListener('click', function() {
                const hour = document.getElementById('hourSelect').value;
                const minute = document.getElementById('minuteSelect').value;
                const timeString = `${hour}:${minute}`;
                
                
                
                // Set flag to indicate time was confirmed
                timeConfirmed = true;
                
                // Determine which input field to update based on currentTimeTarget
                const targetInputId = window.currentTimeTarget || 'trajectoryStartTime';
                const timeInput = document.getElementById(targetInputId);
                
                if (timeInput) {
                    timeInput.value = timeString;
                    
                    
                    // Trigger change event to ensure any listeners are notified
                    timeInput.dispatchEvent(new Event('change', { bubbles: true }));
                    timeInput.dispatchEvent(new Event('input', { bubbles: true }));
                } else {
                    console.error('Time picker - Input field not found:', targetInputId);
                }
                
                // Close the time picker modal
                const timeModal = bootstrap.Modal.getInstance(document.getElementById('timePickerModal'));
                if (timeModal) {
                    timeModal.hide();
                    
                    // Restore the appropriate modal based on the target field
                    if (targetInputId === 'trajectoryStartTime') {
                        setTimeout(() => {
                            const setupModal = new bootstrap.Modal(document.getElementById('trajectorySetupModal'));
                            setupModal.show();

                        }, 300);
                    } else if (targetInputId === 'middleTime' || targetInputId === 'endTime') {
                        setTimeout(() => {
                            const detailsModal = new bootstrap.Modal(document.getElementById('trajectoryDetailsModal'));
                            detailsModal.show();
            
                        }, 300);
                    }
                } else {
                    console.error('Time picker - Modal instance not found!');
                }
                
                // Reset the target for next use
                window.currentTimeTarget = null;
                

            });
        }

        function updateTimeDisplay() {
            const hour = document.getElementById('hourSelect').value;
            const minute = document.getElementById('minuteSelect').value;
            const timeString = `${hour}:${minute}`;
            document.getElementById('selectedTimeDisplay').textContent = timeString;

        }

        // Custom Date Picker Functions
        function openCustomDatePicker() {
            // Get current date value if any
            const currentDate = document.getElementById('trajectoryDate').value;
            
            if (currentDate) {
                // Parse DD/MM/YYYY format
                const [day, month, year] = currentDate.split('/');
                document.getElementById('daySelect').value = day.padStart(2, '0');
                document.getElementById('monthSelect').value = month.padStart(2, '0');
                document.getElementById('yearSelect').value = year;
                updateDateDisplay();

            } else {
                // Default to current date
                const now = new Date();
                const day = String(now.getDate()).padStart(2, '0');
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const year = String(now.getFullYear());
                document.getElementById('daySelect').value = day;
                document.getElementById('monthSelect').value = month;
                document.getElementById('yearSelect').value = year;
                updateDateDisplay();

            }
            
            // Apply date restrictions
            filterAvailableMonths();
            filterAvailableDays();
            
            // Temporarily hide the trajectory setup modal to avoid stacking issues
            const setupModal = bootstrap.Modal.getInstance(document.getElementById('trajectorySetupModal'));
            if (setupModal) {
                setupModal.hide();
            }
            
            // Show the date picker modal after a brief delay
            setTimeout(() => {
                const dateModal = new bootstrap.Modal(document.getElementById('datePickerModal'));
                dateModal.show();
            }, 300);
        }

        function setupCustomDatePicker() {
            
            // Populate days (1-31) - will be filtered based on month/year selection
            const daySelect = document.getElementById('daySelect');
            for (let i = 1; i <= 31; i++) {
                const option = document.createElement('option');
                option.value = String(i).padStart(2, '0');
                option.textContent = String(i).padStart(2, '0');
                daySelect.appendChild(option);
            }
            
            // Populate years (only past years and current year)
            const yearSelect = document.getElementById('yearSelect');
            const currentYear = new Date().getFullYear();
            for (let i = currentYear - 5; i <= currentYear; i++) {
                const option = document.createElement('option');
                option.value = String(i);
                option.textContent = String(i);
                yearSelect.appendChild(option);
            }
            
            // Update display when selections change and filter available options
            document.getElementById('daySelect').addEventListener('change', updateDateDisplay);
            document.getElementById('monthSelect').addEventListener('change', function() {
                filterAvailableDays();
                updateDateDisplay();
            });
            document.getElementById('yearSelect').addEventListener('change', function() {
                filterAvailableMonths();
                filterAvailableDays();
                updateDateDisplay();
            });
            
            // Flag to track if date was confirmed (to avoid double-showing setup modal)
            let dateConfirmed = false;
            
            // Handle modal close events (cancel button or X button)
            document.getElementById('datePickerModal').addEventListener('hidden.bs.modal', function() {
    
                // Only restore trajectory setup modal if date was not confirmed
                if (!dateConfirmed) {
                    setTimeout(() => {
                        const setupModal = new bootstrap.Modal(document.getElementById('trajectorySetupModal'));
                        setupModal.show();

                    }, 300);
                }
                // Reset flag for next time
                dateConfirmed = false;
            });
            
            // Confirm button
            document.getElementById('confirmDateBtn').addEventListener('click', function() {
                const day = document.getElementById('daySelect').value;
                const month = document.getElementById('monthSelect').value;
                const year = document.getElementById('yearSelect').value;
                
                // Validate date exists
                const date = new Date(year, month - 1, day);
                if (date.getDate() != day || date.getMonth() != month - 1 || date.getFullYear() != year) {
                    Swal.fire({
                        title: 'Data inválida',
                        text: 'Por favor selecione uma data válida.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                // Validate date is not in the future
                const today = new Date();
                today.setHours(23, 59, 59, 999); // Set to end of today for comparison
                if (date > today) {
                    Swal.fire({
                        title: 'Data futura não permitida',
                        text: 'Não é possível selecionar uma data futura. Por favor selecione a data de hoje ou uma data anterior.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                const dateString = `${day}/${month}/${year}`;
                

                
                // Set flag to indicate date was confirmed
                dateConfirmed = true;
                
                // Set the date input value
                const dateInput = document.getElementById('trajectoryDate');
                if (dateInput) {
                    dateInput.value = dateString;
    
                    
                    // Trigger change event to ensure any listeners are notified
                    dateInput.dispatchEvent(new Event('change', { bubbles: true }));
                    dateInput.dispatchEvent(new Event('input', { bubbles: true }));
                } else {
                    console.error('Date picker - trajectoryDate input not found!');
                }
                
                // Close the date picker modal
                const dateModal = bootstrap.Modal.getInstance(document.getElementById('datePickerModal'));
                if (dateModal) {
                    dateModal.hide();
                    
                    // Show the trajectory setup modal again after date picker closes
                    setTimeout(() => {
                        const setupModal = new bootstrap.Modal(document.getElementById('trajectorySetupModal'));
                        setupModal.show();

                    }, 300);
                } else {
                    console.error('Date picker - Modal instance not found!');
                }
                

            });
        }

        function updateDateDisplay() {
            const day = document.getElementById('daySelect').value;
            const month = document.getElementById('monthSelect').value;
            const year = document.getElementById('yearSelect').value;
            const dateString = `${day}/${month}/${year}`;
            document.getElementById('selectedDateDisplay').textContent = dateString;

        }

        function filterAvailableMonths() {
            const yearSelect = document.getElementById('yearSelect');
            const monthSelect = document.getElementById('monthSelect');
            const selectedYear = parseInt(yearSelect.value);
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11
            
            // Get all month options
            const monthOptions = monthSelect.querySelectorAll('option');
            
            monthOptions.forEach(option => {
                const monthValue = parseInt(option.value);
                
                if (selectedYear === currentYear) {
                    // For current year, only allow current month and previous months
                    if (monthValue > currentMonth) {
                        option.disabled = true;
                        option.style.display = 'none';
                    } else {
                        option.disabled = false;
                        option.style.display = 'block';
                    }
                } else {
                    // For past years, allow all months
                    option.disabled = false;
                    option.style.display = 'block';
                }
            });
            
            // If currently selected month is disabled, select the current month (or last available)
            const selectedMonth = parseInt(monthSelect.value);
            if (selectedYear === currentYear && selectedMonth > currentMonth) {
                monthSelect.value = String(currentMonth).padStart(2, '0');
            }
        }

        function filterAvailableDays() {
            const daySelect = document.getElementById('daySelect');
            const monthSelect = document.getElementById('monthSelect');
            const yearSelect = document.getElementById('yearSelect');
            
            const selectedYear = parseInt(yearSelect.value);
            const selectedMonth = parseInt(monthSelect.value);
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            const currentDay = currentDate.getDate();
            
            // Get number of days in the selected month
            const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
            
            // Get all day options
            const dayOptions = daySelect.querySelectorAll('option');
            
            dayOptions.forEach(option => {
                const dayValue = parseInt(option.value);
                
                // Hide days that don't exist in the selected month
                if (dayValue > daysInMonth) {
                    option.disabled = true;
                    option.style.display = 'none';
                } else if (selectedYear === currentYear && selectedMonth === currentMonth) {
                    // For current year and month, only allow current day and previous days
                    if (dayValue > currentDay) {
                        option.disabled = true;
                        option.style.display = 'none';
                    } else {
                        option.disabled = false;
                        option.style.display = 'block';
                    }
                } else {
                    // For past months/years, allow all valid days
                    option.disabled = false;
                    option.style.display = 'block';
                }
            });
            
            // If currently selected day is disabled, select the current day (or last available)
            const selectedDay = parseInt(daySelect.value);
            if (selectedDay > daysInMonth) {
                daySelect.value = String(daysInMonth).padStart(2, '0');
            } else if (selectedYear === currentYear && selectedMonth === currentMonth && selectedDay > currentDay) {
                daySelect.value = String(currentDay).padStart(2, '0');
            }
        }



        window.proceedToLocationChoice = function proceedToLocationChoice() {
            // Validate all required fields individually
            const date = document.getElementById('trajectoryDate').value.trim();
            const startTime = document.getElementById('trajectoryStartTime').value.trim();
            const numberOfObservers = document.getElementById('numberOfObservers').value.trim();
            
            // Validate date
            if (!date) {
                Swal.fire({
                    title: 'Campo obrigatório',
                    text: 'Por favor selecione uma data para o trajeto.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                document.getElementById('trajectoryDate').focus();
                return;
            }
            
            // Validate time
            if (!startTime) {
                Swal.fire({
                    title: 'Campo obrigatório',
                    text: 'Por favor selecione a hora de início do trajeto.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                document.getElementById('trajectoryStartTime').focus();
                return;
            }
            
            // Get weather condition (single selection)
            const weatherRadio = document.querySelector('input[name="weather_conditions"]:checked');
            
            // Validate weather condition is selected
            if (!weatherRadio) {
                Swal.fire({
                    title: 'Campo obrigatório',
                    text: 'Por favor selecione uma condição meteorológica.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }
            
            let weatherCondition;
            if (weatherRadio.value === 'other') {
                const otherText = document.getElementById('weatherOtherText').value.trim();
                if (!otherText) {
                    Swal.fire({
                        title: 'Campo obrigatório',
                        text: 'Por favor especifique a condição meteorológica.',
                        icon: 'info',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    document.getElementById('weatherOtherText').focus();
                    return;
                }
                weatherCondition = `Outro: ${otherText}`;
            } else {
                const label = document.querySelector(`label[for="${weatherRadio.id}"]`).textContent.trim();
                weatherCondition = label;
            }
            
            // Validate number of observers
            if (!numberOfObservers || parseInt(numberOfObservers) < 1) {
                Swal.fire({
                    title: 'Campo obrigatório',
                    text: 'Por favor indique o número de observadores (mínimo 1).',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                document.getElementById('numberOfObservers').focus();
                return;
            }
            
            // Convert date from DD/MM/YYYY to YYYY-MM-DD for storage
            const [day, month, year] = date.split('/');
            const isoDate = `${year}-${month}-${day}`;
            
            // Get zone information from the page
            const zoneId = document.getElementById('zoneId').value;
            const zoneName = document.querySelector('.zone-name').textContent.trim();
            
            // Store trajectory setup data
            trajectorySetupData = {
                zoneId: zoneId,
                zoneName: zoneName,
                date: isoDate,
                startTime: startTime,
                weatherCondition: weatherCondition,
                numberOfObservers: parseInt(numberOfObservers),
                name: `Trajeto ${zoneName} - ${date} ${startTime}`,
                description: `Trajeto criado em ${zoneName} no dia ${date} às ${startTime}. Condições meteorológicas: ${weatherCondition}. Observadores: ${numberOfObservers}.`
            };
            
            // Debug: Log trajectory setup data
            console.log('📋 Trajectory setup data stored:', trajectorySetupData);
            

            
            // Hide setup modal and show trajectory tutorial modal
            const setupModal = bootstrap.Modal.getInstance(document.getElementById('trajectorySetupModal'));
            setupModal.hide();
            
            setTimeout(() => {
                // Add custom backdrop for tutorial modal
                addCustomBackdrop();
                
                const tutorialModal = new bootstrap.Modal(document.getElementById('trajectoryTutorialModal'), {
                    backdrop: false,
                    keyboard: false
                });
                tutorialModal.show();
            }, 300);
        }

        function proceedFromTutorialToLocation() {
            // Hide tutorial modal and show location choice modal
            const tutorialModal = bootstrap.Modal.getInstance(document.getElementById('trajectoryTutorialModal'));
            tutorialModal.hide();
            
            // Show location choice modal after tutorial closes
            setTimeout(() => {
                // Keep custom backdrop for location modal
                const locationModal = new bootstrap.Modal(document.getElementById('locationChoiceModal'), {
                    backdrop: false,
                    keyboard: false
                });
                locationModal.show();
            }, 300);
        }

        window.proceedFromTutorialToMap = function proceedFromTutorialToMap() {

            
            // Hide tutorial modal and start trajectory creation directly
            const tutorialModal = bootstrap.Modal.getInstance(document.getElementById('trajectoryTutorialModal'));
            if (tutorialModal) {
                tutorialModal.hide();
    
            }
            
            // Force remove any lingering modal backdrops
            setTimeout(() => {
                // Use comprehensive cleanup function
                cleanupAllModalArtifacts();
                
                // Start trajectory creation
                startTrajectoryCreation();
            }, 300);
        }

        function startTrajectoryCreation() {

            
            // CRITICAL: Ensure trajectory setup modal is completely hidden and not blocking clicks
            const setupModal = document.getElementById('trajectorySetupModal');
            if (setupModal) {
                setupModal.style.display = 'none !important';
                setupModal.style.visibility = 'hidden !important';
                setupModal.style.opacity = '0 !important';
                setupModal.style.pointerEvents = 'none !important';
                setupModal.style.zIndex = '-1 !important';
                setupModal.setAttribute('aria-hidden', 'true');
                setupModal.classList.remove('show');

            }
            
            // Also hide any other modals that might be interfering
            const allModals = document.querySelectorAll('.modal');
            allModals.forEach(modal => {
                if (modal.id !== 'trajectorySavedModal') { // Keep trajectory saved modal available
                    modal.style.display = 'none';
                    modal.style.visibility = 'hidden';
                    modal.style.opacity = '0';
                    modal.style.pointerEvents = 'none';
                    modal.style.zIndex = '-1';
                    modal.setAttribute('aria-hidden', 'true');
                    modal.classList.remove('show');
                }
            });

            
            // Enable map clicking for trajectory creation directly
            enableTrajectoryMode();
            
            // Activate map immediately without SweetAlert

            
            // Add trajectory-active class to ensure map is interactive
            document.body.classList.add('trajectory-active');

            
            // Focus on the map and make it interactive
            const mapElement = document.getElementById('map');
            if (mapElement) {
                mapElement.focus();
                mapElement.style.pointerEvents = 'auto';
                mapElement.style.position = 'relative';
                mapElement.style.zIndex = '1';
                mapElement.style.opacity = '1';

            }
            
            // Ensure map is clickable and properly sized
            if (map) {
                google.maps.event.trigger(map, 'resize');

                
                setTimeout(() => {

            
            // Debug: Check for any elements that might be blocking the map
            setTimeout(() => {
                // Map interaction debug removed for cleaner console
            }, 1000);
                }, 100);
            }
         }

        function enableTrajectoryMode() {
            // Clear any existing trajectory
            clearTrajectory();
            
            // Update UI to show trajectory creation is active
            updateTrajectoryUI(true);
            
            // The map click listener is already set up in initMap()
            // Just need to ensure the map is ready for trajectory creation
            if (!map) {
                console.error('Map not initialized');
            }
        }

        function updateTrajectoryUI(isActive, isFinalized = false) {
            // Update button states and UI indicators
            const undoBtn = document.querySelector('[onclick="undoLastPoint()"]');
            const clearBtn = document.querySelector('[onclick="clearTrajectory()"]');
            const saveBtn = document.querySelector('[onclick="saveTrajectory()"]');
            
            if (isFinalized) {
                // Trajectory is finalized - disable all editing
                if (undoBtn) {
                    undoBtn.disabled = true;
                    undoBtn.innerHTML = '<i class="fas fa-check me-1"></i>Trajeto Finalizado';
                    undoBtn.classList.remove('btn-warning');
                    undoBtn.classList.add('btn-success');
                }
                if (clearBtn) {
                    clearBtn.disabled = true;
                    clearBtn.style.opacity = '0.5';
                }
                if (saveBtn) {
                    saveBtn.disabled = true;
                    saveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Guardado';
                    saveBtn.classList.remove('btn-success');
                    saveBtn.classList.add('btn-secondary');
                }
            } else {
                // Normal trajectory editing mode
                if (undoBtn) undoBtn.disabled = routeCoordinates.length === 0;
                if (clearBtn) clearBtn.disabled = routeCoordinates.length === 0;
                if (saveBtn) saveBtn.disabled = routeCoordinates.length < 2;
            }
            
            // Update distance display
            updateDistanceDisplay();
        }

        function showTrajectorySetupModal() {
            
            // Get or create modal instance (avoid multiple instances)
            let setupModal = bootstrap.Modal.getInstance(document.getElementById('trajectorySetupModal'));
            if (!setupModal) {
                setupModal = new bootstrap.Modal(document.getElementById('trajectorySetupModal'), {
                    backdrop: false,
                    keyboard: false,
                    focus: true
                });
            }
            
            // Ensure modal content is ready before showing
            setTimeout(() => {
                // Add custom backdrop for dimming effect
                addCustomBackdrop();
                
                setupModal.show();
                
                // Ensure modal content is interactive
                const modalContent = document.querySelector('#trajectorySetupModal .modal-content');
                if (modalContent) {
                    modalContent.style.pointerEvents = 'auto';
                    modalContent.style.zIndex = '1053';
                }
            }, 100);
            
            // Set Portuguese locale for date input (but leave empty)
            const dateInput = document.getElementById('trajectoryDate');
            if (dateInput) {
                dateInput.setAttribute('lang', 'pt-PT');
                dateInput.setAttribute('locale', 'pt-PT');
                dateInput.value = ''; // Start empty
            }
            
            // Try to force Portuguese locale
            try {
                // Set document language
                document.documentElement.lang = 'pt-PT';
                
                // Try to override navigator language (doesn't work but worth trying)
                if (dateInput && dateInput.style) {
                    dateInput.style.setProperty('--date-picker-locale', 'pt-PT');
                }
            } catch (e) {
                // Locale override attempt failed - continue silently
            }
            
            // Leave time input empty
            const timeInput = document.getElementById('trajectoryStartTime');
            if (timeInput) {
                timeInput.value = '';
                timeInput.setAttribute('lang', 'pt-PT');
            }
            
            // Set default number of observers to 1
            const observersInput = document.getElementById('numberOfObservers');
            if (observersInput) {
                observersInput.value = 1;
            }
            
            // Time input handlers are now set up via event delegation on page load
            
            // Add debug click listeners to help identify interaction issues
            setTimeout(() => {
                const modal = document.getElementById('trajectorySetupModal');
                if (modal) {
                    // Modal interaction is working - debug removed
                    
                                        // Modal interaction confirmed working
                }
            }, 200);
        }

        window.cancelTrajectoryCreation = function cancelTrajectoryCreation() {
            // Remove custom backdrop
            removeCustomBackdrop();
            
            Swal.fire({
                title: 'Cancelar Criação?',
                text: 'Tem a certeza que pretende sair sem criar o trajeto?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-edit me-2"></i>Continuar Criação',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Sim, Sair',
                confirmButtonColor: '#0a7ea4',
                cancelButtonColor: '#6b7280',
                reverseButtons: true,
                customClass: {
                    popup: 'custom-swal-popup',
                    title: 'custom-swal-title',
                    content: 'custom-swal-content',
                    confirmButton: 'custom-swal-confirm',
                    cancelButton: 'custom-swal-cancel'
                }
            }).then((result) => {
                if (result.isDismissed || result.dismiss === Swal.DismissReason.cancel) {
                    // User clicked "Sim, Sair" (cancel button)
                    window.location.href = 'index.php';
                } else {
                    // User wants to continue - restore backdrop
                    addCustomBackdrop();
                }
            });
        }

        window.showTrajetoHelp = function showTrajetoHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Criar Trajeto</div>',
                html: '<div class="help-modal-content">' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-map-marked-alt text-primary me-2"></i>' +
                                '<span>Como criar um trajeto</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-mouse-pointer text-primary me-2"></i>' +
                                    '<span><strong>Clique no mapa:</strong> Para marcar pontos do trajeto</span>' +
                                '</div>' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-route text-primary me-2"></i>' +
                                    '<span><strong>Pontos conectados:</strong> Os pontos são ligados automaticamente</span>' +
                                '</div>' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-undo text-warning me-2"></i>' +
                                    '<span><strong>Desfazer Último:</strong> Remove o último ponto marcado</span>' +
                                '</div>' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-eraser text-secondary me-2"></i>' +
                                    '<span><strong>Limpar Trajeto:</strong> Remove todos os pontos para recomeçar</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-location-arrow text-primary me-2"></i>' +
                                '<span>Opções de localização</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-crosshairs text-primary me-2"></i>' +
                                    '<span><strong>GPS:</strong> Use a sua localização atual para começar o trajeto</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-search text-primary me-2"></i>' +
                                    '<span><strong>Pesquisar:</strong> Procure por uma localização específica em Portugal</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-mouse-pointer text-primary me-2"></i>' +
                                    '<span><strong>Manual:</strong> Clique diretamente no mapa para definir o ponto inicial</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-info-circle text-primary me-2"></i>' +
                                '<span>Requisitos e dicas</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-circle text-danger me-2"></i>' +
                                    '<span><strong>Mínimo 2 pontos:</strong> Necessário para criar um trajeto válido</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-circle text-success me-2"></i>' +
                                    '<span><strong>Localização automática:</strong> O endereço inicial é detetado automaticamente</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-circle text-info me-2"></i>' +
                                    '<span><strong>Distância calculada:</strong> A distância total é calculada em tempo real</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-envelope text-primary me-2"></i>' +
                                '<span>Precisa de Ajuda?</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">' +
                                    '<div style="color: #0a7ea4; font-size: 0.9rem;">' +
                                        'Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>' +
                                        '<strong><EMAIL></strong>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>',
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }

        // Custom backdrop functions
        function addCustomBackdrop() {
            // Remove existing custom backdrop if any
            removeCustomBackdrop();
            
            // Create custom backdrop
            const backdrop = document.createElement('div');
            backdrop.id = 'customModalBackdrop';
            backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                pointer-events: none;
            `;
            
            document.body.appendChild(backdrop);

        }
        
        function removeCustomBackdrop() {
            const backdrop = document.getElementById('customModalBackdrop');
            if (backdrop) {
                backdrop.remove();

            }
        }
        
        function cleanupAllModalArtifacts() {

            
            // Remove Bootstrap modal backdrops
            const bootstrapBackdrops = document.querySelectorAll('.modal-backdrop');
            bootstrapBackdrops.forEach(backdrop => {
                backdrop.remove();
                
            });
            
            // Remove custom backdrop
            removeCustomBackdrop();
            
            // Remove modal-open class and reset body styles
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Reset any modal z-index issues
            const allModals = document.querySelectorAll('.modal');
            allModals.forEach(modal => {
                if (!modal.classList.contains('show')) {
                    modal.style.display = 'none';
                    modal.style.zIndex = '';
                }
            });
            

        }

        // Missing trajectory functions
        window.clearTrajectory = function clearTrajectory() {

            
            // Clear route coordinates
            routeCoordinates = [];
            
            // Clear markers
            if (markers && markers.length > 0) {
                markers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                markers = [];
            }
            
            // Clear route path
            if (routePath) {
                routePath.setPath([]);
            }
            if (window.shadowPath) {
                window.shadowPath.setPath([]);
            }
            
            // Update UI
            updateTrajectoryUI(false);
            

        }

        function updateDistanceDisplay() {
            // Use the existing updateRouteStats function which handles this correctly
            updateRouteStats();
            

        }



        window.saveTrajectory = function saveTrajectory() {
            if (routeCoordinates.length < 2) {
                Swal.fire({
                    title: 'Trajeto Incompleto',
                    text: 'É necessário pelo menos 2 pontos para criar um trajeto.',
                    icon: 'warning',
                    confirmButtonText: 'Entendi',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }
            
            // Prepare trajectory data
            const trajectoryData = {
                coordinates: routeCoordinates.map(coord => ({
                    lat: coord.lat(),
                    lng: coord.lng()
                })),
                setupData: trajectorySetupData,
                totalDistance: calculateTotalDistance(),
                pointCount: routeCoordinates.length,
                createdAt: new Date().toISOString()
            };
            

            
            // Prepare the request data
            const requestData = {
                zoneId: trajectorySetupData.zoneId || 'default-zone',
                name: trajectorySetupData.name || 'Trajeto sem nome',
                description: trajectorySetupData.description || '',
                difficulty: 'medio',
                coordinates: trajectoryData.coordinates,
                distance: trajectoryData.totalDistance + ' km',
                pointsCount: trajectoryData.pointCount,
                startingAddress: document.getElementById('startingAddress').textContent || '',
                status: 'draft', // Changed from 'active' to 'draft' to match server logs
                // Additional trajectory setup data
                date: trajectorySetupData.date,
                startTime: trajectorySetupData.startTime,
                weatherCondition: trajectorySetupData.weatherCondition,
                numberOfObservers: trajectorySetupData.numberOfObservers
            };
            
            // Debug: Log what we're sending to server
            console.log('🚀 Sending trajectory data to server:', requestData);
            console.log('📋 Current trajectorySetupData state:', trajectorySetupData);
            console.log('🌤️ Weather condition being sent:', requestData.weatherCondition);
            console.log('👥 Number of observers being sent:', requestData.numberOfObservers);
            

            
            // Send trajectory data to server
            fetch('save_trajeto.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {

                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return response.json();
            })
            .then(data => {

                
                if (data.success) {
                    // Store the trajectory ID for contact saving
                    window.savedTrajectoryId = data.documentId;
                    

                    
                    // After successful save, ask about rola-brava contact
                    showContactInquiry();
                } else {
                    throw new Error(data.message || 'Failed to save trajectory');
                }
            })
            .catch(error => {
                console.error('Error saving trajectory:', error);
                console.error('Error details:', error.stack);
                console.error('Error type:', typeof error);
                console.error('Error message:', error.message);
                
                Swal.fire({
                    title: 'Erro ao Guardar',
                    text: 'Ocorreu um erro ao guardar o trajeto: ' + error.message,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
            });
        }

        function showContactInquiry() {
            
            // Remove trajectory-active class temporarily to allow modals

            document.body.classList.remove('trajectory-active');
            
            // Test with a simple modal first
            try {
                const swalPromise = Swal.fire({
                    title: 'Trajeto Guardado!',
                    html: '<p>O seu trajeto foi guardado com sucesso.</p><br><p><strong>Durante este trajeto, teve algum contacto<br>com uma rola-brava?</strong></p>',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Sim, tive contacto',
                    cancelButtonText: 'Não, não tive contacto',
                    confirmButtonColor: '#0a7ea4',
                    cancelButtonColor: '#6b7280',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    focusConfirm: false,
                    focusCancel: false
                });
                

                
                swalPromise.then((result) => {

                    
                    if (result.isConfirmed) {
                        // User had contact with rola-brava

                        showContactExplanationModal();
                    } else if (result.dismiss === Swal.DismissReason.cancel || !result.isConfirmed) {
                        // User had no contact or clicked cancel

                        showTrajectoryCompleted();
                    } else {

                        showTrajectoryCompleted();
                    }
                }).catch((error) => {
                    console.error('Error with contact inquiry modal:', error);
                    showTrajectoryCompleted();
                });
                
                // Check if modal is actually visible and force it to show
                setTimeout(() => {
                    const swalContainer = document.querySelector('.swal2-container');
                    const swalPopup = document.querySelector('.swal2-popup');
                    
                    if (swalContainer) {
                        // Force the modal to be visible
                        if (window.getComputedStyle(swalContainer).display === 'none' || 
                            window.getComputedStyle(swalContainer).visibility === 'hidden') {
                            swalContainer.style.display = 'flex !important';
                            swalContainer.style.visibility = 'visible !important';
                            swalContainer.style.opacity = '1 !important';
                            swalContainer.style.zIndex = '9999 !important';
                        }
                    }
                    
                    if (swalPopup) {
                        // Force the popup to be visible
                        if (window.getComputedStyle(swalPopup).visibility === 'hidden') {
                            swalPopup.style.visibility = 'visible !important';
                            swalPopup.style.opacity = '1 !important';
                        }
                    }
                }, 100);
                
            } catch (error) {
                console.error('Error calling Swal.fire:', error);
                // Fallback to browser alert
                if (confirm('Trajeto guardado! Durante este trajeto, teve algum contacto\ncom uma rola-brava?')) {
                    showContactExplanationModal();
                } else {
                    showTrajectoryCompleted();
                }
            }
        }


        


        function showTrajectoryCompleted() {
            
            // Go directly to trajectory details modal (times are required)
            showTrajectoryDetailsModal();
        }



        function showFinalTrajectoryCompleted() {
            
            // Ensure trajectory-active class is removed for final modal
            document.body.classList.remove('trajectory-active');
            
            // Re-enable sidebar before showing final modal
            enableSidebar();
            
            // Show success message and redirect
            Swal.fire({
                title: 'Trajeto Completo!',
                text: 'O trajeto foi criado com sucesso e todas as informações foram guardadas.',
                icon: 'success',
                confirmButtonText: 'Ver Zonas',
                confirmButtonColor: '#0a7ea4',
                timer: 3000,
                timerProgressBar: true
            }).then(() => {
                // Redirect back to trajectory list
                window.location.href = 'index.php';
            });
        }
        
        function showTrajectoryDetailsModal() {
            
            // Remove any existing details modal
            const existingModal = document.getElementById('trajectoryDetailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create the trajectory details modal
            const modal = document.createElement('div');
            modal.id = 'trajectoryDetailsModal';
            modal.className = 'modal fade';
            modal.style.zIndex = '1060';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-xl">
                    <div class="modal-content" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                        <div class="modal-header" style="border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; padding: 1rem 1.5rem;">
                            <h5 class="modal-title" style="color: white; font-weight: 600; margin-bottom: 0; font-size: 1.1rem; display: flex; align-items: center;">
                                <i class="fas fa-clipboard-check me-2" style="font-size: 1rem;"></i>
                                Finalizar Trajeto
                            </h5>
                        </div>
                                                <div class="modal-body" style="padding: 1.5rem; background: white; max-height: 60vh; overflow-y: auto;">
                            <form id="trajectoryDetailsForm">
                                 <!-- Time Inputs Section -->
                                 <div class="mb-4">
                                     <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; font-size: 1rem;">
                                         <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                             <i class="fas fa-clock" style="color: white; font-size: 0.9rem;"></i>
                                         </div>
                                         Horários do Trajeto
                                     </h6>
                                     <div class="row g-3">
                                         <div class="col-md-6">
                                             <div class="time-input-card" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border: 1px solid rgba(10, 126, 164, 0.15); border-radius: 8px; padding: 1rem; transition: all 0.3s ease;">
                                                 <label for="middleTime" class="form-label" style="color: #374151; font-weight: 500; margin-bottom: 0.5rem; font-size: 0.9rem; text-align: center; display: block;">
                                                     Hora a que chegou a meio do trajeto
                                                 </label>
                                                 <div class="input-group" style="border-radius: 6px; overflow: hidden;">
                                                     <input type="text" class="form-control" id="middleTime" placeholder="--:--" readonly required onclick="showDetailTimePicker('middleTime')" style="border: 1px solid #d1d5db; font-size: 1rem; font-weight: 500; text-align: center; background: white; padding: 0.5rem; cursor: pointer;">
                                                     <button class="btn" type="button" onclick="showDetailTimePicker('middleTime')" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.5rem 0.75rem;">
                                                         <i class="fas fa-clock"></i>
                                                     </button>
                                                 </div>
                                             </div>
                                         </div>
                                         <div class="col-md-6">
                                             <div class="time-input-card" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border: 1px solid rgba(10, 126, 164, 0.15); border-radius: 8px; padding: 1rem; transition: all 0.3s ease;">
                                                 <label for="endTime" class="form-label" style="color: #374151; font-weight: 500; margin-bottom: 0.5rem; font-size: 0.9rem; text-align: center; display: block;">
                                                     Hora a que chegou ao fim do trajeto
                                                 </label>
                                                 <div class="input-group" style="border-radius: 6px; overflow: hidden;">
                                                     <input type="text" class="form-control" id="endTime" placeholder="--:--" readonly required onclick="showDetailTimePicker('endTime')" style="border: 1px solid #d1d5db; font-size: 1rem; font-weight: 500; text-align: center; background: white; padding: 0.5rem; cursor: pointer;">
                                                     <button class="btn" type="button" onclick="showDetailTimePicker('endTime')" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.5rem 0.75rem;">
                                                         <i class="fas fa-clock"></i>
                                                     </button>
                                                 </div>
                                             </div>
                                         </div>
                                     </div>
                                 </div>

                                                                                                  <!-- Photos Section -->
                                 <div class="mb-3">
                                     <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; font-size: 1rem;">
                                         <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                             <i class="fas fa-images" style="color: white; font-size: 0.9rem;"></i>
                                         </div>
                                         Fotografias 
                                         <span style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-weight: 500; font-size: 0.75rem; margin-left: 0.5rem; display: inline-flex; align-items: center; gap: 0.25rem;">
                                             <i class="fas fa-info-circle" style="font-size: 0.7rem;"></i>
                                             opcional
                                         </span>
                                     </h6>
                                     
                                     <div class="row g-3">
                                         <!-- Map Photos Card -->
                                         <div class="col-md-6">
                                             <div class="photo-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; transition: all 0.3s ease; position: relative;">
                                                 <div class="photo-card-header" style="display: flex; align-items: center; margin-bottom: 0.75rem;">
                                                     <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                         <i class="fas fa-map" style="color: white; font-size: 0.8rem;"></i>
                                                     </div>
                                                     <div>
                                                         <h6 style="margin: 0; color: #374151; font-weight: 600; font-size: 0.9rem;">Fotos dos Mapas</h6>
                                                         <p style="margin: 0; color: #6b7280; font-size: 0.75rem;">Mapas criados durante o trajeto</p>
                                                     </div>
                                                 </div>
                                                 <div class="dropzone-container" id="mapPhotosDropzone" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border: 2px dashed #0a7ea4; color: #0a7ea4; font-weight: 500; padding: 1rem; border-radius: 6px; transition: all 0.3s ease; font-size: 0.85rem; text-align: center; cursor: pointer; position: relative;">
                                                     <div class="dropzone-content">
                                                         <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #0a7ea4; margin-bottom: 0.5rem; display: block;"></i>
                                                         <div style="font-weight: 600; margin-bottom: 0.25rem;">Arraste as fotos aqui</div>
                                                         <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">ou clique para selecionar</div>
                                                         <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                                                     </div>
                                                     <input type="file" id="mapPhotosInput" multiple accept="image/*" style="display: none;" onchange="handleMapPhotosSelect(this)">
                                                 </div>
                                                 <div id="mapPhotosPreview" class="photos-preview mt-2"></div>
                                             </div>
                                         </div>
                                         
                                         <!-- Other Photos Card -->
                                         <div class="col-md-6">
                                             <div class="photo-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; transition: all 0.3s ease; position: relative;">
                                                 <div class="photo-card-header" style="display: flex; align-items: center; margin-bottom: 0.75rem;">
                                                     <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                         <i class="fas fa-camera" style="color: white; font-size: 0.8rem;"></i>
                                                     </div>
                                                     <div>
                                                         <h6 style="margin: 0; color: #374151; font-weight: 600; font-size: 0.9rem;">Outras Fotos</h6>
                                                         <p style="margin: 0; color: #6b7280; font-size: 0.75rem;">Fotos relativas ao percurso</p>
                                                     </div>
                                                 </div>
                                                 <div class="dropzone-container" id="otherPhotosDropzone" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border: 2px dashed #0a7ea4; color: #0a7ea4; font-weight: 500; padding: 1rem; border-radius: 6px; transition: all 0.3s ease; font-size: 0.85rem; text-align: center; cursor: pointer; position: relative;">
                                                     <div class="dropzone-content">
                                                         <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #0a7ea4; margin-bottom: 0.5rem; display: block;"></i>
                                                         <div style="font-weight: 600; margin-bottom: 0.25rem;">Arraste as fotos aqui</div>
                                                         <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">ou clique para selecionar</div>
                                                         <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                                                     </div>
                                                     <input type="file" id="otherPhotosInput" multiple accept="image/*" style="display: none;" onchange="handleOtherPhotosSelect(this)">
                                                 </div>
                                                 <div id="otherPhotosPreview" class="photos-preview mt-2"></div>
                                             </div>
                                         </div>
                                     </div>
                                 </div>
                            </form>
                        </div>
                                                 <div class="modal-footer" style="border: none; padding: 1.25rem; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); display: flex; justify-content: center;">
                             <button type="button" class="btn" onclick="saveTrajectoryDetails()" id="saveDetailsBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.75rem 2rem; font-size: 1rem; font-weight: 600; border-radius: 8px; display: inline-flex; align-items: center; gap: 0.5rem; box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3); transition: all 0.3s ease; transform: translateY(0);">
                                 <i class="fas fa-check-circle" style="font-size: 1rem;"></i>
                                 Concluir Trajeto
                             </button>
                         </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            const bsModal = new bootstrap.Modal(modal, {
                backdrop: 'static',
                keyboard: false
            });
            bsModal.show();

            // Initialize dropzone functionality
            setupDropzones();
            
            // Add click event listeners for time picker buttons
            setTimeout(() => {
                // Add click event listeners as fallback
                const middleTimeBtn = modal.querySelector('button[onclick*="middleTime"]');
                const endTimeBtn = modal.querySelector('button[onclick*="endTime"]');
                
                if (middleTimeBtn) {
                    middleTimeBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        if (window.showDetailTimePicker) {
                            window.showDetailTimePicker('middleTime');
                        }
                    });
                }
                
                if (endTimeBtn) {
                    endTimeBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        if (window.showDetailTimePicker) {
                            window.showDetailTimePicker('endTime');
                        }
                    });
                }
            }, 500);
        }
        
        window.redirectToTrajectoryList = function redirectToTrajectoryList() {
            window.location.href = 'index.php';
        }

        function calculateTotalDistance() {
            if (routeCoordinates.length > 1) {
                // Use Google Maps geometry computeLength which works with coordinate objects
                const distance = google.maps.geometry.spherical.computeLength(routeCoordinates);
                return (distance / 1000).toFixed(2); // Return in km
            }
            return '0.00';
        }

        function disableMapInteraction() {
            // Remove all map click listeners
            if (map) {
                google.maps.event.clearListeners(map, 'click');
                google.maps.event.clearListeners(map, 'dblclick');
            }
            
            // Disable trajectory action buttons
            const undoBtn = document.getElementById('undoBtn');
            const clearBtn = document.querySelector('[onclick="clearTrajectory()"]');
            const saveBtn = document.getElementById('saveTrajetoBtn');
            
            if (undoBtn) {
                undoBtn.disabled = true;
                undoBtn.innerHTML = '<i class="fas fa-check me-1"></i>Trajeto Finalizado';
                undoBtn.classList.remove('btn-warning');
                undoBtn.classList.add('btn-success');
            }
            
            if (clearBtn) {
                clearBtn.disabled = true;
                clearBtn.style.opacity = '0.5';
                clearBtn.style.pointerEvents = 'none';
            }
            
            if (saveBtn) {
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Guardado';
                saveBtn.classList.remove('btn-success');
                saveBtn.classList.add('btn-secondary');
            }
            
            // Add visual indicator that trajectory is finalized
            const mapContainer = document.getElementById('map');
            if (mapContainer) {
                mapContainer.style.cursor = 'default';
                // Remove opacity to prevent white overlay effect
                // mapContainer.style.opacity = '0.8';
            }
            
            // Update UI to show trajectory is finalized
            updateTrajectoryUI(false, true);
            

        }

        function showLocationConfirmedModal() {
            
            // Dim the background modal
            document.getElementById('trajectorySetupModal').classList.add('modal-backdrop-dimmed');
            
            const modal = new bootstrap.Modal(document.getElementById('locationConfirmedModal'));
            modal.show();
            
            // Remove dimming when location modal is hidden
            document.getElementById('locationConfirmedModal').addEventListener('hidden.bs.modal', function() {
                document.getElementById('trajectorySetupModal').classList.remove('modal-backdrop-dimmed');
            }, { once: true });
        }

        window.closeLocationConfirmedModal = function closeLocationConfirmedModal() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('locationConfirmedModal'));
            modal.hide();
            
            // Show trajectory setup modal after location confirmation (animation already happened)
            setTimeout(() => {
                showTrajectorySetupModal();
            }, 300);
        }
        
        // Function to animate map to a specific location with smooth cinematic effect
        function animateMapToLocation(targetLocation, targetZoom = 15, fromCurrentPosition = false) {
            return new Promise((resolve) => {
                if (!map) {
                    resolve();
                    return;
                }
                
                const cinematicZoomOut = 8; // Zoom out to level 8 for subtle effect
                const intermediateZoom = 12; // Intermediate zoom level
                
                if (fromCurrentPosition) {
                    // For search locations: start from current map position, zoom out, then travel to target
                    const currentPosition = map.getCenter();
                    
                    // Step 1: Zoom out from current position
                    smoothZoomTo(cinematicZoomOut, 800).then(() => {
                        // Step 2: Pan from current position to target location (journey effect)
                        map.panTo(targetLocation);
                        
                        setTimeout(() => {
                            // Step 3: Zoom to intermediate level
                            smoothZoomTo(intermediateZoom, 600).then(() => {
                                // Step 4: Final zoom to target level
                                smoothZoomTo(targetZoom, 800).then(() => {
                                    // Final centering
                                    setTimeout(() => {
                                        map.panTo(targetLocation);
                                        setTimeout(() => {
                                            resolve();
                                        }, 300);
                                    }, 200);
                                });
                            });
                        }, 1200); // Wait for pan animation to complete
                    });
                } else {
                    // For GPS locations: center on target first, then zoom out and back in
                    // Step 1: Instantly center on target location (for GPS we want to focus on user location)
                    map.setCenter(targetLocation);
                    
                    // Step 2: Zoom out from target location
                    smoothZoomTo(cinematicZoomOut, 800).then(() => {
                        // Step 3: Small re-center during zoom out
                        map.panTo(targetLocation);
                        
                        setTimeout(() => {
                            // Step 4: Zoom to intermediate level
                            smoothZoomTo(intermediateZoom, 600).then(() => {
                                // Step 5: Final zoom to target level
                                smoothZoomTo(targetZoom, 800).then(() => {
                                    // Final centering
                                    setTimeout(() => {
                                        map.panTo(targetLocation);
                                        setTimeout(() => {
                                            resolve();
                                        }, 300);
                                    }, 200);
                                });
                            });
                        }, 400); // Shorter wait since no long pan needed
                    });
                }
            });
        }
        
        // Function to animate map from one specific location to another (for search)
        function animateMapFromTo(startLocation, startZoom, targetLocation, targetZoom) {
            return new Promise((resolve) => {
                if (!map) {
                    resolve();
                    return;
                }
                
                // Ensure we start from the original position
                map.setCenter(startLocation);
                map.setZoom(startZoom);
                
                const cinematicZoomOut = 8; // Zoom out level
                const intermediateZoom = 12; // Intermediate zoom level
                
                // Step 1: Zoom out from original position
                smoothZoomTo(cinematicZoomOut, 800).then(() => {
                    // Step 2: Pan from original position to target location (true journey effect)
                    map.panTo(targetLocation);
                    
                    setTimeout(() => {
                        // Step 3: Zoom to intermediate level
                        smoothZoomTo(intermediateZoom, 600).then(() => {
                            // Step 4: Final zoom to target level
                            smoothZoomTo(targetZoom, 800).then(() => {
                                // Final centering
                                setTimeout(() => {
                                    map.panTo(targetLocation);
                                    setTimeout(() => {
                                        resolve();
                                    }, 300);
                                }, 200);
                            });
                        });
                    }, 1200); // Wait for pan animation to complete
                });
            });
        }

        // Helper function for smooth zoom animations
        function smoothZoomTo(targetZoom, duration = 1000) {
            return new Promise((resolve) => {
                const startZoom = map.getZoom();
                const zoomDifference = targetZoom - startZoom;
                const steps = 20; // Number of animation steps
                const stepDuration = duration / steps;
                const zoomStep = zoomDifference / steps;
                
                let currentStep = 0;
                
                const animateStep = () => {
                    if (currentStep >= steps) {
                        map.setZoom(targetZoom); // Ensure exact final zoom
                        resolve();
                        return;
                    }
                    
                    const newZoom = startZoom + (zoomStep * currentStep);
                    map.setZoom(newZoom);
                    currentStep++;
                    
                    setTimeout(animateStep, stepDuration);
                };
                
                animateStep();
            });
        }

        function showTrajectorySavedModal() {
            
            
            // Ensure trajectory-active class is removed for proper modal display
            document.body.classList.remove('trajectory-active');
            
            const modal = new bootstrap.Modal(document.getElementById('trajectorySavedModal'), {
                backdrop: 'static',
                keyboard: false
            });
            modal.show();
            
            // Force backdrop to be visible and properly styled
            setTimeout(() => {
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.style.opacity = '0.7';
                    backdrop.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    backdrop.style.zIndex = '1054';
                }
            }, 100);
        }

        window.handleContactResponse = function handleContactResponse(hasContact) {
            
            // Close the trajectory saved modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('trajectorySavedModal'));
            modal.hide();
            
            setTimeout(() => {
                if (hasContact) {
                    // User had contact with rola-brava - show explanation modal then switch to contact mode
                    showContactExplanationModal();
                } else {
                    // User had no contact
                    showTrajectoryCompleted();
                }
            }, 300);
        }

        // Global variables for contact mode
        let isContactMode = false;
        let contactMarkers = [];
        let contactCount = 0;
        let pendingContactLocation = null;
        let savedTrajectoryId = null;

        function showContactExplanationModal() {
            // Create the contact explanation modal if it doesn't exist
            let modal = document.getElementById('contactExplanationModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'contactExplanationModal';
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content trajectory-setup-modal" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden; max-height: 90vh; display: flex; flex-direction: column;">
                            <div class="modal-header" style="border-radius: 16px 16px 0 0; border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; display: flex; justify-content: center; align-items: center; padding: 1.5rem 2rem; position: relative; margin: 0; flex-shrink: 0;">
                                <div class="d-flex align-items-center">
                                    <div class="step-indicator me-3" style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="fas fa-dove" style="color: white; font-size: 1.2rem;"></i>
                                    </div>
                                    <h5 class="modal-title mb-0" style="color: white; font-weight: 600; font-size: 1.25rem;">Registar Contactos com Rola-Brava</h5>
                                </div>
                            </div>
                                                          <div class="modal-body" style="flex: 1; overflow-y: auto; padding: 1.5rem; min-height: 0; background: white;">
                                <div class="text-center mb-3">
                                    <h2 style="color: #374151; font-weight: 700; font-size: 1.5rem; margin-bottom: 0.5rem;">Adicionar Contactos</h2>
                                    <p style="color: #6b7280; font-size: 0.9rem; margin-bottom: 0;">Siga estes passos simples para registar os contactos</p>
                                </div>

                                <!-- Visual Demo Section -->
                                <div class="demo-section mb-3" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border-radius: 12px; padding: 1rem; border: 1px solid rgba(10, 126, 164, 0.1);">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="demo-icon" style="width: 36px; height: 36px; border-radius: 50%; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                            <i class="fas fa-play" style="color: white; font-size: 0.9rem;"></i>
                                        </div>
                                        <div>
                                            <h6 style="color: #374151; font-weight: 600; margin-bottom: 0.125rem; font-size: 1rem;">Demonstração Visual</h6>
                                            <p style="color: #6b7280; margin-bottom: 0; font-size: 0.8rem;">Veja como adicionar contactos no mapa do trajeto</p>
                                        </div>
                                    </div>
                                    <div class="demo-gif-container" style="background: white; border-radius: 8px; padding: 0.5rem; margin-top: 0.75rem; text-align: center;">
                                        <img src="../../assets/images/contacto1.gif" alt="Demonstração de como adicionar contactos" style="max-width: 70%; height: auto; border-radius: 6px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                                    </div>
                                </div>

                                <!-- Process Steps Section -->
                                <div class="steps-section mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="steps-icon" style="width: 36px; height: 36px; border-radius: 50%; background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                            <i class="fas fa-list-ol" style="color: white; font-size: 0.9rem;"></i>
                                        </div>
                                        <div>
                                            <h6 style="color: #374151; font-weight: 600; margin-bottom: 0.125rem; font-size: 1rem;">Passos do Processo</h6>
                                            <p style="color: #6b7280; margin-bottom: 0; font-size: 0.8rem;">Cada passo é identificado por cores e símbolos específicos</p>
                                        </div>
                                    </div>
                                    
                                    <div class="row" style="margin-left: 0; margin-right: 0;">
                                        <div class="col-md-6" style="padding-left: 0; padding-right: 0.5rem;">
                                            <div class="step-item" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; margin-bottom: 0.75rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                                <div class="d-flex align-items-center mb-1">
                                                    <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                        <i class="fas fa-mouse-pointer" style="color: white; font-size: 0.8rem;"></i>
                                                    </div>
                                                    <span style="font-weight: 600; color: #374151; font-size: 0.9rem;">1. Clique no Mapa</span>
                                                </div>
                                                <p style="color: #6b7280; margin-bottom: 0; font-size: 0.8rem; margin-left: 2rem;">Clique no local onde teve contacto com rola-brava</p>
                                            </div>
                                            
                                            <div class="step-item" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; margin-bottom: 0.75rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                                <div class="d-flex align-items-center mb-1">
                                                    <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #10b981 0%, #059669 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                        <i class="fas fa-dove" style="color: white; font-size: 0.8rem;"></i>
                                                    </div>
                                                    <span style="font-weight: 600; color: #374151; font-size: 0.9rem;">2. Ícone de Rola</span>
                                                </div>
                                                <p style="color: #6b7280; margin-bottom: 0; font-size: 0.8rem; margin-left: 2rem;">Um ícone de rola aparece no local selecionado</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6" style="padding-left: 0.5rem; padding-right: 0;">
                                            <div class="step-item" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; margin-bottom: 0.75rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                                <div class="d-flex align-items-center mb-1">
                                                    <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                        <i class="fas fa-check-circle" style="color: white; font-size: 0.8rem;"></i>
                                                    </div>
                                                    <span style="font-weight: 600; color: #374151; font-size: 0.9rem;">3. Confirmação</span>
                                                </div>
                                                <p style="color: #6b7280; margin-bottom: 0; font-size: 0.8rem; margin-left: 2rem;">Confirme se deseja adicionar o contacto</p>
                                            </div>
                                            
                                            <div class="step-item" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; margin-bottom: 0.75rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                                <div class="d-flex align-items-center mb-1">
                                                    <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                        <i class="fas fa-edit" style="color: white; font-size: 0.8rem;"></i>
                                                    </div>
                                                    <span style="font-weight: 600; color: #374151; font-size: 0.9rem;">4. Detalhes</span>
                                                </div>
                                                <p style="color: #6b7280; margin-bottom: 0; font-size: 0.8rem; margin-left: 2rem;">Preencha os detalhes do contacto</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Important Tips Section -->
                                <div class="tips-section" style="background: linear-gradient(135deg, rgba(251, 191, 36, 0.08) 0%, rgba(245, 158, 11, 0.05) 100%); border: 1px solid rgba(251, 191, 36, 0.2); border-radius: 8px; padding: 1rem;">
                                    <div class="d-flex align-items-center mb-2">
                                        <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                            <i class="fas fa-lightbulb" style="color: white; font-size: 0.8rem;"></i>
                                        </div>
                                        <h6 style="color: #92400e; font-weight: 600; margin-bottom: 0; font-size: 0.9rem;">Dicas Importantes</h6>
                                    </div>
                                    <ul style="color: #92400e; margin-bottom: 0; padding-left: 1rem; font-size: 0.8rem;">
                                        <li style="margin-bottom: 0.25rem;">Clique com precisão no local exato do contacto</li>
                                        <li style="margin-bottom: 0.25rem;">Pode adicionar múltiplos contactos no mesmo trajeto</li>
                                        <li style="margin-bottom: 0;">Todos os campos do formulário são obrigatórios</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="modal-footer" style="flex-shrink: 0; border: none; padding: 1.5rem 2rem; background: white; display: flex; justify-content: center;">
                                <button type="button" class="btn" onclick="startContactMode()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-arrow-right"></i>
                                    Entendi, Continuar
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            const bsModal = new bootstrap.Modal(modal, {
                backdrop: 'static',
                keyboard: false
            });
            bsModal.show();
        }

        window.startContactMode = function startContactMode() {
            console.log('Starting contact addition mode');
            
            // Close the explanation modal properly
            const modalElement = document.getElementById('contactExplanationModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
                
                // Remove the modal element and backdrop after hiding
                setTimeout(() => {
                    // Remove modal element
                    if (modalElement && modalElement.parentNode) {
                        modalElement.parentNode.removeChild(modalElement);
                    }
                    
                    // Force remove any remaining backdrops
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => {
                        if (backdrop && backdrop.parentNode) {
                            backdrop.parentNode.removeChild(backdrop);
                        }
                    });
                    
                    // Ensure body classes are cleaned up
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                    
                    // Now switch to contact mode
                    switchToContactMode();
                }, 300);
            } else {
                // If modal doesn't exist, switch directly
                switchToContactMode();
            }
        }

        function switchToContactMode() {
            console.log('Switching to contact addition mode');
            
            // Ensure page is fully interactive
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Remove any remaining modal backdrops
            const remainingBackdrops = document.querySelectorAll('.modal-backdrop');
            remainingBackdrops.forEach(backdrop => {
                if (backdrop && backdrop.parentNode) {
                    backdrop.parentNode.removeChild(backdrop);
                }
            });
            
            // Re-enable modals needed for contact mode (time picker, date picker, etc.)
            enableContactModeModals();
            
            isContactMode = true;
            contactMarkers = [];
            contactCount = 0;
            
            console.log('Contact mode activated, updating UI...');
            
            // Update page title and header
            updateUIForContactMode();
            
            // Update map click handler for contact placement
            updateMapForContactMode();
            
            // Update buttons for contact mode
            updateButtonsForContactMode();
            
            // Update stats display
            updateStatsForContactMode();
            
            // Keep sidebar blocked during contact addition
            // (it's already blocked from trajectory creation)
            
            console.log('Contact mode UI updated successfully');
        }

        function enableContactModeModals() {
            console.log('Re-enabling modals needed for contact mode');
            
            // List of modals that should be available during contact mode
            const contactModeModals = [
                'timePickerModal',
                'datePickerModal',
                'contactDetailsModal',
                'contactConfirmationModal'
            ];
            
            contactModeModals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    // Reset modal styles to allow it to be shown
                    modal.style.display = '';
                    modal.style.visibility = '';
                    modal.style.opacity = '';
                    modal.style.pointerEvents = '';
                    modal.style.zIndex = '';
                    modal.removeAttribute('aria-hidden');
                    
                    console.log(`Re-enabled modal: ${modalId}`);
                }
            });
        }

        function updateUIForContactMode() {
            // Update page title
            const pageTitle = document.querySelector('.page-title');
            if (pageTitle) {
                pageTitle.innerHTML = `
                    <i class="fas fa-dove"></i>
                    Adicionar Contactos
                    <span class="zone-name">${document.querySelector('.zone-name').textContent}</span>
                `;
            }
            
            // Update card header
            const cardHeader = document.querySelector('.card-header h5');
            if (cardHeader) {
                cardHeader.innerHTML = `
                    <i class="fas fa-dove me-2"></i>
                    Contactos com Rola-Brava
                `;
            }
            
            // Update info badge
            const infoBadge = document.querySelector('.info-badge');
            if (infoBadge) {
                infoBadge.innerHTML = `
                    <i class="fas fa-mouse-pointer me-1"></i>
                    Clique no mapa para adicionar contactos com rola-brava
                `;
            }
        }

        function updateMapForContactMode() {
            // Remove existing trajectory creation click handler
            if (map) {
                google.maps.event.clearListeners(map, 'click');
                
                // Add contact placement click handler
                map.addListener('click', function(event) {
                    if (isContactMode) {
                        handleContactMapClick(event);
                    }
                });
            }
        }

        function updateButtonsForContactMode() {
            const formActions = document.querySelector('.form-actions');
            if (formActions) {
                formActions.innerHTML = `
                    <button type="button" class="btn btn-outline-secondary" onclick="cancelContactAddition()">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-secondary" id="clearContactsBtn" disabled onclick="clearAllContacts()">
                        <i class="fas fa-eraser me-1"></i>
                        Limpar Contactos
                    </button>
                    <button type="button" class="btn btn-warning" id="undoContactBtn" disabled onclick="undoLastContact()">
                        <i class="fas fa-undo me-1"></i>
                        Desfazer Último
                    </button>
                    <button type="button" class="btn btn-success" id="saveContactsBtn" disabled onclick="saveAllContacts()">
                        <i class="fas fa-save me-1"></i>
                        Guardar Contactos
                    </button>
                `;
            }
        }

        function updateStatsForContactMode() {
            // Add contact counter to stats and adjust layout
            const statsSection = document.querySelector('.stats-section .row');
            if (statsSection) {
                // Change layout: address becomes smaller, distance and points become smaller, add contacts
                const addressCol = statsSection.querySelector('.col-md-8');
                const distCol = statsSection.querySelector('.col-md-2:nth-child(2)');
                const ptsCol = statsSection.querySelector('.col-md-2:nth-child(3)');
                
                if (addressCol) addressCol.className = addressCol.className.replace('col-md-8', 'col-md-6');
                if (distCol) distCol.className = distCol.className.replace('col-md-2', 'col-md-2');
                if (ptsCol) ptsCol.className = ptsCol.className.replace('col-md-2', 'col-md-2');
                
                // Add contact count column
                const contactCol = document.createElement('div');
                contactCol.className = 'col-md-2';
                contactCol.innerHTML = `
                    <div class="stat-item stat-item-compact">
                        <i class="fas fa-dove me-1"></i>
                        <span class="stat-label">CONTACTOS:</span>
                        <span class="stat-value" id="contactCount">0</span>
                    </div>
                `;
                statsSection.appendChild(contactCol);
            }
        }

        // Contact handling functions
        function handleContactMapClick(event) {
            console.log('Contact map clicked at:', event.latLng.lat(), event.latLng.lng());
            
            pendingContactLocation = {
                lat: event.latLng.lat(),
                lng: event.latLng.lng()
            };
            
            showContactConfirmationModal();
        }

        function showContactConfirmationModal() {
            // Create the contact confirmation modal if it doesn't exist
            let modal = document.getElementById('contactConfirmationModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'contactConfirmationModal';
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered" style="max-width: 500px;">
                        <div class="modal-content trajectory-setup-modal" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                            <div class="modal-header" style="border-radius: 16px 16px 0 0; border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; display: flex; justify-content: space-between; align-items: center; padding: 1.5rem 2rem; position: relative; margin: 0;">
                                <div class="d-flex align-items-center">
                                    <div class="step-indicator me-3" style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="fas fa-dove" style="color: white; font-size: 1.2rem;"></i>
                                    </div>
                                    <h5 class="modal-title mb-0" style="color: white; font-weight: 600; font-size: 1.25rem;">Adicionar Contacto</h5>
                                </div>
                            </div>
                            <div class="modal-body" style="padding: 2.5rem 2rem; text-align: center; background: white;">
                                <div class="mb-4">
                                    <div class="mb-3" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.08) 0%, rgba(8, 145, 178, 0.06) 100%); border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
                                        <i class="fas fa-map-marker-alt" style="color: #0a7ea4; font-size: 2rem;"></i>
                                    </div>
                                    <h6 class="mb-3" style="color: #374151; font-weight: 600; font-size: 1.1rem;">Confirmar Localização do Contacto</h6>
                                    <p class="text-muted mb-3" style="font-size: 0.95rem; line-height: 1.5;">
                                        Deseja adicionar um contacto com rola-brava nesta localização?
                                    </p>
                                    <div class="coordinates-display" style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 0.75rem 1rem; font-family: monospace; font-size: 0.85rem; color: #64748b;">
                                        <small id="contactCoordinates"></small>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: white; display: flex; gap: 1rem; justify-content: center;">
                                <button type="button" class="btn" onclick="cancelContactPlacement()" style="background-color: #6b7280; border-color: #6b7280; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-times"></i>
                                    Não
                                </button>
                                <button type="button" class="btn" onclick="confirmContactPlacement()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-check"></i>
                                    Sim, Adicionar
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            // Update coordinates display
            if (pendingContactLocation) {
                document.getElementById('contactCoordinates').textContent = 
                    `${pendingContactLocation.lat.toFixed(6)}, ${pendingContactLocation.lng.toFixed(6)}`;
            }

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }

        window.cancelContactPlacement = function cancelContactPlacement() {
            console.log('Contact placement cancelled');
            pendingContactLocation = null;
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactConfirmationModal'));
            modal.hide();
        }

        window.confirmContactPlacement = function confirmContactPlacement() {
            console.log('Contact placement confirmed');
            
            if (!pendingContactLocation) return;

            // Close confirmation modal
            const confirmModal = bootstrap.Modal.getInstance(document.getElementById('contactConfirmationModal'));
            confirmModal.hide();

            // Add temporary contact marker
            addContactMarker(pendingContactLocation);

            // Show contact details modal after a short delay
            setTimeout(() => {
                showContactDetailsModal();
            }, 300);
        }

        function addContactMarker(location) {
            // Create dove/rola marker using a canvas to combine circle background with dove icon
            const canvas = document.createElement('canvas');
            canvas.width = 32;
            canvas.height = 32;
            const ctx = canvas.getContext('2d');
            
            // Draw circle background
            ctx.beginPath();
            ctx.arc(16, 16, 14, 0, 2 * Math.PI);
            ctx.fillStyle = '#0a7ea4';
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Load and draw dove icon
            const doveImg = new Image();
            doveImg.onload = function() {
                // Create a temporary canvas to process the dove icon
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = 16;
                tempCanvas.height = 16;
                const tempCtx = tempCanvas.getContext('2d');
                
                // Draw the dove icon on temp canvas
                tempCtx.drawImage(doveImg, 0, 0, 16, 16);
                
                // Get image data to process pixels
                const imageData = tempCtx.getImageData(0, 0, 16, 16);
                const data = imageData.data;
                
                // Convert non-transparent pixels to white
                for (let i = 0; i < data.length; i += 4) {
                    if (data[i + 3] > 0) { // If pixel is not transparent
                        data[i] = 255;     // Red = 255 (white)
                        data[i + 1] = 255; // Green = 255 (white)
                        data[i + 2] = 255; // Blue = 255 (white)
                        // Keep original alpha (data[i + 3])
                    }
                }
                
                // Put the processed image data back
                tempCtx.putImageData(imageData, 0, 0);
                
                // Draw the white dove icon on the main canvas
                ctx.drawImage(tempCanvas, 8, 8);
                
                // Create marker with the canvas image
                const marker = new google.maps.Marker({
                    position: location,
                    map: map,
                    icon: {
                        url: canvas.toDataURL(),
                        scaledSize: new google.maps.Size(32, 32),
                        anchor: new google.maps.Point(16, 16)
                    },
                    title: `Contacto ${contactCount + 1}`,
                    zIndex: 200
                });
                
                // Store marker data
                contactMarkers.push({
                    marker: marker,
                    location: location,
                    details: null
                });
                
                updateContactCounter();
                         };
             doveImg.src = '../../assets/images/icons/dove-icon.png';
        }

        function showContactDetailsModal() {
            // Create the contact details modal if it doesn't exist
            let modal = document.getElementById('contactDetailsModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'contactDetailsModal';
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content trajectory-setup-modal" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                            <div class="modal-header" style="border-radius: 16px 16px 0 0; border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; display: flex; justify-content: space-between; align-items: center; padding: 1.5rem 2rem; position: relative; margin: 0;">
                                <div class="d-flex align-items-center">
                                    <div class="step-indicator me-3" style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <i class="fas fa-dove" style="color: white; font-size: 1.2rem;"></i>
                                    </div>
                                    <h5 class="modal-title mb-0" style="color: white; font-weight: 600; font-size: 1.25rem;">Detalhes do contacto com rola-brava</h5>
                                </div>
                            </div>
                            <div class="modal-body" style="padding: 2rem; background: white; max-height: 70vh; overflow-y: auto;">
                                <form id="contactDetailsForm">
                                    <!-- Time and Coordinates -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <label for="contactTime" class="form-label" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                                                <i class="fas fa-clock me-2" style="color: #0a7ea4;"></i>
                                                Hora
                                            </label>
                                            <div class="input-group" style="margin-bottom: 0.5rem;">
                                                <input type="text" class="form-control" id="contactTime" placeholder="Selecione a hora" readonly required style="border: 2px solid #e5e7eb; border-radius: 8px 0 0 8px; font-weight: 500;">
                                                <button class="btn" type="button" id="contactTimePickerBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; border-radius: 0 8px 8px 0;">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                            </div>
                                            <div class="form-text" style="font-size: 0.8rem; color: #6b7280; display: flex; align-items: center;">
                                                <i class="fas fa-info-circle me-1" style="color: #0a7ea4;"></i>
                                                Clique em qualquer parte do campo para selecionar a hora
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                                                <i class="fas fa-map-marker-alt me-2" style="color: #0a7ea4;"></i>
                                                Coordenadas
                                            </label>
                                            <input type="text" class="form-control" id="contactCoords" readonly style="background-color: #f8fafc; border: 2px solid #e5e7eb; border-radius: 8px; font-family: monospace; font-weight: 500; color: #374151;">
                                        </div>
                                    </div>

                                                                         <!-- Contact Circumstances Section -->
                                     <div class="form-section mb-4">
                                         <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.75rem; display: flex; align-items: center; font-size: 1rem;">
                                             <i class="fas fa-eye me-2" style="color: #0a7ea4;"></i>
                                             Circunstância do contacto
                                         </label>
                                         <div class="contact-circumstances" style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-top: 0.75rem;">
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_singing" name="contact_circumstances" value="adultSinging" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_singing" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-music me-2" style="color: #f59e0b;"></i>
                                                     Rola adulta a cantar
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_display" name="contact_circumstances" value="adultDisplay" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_display" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-heart me-2" style="color: #ef4444;"></i>
                                                     Adulto em display
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_flying" name="contact_circumstances" value="flying" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_flying" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-dove me-2" style="color: #0a7ea4;"></i>
                                                     Rola em voo
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_empty_nest" name="contact_circumstances" value="emptyNest" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_empty_nest" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-circle me-2" style="color: #6b7280;"></i>
                                                     Ninho vazio
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_perched" name="contact_circumstances" value="adultPerched" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_perched" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-feather me-2" style="color: #10b981;"></i>
                                                     Adulto pousado
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_occupied_nest" name="contact_circumstances" value="occupiedNest" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_occupied_nest" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-egg me-2" style="color: #06b6d4;"></i>
                                                     Ninho ocupado
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_groups" name="contact_circumstances" value="groups" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_groups" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-dove me-2" style="color: #8b5cf6;"></i>
                                                     Grupos de Rolas
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="circumstance_other" name="contact_circumstances" value="other" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="circumstance_other" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-question-circle me-2" style="color: #6b7280;"></i>
                                                     Outro. Qual?
                                                 </label>
                                             </div>
                                         </div>
                                         <!-- Number input for "Grupos de Rolas" -->
                                         <div id="circumstanceGroupsInput" style="display: none; margin-top: 0.75rem;">
                                             <input type="number" class="form-control" id="circumstanceGroupsNumber" placeholder="Número de Indivíduos..." min="1" max="999" style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; font-size: 0.9rem;" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                         </div>
                                         <!-- Text input for "Outro. Qual?" in circumstances -->
                                         <div id="circumstanceOtherInput" style="display: none; margin-top: 0.75rem;">
                                             <input type="text" class="form-control" id="circumstanceOtherText" placeholder="Especifique a circunstância..." style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; font-size: 0.9rem;">
                                         </div>
                                     </div>

                                                                         <!-- Location Details Section -->
                                     <div class="form-section mb-3">
                                         <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.75rem; display: flex; align-items: center; font-size: 1rem;">
                                             <i class="fas fa-tree me-2" style="color: #0a7ea4;"></i>
                                             Local do contacto
                                         </label>
                                         <div class="contact-location" style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-top: 0.75rem;">
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="location_tree" name="contact_location" value="tree" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="location_tree" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-tree me-2" style="color: #10b981;"></i>
                                                     Árvore
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="location_water" name="contact_location" value="waterPoint" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="location_water" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-tint me-2" style="color: #0a7ea4;"></i>
                                                     Ponto de água
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="location_shrub" name="contact_location" value="shrub" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="location_shrub" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-seedling me-2" style="color: #10b981;"></i>
                                                     Arbusto
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="location_clearing" name="contact_location" value="clearing" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="location_clearing" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-circle me-2" style="color: #f59e0b;"></i>
                                                     Clareira
                                                 </label>
                                             </div>
                                             <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 0.75rem 1rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 52px;">
                                                 <input class="form-check-input" type="radio" id="location_other" name="contact_location" value="other" style="position: relative; margin: 0 0.75rem 0 0; width: 18px; height: 18px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                                 <label class="form-check-label" for="location_other" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.9rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                                     <i class="fas fa-question-circle me-2" style="color: #6b7280;"></i>
                                                     Outro. Qual?
                                                 </label>
                                             </div>
                                         </div>
                                         <!-- Text input for "Outro. Qual?" in location -->
                                         <div id="locationOtherInput" style="display: none; margin-top: 0.75rem;">
                                             <input type="text" class="form-control" id="locationOtherText" placeholder="Especifique o local..." style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem; font-size: 0.9rem;">
                                         </div>
                                     </div>
                                </form>
                            </div>
                            <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: white; display: flex; gap: 1rem; justify-content: center;">
                                <button type="button" class="btn" onclick="cancelContactDetails()" style="background-color: #6b7280; border-color: #6b7280; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-times"></i>
                                    Cancelar
                                </button>
                                <button type="button" class="btn" onclick="saveContactDetails()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.625rem 1.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-save"></i>
                                    Guardar Contacto
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                
                // Add event listeners for contact time picker
                setupContactTimePickerListeners();
                
                // Add event listeners for "Outro. Qual?" options
                setupOtherOptionListeners();
            }

            // DON'T set current time - leave empty for validation
            document.getElementById('contactTime').value = '';

            // Set contact coordinates (readonly)
            if (pendingContactLocation) {
                document.getElementById('contactCoords').value = 
                    `${pendingContactLocation.lat.toFixed(6)}, ${pendingContactLocation.lng.toFixed(6)}`;
            }

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }

        function setupContactTimePickerListeners() {
            // Add event listeners for time picker
            const timeInput = document.getElementById('contactTime');
            const timePickerBtn = document.getElementById('contactTimePickerBtn');
            
            if (timeInput && timePickerBtn) {
                // Click on input field
                timeInput.addEventListener('click', function() {
                    showContactTimePicker();
                });
                
                // Click on button
                timePickerBtn.addEventListener('click', function() {
                    showContactTimePicker();
                });
            }
        }

        function setupOtherOptionListeners() {
            // Add event listeners for "Outro. Qual?" and "Grupos de Rolas" options
            
            // Circumstance options
            const circumstanceOtherRadio = document.getElementById('circumstance_other');
            const circumstanceOtherInput = document.getElementById('circumstanceOtherInput');
            const circumstanceGroupsRadio = document.getElementById('circumstance_groups');
            const circumstanceGroupsInput = document.getElementById('circumstanceGroupsInput');
            const circumstanceRadios = document.querySelectorAll('input[name="contact_circumstances"]');
            
            if (circumstanceRadios.length > 0) {
                circumstanceRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        // Handle "Outro. Qual?" option
                        if (this.value === 'other') {
                            if (circumstanceOtherInput) {
                                circumstanceOtherInput.style.display = 'block';
                                document.getElementById('circumstanceOtherText').focus();
                            }
                        } else {
                            if (circumstanceOtherInput) {
                                circumstanceOtherInput.style.display = 'none';
                                document.getElementById('circumstanceOtherText').value = '';
                            }
                        }
                        
                        // Handle "Grupos de Rolas" option
                        if (this.value === 'groups') {
                            if (circumstanceGroupsInput) {
                                circumstanceGroupsInput.style.display = 'block';
                                document.getElementById('circumstanceGroupsNumber').focus();
                            }
                        } else {
                            if (circumstanceGroupsInput) {
                                circumstanceGroupsInput.style.display = 'none';
                                document.getElementById('circumstanceGroupsNumber').value = '';
                            }
                        }
                    });
                });
            }
            
            // Add numeric-only validation for groups number input
            const circumstanceGroupsNumber = document.getElementById('circumstanceGroupsNumber');
            if (circumstanceGroupsNumber) {
                // Prevent negative values and non-numeric characters
                circumstanceGroupsNumber.addEventListener('keydown', function(e) {
                    // Allow: backspace, delete, tab, escape, enter
                    if ([46, 8, 9, 27, 13].indexOf(e.keyCode) !== -1 ||
                        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                        (e.keyCode === 65 && e.ctrlKey === true) ||
                        (e.keyCode === 67 && e.ctrlKey === true) ||
                        (e.keyCode === 86 && e.ctrlKey === true) ||
                        (e.keyCode === 88 && e.ctrlKey === true) ||
                        // Allow: home, end, left, right
                        (e.keyCode >= 35 && e.keyCode <= 39)) {
                        return;
                    }
                    // Ensure that it is a number and stop the keypress
                    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                        e.preventDefault();
                    }
                });
                
                // Prevent pasting non-numeric content
                circumstanceGroupsNumber.addEventListener('paste', function(e) {
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    if (!/^\d+$/.test(paste) || parseInt(paste) < 1 || parseInt(paste) > 999) {
                        e.preventDefault();
                    }
                });
                
                // Ensure value stays within range
                circumstanceGroupsNumber.addEventListener('input', function(e) {
                    let value = parseInt(e.target.value);
                    if (value < 1) e.target.value = 1;
                    if (value > 999) e.target.value = 999;
                });
            }
            
            // Location "Outro. Qual?" option
            const locationOtherRadio = document.getElementById('location_other');
            const locationOtherInput = document.getElementById('locationOtherInput');
            const locationRadios = document.querySelectorAll('input[name="contact_location"]');
            
            if (locationOtherRadio && locationOtherInput) {
                locationRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'other') {
                            locationOtherInput.style.display = 'block';
                            document.getElementById('locationOtherText').focus();
                        } else {
                            locationOtherInput.style.display = 'none';
                            document.getElementById('locationOtherText').value = '';
                        }
                    });
                });
            }
        }

        function showContactTimePicker() {
            console.log('Opening contact time picker...');
            
            // Set up the time picker for contact time
            window.currentTimeTarget = 'contactTime';
            
            // Get current contact time value if any
            const currentTime = document.getElementById('contactTime').value;
            console.log('Current contact time value:', currentTime);
            
            if (currentTime) {
                const [hours, minutes] = currentTime.split(':');
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();
                console.log('Set time picker to existing contact time:', currentTime);
            } else {
                // Default to current time
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0'); // Round to nearest 5 minutes
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();
                console.log('Set contact time picker to current time:', `${hours}:${minutes}`);
            }
            
            // Show the time picker modal (no need to hide other modals for contact time)
            const timePickerModal = new bootstrap.Modal(document.getElementById('timePickerModal'));
            timePickerModal.show();
            console.log('Contact time picker modal shown');
        }

        window.cancelContactDetails = function cancelContactDetails() {
            console.log('Contact details cancelled - removing marker');
            
            // Remove the last added marker
            if (contactMarkers.length > 0) {
                const lastContact = contactMarkers.pop();
                lastContact.marker.setMap(null);
                updateContactCounter();
            }

            pendingContactLocation = null;
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactDetailsModal'));
            modal.hide();
        }

        window.saveContactDetails = function saveContactDetails() {
            console.log('Saving contact details');
            
            // Validate time field first
            const timeValue = document.getElementById('contactTime').value;
            if (!timeValue) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione a hora do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }
            
            // Get selected radio button values
            const selectedCircumstance = document.querySelector('input[name="contact_circumstances"]:checked');
            const selectedLocation = document.querySelector('input[name="contact_location"]:checked');

            // Validate that both are selected
            if (!selectedCircumstance) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione uma circunstância do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            if (!selectedLocation) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione um local do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            // Validate "Outro. Qual?" and "Grupos de Rolas" fields if selected
            let circumstanceValue = selectedCircumstance.value;
            let locationValue = selectedLocation.value;
            
            if (selectedCircumstance.value === 'other') {
                const otherText = document.getElementById('circumstanceOtherText').value.trim();
                if (!otherText) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor, especifique a circunstância do contacto.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                circumstanceValue = otherText;
            } else if (selectedCircumstance.value === 'groups') {
                const groupsNumber = document.getElementById('circumstanceGroupsNumber').value.trim();
                if (!groupsNumber || parseInt(groupsNumber) < 1) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor, especifique o número de indivíduos (mínimo 1).',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                circumstanceValue = `Grupos de Rolas (${groupsNumber} indivíduos)`;
            }
            
            if (selectedLocation.value === 'other') {
                const otherText = document.getElementById('locationOtherText').value.trim();
                if (!otherText) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor, especifique o local do contacto.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                locationValue = otherText;
            }

            // Collect form data
            const formData = {
                time: timeValue,
                location: pendingContactLocation,
                circumstance: circumstanceValue,
                locationDetail: locationValue
            };

            // Store details in the last contact marker
            if (contactMarkers.length > 0) {
                contactMarkers[contactMarkers.length - 1].details = formData;
            }

            console.log('Contact saved:', formData);

            // Reset form
            document.getElementById('contactDetailsForm').reset();
            pendingContactLocation = null;

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactDetailsModal'));
            modal.hide();

            // Show success message
            Swal.fire({
                title: 'Contacto Adicionado!',
                text: 'Contacto adicionado com sucesso!',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#0a7ea4',
                timer: 2000
            });
        }

        function updateContactCounter() {
            contactCount = contactMarkers.length;
            const countElement = document.getElementById('contactCount');
            if (countElement) {
                countElement.textContent = contactCount;
            }

            // Enable/disable buttons
            const saveBtn = document.getElementById('saveContactsBtn');
            const clearBtn = document.getElementById('clearContactsBtn');
            const undoBtn = document.getElementById('undoContactBtn');
            
            if (contactCount > 0) {
                if (saveBtn) saveBtn.disabled = false;
                if (clearBtn) clearBtn.disabled = false;
                if (undoBtn) undoBtn.disabled = false;
            } else {
                if (saveBtn) saveBtn.disabled = true;
                if (clearBtn) clearBtn.disabled = true;
                if (undoBtn) undoBtn.disabled = true;
            }
        }

        window.cancelContactAddition = function cancelContactAddition() {
            if (contactMarkers.length > 0) {
                Swal.fire({
                    title: 'Cancelar Adição de Contactos?',
                    text: 'Tem contactos não guardados. Tem a certeza que deseja cancelar?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sim, Cancelar',
                    cancelButtonText: 'Continuar a Adicionar',
                    confirmButtonColor: '#dc2626',
                    cancelButtonColor: '#0a7ea4'
                }).then((result) => {
                    if (result.isConfirmed) {
                        enableSidebar();
                        window.location.href = 'index.php';
                    }
                });
            } else {
                enableSidebar();
                window.location.href = 'index.php';
            }
        }

        window.clearAllContacts = function clearAllContacts() {
            if (contactMarkers.length === 0) return;
            
            Swal.fire({
                title: 'Limpar Todos os Contactos?',
                text: 'Tem a certeza que deseja remover todos os contactos?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sim, Limpar',
                cancelButtonText: 'Cancelar',
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280'
            }).then((result) => {
                if (result.isConfirmed) {
                    contactMarkers.forEach(contact => {
                        contact.marker.setMap(null);
                    });
                    contactMarkers = [];
                    updateContactCounter();
                }
            });
        }

        window.undoLastContact = function undoLastContact() {
            if (contactMarkers.length > 0) {
                const lastContact = contactMarkers.pop();
                lastContact.marker.setMap(null);
                updateContactCounter();
                console.log('Removed last contact, remaining:', contactMarkers.length);
            }
        }

        function saveAllContacts() {
            if (contactMarkers.length === 0) {
                Swal.fire({
                    title: 'Sem Contactos',
                    text: 'Não há contactos para guardar.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            console.log('Saving all contacts:', contactMarkers);
            
            // Get the save button and show loading state
            const saveBtn = document.getElementById('saveContactsBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>A guardar...';
            
            // Check if we have a saved trajectory ID
            console.log('Checking savedTrajectoryId:', window.savedTrajectoryId);
            console.log('savedTrajectoryId type:', typeof window.savedTrajectoryId);
            console.log('savedTrajectoryId empty?', !window.savedTrajectoryId);
            
            if (!window.savedTrajectoryId) {
                console.error('No trajectory ID found for contact saving');
                
                // Reset button state
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                
                Swal.fire({
                    title: 'Erro',
                    text: 'ID do trajeto não encontrado. Não é possível guardar os contactos.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }
            
            // Prepare contact data for server
            const contactsData = contactMarkers.map(contact => ({
                lat: contact.location.lat,
                lng: contact.location.lng,
                time: contact.details?.time || '',
                circumstance: contact.details?.circumstance || '',
                location: contact.details?.locationDetail || ''
            }));
            
            // Send contact data to server
            fetch('save_contacts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    trajectoryId: window.savedTrajectoryId,
                    contacts: contactsData
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Contacts save response:', data);
                
                if (data.success) {
                    // Reset button state
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = originalText;
                    
                    showContactsSuccessModal(data.contactCount);
                } else {
                    // Reset button state on error
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = originalText;
                    
                    throw new Error(data.message || 'Failed to save contacts');
                }
            })
            .catch(error => {
                console.error('Error saving contacts:', error);
                
                // Reset button state on error
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                
                Swal.fire({
                    title: 'Erro ao Guardar Contactos',
                    text: 'Ocorreu um erro ao guardar os contactos: ' + error.message,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
            });
        }

        function showContactsSuccessModal(contactCount) {
            // Remove any existing success modal
            const existingModal = document.getElementById('contactsSuccessModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create the modern success modal
            const modal = document.createElement('div');
            modal.id = 'contactsSuccessModal';
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                        <div class="modal-header" style="border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; padding: 1rem 1.5rem;">
                            <h5 class="modal-title" style="color: white; font-weight: 600; margin-bottom: 0; font-size: 1.1rem; display: flex; align-items: center;">
                                <i class="fas fa-check-circle me-2" style="font-size: 1rem;"></i>
                                Contactos Guardados
                            </h5>
                        </div>
                        <div class="modal-body text-center" style="padding: 2rem;">
                            <div class="mb-3">
                                <div class="icon-circle mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-check" style="font-size: 1.5rem; color: white;"></i>
                                </div>
                            </div>
                            <h6 class="mb-3" style="color: #1f2937; font-weight: 600;">${contactCount} contacto(s) guardado(s) com sucesso!</h6>
                            <div style="background: rgba(10, 126, 164, 0.1); border: 1px solid rgba(10, 126, 164, 0.2); border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem;">
                                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                                    <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                        <i class="fas fa-dove" style="color: white; font-size: 0.9rem;"></i>
                                    </div>
                                    <div>
                                        <h6 style="color: #0a7ea4; font-weight: 600; margin-bottom: 0.25rem; font-size: 1rem;">Dados Sincronizados</h6>
                                        <p style="color: #374151; margin-bottom: 0; font-size: 0.9rem;">Os contactos foram guardados na base de dados</p>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; justify-content: space-around; gap: 1rem; font-size: 0.9rem; color: #0a7ea4;">
                                    <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: rgba(10, 126, 164, 0.1); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-map-marker-alt" style="color: #0a7ea4; font-size: 0.9rem;"></i>
                                        </div>
                                        <span style="font-weight: 600;">Localização</span>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: rgba(10, 126, 164, 0.1); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-clock" style="color: #0a7ea4; font-size: 0.9rem;"></i>
                                        </div>
                                        <span style="font-weight: 600;">Hora</span>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: rgba(10, 126, 164, 0.1); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-info-circle" style="color: #0a7ea4; font-size: 0.9rem;"></i>
                                        </div>
                                        <span style="font-weight: 600;">Detalhes</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: #f9fafb; display: flex; justify-content: center;">
                            <button type="button" class="btn" onclick="closeSuccessModalAndRedirect()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.75rem 2rem; font-size: 1rem; font-weight: 600; border-radius: 8px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                                <i class="fas fa-arrow-right"></i>
                                Continuar
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            const bsModal = new bootstrap.Modal(modal, {
                backdrop: 'static',
                keyboard: false
            });
            bsModal.show();
        }

        window.closeSuccessModalAndRedirect = function closeSuccessModalAndRedirect() {
            const modalElement = document.getElementById('contactsSuccessModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
                
                setTimeout(() => {
                    if (modalElement && modalElement.parentNode) {
                        modalElement.parentNode.removeChild(modalElement);
                    }
                    
                    // Show trajectory details modal directly (times are required)
                    showTrajectoryDetailsModal();
                }, 300);
            }
        }

        // Sidebar control functions
        function disableSidebar() {

            
            // Get all sidebar navigation links
            const sidebarLinks = document.querySelectorAll('.sidebar a[href], .sidebar button');
            
            sidebarLinks.forEach(link => {
                // Store original href/onclick for restoration
                if (link.href) {
                    link.dataset.originalHref = link.href;
                    link.removeAttribute('href');
                }
                if (link.onclick) {
                    link.dataset.originalOnclick = link.onclick.toString();
                    link.onclick = null;
                }
                
                // Add disabled styling and prevent clicks
                link.classList.add('sidebar-disabled');
                link.style.pointerEvents = 'none';
                link.style.opacity = '0.5';
                link.style.cursor = 'not-allowed';
                
                // Add click prevention
                link.addEventListener('click', preventSidebarClick, true);
            });
            
            // Warning overlay removed - not needed on trajectory creation page
        }

        function enableSidebar() {

            
            // Get all disabled sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar .sidebar-disabled');
            
            sidebarLinks.forEach(link => {
                // Restore original href/onclick
                if (link.dataset.originalHref) {
                    link.href = link.dataset.originalHref;
                    delete link.dataset.originalHref;
                }
                if (link.dataset.originalOnclick) {
                    link.onclick = new Function(link.dataset.originalOnclick);
                    delete link.dataset.originalOnclick;
                }
                
                // Remove disabled styling
                link.classList.remove('sidebar-disabled');
                link.style.pointerEvents = '';
                link.style.opacity = '';
                link.style.cursor = '';
                
                // Remove click prevention
                link.removeEventListener('click', preventSidebarClick, true);
            });
            
            // Warning overlay removed - not needed on trajectory creation page
        }

        function preventSidebarClick(event) {
            event.preventDefault();
            event.stopPropagation();
            
            // Show warning message
            Swal.fire({
                title: 'Navegação Bloqueada',
                text: 'Termine de criar o trajeto antes de navegar para outra página. Use "Cancelar" se pretende sair sem guardar.',
                icon: 'warning',
                confirmButtonText: 'Entendi',
                confirmButtonColor: '#0a7ea4',
                timer: 3000,
                timerProgressBar: true
            });
            
            return false;
        }

        function addSidebarWarning() {
            // Remove existing warning if any
            removeSidebarWarning();
            
            // Create warning overlay
            const warningOverlay = document.createElement('div');
            warningOverlay.id = 'sidebarWarningOverlay';
            // Get sidebar width dynamically
            const sidebar = document.querySelector('.sidebar');
            const sidebarWidth = sidebar ? sidebar.offsetWidth : 180;
            
            warningOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: ${sidebarWidth}px;
                background: linear-gradient(135deg, rgba(220, 38, 38, 0.95) 0%, rgba(239, 68, 68, 0.95) 100%);
                color: white;
                padding: 0.5rem;
                text-align: center;
                font-size: 0.75rem;
                font-weight: 600;
                z-index: 1001;
                border-bottom: 2px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(2px);
                transition: all 0.3s ease;
                transform: translateY(-100%);
                opacity: 0;
            `;
            warningOverlay.innerHTML = `
                <i class="fas fa-lock me-1"></i>
                Navegação bloqueada durante criação
            `;
            
            // Add to body instead of sidebar to avoid layout issues
            document.body.appendChild(warningOverlay);
            
            // Add subtle animation
            setTimeout(() => {
                warningOverlay.style.transform = 'translateY(0)';
                warningOverlay.style.opacity = '1';
            }, 100);
        }

        function removeSidebarWarning() {
            const warningOverlay = document.getElementById('sidebarWarningOverlay');
            if (warningOverlay) {
                warningOverlay.remove();
            }
            
            // Reset sidebar positioning if it was changed
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.style.position = '';
            }
        }

        // Trajectory Details Modal Functions
        let mapPhotosFiles = [];
        let otherPhotosFiles = [];
        let currentDetailTimeField = null;



        window.openMapPhotosModal = function openMapPhotosModal() {

            createPhotoUploadModal('map', 'Fotos dos Mapas', 'Selecione as fotos dos mapas criados durante o trajeto', '#0a7ea4');
        }

        window.openOtherPhotosModal = function openOtherPhotosModal() {

            createPhotoUploadModal('other', 'Outras Fotos', 'Selecione outras fotos relativas ao percurso', '#0a7ea4');
        }

        function createPhotoUploadModal(type, title, description, color) {
            // Remove any existing photo modal
            const existingModal = document.getElementById('photoUploadModal');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'photoUploadModal';
            modal.className = 'modal fade';
            modal.style.zIndex = '1070'; // Higher than trajectory details modal (1060)
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                        <div class="modal-header" style="border: none; background: linear-gradient(135deg, ${color} 0%, ${color}dd 100%); color: white; padding: 1.5rem 2rem; display: flex; justify-content: space-between; align-items: center;">
                            <div class="d-flex align-items-center">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                    <i class="fas fa-${type === 'map' ? 'map' : 'camera'}" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <h5 style="color: white; font-weight: 600; margin-bottom: 0; font-size: 1.25rem;">${title}</h5>
                            </div>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" style="padding: 2rem; background: white;">
                            <p style="color: #6b7280; margin-bottom: 1.5rem; text-align: center;">${description}</p>
                            
                            <div class="dropzone-container" id="photoDropzone" style="border: 2px dashed ${color}; border-radius: 12px; padding: 3rem 2rem; text-align: center; background: linear-gradient(135deg, ${color}08 0%, ${color}04 100%); transition: all 0.3s ease; cursor: pointer;">
                                <div class="dropzone-content">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: ${color}; margin-bottom: 1rem;"></i>
                                    <h6 style="color: ${color}; font-weight: 600; margin-bottom: 0.5rem; font-size: 1.1rem;">Arraste as fotos aqui</h6>
                                    <p style="color: #6b7280; font-size: 0.9rem; margin-bottom: 1rem;">ou clique para selecionar</p>
                                    <p style="color: #9ca3af; font-size: 0.8rem; margin-bottom: 0;">Aceita: JPG, PNG, GIF • Máx. 5MB cada • Até 6 fotos</p>
                                    <input type="file" id="photoInput" multiple accept="image/*" style="display: none;" onchange="handlePhotoSelect(this, '${type}')">
                                </div>
                            </div>
                            
                            <div id="photoPreviewContainer" class="photos-preview mt-4" style="min-height: 60px;"></div>
                        </div>
                        <div class="modal-footer" style="border: none; padding: 1.5rem 2rem; background: #f8fafc; display: flex; gap: 1rem; justify-content: center;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="padding: 0.75rem 1.5rem;">
                                Cancelar
                            </button>
                            <button type="button" class="btn" onclick="confirmPhotoSelection('${type}')" style="background: ${color}; border: none; color: white; padding: 0.75rem 1.5rem; font-weight: 500;">
                                <i class="fas fa-check me-1"></i>Confirmar
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Setup dropzone functionality
            const dropzone = modal.querySelector('#photoDropzone');
            const input = modal.querySelector('#photoInput');
            
            dropzone.onclick = () => input.click();
            
            dropzone.ondragover = function(e) {
                e.preventDefault();
                this.style.backgroundColor = `${color}15`;
                this.style.borderColor = `${color}cc`;
                this.style.transform = 'scale(1.02)';
            };
            
            dropzone.ondragleave = function(e) {
                e.preventDefault();
                this.style.backgroundColor = `${color}08`;
                this.style.borderColor = color;
                this.style.transform = 'scale(1)';
            };
            
            dropzone.ondrop = function(e) {
                e.preventDefault();
                this.style.backgroundColor = `${color}08`;
                this.style.borderColor = color;
                this.style.transform = 'scale(1)';
                
                const files = e.dataTransfer.files;
                input.files = files;
                handlePhotoSelect(input, type);
            };
            
            // Temporarily hide the trajectory details modal to avoid backdrop conflicts
            const detailsModal = bootstrap.Modal.getInstance(document.getElementById('trajectoryDetailsModal'));
            if (detailsModal) {
                detailsModal.hide();

            }
            
            // Show photo modal after a brief delay
            setTimeout(() => {
                const bsModal = new bootstrap.Modal(modal, {
                    backdrop: 'static',
                    keyboard: false
                });
                
                // Handle modal close (cancel button)
                modal.addEventListener('hidden.bs.modal', function() {
                    // Restore trajectory details modal when photo modal is closed
                    setTimeout(() => {
                        const detailsModal = new bootstrap.Modal(document.getElementById('trajectoryDetailsModal'));
                        detailsModal.show();
        
                    }, 300);
                    
                    // Clean up the modal element
                    modal.remove();
                }, { once: true });
                
                bsModal.show();

            }, 300);
        }

        function setupDropzones() {
            // Setup map photos dropzone
            const mapDropzone = document.getElementById('mapPhotosDropzone');
            const mapInput = document.getElementById('mapPhotosInput');
            
            if (mapDropzone && mapInput) {
                // Click to browse files
                mapDropzone.onclick = () => mapInput.click();
                
                // Drag and drop functionality
                mapDropzone.ondragover = function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'rgba(10, 126, 164, 0.1)';
                    this.style.borderColor = '#0891b2';
                    this.style.transform = 'scale(1.02)';
                    this.classList.add('dragover');
                };
                
                mapDropzone.ondragleave = function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'rgba(10, 126, 164, 0.05)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                    this.classList.remove('dragover');
                };
                
                mapDropzone.ondrop = function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'rgba(10, 126, 164, 0.05)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    mapInput.files = files;
                    handleMapPhotosSelect(mapInput);
                };
            }

            // Setup other photos dropzone
            const otherDropzone = document.getElementById('otherPhotosDropzone');
            const otherInput = document.getElementById('otherPhotosInput');
            
            if (otherDropzone && otherInput) {
                // Click to browse files
                otherDropzone.onclick = () => otherInput.click();
                
                // Drag and drop functionality
                otherDropzone.ondragover = function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'rgba(10, 126, 164, 0.1)';
                    this.style.borderColor = '#0891b2';
                    this.style.transform = 'scale(1.02)';
                    this.classList.add('dragover');
                };
                
                otherDropzone.ondragleave = function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'rgba(10, 126, 164, 0.05)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                    this.classList.remove('dragover');
                };
                
                otherDropzone.ondrop = function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'rgba(10, 126, 164, 0.05)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    otherInput.files = files;
                    handleOtherPhotosSelect(otherInput);
                };
            }
        }

        let tempPhotoFiles = [];

        // Direct photo selection handlers for the dropzones
        function handleMapPhotosSelect(input) {
            const files = Array.from(input.files);
            const validFiles = [];
            const maxFiles = 6;

            // Check if total files would exceed limit
            if (mapPhotosFiles.length + files.length > maxFiles) {
                Swal.fire({
                    title: 'Limite de fotos excedido',
                    text: `Pode selecionar no máximo ${maxFiles} fotos para mapas. Atualmente tem ${mapPhotosFiles.length} foto${mapPhotosFiles.length !== 1 ? 's' : ''} selecionada${mapPhotosFiles.length !== 1 ? 's' : ''}.`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            files.forEach(file => {
                if (validFiles.length >= maxFiles) {
                    return; // Stop adding more files
                }

                if (file.size > 5 * 1024 * 1024) { // 5MB limit
                    Swal.fire({
                        title: 'Arquivo muito grande',
                        text: `O arquivo "${file.name}" excede o limite de 5MB.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Tipo de arquivo inválido',
                        text: `O arquivo "${file.name}" não é uma imagem válida.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }

                validFiles.push(file);
            });

            // Add new files to existing ones (up to limit)
            const remainingSlots = maxFiles - mapPhotosFiles.length;
            const filesToAdd = validFiles.slice(0, remainingSlots);
            mapPhotosFiles = [...mapPhotosFiles, ...filesToAdd];
            
            updateMapPhotosPreview();
        }

        function handleOtherPhotosSelect(input) {
            const files = Array.from(input.files);
            const validFiles = [];
            const maxFiles = 6;

            // Check if total files would exceed limit
            if (otherPhotosFiles.length + files.length > maxFiles) {
                Swal.fire({
                    title: 'Limite de fotos excedido',
                    text: `Pode selecionar no máximo ${maxFiles} outras fotos. Atualmente tem ${otherPhotosFiles.length} foto${otherPhotosFiles.length !== 1 ? 's' : ''} selecionada${otherPhotosFiles.length !== 1 ? 's' : ''}.`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            files.forEach(file => {
                if (validFiles.length >= maxFiles) {
                    return; // Stop adding more files
                }

                if (file.size > 5 * 1024 * 1024) { // 5MB limit
                    Swal.fire({
                        title: 'Arquivo muito grande',
                        text: `O arquivo "${file.name}" excede o limite de 5MB.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Tipo de arquivo inválido',
                        text: `O arquivo "${file.name}" não é uma imagem válida.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }

                validFiles.push(file);
            });

            // Add new files to existing ones (up to limit)
            const remainingSlots = maxFiles - otherPhotosFiles.length;
            const filesToAdd = validFiles.slice(0, remainingSlots);
            otherPhotosFiles = [...otherPhotosFiles, ...filesToAdd];
            
            updateOtherPhotosPreview();
        }

        function handlePhotoSelect(input, type) {
            const files = Array.from(input.files);
            const validFiles = [];
            const maxFiles = 6;

            // Check if total files would exceed limit
            if (tempPhotoFiles.length + files.length > maxFiles) {
                Swal.fire({
                    title: 'Limite de fotos excedido',
                    text: `Pode selecionar no máximo ${maxFiles} fotos. Atualmente tem ${tempPhotoFiles.length} foto${tempPhotoFiles.length !== 1 ? 's' : ''} selecionada${tempPhotoFiles.length !== 1 ? 's' : ''}.`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            files.forEach(file => {
                if (validFiles.length >= maxFiles) {
                    return; // Stop adding more files
                }

                if (file.size > 5 * 1024 * 1024) { // 5MB limit
                    Swal.fire({
                        title: 'Arquivo muito grande',
                        text: `O arquivo "${file.name}" excede o limite de 5MB.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Tipo de arquivo inválido',
                        text: `O arquivo "${file.name}" não é uma imagem válida.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }

                validFiles.push(file);
            });

            // Add new files to existing ones (up to limit)
            const remainingSlots = maxFiles - tempPhotoFiles.length;
            const filesToAdd = validFiles.slice(0, remainingSlots);
            tempPhotoFiles = [...tempPhotoFiles, ...filesToAdd];
            
            updateModalPhotoPreview();
        }

        function updateModalPhotoPreview() {
            const previewContainer = document.getElementById('photoPreviewContainer');
            if (!previewContainer) return;
            
            previewContainer.innerHTML = '';

            if (tempPhotoFiles.length === 0) {
                return;
            }

            tempPhotoFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const photoDiv = document.createElement('div');
                    photoDiv.className = 'photo-preview-item';
                    photoDiv.style.cssText = `
                        display: inline-block;
                        position: relative;
                        margin: 0.5rem;
                        border-radius: 12px;
                        overflow: hidden;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                        background: white;
                        border: 2px solid #f3f4f6;
                        transition: all 0.3s ease;
                    `;
                    
                    photoDiv.innerHTML = `
                        <img src="${e.target.result}" style="width: 140px; height: 140px; object-fit: cover; display: block;">
                        <div style="position: absolute; top: 8px; right: 8px;">
                            <button type="button" class="btn btn-sm" onclick="removeModalPhoto(${index})" style="width: 28px; height: 28px; padding: 0; border-radius: 50%; display: flex; align-items: center; justify-content: center; background: rgba(220, 38, 38, 0.9); border: none; color: white; backdrop-filter: blur(4px);">
                                <i class="fas fa-times" style="font-size: 0.8rem;"></i>
                            </button>
                        </div>
                        <div style="padding: 0.75rem; background: white;">
                            <p style="margin: 0; font-size: 0.8rem; color: #6b7280; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; font-weight: 500;">${file.name}</p>
                            <p style="margin: 0; font-size: 0.7rem; color: #9ca3af; margin-top: 0.25rem;">${(file.size / 1024 / 1024).toFixed(1)} MB</p>
                        </div>
                    `;
                    previewContainer.appendChild(photoDiv);
                    
                    // Add hover effect
                    photoDiv.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.2)';
                    });
                    
                    photoDiv.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                    });
                };
                reader.readAsDataURL(file);
            });
        }

        window.removeModalPhoto = function removeModalPhoto(index) {
            tempPhotoFiles.splice(index, 1);
            updateModalPhotoPreview();
        }

        window.confirmPhotoSelection = function confirmPhotoSelection(type) {
            if (type === 'map') {
                mapPhotosFiles = [...tempPhotoFiles];
                updateMapPhotosPreview();
            } else {
                otherPhotosFiles = [...tempPhotoFiles];
                updateOtherPhotosPreview();
            }
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('photoUploadModal'));
            if (modal) {
                modal.hide();
                
                // Restore trajectory details modal after photo modal is closed
                setTimeout(() => {
                    const detailsModal = new bootstrap.Modal(document.getElementById('trajectoryDetailsModal'));
                    detailsModal.show();
                    
                }, 300);
            }
            
            // Clear temp files
            tempPhotoFiles = [];
        }

        function updateMapPhotosPreview() {
            const container = document.getElementById('mapPhotosPreview');
            container.innerHTML = '';
            
            if (mapPhotosFiles.length === 0) {
                return;
            }

            mapPhotosFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const photoDiv = document.createElement('div');
                    photoDiv.className = 'photo-preview-mini';
                    photoDiv.style.cssText = `
                        display: inline-block;
                        position: relative;
                        margin: 0.25rem;
                        border-radius: 8px;
                        overflow: hidden;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        background: white;
                        border: 2px solid #0a7ea4;
                    `;
                    
                    photoDiv.innerHTML = `
                        <img src="${e.target.result}" alt="${file.name}" style="width: 60px; height: 60px; object-fit: cover; display: block;">
                        <div style="position: absolute; top: 2px; right: 2px;">
                            <button type="button" class="btn btn-sm" onclick="removeMainPhoto('map', ${index})" style="width: 18px; height: 18px; padding: 0; border-radius: 50%; display: flex; align-items: center; justify-content: center; background: rgba(220, 38, 38, 0.9); border: none; color: white; font-size: 0.65rem;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    
                    container.appendChild(photoDiv);
                };
                reader.readAsDataURL(file);
            });
            
            // Update dropzone text
            const dropzone = container.parentElement.querySelector('.dropzone-container');
            if (dropzone && mapPhotosFiles.length > 0) {
                const dropzoneContent = dropzone.querySelector('.dropzone-content');
                if (dropzoneContent) {
                    dropzoneContent.innerHTML = `
                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #16a34a; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.25rem; color: #16a34a;">${mapPhotosFiles.length} foto${mapPhotosFiles.length > 1 ? 's' : ''} selecionada${mapPhotosFiles.length > 1 ? 's' : ''}</div>
                        <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">Clique ou arraste para adicionar mais</div>
                        <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                    `;
                    dropzone.style.background = 'linear-gradient(135deg, rgba(22, 163, 74, 0.05) 0%, rgba(34, 197, 94, 0.03) 100%)';
                    dropzone.style.borderColor = '#16a34a';
                }
            }
        }

        function updateOtherPhotosPreview() {
            const container = document.getElementById('otherPhotosPreview');
            container.innerHTML = '';
            
            if (otherPhotosFiles.length === 0) {
                return;
            }

            otherPhotosFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const photoDiv = document.createElement('div');
                    photoDiv.className = 'photo-preview-mini';
                    photoDiv.style.cssText = `
                        display: inline-block;
                        position: relative;
                        margin: 0.25rem;
                        border-radius: 8px;
                        overflow: hidden;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        background: white;
                        border: 2px solid #0a7ea4;
                    `;
                    
                    photoDiv.innerHTML = `
                        <img src="${e.target.result}" alt="${file.name}" style="width: 60px; height: 60px; object-fit: cover; display: block;">
                        <div style="position: absolute; top: 2px; right: 2px;">
                            <button type="button" class="btn btn-sm" onclick="removeMainPhoto('other', ${index})" style="width: 18px; height: 18px; padding: 0; border-radius: 50%; display: flex; align-items: center; justify-content: center; background: rgba(220, 38, 38, 0.9); border: none; color: white; font-size: 0.65rem;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    
                    container.appendChild(photoDiv);
                };
                reader.readAsDataURL(file);
            });
            
            // Update dropzone text
            const dropzone = container.parentElement.querySelector('.dropzone-container');
            if (dropzone && otherPhotosFiles.length > 0) {
                const dropzoneContent = dropzone.querySelector('.dropzone-content');
                if (dropzoneContent) {
                    dropzoneContent.innerHTML = `
                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #16a34a; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.25rem; color: #16a34a;">${otherPhotosFiles.length} foto${otherPhotosFiles.length > 1 ? 's' : ''} selecionada${otherPhotosFiles.length > 1 ? 's' : ''}</div>
                        <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">Clique ou arraste para adicionar mais</div>
                        <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                    `;
                    dropzone.style.background = 'linear-gradient(135deg, rgba(22, 163, 74, 0.05) 0%, rgba(34, 197, 94, 0.03) 100%)';
                    dropzone.style.borderColor = '#16a34a';
                }
            }
        }

        window.removeMainPhoto = function removeMainPhoto(type, index) {
            if (type === 'map') {
                mapPhotosFiles.splice(index, 1);
                updateMapPhotosPreview();
            } else {
                otherPhotosFiles.splice(index, 1);
                updateOtherPhotosPreview();
            }
        }

        // Make sure this function is globally accessible
        window.showDetailTimePicker = function(fieldId) {
            // Set the target for detail time fields (using the same system as trajectory setup)
            window.currentTimeTarget = fieldId;
            
            // Get current time value if any
            const targetField = document.getElementById(fieldId);
            if (!targetField) {
                return;
            }
            
            const currentTime = targetField.value;
            
            // Check if time picker modal exists
            const timePickerModal = document.getElementById('timePickerModal');
            if (!timePickerModal) {
                return;
            }
            
            // Check if hour and minute selects exist
            const hourSelect = document.getElementById('hourSelect');
            const minuteSelect = document.getElementById('minuteSelect');
            if (!hourSelect || !minuteSelect) {
                return;
            }
            
            if (currentTime && currentTime !== '--:--' && currentTime.includes(':')) {
                const [hours, minutes] = currentTime.split(':');
                hourSelect.value = hours;
                minuteSelect.value = minutes;
                updateTimeDisplay();
            } else {
                // Default to current time
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0'); // Round to nearest 5 minutes
                hourSelect.value = hours;
                minuteSelect.value = minutes;
                updateTimeDisplay();
            }
            
            // Temporarily hide the trajectory details modal to avoid stacking issues
            const detailsModalElement = document.getElementById('trajectoryDetailsModal');
            const detailsModal = bootstrap.Modal.getInstance(detailsModalElement);
            if (detailsModal) {
                detailsModal.hide();
            }
            
            // Show the time picker modal after a brief delay
            setTimeout(() => {
                try {
                    // Check if there's already a modal instance
                    let timeModal = bootstrap.Modal.getInstance(timePickerModal);
                    if (timeModal) {
                        timeModal.dispose();
                    }
                    
                    // Create new modal instance
                    timeModal = new bootstrap.Modal(timePickerModal, {
                        backdrop: 'static',
                        keyboard: false
                    });
                    
                    // Force show the modal
                    timeModal.show();
                    
                    // Double-check that it's actually showing
                    setTimeout(() => {
                        const isShowing = timePickerModal.classList.contains('show');
                        
                        if (!isShowing) {
                            // Manual approach - force show
                            timePickerModal.style.display = 'block';
                            timePickerModal.style.visibility = 'visible';
                            timePickerModal.style.opacity = '1';
                            timePickerModal.style.zIndex = '9999';
                            timePickerModal.classList.add('show');
                            document.body.classList.add('modal-open');
                            
                            // Create a backdrop manually
                            const backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            backdrop.style.zIndex = '9998';
                            backdrop.id = 'manual-time-picker-backdrop';
                            document.body.appendChild(backdrop);
                        }
                    }, 100);
                } catch (error) {
                    console.error('Error showing time picker modal:', error);
                    console.error('Error stack:', error.stack);
                }
            }, 300);
        };

        window.saveTrajectoryDetails = function saveTrajectoryDetails() {
            
            // Validate required times
            const middleTime = document.getElementById('middleTime').value;
            const endTime = document.getElementById('endTime').value;
            
            if (!middleTime) {
                Swal.fire({
                    title: 'Horário Obrigatório',
                    text: 'Por favor, indique a hora a que chegou a meio do trajeto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }
            
            if (!endTime) {
                Swal.fire({
                    title: 'Horário Obrigatório',
                    text: 'Por favor, indique a hora a que chegou ao fim do trajeto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }
            
            const saveBtn = document.getElementById('saveDetailsBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>A guardar...';
            
            // Collect form data
            const hasMapPhotos = mapPhotosFiles.length > 0;
            const hasOtherPhotos = otherPhotosFiles.length > 0;
            
            // Prepare data for saving
            const detailsData = {
                trajectoryId: window.savedTrajectoryId,
                middleTime: middleTime,
                endTime: endTime,
                hasMapPhotos: hasMapPhotos,
                hasOtherPhotos: hasOtherPhotos,
                mapPhotosCount: mapPhotosFiles.length,
                otherPhotosCount: otherPhotosFiles.length
            };
            
            console.log('Details data:', detailsData);
            
            // First save the basic details
            fetch('save_trajectory_details.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(detailsData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                // Get the raw response text first
                return response.text().then(text => {
                    console.log('Raw response text:', text);
                    
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response was not valid JSON:', text);
                        throw new Error('Server returned invalid JSON: ' + text.substring(0, 200));
                    }
                });
            })
            .then(data => {
                console.log('Details save response:', data);
                
                if (data.success) {
                    // If there are photos to upload, upload them
                    if (mapPhotosFiles.length > 0 || otherPhotosFiles.length > 0) {
                        uploadTrajectoryPhotos(detailsData.trajectoryId)
                            .then(() => {
                                showDetailsSuccessModal();
                            })
                            .catch(error => {
                                console.error('Error uploading photos:', error);
                                // Still show success for details, but warn about photos
                                Swal.fire({
                                    title: 'Detalhes Guardados',
                                    text: 'Os detalhes foram guardados mas houve um erro ao carregar as fotos.',
                                    icon: 'warning',
                                    confirmButtonText: 'OK',
                                    confirmButtonColor: '#0a7ea4'
                                }).then(() => {
                                    showFinalTrajectoryCompleted();
                                });
                            });
                    } else {
                        showDetailsSuccessModal();
                    }
                } else {
                    throw new Error(data.message || 'Failed to save details');
                }
            })
            .catch(error => {
                console.error('Error saving details:', error);
                
                // Reset button state
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                
                Swal.fire({
                    title: 'Erro ao Guardar',
                    text: 'Ocorreu um erro ao guardar os detalhes: ' + error.message,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
            });
        }

        async function uploadTrajectoryPhotos(trajectoryId) {
            console.log('Uploading trajectory photos for ID:', trajectoryId);
            
            const totalPhotos = mapPhotosFiles.length + otherPhotosFiles.length;
            let uploadedPhotos = 0;
            
            // Show upload progress modal
            Swal.fire({
                title: 'A carregar fotografias...',
                html: `
                    <div class="upload-progress-container">
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);" 
                                 id="uploadProgressBar">
                                0%
                            </div>
                        </div>
                        <p id="uploadStatus">A preparar carregamento...</p>
                        <small class="text-muted" id="uploadDetails">0 de ${totalPhotos} fotografias carregadas</small>
                    </div>
                `,
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            const updateProgress = () => {
                const percentage = Math.round((uploadedPhotos / totalPhotos) * 100);
                const progressBar = document.getElementById('uploadProgressBar');
                const statusText = document.getElementById('uploadStatus');
                const detailsText = document.getElementById('uploadDetails');
                
                if (progressBar) {
                    progressBar.style.width = percentage + '%';
                    progressBar.textContent = percentage + '%';
                }
                
                if (statusText) {
                    statusText.textContent = `A carregar fotografia ${uploadedPhotos + 1} de ${totalPhotos}...`;
                }
                
                if (detailsText) {
                    detailsText.textContent = `${uploadedPhotos} de ${totalPhotos} fotografias carregadas`;
                }
            };
            
            const uploadPromises = [];
            
            // Upload map photos
            mapPhotosFiles.forEach((file, index) => {
                uploadPromises.push(
                    uploadPhotoToFirebase(file, trajectoryId, 'maps', index)
                        .then(result => {
                            uploadedPhotos++;
                            updateProgress();
                            return result;
                        })
                );
            });
            
            // Upload other photos
            otherPhotosFiles.forEach((file, index) => {
                uploadPromises.push(
                    uploadPhotoToFirebase(file, trajectoryId, 'other', index)
                        .then(result => {
                            uploadedPhotos++;
                            updateProgress();
                            return result;
                        })
                );
            });
            
            return Promise.all(uploadPromises);
        }

        async function uploadPhotoToFirebase(file, trajectoryId, type, index) {
            return new Promise((resolve, reject) => {
                console.log(`Uploading ${type} photo ${index}:`, file.name, 'Size:', file.size);
                
                const formData = new FormData();
                formData.append('photo', file);
                formData.append('trajectoryId', trajectoryId);
                formData.append('type', type);
                formData.append('index', index);
                
                fetch('upload_trajectory_photo.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('Upload response status:', response.status);
                    
                    // Get the raw response text first
                    return response.text().then(text => {
                        console.log('Upload raw response:', text);
                        
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('Upload JSON parse error:', e);
                            console.error('Upload response was not valid JSON:', text);
                            throw new Error('Server returned invalid JSON: ' + text.substring(0, 200));
                        }
                    });
                })
                .then(data => {
                    console.log('Upload response data:', data);
                    
                    if (data.success) {
                        console.log(`Successfully uploaded ${type} photo ${index}:`, data.fileName);
                        resolve(data);
                    } else {
                        console.error(`Failed to upload ${type} photo ${index}:`, data.message);
                        reject(new Error(data.message || 'Upload failed'));
                    }
                })
                .catch(error => {
                    console.error(`Error uploading ${type} photo ${index}:`, error);
                    reject(error);
                });
            });
        }

        function showDetailsSuccessModal() {
            console.log('Showing details success modal');
            
            Swal.fire({
                title: 'Detalhes Guardados!',
                text: 'Os detalhes do trajeto foram guardados com sucesso.',
                icon: 'success',
                confirmButtonText: 'Continuar',
                confirmButtonColor: '#0a7ea4',
                timer: 2000,
                timerProgressBar: true
            }).then(() => {
                showFinalTrajectoryCompleted();
            });
        }
        
        // ===========================================
        // FIX: MODAL BACKDROP BLOCKING INTERACTION
        // ===========================================
        
        function fixModalBackdropIssue() {
            console.log('🔧 Fixing modal backdrop blocking issue...');
            
            // Find the problematic modals
            const locationModal = document.getElementById('locationChoiceModal');
            const searchModal = document.getElementById('locationSearchModal');
            
            if (locationModal) {
                console.log('Found locationChoiceModal:', locationModal);
                
                // Check if it's currently showing and blocking interaction
                if (locationModal.classList.contains('show') || locationModal.style.display === 'block') {
                    console.log('LocationChoiceModal is currently active and potentially blocking interaction');
                    
                    // Ensure modal is properly above other elements
                    locationModal.style.zIndex = '1055';
                    
                    console.log('✅ LocationChoiceModal z-index adjusted');
                } else {
                    console.log('LocationChoiceModal is not currently active');
                }
            } else {
                console.log('locationChoiceModal not found');
            }
            
            if (searchModal) {
                console.log('Found locationSearchModal:', searchModal);
                
                // Check if it's currently showing and blocking interaction
                if (searchModal.classList.contains('show') || searchModal.style.display === 'block') {
                    console.log('LocationSearchModal is currently active and potentially blocking interaction');
                    
                    // Ensure modal is properly above other elements
                    searchModal.style.zIndex = '1056';
                    
                    console.log('✅ LocationSearchModal z-index adjusted');
                } else {
                    console.log('LocationSearchModal is not currently active');
                }
            } else {
                console.log('locationSearchModal not found');
            }
            
            // Make sure backdrop doesn't block interaction with other elements
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach((backdrop, index) => {
                console.log(`Adjusting backdrop ${index} z-index`);
                backdrop.style.zIndex = '1054';
            });
            
            // Remove any accidentally stuck modal states
            const stuckModals = document.querySelectorAll('.modal.show[style*="display: block"]');
            stuckModals.forEach(modal => {
                if (modal.id !== 'locationChoiceModal') {
                    console.log('Found stuck modal:', modal.id);
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                    modal.setAttribute('aria-hidden', 'true');
                    modal.removeAttribute('aria-modal');
                    modal.removeAttribute('role');
                }
            });
            
            // Clean up any orphaned backdrops
            const orphanedBackdrops = document.querySelectorAll('.modal-backdrop');
            orphanedBackdrops.forEach(backdrop => {
                const associatedModal = backdrop.previousElementSibling;
                if (!associatedModal || !associatedModal.classList.contains('show')) {
                    console.log('Removing orphaned backdrop');
                    backdrop.remove();
                }
            });
            
            console.log('✅ Modal backdrop issue fix complete');
        }
        
        // Run fix when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, fixing modal backdrop issue...');
            setTimeout(fixModalBackdropIssue, 500);
        });
        
        // Also run when window loads
        window.addEventListener('load', function() {
            setTimeout(fixModalBackdropIssue, 500);
        });
        
        // Fix modal issues when they're shown
        function fixModalOnShow() {
            console.log('🔧 Fixing modal z-index on show...');
            
            // Listen for modal show events
            document.addEventListener('shown.bs.modal', function(e) {
                console.log('Modal shown:', e.target.id);
                
                if (e.target.id === 'locationChoiceModal') {
                    e.target.style.zIndex = '1055';
                    console.log('✅ LocationChoiceModal z-index set to 1055');
                } else if (e.target.id === 'locationSearchModal') {
                    e.target.style.zIndex = '1056';
                    console.log('✅ LocationSearchModal z-index set to 1056');
                }
                
                // Fix backdrop z-index
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach((backdrop, index) => {
                    backdrop.style.zIndex = '1054';
                    console.log(`✅ Backdrop ${index} z-index set to 1054`);
                });
            });
        }
        
        // Initialize modal fix on show
        fixModalOnShow();


    </script>
</body>
</html>