<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set HTML response header
header('Content-Type: text/html; charset=utf-8');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo '<h1>Unauthorized</h1><p>You must be logged in as a gestor to access this page.</p>';
    exit();
}

// SECURITY: Get current user info for validation
$user_id = $_SESSION['user']['id'] ?? '';
$user_email = $_SESSION['user']['email'] ?? '';
$user_nif = $_SESSION['user']['nif'] ?? '';

if (empty($user_id) || empty($user_email)) {
    ob_end_clean();
    http_response_code(400);
    echo '<h1>Invalid Session</h1><p>User session is invalid - missing user ID or email.</p>';
    exit();
}

// SECURITY: Get trajectory ID from URL parameter with validation
$trajectoryId = $_GET['trajectory'] ?? '';
if (empty($trajectoryId)) {
    ob_end_clean();
    http_response_code(400);
    echo '<h1>Missing Parameter</h1><p>Trajectory ID is required.</p>';
    exit();
}

// SECURITY: Validate trajectory ID format to prevent injection attacks
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $trajectoryId) || strlen($trajectoryId) > 100) {
    error_log("SECURITY VIOLATION: Invalid trajectory ID format attempted: " . $trajectoryId . " by user " . $user_email);
    ob_end_clean();
    http_response_code(400);
    echo '<h1>Invalid Parameter</h1><p>Invalid trajectory ID format.</p>';
    exit();
}

/**
 * Parse Firestore document format
 */
function parseFirestoreDocument($doc) {
    if (!isset($doc['fields'])) {
        return [];
    }
    
    $result = [];
    foreach ($doc['fields'] as $key => $field) {
        $result[$key] = convertFromFirestoreValue($field);
    }
    
    return $result;
}

/**
 * Convert Firestore field value to PHP value
 */
function convertFromFirestoreValue($field) {
    if (isset($field['stringValue'])) {
        return $field['stringValue'];
    } elseif (isset($field['integerValue'])) {
        return (int)$field['integerValue'];
    } elseif (isset($field['doubleValue'])) {
        return (float)$field['doubleValue'];
    } elseif (isset($field['booleanValue'])) {
        return $field['booleanValue'];
    } elseif (isset($field['timestampValue'])) {
        return $field['timestampValue'];
    } elseif (isset($field['nullValue'])) {
        return null;
    } elseif (isset($field['arrayValue'])) {
        $result = [];
        if (isset($field['arrayValue']['values'])) {
            foreach ($field['arrayValue']['values'] as $value) {
                $result[] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    } elseif (isset($field['mapValue'])) {
        $result = [];
        if (isset($field['mapValue']['fields'])) {
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $result[$key] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    }
    
    return null;
}

/**
 * Delete a specific contact by ID
 */
function deleteContactById($contactId, $adminToken) {
    $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts/{$contactId}";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $status === 200;
}

// Handle POST request for deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    try {
        // Use the global database instance
        global $database;
        
        // Get admin token for deletion
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception("Failed to get admin access token");
        }
        
        // Query Firestore for all contacts - get ALL documents with pagination
        $allFirestoreContacts = [];
        $nextPageToken = null;
        
        do {
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts";
            if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
            }
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status !== 200) {
                throw new Exception("Failed to fetch contacts, status: {$status}");
            }
            
            $pageResult = json_decode($response, true);
            
            if (isset($pageResult['documents'])) {
                $allFirestoreContacts = array_merge($allFirestoreContacts, $pageResult['documents']);
            }
            
            $nextPageToken = $pageResult['nextPageToken'] ?? null;
            
        } while ($nextPageToken);
        
        // Create result structure for compatibility
        $result = ['documents' => $allFirestoreContacts];
        $contactsToDelete = [];
        $totalContacts = 0;
        
        if (isset($result['documents'])) {
            $totalContacts = count($result['documents']);
            
            foreach ($result['documents'] as $doc) {
                $contactData = parseFirestoreDocument($doc);
                
                // Extract document ID from the document name
                if (isset($doc['name'])) {
                    $nameParts = explode('/', $doc['name']);
                    $contactData['id'] = end($nameParts);
                }
                
                // SAFETY CHECK: Only select contacts that match BOTH criteria EXACTLY
                $emailMatch = isset($contactData['createdByEmail']) && $contactData['createdByEmail'] === TARGET_EMAIL;
                $trajectoryMatch = isset($contactData['trajectoryId']) && $contactData['trajectoryId'] === TARGET_TRAJECTORY_ID;
                
                if ($emailMatch && $trajectoryMatch) {
                    $contactsToDelete[] = $contactData;
                }
            }
        }
        
        // Delete the contacts
        $deletedCount = 0;
        $errors = [];
        
        foreach ($contactsToDelete as $contact) {
            if (deleteContactById($contact['id'], $adminToken)) {
                $deletedCount++;
            } else {
                $errors[] = "Failed to delete contact ID: " . $contact['id'];
            }
        }
        
        $success = true;
        $message = "Successfully deleted {$deletedCount} contacts.";
        if (!empty($errors)) {
            $message .= " Errors: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $success = false;
        $message = 'Error: ' . $e->getMessage();
        $deletedCount = 0;
    }
}

// If not a POST request, show the confirmation form
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    try {
        // Use the global database instance
        global $database;
        
        // Get admin token for preview
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception("Failed to get admin access token");
        }
        
        // Query Firestore for all contacts - get ALL documents with pagination (preview only)
        $allFirestoreContacts = [];
        $nextPageToken = null;
        
        do {
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts";
            if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
            }
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status !== 200) {
                throw new Exception("Failed to fetch contacts, status: {$status}");
            }
            
            $pageResult = json_decode($response, true);
            
            if (isset($pageResult['documents'])) {
                $allFirestoreContacts = array_merge($allFirestoreContacts, $pageResult['documents']);
            }
            
            $nextPageToken = $pageResult['nextPageToken'] ?? null;
            
        } while ($nextPageToken);
        
        // Create result structure for compatibility
        $result = ['documents' => $allFirestoreContacts];
        $contactsToDelete = [];
        $totalContacts = 0;
        
        if (isset($result['documents'])) {
            $totalContacts = count($result['documents']);
            
            foreach ($result['documents'] as $doc) {
                $contactData = parseFirestoreDocument($doc);
                
                // Extract document ID from the document name
                if (isset($doc['name'])) {
                    $nameParts = explode('/', $doc['name']);
                    $contactData['id'] = end($nameParts);
                }
                
                // SAFETY CHECK: Only select contacts that match BOTH criteria EXACTLY
                $emailMatch = isset($contactData['createdByEmail']) && $contactData['createdByEmail'] === TARGET_EMAIL;
                $trajectoryMatch = isset($contactData['trajectoryId']) && $contactData['trajectoryId'] === TARGET_TRAJECTORY_ID;
                
                if ($emailMatch && $trajectoryMatch) {
                    $contactsToDelete[] = $contactData;
                }
            }
        }
        
    } catch (Exception $e) {
        $contactsToDelete = [];
        $totalContacts = 0;
        $previewError = $e->getMessage();
    }
}

ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eliminar Contactos Específicos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .danger-zone {
            border: 3px solid #dc2626;
            border-radius: 12px;
            background: #fef2f2;
            padding: 2rem;
            margin: 2rem 0;
        }
        .contact-preview {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: white;
        }
        .criteria-box {
            background: #fffbeb;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="mb-4">
            <i class="fas fa-trash-alt text-danger me-3"></i>
            Eliminar Contactos Específicos
        </h1>
        
        <?php if (isset($success)): ?>
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle me-2"></i>Eliminação Concluída</h4>
                    <p><?php echo htmlspecialchars($message); ?></p>
                    <p><strong>Contactos eliminados:</strong> <?php echo $deletedCount; ?></p>
                    <a href="list_my_contacts.php" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>Ver Lista Atualizada
                    </a>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>Erro na Eliminação</h4>
                    <p><?php echo htmlspecialchars($message); ?></p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['confirm_delete'])): ?>
            <!-- Criteria Display -->
            <div class="criteria-box">
                <h3><i class="fas fa-filter text-warning me-2"></i>Critérios de Eliminação</h3>
                <p class="mb-3">Serão eliminados APENAS os contactos que cumpram <strong>AMBOS</strong> os critérios:</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <strong>Email do Criador:</strong>
                        </div>
                        <code class="bg-light p-2 rounded d-block"><?php echo htmlspecialchars(TARGET_EMAIL); ?></code>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-route text-primary me-2"></i>
                            <strong>ID do Trajeto:</strong>
                        </div>
                        <code class="bg-light p-2 rounded d-block"><?php echo htmlspecialchars(TARGET_TRAJECTORY_ID); ?></code>
                    </div>
                </div>
            </div>
            
            <!-- Preview of contacts to be deleted -->
            <?php if (isset($contactsToDelete) && !empty($contactsToDelete)): ?>
                <div class="alert alert-info">
                    <h4><i class="fas fa-info-circle me-2"></i>Pré-visualização</h4>
                    <p>Foram encontrados <strong><?php echo count($contactsToDelete); ?></strong> contactos que correspondem aos critérios e serão eliminados:</p>
                </div>
                
                <div class="row mb-4">
                    <?php foreach ($contactsToDelete as $i => $contact): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="contact-preview">
                                <h6><i class="fas fa-dove me-1"></i>Contacto <?php echo $i + 1; ?></h6>
                                <p class="small mb-1"><strong>ID:</strong> <code><?php echo htmlspecialchars($contact['id']); ?></code></p>
                                <p class="small mb-1"><strong>Coordenadas:</strong> 
                                    <?php 
                                    if (isset($contact['coordinates']['lat']) && isset($contact['coordinates']['lng'])) {
                                        echo number_format($contact['coordinates']['lat'], 6) . ', ' . number_format($contact['coordinates']['lng'], 6);
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </p>
                                <p class="small mb-1"><strong>Hora:</strong> <?php echo htmlspecialchars($contact['time'] ?? 'N/A'); ?></p>
                                <p class="small mb-1"><strong>Circunstância:</strong> <?php echo htmlspecialchars($contact['circumstance'] ?? 'N/A'); ?></p>
                                <p class="small mb-0"><strong>Criado:</strong> 
                                    <?php 
                                    if (isset($contact['createdAt'])) {
                                        $date = new DateTime($contact['createdAt']);
                                        echo $date->format('d/m/Y H:i:s');
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Danger Zone -->
                <div class="danger-zone">
                    <h2 class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Zona de Perigo
                    </h2>
                    <p class="mb-3">
                        <strong>ATENÇÃO:</strong> Esta ação irá eliminar <strong><?php echo count($contactsToDelete); ?> contactos</strong> 
                        permanentemente da base de dados. Esta ação <strong>NÃO PODE SER DESFEITA</strong>.
                    </p>
                    <p class="mb-4">
                        Confirme que deseja eliminar APENAS os contactos mostrados acima que correspondem aos critérios:
                        <br>• Email: <code><?php echo htmlspecialchars(TARGET_EMAIL); ?></code>
                        <br>• Trajeto: <code><?php echo htmlspecialchars(TARGET_TRAJECTORY_ID); ?></code>
                    </p>
                    
                    <form method="POST" onsubmit="return confirm('Tem a certeza absoluta que deseja eliminar <?php echo count($contactsToDelete); ?> contactos? Esta ação não pode ser desfeita!');">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                            <label class="form-check-label" for="confirmCheck">
                                <strong>Confirmo que quero eliminar apenas os <?php echo count($contactsToDelete); ?> contactos mostrados acima</strong>
                            </label>
                        </div>
                        <button type="submit" name="confirm_delete" class="btn btn-danger btn-lg">
                            <i class="fas fa-trash-alt me-2"></i>
                            ELIMINAR <?php echo count($contactsToDelete); ?> CONTACTOS
                        </button>
                        <a href="list_my_contacts.php" class="btn btn-secondary btn-lg ms-3">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </a>
                    </form>
                </div>
                
            <?php elseif (isset($previewError)): ?>
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>Erro</h4>
                    <p><?php echo htmlspecialchars($previewError); ?></p>
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle me-2"></i>Nenhum Contacto Encontrado</h4>
                    <p>Não foram encontrados contactos que correspondam aos critérios especificados.</p>
                    <p>Isto significa que não há contactos para eliminar com:</p>
                    <ul>
                        <li>Email: <code><?php echo htmlspecialchars(TARGET_EMAIL); ?></code></li>
                        <li>Trajeto: <code><?php echo htmlspecialchars(TARGET_TRAJECTORY_ID); ?></code></li>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="list_my_contacts.php" class="btn btn-primary">
                <i class="fas fa-list me-2"></i>Ver Lista de Contactos
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 