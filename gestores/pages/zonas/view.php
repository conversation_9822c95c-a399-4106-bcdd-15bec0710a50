<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Get trajeto ID from URL parameters
$trajetoId = $_GET['id'] ?? '';

if (empty($trajetoId)) {
    ob_end_clean();
    header('Location: index.php');
    exit();
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ver Trajeto - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        /* Modern Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(10, 126, 164, 0.1);
            max-width: 400px;
            width: 90%;
        }
        
        .loading-animation {
            position: relative;
            display: inline-block;
            margin-bottom: 1.5rem;
        }
        
        .loading-spinner {
            position: relative;
            width: 60px;
            height: 60px;
            margin: 0 auto;
        }
        
        .spinner-ring {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 3px solid rgba(10, 126, 164, 0.1);
            border-top: 3px solid #0a7ea4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .loading-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2rem;
            color: #0a7ea4;
            animation: pulse 1.5s ease-in-out infinite;
        }
        
        .loading-text h4 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.25rem;
        }
        
        .loading-text p {
            color: #6b7280;
            margin: 0;
            font-size: 0.9rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { 
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            50% { 
                opacity: 0.7;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        /* Equal Height Row */
        .equal-height-row {
            display: flex !important;
            flex-wrap: wrap;
        }
        
        .equal-height-row > [class*="col-"] {
            display: flex;
        }

        /* Simple App-Style Cards */
        .info-card {
            background: white;
            border-radius: 8px;
            padding: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            height: 100%;
            margin-bottom: 0.5rem;
            display: flex;
            flex-direction: column;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.5rem !important;
            padding-bottom: 0.5rem !important;
            padding-top: 0rem !important;
            padding-left: 0rem !important;
            border-bottom: 1px solid #e5e7eb;
            text-align: left;
        }

        .card-header-icon {
            width: 32px;
            height: 32px;
            background: #0a7ea4;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .card-header-title {
            font-weight: 600;
            color: #374151;
            font-size: 0.95rem;
        }

        /* Info Content Styles */
        .info-content {
            display: flex;
            flex-direction: column;
            gap: 0.4rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .info-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #374151;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            width: 100%;
        }

        .info-row-icon {
            width: 20px;
            height: 20px;
            background: #f3f4f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0a7ea4;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .info-row-text {
            font-weight: 500;
            line-height: 1.3;
        }

        /* Timeline Styles */
        .timeline-container {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            border-left: 3px solid transparent;
        }

        .timeline-item.start {
            border-left-color: #22c55e;
        }

        .timeline-item.middle {
            border-left-color: #f59e0b;
        }

        .timeline-item.end {
            border-left-color: #ef4444;
        }

        .timeline-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .timeline-dot.start {
            background: #22c55e;
        }

        .timeline-dot.middle {
            background: #f59e0b;
        }

        .timeline-dot.end {
            background: #ef4444;
        }

        .timeline-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 0.5rem;
            min-height: 0;
        }

        .timeline-label {
            font-size: 0.65rem;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            line-height: 1;
            margin-bottom: 0.1rem;
        }

        .timeline-time {
            font-size: 0.8rem;
            color: #374151;
            font-weight: 600;
            line-height: 1;
        }

        /* Stats Styles */
        .stats-grid {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            width: 100%;
        }

        .stat-label {
            color: #6b7280;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        .stat-label i {
            color: #0a7ea4;
            font-size: 0.8rem;
        }

        .stat-value {
            color: #0a7ea4;
            font-weight: 700;
            font-size: 1rem;
            background: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            border: 1px solid #0a7ea4;
            min-width: 30px;
            text-align: center;
        }

        /* Timeline Dots */
        .timeline-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .timeline-dot.start {
            background: #22c55e;
        }

        .timeline-dot.middle {
            background: #f59e0b;
        }

        .timeline-dot.end {
            background: #ef4444;
        }

        .time-value {
            font-size: 0.8rem;
            color: #374151;
            font-weight: 600;
            line-height: 1;
        }

        /* Photos Gallery */
        .photos-gallery {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .photo-section {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .photo-section-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .photo-section-title i {
            color: #0a7ea4;
            font-size: 0.8rem;
        }

        .photo-thumbnails {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            padding: 2rem;
            justify-items: center;
        }

        .photo-thumbnail {
            position: relative;
            width: 250px;
            height: 180px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-thumbnail:hover {
            border-color: #0a7ea4;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .photo-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #6b7280;
            font-size: 0.9rem;
            text-align: center;
            font-weight: 500;
        }

        .photo-placeholder i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #0a7ea4;
            opacity: 0.6;
        }



        .no-photos-message {
            color: #6b7280;
            font-size: 0.85rem;
            font-style: italic;
            text-align: center;
            padding: 2rem;
            background: #f9fafb;
            border-radius: 8px;
            border: 2px dashed #e5e7eb;
        }

        /* Weather Card */
        .weather-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            padding: 1rem;
            text-align: center;
        }

        .weather-icon-large {
            font-size: 1.8rem;
        }

        .weather-text-large {
            font-weight: 600;
            color: #374151;
            font-size: 0.85rem;
            text-transform: uppercase;
        }

        /* Photos Card */
        .photos-summary {
            display: flex;
            gap: 0.75rem;
        }

        .photo-type {
            flex: 1;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 0.75rem;
            text-align: center;
        }

        .photo-count {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #0a7ea4;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .photo-label {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
        }

        /* Contacts Card */
        .contacts-summary {
            text-align: center;
        }

        .contact-count {
            font-size: 1.8rem;
            font-weight: 700;
            color: #0a7ea4;
            line-height: 1;
        }

        .contact-label {
            font-size: 0.7rem;
            color: #6b7280;
            margin-top: 0.3rem;
            text-transform: uppercase;
            letter-spacing: 0.02em;
        }

        /* Map Card Specific Styles */
        .map-card {
            margin-bottom: 1.5rem;
        }

        .map-card .card-header {
            justify-content: space-between;
        }

        .map-stats {
            margin-left: auto;
        }

        .map-stat {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        .map-stat i {
            color: #0a7ea4;
            font-size: 0.75rem;
        }

        .map-container {
            height: 400px;
            border-radius: 0 0 12px 12px;
            overflow: hidden;
            position: relative;
        }

        .google-map {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            padding: 1rem 0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .loading-container {
                padding: 1.5rem;
                margin: 1rem;
            }
            
            .loading-spinner {
                width: 60px;
                height: 60px;
            }
            
            .loading-icon {
                font-size: 1.2rem;
            }
            
            .loading-text h4 {
                font-size: 1.1rem;
            }
            
            .loading-text p {
                font-size: 0.85rem;
            }

            .info-card {
                padding: 1rem;
            }

            .card-header-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .card-header-title {
                font-size: 0.9rem;
            }

            .map-container {
                height: 300px;
            }



            .weather-container {
                padding: 0.75rem;
            }

            .weather-icon-large {
                font-size: 1.5rem;
            }

            .timeline-container {
                gap: 0.4rem;
            }

            .timeline-item {
                padding: 0.4rem;
            }
        }
    </style>
</head>
<body>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-eye"></i>
                <span id="headerTrajetoName">Ver Trajeto</span>
            </h1>
        </div>
        <div class="header-actions">
            <button type="button" class="btn btn-help" onclick="showTrajetoHelp()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div>

            <!-- Loading State -->
            <div id="loadingState" class="loading-overlay">
                <div class="loading-container">
                    <div class="loading-animation">
                        <div class="loading-spinner">
                            <div class="spinner-ring"></div>
                        </div>
                        <div class="loading-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="loading-text">
                        <h4>A carregar trajeto</h4>
                        <p>Por favor aguarde enquanto carregamos os dados...</p>
                    </div>
                </div>
            </div>

            <!-- Trajeto View Content -->
            <div id="mainContent" style="display: none;">
                


                <!-- Info Cards Row -->
                <div class="row g-3 mb-3" style="align-items: stretch;">
                    <!-- Basic Info Card -->
                    <div class="col-lg-4 col-md-6 d-flex">
                        <div class="info-card w-100">
                            <div class="card-header">
                                <div class="card-header-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="card-header-title">Informações</div>
                            </div>
                            <div class="info-content">
                                <div class="info-row">
                                    <div class="info-row-icon">
                                        <i class="fas fa-route"></i>
                                    </div>
                                    <div class="info-row-text" id="routeDistanceMain">-</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-row-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="info-row-text">
                                        <span id="createdDateMain">-</span>
                                        <span id="editedDateDisplay" style="display: none; margin-left: 8px; color: #6b7280; font-size: 0.85em;">
                                            | Editado: <span id="editedDateMain">-</span>
                                        </span>
                                    </div>
                                </div>
                                <div class="info-row">
                                    <div class="info-row-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="info-row-text" id="startingAddressMain">-</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Card -->
                    <div class="col-lg-2 col-md-3 d-flex">
                        <div class="info-card w-100">
                            <div class="card-header">
                                <div class="card-header-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="card-header-title">Timeline</div>
                            </div>
                            <div class="timeline-container">
                                <div class="timeline-item start">
                                    <div class="timeline-dot start"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-label">Início</div>
                                        <div class="timeline-time" id="startTimeCompact">--:--</div>
                                    </div>
                                </div>
                                <div class="timeline-item middle">
                                    <div class="timeline-dot middle"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-label">Meio</div>
                                        <div class="timeline-time" id="middleTimeCompact">--:--</div>
                                    </div>
                                </div>
                                <div class="timeline-item end">
                                    <div class="timeline-dot end"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-label">Fim</div>
                                        <div class="timeline-time" id="endTimeCompact">--:--</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Weather Card -->
                    <div class="col-lg-3 col-md-4 d-flex" id="weatherCardCompact" style="display: none;">
                        <div class="info-card w-100">
                            <div class="card-header">
                                <div class="card-header-icon">
                                    <i class="fas fa-cloud-sun"></i>
                                </div>
                                <div class="card-header-title">Condições</div>
                            </div>
                            <div class="weather-container">
                                <div class="weather-icon-large" id="weatherIconCompact">🌤️</div>
                                <div class="weather-text-large" id="weatherTextCompact">-</div>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Card -->
                    <div class="col-lg-3 col-md-4 d-flex">
                        <div class="info-card w-100">
                            <div class="card-header">
                                <div class="card-header-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="card-header-title">Estatísticas</div>
                            </div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-label">
                                        <i class="fas fa-map-pin"></i>
                                        Pontos
                                    </div>
                                    <div class="stat-value" id="routePointsDisplay">-</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">
                                        <i class="fas fa-eye"></i>
                                        Observadores
                                    </div>
                                    <div class="stat-value" id="observersCountDisplay">-</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">
                                        <i class="fas fa-dove"></i>
                                        Contactos
                                    </div>
                                    <div class="stat-value" id="contactsCountCompact">-</div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Map Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="info-card map-card">
                            <div class="card-header">
                                <div class="card-header-icon">
                                    <i class="fas fa-map-marked-alt"></i>
                                </div>
                                <div class="card-header-title">Mapa do Trajeto</div>
                                <div class="map-stats">
                                    <span class="map-stat">
                                        <i class="fas fa-map-pin"></i>
                                        <span id="routePoints">0</span> pontos
                                    </span>
                                </div>
                            </div>
                            <div class="map-container">
                                <div id="map" class="google-map"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Photos Gallery Row -->
                <div class="row mb-3" id="photosGalleryRow" style="display: none;">
                    <div class="col-12">
                        <div class="info-card">
                            <div class="card-header">
                                <div class="card-header-icon">
                                    <i class="fas fa-images"></i>
                                </div>
                                <div class="card-header-title">Galeria de Fotografias (<span id="totalPhotosCount">0</span>)</div>
                            </div>
                            <div class="photo-thumbnails" id="allPhotoThumbnails">
                                <!-- All photo thumbnails will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="action-buttons">
                            <button type="button" class="btn btn-secondary" onclick="goBack()">
                                <i class="fas fa-arrow-left me-2"></i>
                                Voltar à Lista
                            </button>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>

    <style>
        /* Clean up old styles - keep only what we need */
        
        /* Modern Card Styles */
        .modern-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .card-content {
            padding: 0.75rem 1rem;
            display: flex;
            flex-direction: column;
            flex: 1;
            text-align: center;
        }

        .card-title-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            flex-shrink: 0;
        }

        .card-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .flex-grow-1 {
            flex: 1;
        }

        .card-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
            line-height: 1.2;
        }

        .card-subtitle {
            font-size: 0.75rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.2;
        }

        /* Info Grid Layout */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 0.75rem;
            flex: 1;
            align-content: start;
            justify-items: center;
        }

        .info-item {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            gap: 0.75rem;
            padding: 0.875rem;
            background: rgba(10, 126, 164, 0.04);
            border-radius: 8px;
            border-left: 3px solid #0a7ea4;
            transition: all 0.2s ease;
            text-align: center;
            width: 100%;
        }

        .info-item:hover {
            background: rgba(10, 126, 164, 0.08);
            transform: translateX(2px);
        }

        .info-item.full-width {
            grid-column: 1 / -1;
        }

        .info-icon {
            width: 24px;
            height: 24px;
            background: #0a7ea4;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .info-content {
            flex: 1;
            min-width: 0;
        }

        .info-label {
            display: block;
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .info-value {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            color: #374151;
            line-height: 1.4;
            word-wrap: break-word;
        }

        /* Timeline Specific Styles */
        .timeline-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .timeline-items {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            flex: 1;
            justify-content: space-between;
            align-items: center;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: rgba(245, 158, 11, 0.05);
            border-radius: 8px;
            border-left: 3px solid #f59e0b;
            width: 100%;
            max-width: 250px;
        }

        .timeline-marker {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .timeline-marker.start { background: #16a34a; }
        .timeline-marker.middle { background: #f59e0b; }
        .timeline-marker.end { background: #dc2626; }

        .timeline-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .timeline-label {
            font-size: 0.8rem;
            font-weight: 500;
            color: #6b7280;
        }

        .timeline-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
        }

        /* Weather Card */
        .weather-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        /* Info Card */
        .card-icon.info-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            font-size: 0.9rem;
        }

        .weather-badge {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.1) 0%, rgba(8, 145, 178, 0.08) 100%);
            color: #0a7ea4;
            padding: 0.35rem 0.85rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            border: 1px solid rgba(10, 126, 164, 0.2);
            white-space: nowrap;
            margin: 0 auto;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
        }

        /* Observers Card */
        .observers-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .observers-count-inline {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            white-space: nowrap;
            margin: 0 auto;
        }

        .observers-count {
            font-size: 1.25rem;
            font-weight: 700;
            color: #0a7ea4;
            line-height: 1;
        }

        .observers-label {
            font-size: 0.7rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: 500;
        }

        /* Photos Card */
        .photos-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .photos-display {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
        }

        .photo-section {
            flex: 1;
            text-align: center;
        }

        .photo-count {
            font-size: 1.25rem;
            font-weight: 700;
            color: #0a7ea4;
            display: block;
            line-height: 1;
        }

        .photo-label {
            font-size: 0.7rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: 500;
            margin-top: 0.25rem;
        }



        .photo-placeholder {
            color: #9ca3af;
            font-size: 0.75rem;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .photo-placeholder i {
            font-size: 1rem;
        }

        .no-photos-message {
            color: #9ca3af;
            font-size: 0.8rem;
            text-align: center;
            padding: 1rem;
            font-style: italic;
        }

        /* Contacts Card */
        .contacts-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .contacts-count-inline {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            white-space: nowrap;
            margin: 0 auto;
        }

        .contacts-count {
            font-size: 1.25rem;
            font-weight: 700;
            color: #0a7ea4;
            line-height: 1;
        }

        .contacts-label {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Map Card */
        .map-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .map-header {
            padding: 1rem 1rem 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 1rem;
        }

        .map-stats {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            align-items: center;
        }

        .map-stat-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            background: rgba(10, 126, 164, 0.1);
            border-radius: 6px;
            color: #0a7ea4;
            font-size: 0.7rem;
            font-weight: 500;
            border: 1px solid rgba(10, 126, 164, 0.2);
        }

        .map-stat-item i {
            font-size: 0.6rem;
        }

        .map-container {
            height: 400px;
            position: relative;
            margin-top: 0.75rem;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
        }

        .google-map {
            width: 100%;
            height: 100%;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
            padding: 1.5rem 0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(10, 126, 164, 0.3);
            color: white;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
        }

        .btn-secondary:hover {
            background: #374151;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
            color: white;
        }

        /* Height Distribution Fixes */
        .row.g-3 {
            align-items: stretch;
        }
        
        .row.g-3 > [class*="col-"] {
            display: flex;
            flex-direction: column;
        }
        
        /* Remove extra spacing from cards */
        .col-12 .modern-card:last-child {
            margin-bottom: 0;
        }
        
        /* Ensure timeline fills available space */
        .timeline-item:last-child {
            margin-bottom: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .card-content {
                padding: 0.5rem 0.75rem;
            }
            
            .card-title-section {
                gap: 0.5rem;
            }
            
            .card-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }
            
            .card-title {
                font-size: 0.85rem;
            }
            
            .card-subtitle {
                font-size: 0.7rem;
            }
            
            .map-container {
                height: 300px;
            }
        }
    </style>

    <!-- Google Maps API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&libraries=geometry&callback=initMap" async defer></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let map;
        let routePath;
        let markers = [];
        let trajetoData = null;

        // Initialize Google Map
        function initMap() {
            // Initialize map with default options
            const mapOptions = {
                zoom: 16,
                center: { lat: 39.5, lng: -8.0 }, // Default center (Portugal)
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_RIGHT
                },
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_CENTER
                },
                streetViewControl: false,
                fullscreenControl: true,
                fullscreenControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_TOP
                },
                styles: [
                    {
                        featureType: "poi",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    },
                    {
                        featureType: "poi.business",
                        stylers: [{ visibility: "off" }]
                    },
                    {
                        featureType: "poi.attraction",
                        stylers: [{ visibility: "off" }]
                    },
                    {
                        featureType: "poi.park",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    }
                ]
            };

            // Create map instance
            map = new google.maps.Map(document.getElementById('map'), mapOptions);

            // Initialize route path with shadow effect
            const shadowPath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#000000',
                strokeOpacity: 0.4,
                strokeWeight: 6,
                zIndex: 1
            });
            shadowPath.setMap(map);

            routePath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#0a7ea4',
                strokeOpacity: 1.0,
                strokeWeight: 4,
                zIndex: 2
            });
            routePath.setMap(map);

            window.shadowPath = shadowPath;

            // Load trajeto data
            loadTrajetoData();
        }

        function loadTrajetoData() {
            const trajetoId = '<?php echo htmlspecialchars($trajetoId); ?>';
            
            // Load trajeto data
            fetch('load_trajeto.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ trajetoId: trajetoId })
            })
            .then(response => response.json())
            .then(data => {
                            if (data.success) {
                trajetoData = data.data;
                populateTrajetoInfo(trajetoData);
                
                // Load route first
                loadRouteOnMap(trajetoData.coordinates || []);
                
                // Then load contacts (after route is loaded) - OPTIMIZED
                if (trajetoData.contacts && trajetoData.contacts.length > 0) {
                    // Reduced delay for faster loading
                    setTimeout(() => {
                        loadContactsOnMap(trajetoData.contacts);
                    }, 100); // Minimal delay to ensure map is ready
                }
                
                // Hide loading, show content
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                } else {
                    console.error('Trajeto not found:', data.message);
                    Swal.fire({
                        title: 'Erro',
                        text: data.message || 'Trajeto não encontrado.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc2626'
                    }).then(() => {
                        window.location.href = 'index.php';
                    });
                }
            })
            .catch(error => {
                console.error('Error loading trajeto:', error);
                Swal.fire({
                    title: 'Erro',
                    text: 'Erro ao carregar o trajeto.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                }).then(() => {
                    window.location.href = 'index.php';
                });
            });
        }

        function populateTrajetoInfo(data) {
            // Update header elements with trajectory name and date
            let headerHtml = data.name || 'Trajeto sem nome';
            
            // If trajectory name doesn't include the custom date, update it
            if (data.trajetoDate && data.startTime) {
                // Extract base name without date and time
                let baseName = data.name || 'Trajeto';
                baseName = baseName.replace(/ - \d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/, '');
                
                // Create name with updated date and start time
                headerHtml = `${baseName} - ${data.trajetoDate} ${data.startTime}`;
            }
            
            // Add only edited date to header if it exists and is different from created date
            if (data.updatedAt && data.updatedAt !== data.createdAt) {
                const editedDate = formatDate(data.updatedAt);
                headerHtml += ` <span style="font-size: 0.7em; font-weight: normal; color: #6b7280;">- Editado: ${editedDate}</span>`;
            }
            
            document.getElementById('headerTrajetoName').innerHTML = headerHtml;
            document.getElementById('routeDistanceMain').textContent = data.distance || '0 km';
            
            // Use trajectory date if available, otherwise use createdAt
            const displayDate = data.trajetoDate ? 
                formatTrajetoDate(data.trajetoDate) : 
                formatDate(data.createdAt);
            document.getElementById('createdDateMain').textContent = displayDate || 'Não disponível';
            
            // Show edited date if it exists and is different from created date
            if (data.updatedAt && data.updatedAt !== data.createdAt) {
                document.getElementById('editedDateMain').textContent = formatDate(data.updatedAt);
                document.getElementById('editedDateDisplay').style.display = 'inline';
            } else {
                document.getElementById('editedDateDisplay').style.display = 'none';
            }
            
            document.getElementById('startingAddressMain').textContent = data.startingAddress || 'Não definida';
            
            // Update stats
            document.getElementById('routePointsDisplay').textContent = data.routePoints || '0';
            document.getElementById('observersCountDisplay').textContent = data.numberOfObservers || '0';
            
            // Timeline information (compact version)
            document.getElementById('startTimeCompact').textContent = data.startTime || '--:--';
            document.getElementById('middleTimeCompact').textContent = data.middleTime || '--:--';
            document.getElementById('endTimeCompact').textContent = data.endTime || '--:--';
            
            // Weather information (show only if available)
            if (data.weatherCondition) {
                document.getElementById('weatherCardCompact').style.display = 'block';
                const weatherIcon = getWeatherIcon(data.weatherCondition);
                const weatherText = getWeatherText(data.weatherCondition);
                document.getElementById('weatherIconCompact').textContent = weatherIcon;
                document.getElementById('weatherTextCompact').textContent = weatherText;
            } else {
                document.getElementById('weatherCardCompact').style.display = 'none';
                console.log('No weather condition data available');
            }
            

            
            // Contacts information (compact version)
            document.getElementById('contactsCountCompact').textContent = data.contacts ? data.contacts.length : '0';
            
            // Date information (if different from createdAt)
            if (data.date && data.date !== data.createdAt) {
                console.log('Trajectory date:', data.date);
            }
            
            // Debug: Log all available fields
            console.log('Available trajectory data fields:', Object.keys(data));
            console.log('Date information:');
            console.log('- createdAt:', data.createdAt);
            console.log('- updatedAt:', data.updatedAt);
            console.log('- Will show edited date:', data.updatedAt && data.updatedAt !== data.createdAt);
            console.log('Photo data debug:');
            console.log('- mapPhotosCount:', data.mapPhotosCount);
            console.log('- otherPhotosCount:', data.otherPhotosCount);
            console.log('- hasMapPhotos:', data.hasMapPhotos);
            console.log('- hasOtherPhotos:', data.hasOtherPhotos);
            console.log('- mapPhotos array:', data.mapPhotos);
            console.log('- otherPhotos array:', data.otherPhotos);
            console.log('- contacts array:', data.contacts);
            console.log('🔍 DETAILED CONTACT ANALYSIS IN VIEW:');
            console.log('📊 Contacts count from server:', data.contacts ? data.contacts.length : 'null/undefined');
            console.log('📊 Full contacts data:', JSON.stringify(data.contacts, null, 2));
            
            // Handle photos display
            const totalPhotos = (data.mapPhotosCount || 0) + (data.otherPhotosCount || 0);
            if (totalPhotos > 0) {
                document.getElementById('photosGalleryRow').style.display = 'block';
                document.getElementById('totalPhotosCount').textContent = totalPhotos;
                
                // Generate combined photo thumbnails
                generateCombinedPhotoThumbnails('allPhotoThumbnails', data.mapPhotos || [], data.otherPhotos || []);
            } else {
                document.getElementById('photosGalleryRow').style.display = 'none';
            }

            // Load contacts on map if available
            if (data.contacts && data.contacts.length > 0) {
                console.log(`Found ${data.contacts.length} contacts, loading on map`);
                loadContactsOnMap(data.contacts);
            } else {
                console.log('No contacts found');
            }
        }

        function generateCombinedPhotoThumbnails(containerId, mapPhotos, otherPhotos) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            container.innerHTML = ''; // Clear existing content
            
            const allPhotos = [];
            
            // Add map photos with type identifier
            if (mapPhotos && mapPhotos.length > 0) {
                mapPhotos.forEach((photo, index) => {
                    // Handle both URL string format and object format
                    const photoData = typeof photo === 'string' ? { url: photo } : photo;
                    allPhotos.push({
                        ...photoData,
                        type: 'maps',
                        originalIndex: index + 1
                    });
                });
            }
            
            // Add other photos with type identifier  
            if (otherPhotos && otherPhotos.length > 0) {
                otherPhotos.forEach((photo, index) => {
                    // Handle both URL string format and object format
                    const photoData = typeof photo === 'string' ? { url: photo } : photo;
                    allPhotos.push({
                        ...photoData,
                        type: 'other',
                        originalIndex: index + 1
                    });
                });
            }
            
            if (allPhotos.length === 0) {
                container.innerHTML = '<div class="no-photos-message">Nenhuma foto disponível</div>';
                return;
            }
            
            // Generate thumbnails for all photos
            allPhotos.forEach((photo, index) => {
                const thumbnail = document.createElement('div');
                thumbnail.className = 'photo-thumbnail';
                
                // Check if we have actual photo URL
                if (photo && photo.url) {
                    const img = document.createElement('img');
                    img.src = photo.url;
                    img.alt = `${photo.type === 'maps' ? 'Mapa' : 'Outra'} foto ${photo.originalIndex}`;
                    img.onerror = function() {
                        console.error('Failed to load photo:', photo.url);
                        // Fallback to placeholder if image fails to load
                        this.parentNode.innerHTML = createPhotoPlaceholder(photo.type, photo.originalIndex);
                    };
                    thumbnail.appendChild(img);
                } else {
                    console.warn('Photo missing URL:', photo);
                    // Show placeholder thumbnail
                    thumbnail.innerHTML = createPhotoPlaceholder(photo.type, photo.originalIndex);
                }
                
                // Add click handler for photo viewing
                thumbnail.onclick = function() {
                    showPhotoModal(photo.type, photo.originalIndex, photo);
                };
                
                container.appendChild(thumbnail);
            });
        }
        
        function createPhotoPlaceholder(photoType, photoNumber) {
            const iconClass = photoType === 'maps' ? 'fas fa-map' : 'fas fa-image';
            return `
                <div class="photo-placeholder">
                    <i class="${iconClass}"></i>
                    <span>Foto ${photoNumber}</span>
                </div>
            `;
        }
        
        function showPhotoModal(photoType, photoNumber, photoData) {
            const photoTypeText = photoType === 'maps' ? 'do mapa' : 'geral';
            
            // Handle both URL string format and object format
            const photoUrl = typeof photoData === 'string' ? photoData : (photoData && photoData.url ? photoData.url : null);
            
            Swal.fire({
                title: `Foto ${photoTypeText} #${photoNumber}`,
                html: photoUrl ? 
                    `<img src="${photoUrl}" style="max-width: 100%; height: auto; border-radius: 8px;" onerror="this.style.display='none'; this.nextSibling.style.display='block';">
                     <div style="display: none; padding: 2rem; color: #6b7280;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem; color: #f59e0b;"></i>
                        <p>Erro ao carregar a foto</p>
                        <p style="font-size: 0.9rem;">A imagem pode ter sido removida ou estar temporariamente indisponível.</p>
                    </div>` :
                    `<div style="padding: 2rem; color: #6b7280;">
                        <i class="fas fa-image" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p>Foto ${photoTypeText} não disponível</p>
                        <p style="font-size: 0.9rem;">Esta foto pode ter sido removida ou não foi carregada corretamente.</p>
                    </div>`,
                showConfirmButton: true,
                confirmButtonText: 'Fechar',
                confirmButtonColor: '#0a7ea4',
                width: '600px'
            });
        }



        function loadContactsOnMap(contacts) {
            if (!contacts || contacts.length === 0) {
                console.log('No contacts to load on map');
                return;
            }

            console.log('Loading contacts on map:', contacts);
            console.log('Number of contacts to process:', contacts.length);

            contacts.forEach((contact, index) => {
                console.log(`Processing contact ${index + 1}:`, contact);
                
                if (contact.coordinates && contact.coordinates.lat && contact.coordinates.lng) {
                    const position = { 
                        lat: parseFloat(contact.coordinates.lat), 
                        lng: parseFloat(contact.coordinates.lng) 
                    };
                    
                    console.log(`Contact ${index + 1} position:`, position);

                    // Create canvas for dove marker
                    const canvas = document.createElement('canvas');
                    canvas.width = 32;
                    canvas.height = 32;
                    const ctx = canvas.getContext('2d');
                    
                    // Draw circle background
                    ctx.beginPath();
                    ctx.arc(16, 16, 14, 0, 2 * Math.PI);
                    ctx.fillStyle = '#16a34a';  // Keep the green color for consistency
                    ctx.fill();
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // Load and draw dove icon
                    const doveImg = new Image();
                    doveImg.onload = function() {
                        // Create a temporary canvas to process the dove icon
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = 16;
                        tempCanvas.height = 16;
                        const tempCtx = tempCanvas.getContext('2d');
                        
                        // Draw the dove icon on temp canvas
                        tempCtx.drawImage(doveImg, 0, 0, 16, 16);
                        
                        // Get image data to process pixels
                        const imageData = tempCtx.getImageData(0, 0, 16, 16);
                        const data = imageData.data;
                        
                        // Convert non-transparent pixels to white
                        for (let i = 0; i < data.length; i += 4) {
                            if (data[i + 3] > 0) { // If pixel is not transparent
                                data[i] = 255;     // Red = 255 (white)
                                data[i + 1] = 255; // Green = 255 (white)
                                data[i + 2] = 255; // Blue = 255 (white)
                            }
                        }
                        
                        // Put the processed image data back
                        tempCtx.putImageData(imageData, 0, 0);
                        
                        // Draw the white dove icon on the main canvas
                        ctx.drawImage(tempCanvas, 8, 8);
                        
                        // Create marker with the canvas image
                        const contactMarker = new google.maps.Marker({
                            position: position,
                            map: map,
                            icon: {
                                url: canvas.toDataURL(),
                                scaledSize: new google.maps.Size(32, 32),
                                anchor: new google.maps.Point(16, 16)
                            },
                            title: `Contacto ${index + 1}: ${contact.circumstance || 'Observação'}`,
                            zIndex: 500
                        });

                        // Add info window for contact details
                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 0.5rem; min-width: 200px;">
                                    <h6 style="margin: 0 0 0.5rem 0; color: #16a34a; font-weight: 600;">
                                        <i class="fas fa-dove" style="margin-right: 0.5rem;"></i>
                                        Contacto ${index + 1}
                                    </h6>
                                    <div style="font-size: 0.9rem; line-height: 1.4;">
                                        <div style="margin-bottom: 0.25rem;">
                                            <strong>Circunstância:</strong> ${translateCircumstance(contact.circumstance) || 'Não especificada'}
                                        </div>
                                        <div style="margin-bottom: 0.25rem;">
                                            <strong>Local:</strong> ${translateLocation(contact.location) || 'Não especificado'}
                                        </div>
                                        <div style="margin-bottom: 0.25rem;">
                                            <strong>Hora:</strong> ${contact.time || 'Não especificada'}
                                        </div>
                                        <div style="color: #6b7280; font-size: 0.8rem; margin-top: 0.5rem;">
                                            Lat: ${position.lat.toFixed(6)}, Lng: ${position.lng.toFixed(6)}
                                        </div>
                                    </div>
                                </div>
                            `
                        });

                        // Add click listener to show info window
                        contactMarker.addListener('click', function() {
                            infoWindow.open(map, contactMarker);
                        });

                        markers.push(contactMarker);
                        console.log(`Successfully created contact marker ${index + 1} at position:`, position);
                    };
                    doveImg.src = '../../assets/images/icons/dove-icon.png';
                } else {
                    console.log(`Contact ${index + 1} missing coordinates:`, contact);
                }
            });

            console.log(`Added ${contacts.length} contact markers to map`);
            console.log('Total markers on map now:', markers.length);
        }

        function loadRouteOnMap(coordinates) {
            if (!coordinates || coordinates.length === 0) {
                return;
            }

            // Clear existing markers
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Convert coordinates and create markers with same styling as edit.php
            const routeCoordinates = coordinates.map((coord, index) => {
                const position = { lat: coord.lat, lng: coord.lng };
                
                // Create marker icon based on position (same as edit.php)
                let markerIcon;
                if (index === 0) {
                    // Start point - green with floating label
                    markerIcon = {
                        path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                        fillColor: '#22c55e',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                        scale: 0.7,
                        anchor: new google.maps.Point(0, 8),
                        labelOrigin: new google.maps.Point(0, -40)
                    };
                } else if (index === coordinates.length - 1 && coordinates.length > 1) {
                    // End point - red with floating label
                    markerIcon = {
                        path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                        fillColor: '#dc2626',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                        scale: 0.7,
                        anchor: new google.maps.Point(0, 8),
                        labelOrigin: new google.maps.Point(0, -40)
                    };
                } else {
                    // Regular point - blue circle
                    markerIcon = {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#0a7ea4',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 3
                    };
                }

                // Create marker for each point
                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: index === 0 ? 'Início' : (index === coordinates.length - 1 && coordinates.length > 1 ? 'Fim' : `Ponto ${index + 1}`),
                    icon: markerIcon,
                    label: index === 0 ? {
                        text: 'INÍCIO',
                        fontSize: '9px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    } : (index === coordinates.length - 1 && coordinates.length > 1 ? {
                        text: 'FIM',
                        fontSize: '9px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    } : {
                        text: (index + 1).toString(),
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    }),
                    zIndex: index === 0 || (index === coordinates.length - 1 && coordinates.length > 1) ? 1000 : 100
                });

                // Add ground point for start and end (same as edit.php)
                if (index === 0 || (index === coordinates.length - 1 && coordinates.length > 1)) {
                    const groundMarker = new google.maps.Marker({
                        position: position,
                        map: map,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: index === 0 ? '#22c55e' : '#dc2626',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2
                        },
                        label: {
                            text: (index + 1).toString(),
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        },
                        title: index === 0 ? 'Início Ground' : 'Fim Ground',
                        zIndex: 50
                    });
                    markers.push(groundMarker);
                }
                
                markers.push(marker);
                return position;
            });

            // Update route path
            routePath.setPath(routeCoordinates);
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Update points count
            document.getElementById('routePoints').textContent = coordinates.length;
            // Also update the display version in the header stats
            document.getElementById('routePointsDisplay').textContent = coordinates.length;

            // Fit map to show all points
            if (routeCoordinates.length > 0) {
                const bounds = new google.maps.LatLngBounds();
                routeCoordinates.forEach(coord => bounds.extend(coord));
                map.fitBounds(bounds);
                
                // Add some padding
                setTimeout(() => {
                    if (routeCoordinates.length === 1) {
                        map.setZoom(15);
                    }
                }, 100);
            }
        }

        function getStatusLabel(status) {
            const labels = {
                'draft': 'Rascunho',
                'completed': 'Concluído',
                'review': 'Em Revisão'
            };
            return labels[status] || status;
        }

        function getDifficultyLabel(difficulty) {
            const labels = {
                'facil': 'Fácil',
                'medio': 'Médio',
                'dificil': 'Difícil'
            };
            return labels[difficulty] || difficulty;
        }

        function formatDate(dateString) {
            if (!dateString) return null;
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('pt-PT', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (e) {
                return dateString;
            }
        }

        function formatTrajetoDate(dateString) {
            if (!dateString) return null;
            
            // If it's already in DD/MM/YYYY format, return as is
            if (dateString.includes('/')) {
                return dateString;
            }
            
            // Otherwise, try to parse and format
            try {
                const date = new Date(dateString);
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = String(date.getFullYear());
                return `${day}/${month}/${year}`;
            } catch (e) {
                return dateString;
            }
        }

        function editTrajeto() {
            const trajetoId = '<?php echo htmlspecialchars($trajetoId); ?>';
            window.location.href = `edit.php?id=${encodeURIComponent(trajetoId)}`;
        }

        function goBack() {
            window.location.href = 'index.php';
        }

        function showTrajetoHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Ver Trajeto</div>',
                html: `
                    <div class="help-modal-content">
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-eye text-primary me-2"></i>
                                <span>Visualização do Trajeto</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-map-marked-alt text-primary me-2"></i>
                                    <span><strong>Mapa:</strong> Visualize o trajeto completo com todos os pontos marcados</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    <span><strong>Informações:</strong> Veja todos os detalhes do trajeto incluindo distância, dificuldade e estado</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <span><strong>Timeline:</strong> Horários de início, meio e fim do trajeto se disponíveis</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-camera text-primary me-2"></i>
                                    <span><strong>Fotografias:</strong> Informação sobre fotos associadas ao trajeto</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-cog text-primary me-2"></i>
                                <span>Ações Disponíveis</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-arrow-left text-secondary me-2"></i>
                                    <span><strong>Voltar:</strong> Regresse à lista de zonas e trajetos</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span>Precisa de Ajuda?</span>
                            </div>
                            <div class="help-section-body">
                                <div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">
                                    <div style="color: #0a7ea4; font-size: 0.9rem;">
                                        Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>
                                        <strong><EMAIL></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendido',
                confirmButtonColor: '#0a7ea4',
                width: '600px',
                customClass: {
                    popup: 'help-modal-popup'
                }
            });
        }

        // Add translation functions
        function translateCircumstance(circumstance) {
            if (!circumstance) return 'Não especificada';
            
            const translations = {
                // Main circumstances
                'flying': 'Rola em voo',
                'landed': 'Pousada',
                'feeding': 'Alimentando-se',
                'resting': 'Descansando',
                'emptyNest': 'Ninho vazio',
                'adultPerched': 'Adulto pousado',
                'occupiedNest': 'Ninho ocupado',
                'groups': 'Grupos de Rolas',
                'other': 'Outro',
                
                // Legacy values
                'perched': 'Pousada',
                'drinking': 'Bebendo água',
                'nesting': 'No ninho',
                'calling': 'Vocalizando',
                'mating': 'Acasalando',
                
                // Alternative spellings/formats
                'Voando': 'Rola em voo',
                'Em voo': 'Rola em voo',
                'Pousada': 'Pousada',
                'Alimentando-se': 'Alimentando-se',
                'Descansando': 'Descansando',
                'Ninho Vazio': 'Ninho vazio',
                'Ninho vazio': 'Ninho vazio',
                'Adulto pousado': 'Adulto pousado',
                'Ninho ocupado': 'Ninho ocupado',
                'Grupos de Rolas': 'Grupos de Rolas',
                'Outro': 'Outro',
                'Outra': 'Outro'
            };
            
            return translations[circumstance] || circumstance;
        }

        function translateLocation(location) {
            if (!location) return 'Não especificado';
            
            const translations = {
                // Main locations
                'field': 'Campo',
                'water': 'Água',
                'waterPoint': 'Ponto de água',
                'tree': 'Árvore',
                'trees': 'Árvores',
                'shrub': 'Arbusto',
                'clearing': 'Clareira',
                'building': 'Edifício',
                'other': 'Outro',
                
                // Legacy values
                'ground': 'Solo',
                'rock': 'Rocha',
                'grass': 'Relva',
                'fence': 'Cerca',
                
                // Alternative spellings/formats
                'Campo': 'Campo',
                'Água': 'Água',
                'Ponto de água': 'Ponto de água',
                'Árvore': 'Árvore',
                'Árvores': 'Árvores',
                'Arbusto': 'Arbusto',
                'Clareira': 'Clareira',
                'Edifício': 'Edifício',
                'Outro': 'Outro'
            };
            
            return translations[location] || location;
        }

        // Weather icon and text functions
        function getWeatherIcon(weatherCondition) {
            const weatherIcons = {
                'Céu limpo': '☀️',
                'Nublado': '☁️',
                'Chuva': '🌧️',
                'Vento moderado a forte': '💨',
                'Outro': '❓'
            };
            return weatherIcons[weatherCondition] || '🌤️';
        }

        function getWeatherText(weatherCondition) {
            // Return the condition as is, since it's already in Portuguese
            return weatherCondition;
        }
    </script>

</body>
</html> 