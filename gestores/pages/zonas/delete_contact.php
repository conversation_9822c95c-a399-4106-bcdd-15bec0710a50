<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json; charset=utf-8');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['contactId'])) {
        throw new Exception('Contact ID is required');
    }
    
    $contactId = trim($input['contactId']);
    $trajectoryId = trim($input['trajectoryId'] ?? '');
    
    // SECURITY: Validate contact ID format to prevent injection attacks
    if (empty($contactId) || !preg_match('/^[a-zA-Z0-9_-]+$/', $contactId) || strlen($contactId) > 100) {
        error_log("SECURITY VIOLATION: Invalid contact ID format attempted: " . $contactId . " by user " . ($currentUserEmail ?? 'unknown'));
        throw new Exception('Invalid contact ID format');
    }
    
    // SECURITY: Validate trajectory ID format if provided
    if (!empty($trajectoryId) && (!preg_match('/^[a-zA-Z0-9_-]+$/', $trajectoryId) || strlen($trajectoryId) > 100)) {
        error_log("SECURITY VIOLATION: Invalid trajectory ID format attempted: " . $trajectoryId . " by user " . ($currentUserEmail ?? 'unknown'));
        throw new Exception('Invalid trajectory ID format');
    }
    
    // SECURITY: Rate limiting - prevent rapid deletion attempts
    $current_time = time();
    $last_deletion_key = "last_contact_deletion_" . ($user_id ?? 'unknown');
    
    if (isset($_SESSION[$last_deletion_key])) {
        $time_since_last = $current_time - $_SESSION[$last_deletion_key];
        if ($time_since_last < 3) { // 3 second cooldown between deletions
            throw new Exception('Please wait before performing another deletion operation');
        }
    }
    
    $_SESSION[$last_deletion_key] = $current_time;
    
    // SAFETY CHECK 3: Get current user session info for additional verification
    $currentUserEmail = $_SESSION['user']['email'] ?? '';
    if (empty($currentUserEmail)) {
        throw new Exception('User session not found');
    }
    
    // SECURITY CHECK 4: Get current user session info for validation
    $user_id = $_SESSION['user']['id'] ?? '';
    $user_nif = $_SESSION['user']['nif'] ?? '';
    
    if (empty($user_id)) {
        throw new Exception('User session invalid - missing user ID');
    }
    
    // Use the global database instance
    global $database;
    
    // Get admin token for deletion
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // First, verify the contact exists and belongs to the current user
    $getUrl = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts/" . $contactId;
    
    $ch = curl_init($getUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($status === 404) {
        throw new Exception('Contact not found');
    } elseif ($status !== 200) {
        throw new Exception("Failed to verify contact, status: {$status}");
    }
    
    $contactData = json_decode($response, true);
    
    // SAFETY CHECK 5: Verify contact data structure
    if (!isset($contactData['fields']) || !is_array($contactData['fields'])) {
        throw new Exception('Invalid contact data structure');
    }
    
    // Check if user is admin (get role early for trajectory validation)
    $userRole = $_SESSION['user']['role'] ?? '';
    $isAdmin = in_array($userRole, ['admin', 'administrador']);
    
    // SECURITY LAYER 1: Verify trajectory ownership if trajectory ID provided (admins bypass this)
    // NOTE: For global duplicates, trajectory validation is optional since contacts may come from different sources
    if (!empty($trajectoryId) && !$isAdmin) {
        try {
            $trajectoryDoc = $database->getDocument('trajetos', $trajectoryId);
            if ($trajectoryDoc) {
                // Check if the trajectory was created by the current user
                if (isset($trajectoryDoc['createdBy']) && $trajectoryDoc['createdBy'] !== $user_id) {
                    error_log("SECURITY VIOLATION: User {$currentUserEmail} attempted to delete contact from trajectory {$trajectoryId} owned by " . ($trajectoryDoc['createdBy'] ?? 'unknown'));
                    throw new Exception("Access denied: You can only delete contacts from your own trajectories");
                }
                
                // SECURITY LAYER 2: Verify the zone associated with this trajectory belongs to the user
                if (isset($trajectoryDoc['zoneId']) && !empty($trajectoryDoc['zoneId'])) {
                    $zoneDoc = $database->getDocument('zonasCaca', $trajectoryDoc['zoneId']);
                    if ($zoneDoc) {
                        // Check if the zone belongs to the user's NIF
                        if (isset($zoneDoc['nifEntidade']) && $zoneDoc['nifEntidade'] !== $user_nif) {
                            error_log("SECURITY VIOLATION: User {$currentUserEmail} (NIF: {$user_nif}) attempted to delete contact from zone {$trajectoryDoc['zoneId']} belonging to NIF " . ($zoneDoc['nifEntidade'] ?? 'unknown'));
                            throw new Exception("Access denied: The zone associated with this trajectory does not belong to you");
                        }
                    }
                }
            } else {
                // Trajectory not found - this is OK for global duplicates, we'll rely on contact ownership validation
                error_log("WARNING: Trajectory {$trajectoryId} not found during contact deletion, proceeding with contact ownership validation only");
            }
        } catch (Exception $e) {
            // If trajectory validation fails, we'll rely on contact ownership validation
            // This allows deletion of orphaned contacts or contacts from global duplicates
            error_log("WARNING: Trajectory validation failed for {$trajectoryId}: " . $e->getMessage() . ", proceeding with contact ownership validation only");
        }
    } else if (!empty($trajectoryId) && $isAdmin) {
        error_log("ADMIN BYPASS: Admin user {$currentUserEmail} (Role: {$userRole}) bypassing trajectory ownership validation for trajectory {$trajectoryId}");
    }
    
    // SECURITY CHECK 6: Verify the contact belongs to the current user OR user is admin
    $contactCreatedByEmail = $contactData['fields']['createdByEmail']['stringValue'] ?? '';
    $contactCreatedById = $contactData['fields']['createdBy']['stringValue'] ?? '';
    
    // Check both email and user ID for ownership
    $emailMatch = !empty($contactCreatedByEmail) && $contactCreatedByEmail === $currentUserEmail;
    $idMatch = !empty($contactCreatedById) && $contactCreatedById === $user_id;
    
    if (!$emailMatch && !$idMatch && !$isAdmin) {
        error_log("SECURITY VIOLATION: User {$currentUserEmail} (ID: {$user_id}, Role: {$userRole}) attempted to delete contact created by email: {$contactCreatedByEmail}, ID: {$contactCreatedById}");
        throw new Exception('Access denied: You can only delete contacts that you created');
    }
    
    // Log admin deletion for audit trail
    if ($isAdmin && !$emailMatch && !$idMatch) {
        error_log("ADMIN DELETION: Admin user {$currentUserEmail} (Role: {$userRole}) is deleting contact created by {$contactCreatedByEmail} (ID: {$contactCreatedById})");
    }
    
    // SAFETY CHECK 7: Verify trajectory ID matches if provided (flexible for global duplicates)
    $contactTrajectoryId = $contactData['fields']['trajectoryId']['stringValue'] ?? '';
    if (!empty($trajectoryId) && !empty($contactTrajectoryId) && $contactTrajectoryId !== $trajectoryId) {
        // For global duplicates, trajectory ID mismatch is a warning, not an error
        error_log("WARNING: Trajectory ID mismatch during deletion. Expected: {$trajectoryId}, Found: {$contactTrajectoryId}. Proceeding with contact ownership validation.");
    }
    
    // SAFETY CHECK 8: Additional verification - check contact coordinates exist (valid contact)
    if (!isset($contactData['fields']['coordinates']['mapValue']['fields']['lat']) || 
        !isset($contactData['fields']['coordinates']['mapValue']['fields']['lng'])) {
        throw new Exception('SECURITY: Contact missing required coordinate data - may not be a valid contact');
    }
    
    // SAFETY CHECK 9: Extract and log contact details including images
    $contactLat = $contactData['fields']['coordinates']['mapValue']['fields']['lat']['doubleValue'] ?? 'N/A';
    $contactLng = $contactData['fields']['coordinates']['mapValue']['fields']['lng']['doubleValue'] ?? 'N/A';
    $contactTime = $contactData['fields']['time']['stringValue'] ?? 'N/A';
    $contactCircumstance = $contactData['fields']['circumstance']['stringValue'] ?? 'N/A';
    
    // Extract images array for deletion
    $contactImages = [];
    if (isset($contactData['fields']['images']['arrayValue']['values'])) {
        foreach ($contactData['fields']['images']['arrayValue']['values'] as $imageValue) {
            if (isset($imageValue['stringValue'])) {
                $contactImages[] = $imageValue['stringValue'];
            }
        }
    }
    
    error_log("CONTACT DELETION ATTEMPT - User: {$currentUserEmail}, Contact ID: {$contactId}, Trajectory: {$contactTrajectoryId}, Coordinates: {$contactLat},{$contactLng}, Time: {$contactTime}, Circumstance: {$contactCircumstance}, Images: " . count($contactImages));
    
    // SAFETY CHECK 10: Delete associated images from Firebase Storage first
    $deletedImagesCount = 0;
    $imageDeleteErrors = [];
    
    if (!empty($contactImages)) {
        error_log("Deleting " . count($contactImages) . " images associated with contact {$contactId}");
        
        foreach ($contactImages as $imageIndex => $imageUrl) {
            try {
                // Extract the storage path from Firebase Storage URL
                if (strpos($imageUrl, 'firebasestorage.googleapis.com') !== false) {
                    // Parse Firebase Storage URL to get the file path
                    $urlParts = parse_url($imageUrl);
                    $path = $urlParts['path'] ?? '';
                    
                    // Extract the file path from the URL (format: /v0/b/bucket/o/path)
                    if (preg_match('/\/v0\/b\/[^\/]+\/o\/(.+)/', $path, $matches)) {
                        $filePath = urldecode($matches[1]);
                        
                        // Remove any query parameters from the file path
                        $filePath = strtok($filePath, '?');
                        
                        error_log("Attempting to delete image: {$filePath}");
                        
                        // Delete from Firebase Storage
                        $deleteUrl = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_PROJECT_ID . ".firebasestorage.app/o/" . urlencode($filePath);
                        
                        $ch = curl_init($deleteUrl);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                        curl_setopt($ch, CURLOPT_HTTPHEADER, [
                            'Authorization: Bearer ' . $adminToken
                        ]);
                        
                        $response = curl_exec($ch);
                        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        if ($status === 200 || $status === 204) {
                            $deletedImagesCount++;
                            error_log("Successfully deleted image: {$filePath}");
                        } else {
                            $error = "Failed to delete image {$filePath}, status: {$status}";
                            $imageDeleteErrors[] = $error;
                            error_log($error);
                        }
                    } else {
                        $error = "Could not parse Firebase Storage path from URL: {$imageUrl}";
                        $imageDeleteErrors[] = $error;
                        error_log($error);
                    }
                } else {
                    error_log("Skipping non-Firebase Storage URL: {$imageUrl}");
                }
            } catch (Exception $e) {
                $error = "Error deleting image {$imageIndex}: " . $e->getMessage();
                $imageDeleteErrors[] = $error;
                error_log($error);
            }
        }
        
        error_log("Image deletion summary: {$deletedImagesCount}/{" . count($contactImages) . "} images deleted successfully");
    }
    
    // Delete the contact document
    $deleteUrl = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts/" . $contactId;
    
    $ch = curl_init($deleteUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken
    ]);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($status !== 200) {
        // Log failed deletion attempt
        error_log("CONTACT DELETION FAILED - User: {$currentUserEmail}, Contact ID: {$contactId}, HTTP Status: {$status}, Response: " . $response);
        throw new Exception("Failed to delete contact, status: {$status}");
    }
    
    // SAFETY CHECK 11: Verify deletion was successful by trying to fetch the document again
    $verifyUrl = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts/" . $contactId;
    
    $ch = curl_init($verifyUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken
    ]);
    
    $verifyResponse = curl_exec($ch);
    $verifyStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // Document should return 404 if successfully deleted
    if ($verifyStatus !== 404) {
        error_log("CONTACT DELETION VERIFICATION FAILED - Contact still exists after deletion attempt. Status: {$verifyStatus}");
        throw new Exception("Deletion verification failed - contact may still exist");
    }
    
    // Log successful deletion for audit trail
    error_log("CONTACT DELETION SUCCESS - User: {$currentUserEmail}, Contact ID: {$contactId}, Trajectory: {$contactTrajectoryId}, Verified: Document no longer exists, Images deleted: {$deletedImagesCount}/" . count($contactImages));
    
    ob_end_clean();
    
    // Success response with additional verification info
    echo json_encode([
        'success' => true,
        'message' => 'Contact deleted successfully and verified',
        'contactId' => $contactId,
        'trajectoryId' => $trajectoryId,
        'deletedBy' => $currentUserEmail,
        'verified' => true,
        'imagesDeleted' => $deletedImagesCount,
        'totalImages' => count($contactImages),
        'imageErrors' => $imageDeleteErrors
    ]);
    
} catch (Exception $e) {
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?> 