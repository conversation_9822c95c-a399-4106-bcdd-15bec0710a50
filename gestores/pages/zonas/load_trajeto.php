<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

/**
 * Get photo URLs from Firebase Storage
 */
function getStoragePhotoUrls($trajetoId, $photoType, $photoCount) {
    $photos = [];
    
    try {
        // Get admin access token for Storage API
        global $database;
        $adminToken = $database->getAdminAccessToken();
        
        if (!$adminToken) {
            error_log("Failed to get admin token for Storage access");
            return $photos;
        }
        
        // List all files in the photos directory and filter by type
        $prefix = "zonas/{$trajetoId}/photos/";
        $url = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o?prefix=" . urlencode($prefix);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status === 200) {
            $result = json_decode($response, true);
            if (isset($result['items'])) {
                foreach ($result['items'] as $item) {
                    $fileName = basename($item['name']);
                    
                    // Look for files that match the pattern: trajetoId_photoType_*
                    $searchPattern = "{$trajetoId}_{$photoType}_";
                    
                    if (strpos($fileName, $searchPattern) === 0) {
                        $photoUrl = getFirebaseStorageDownloadUrl($item['name'], $adminToken);
                        if ($photoUrl) {
                            $photos[] = $photoUrl; // Return just the URL string for simplicity
                        }
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Error fetching storage photo URLs: " . $e->getMessage());
    }
            
        return $photos;
}

/**
 * Get Firebase Storage download URL with token for a specific file path
 */
function getFirebaseStorageDownloadUrl($storagePath, $adminToken) {
    try {
        // First, get the file metadata to get the download token
        $encodedPath = urlencode($storagePath);
        $metadataUrl = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/{$encodedPath}";
        
        $ch = curl_init($metadataUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status === 200) {
            $metadata = json_decode($response, true);
            if (isset($metadata['downloadTokens'])) {
                $token = explode(',', $metadata['downloadTokens'])[0]; // Get first token
                return "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/{$encodedPath}?alt=media&token={$token}";
            } else {
                // Fallback to media URL without token (might work if file is public)
                return "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/{$encodedPath}?alt=media";
            }
        }
        
    } catch (Exception $e) {
        error_log("Error getting Firebase Storage URL: " . $e->getMessage());
    }
    
    return null;
}

/**
 * Find photo in storage by listing directory contents
 */
function findPhotoInStorage($trajetoId, $photoType, $photoIndex, $adminToken) {
    try {
        // List files in the photos directory
        $prefix = "zonas/{$trajetoId}/photos/";
        $url = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o?prefix=" . urlencode($prefix);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status === 200) {
            $result = json_decode($response, true);
            if (isset($result['items'])) {
                error_log("Found " . count($result['items']) . " files in storage for trajeto {$trajetoId}");
                
                foreach ($result['items'] as $item) {
                    $fileName = basename($item['name']);
                    error_log("Checking file: {$fileName}");
                    
                    // Map the photo type to match the actual file naming
                    $storagePhotoType = $photoType;
                    if ($photoType === 'maps') {
                        $storagePhotoType = 'maps'; // Keep as is
                    } elseif ($photoType === 'other') {
                        $storagePhotoType = 'other'; // Keep as is
                    }
                    
                    $searchPattern = "{$trajetoId}_{$storagePhotoType}_{$photoIndex}_";
                    error_log("Looking for pattern: {$searchPattern}");
                    
                    // Look for files that match the pattern: trajetoId_photoType_index_*
                    if (strpos($fileName, $searchPattern) === 0) {
                        error_log("Found matching photo: {$fileName}");
                        return getFirebaseStorageDownloadUrl($item['name'], $adminToken);
                    }
                }
            } else {
                error_log("No items found in storage response");
            }
        } else {
            error_log("Storage API returned status: {$status}");
        }
        
        error_log("No photo found for: {$trajetoId}_{$photoType}_{$photoIndex}");
        
    } catch (Exception $e) {
        error_log("Error finding photo in storage: " . $e->getMessage());
    }
    
    return null;
}

/**
 * Get contacts associated with a trajectory - OPTIMIZED VERSION
 */
function getTrajectoryContacts($trajectoryId, $adminToken) {
    global $database;
    
    try {
        // Set admin token for database operations
        $database->setAccessToken($adminToken);
        
        // Use the optimized queryDocuments method from config.php
        // This will fetch only contacts that match the trajectoryId
        $contacts = $database->queryDocuments('contacts', 'trajectoryId', 'EQUAL', $trajectoryId);
        
        // Convert to array format expected by the frontend
        $contactsArray = [];
        foreach ($contacts as $docId => $contactData) {
            $contactData['id'] = $docId; // Add document ID
            $contactsArray[] = $contactData;
        }
        
        return $contactsArray;
        
    } catch (Exception $e) {
        error_log("Error fetching trajectory contacts: " . $e->getMessage());
        return [];
    }
}

/**
 * Parse Firestore document format
 */
function parseFirestoreDocument($doc) {
    if (!isset($doc['fields'])) {
        return [];
    }
    
    $result = [];
    foreach ($doc['fields'] as $key => $field) {
        $result[$key] = convertFromFirestoreValue($field);
    }
    
    return $result;
}

/**
 * Convert Firestore field value to PHP value
 */
function convertFromFirestoreValue($field) {
    if (isset($field['stringValue'])) {
        return $field['stringValue'];
    } elseif (isset($field['integerValue'])) {
        return (int)$field['integerValue'];
    } elseif (isset($field['doubleValue'])) {
        return (float)$field['doubleValue'];
    } elseif (isset($field['booleanValue'])) {
        return $field['booleanValue'];
    } elseif (isset($field['timestampValue'])) {
        return $field['timestampValue'];
    } elseif (isset($field['nullValue'])) {
        return null;
    } elseif (isset($field['arrayValue'])) {
        $result = [];
        if (isset($field['arrayValue']['values'])) {
            foreach ($field['arrayValue']['values'] as $value) {
                $result[] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    } elseif (isset($field['mapValue'])) {
        $result = [];
        if (isset($field['mapValue']['fields'])) {
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $result[$key] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    }
    
    return null;
}

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!$input || !isset($input['trajetoId'])) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Trajeto ID is required']);
    exit();
}

$trajetoId = $input['trajetoId'];

try {
    // Use the global database instance
    global $database;
    
    // Get admin token for loading
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // Set admin token
    $database->setAccessToken($adminToken);
    
    // Get trajeto document from 'zonas' collection (same as save_trajeto.php)
    $trajetoDoc = $database->getDocument('zonas', $trajetoId);
    
    if (!$trajetoDoc) {
        throw new Exception('Trajeto not found');
    }
    
    // Fetch photo URLs from Firebase Storage if photos exist - OPTIMIZED
    $mapPhotos = [];
    $otherPhotos = [];
    
    // Only load photos if they exist (avoid unnecessary API calls)
    if (isset($trajetoDoc['mapPhotosCount']) && $trajetoDoc['mapPhotosCount'] > 0) {
        $mapPhotos = getStoragePhotoUrls($trajetoId, 'maps', $trajetoDoc['mapPhotosCount']);
    }
    
    if (isset($trajetoDoc['otherPhotosCount']) && $trajetoDoc['otherPhotosCount'] > 0) {
        $otherPhotos = getStoragePhotoUrls($trajetoId, 'other', $trajetoDoc['otherPhotosCount']);
    }
    
    // Ensure the trajectory ID is included in the response
    $trajetoDoc['id'] = $trajetoId;
    
    // Add photo URLs to the response
    $trajetoDoc['mapPhotos'] = $mapPhotos;
    $trajetoDoc['otherPhotos'] = $otherPhotos;
    
    // Fetch contacts associated with this trajectory - OPTIMIZED
    $contacts = [];
    try {
        $contacts = getTrajectoryContacts($trajetoId, $adminToken);
    } catch (Exception $e) {
        error_log("Error loading contacts: " . $e->getMessage());
    }
    
    // Add contacts to the response
    $trajetoDoc['contacts'] = $contacts;
    
    // Clean output buffer and return success response
    ob_end_clean();
    echo json_encode([
        'success' => true,
        'data' => $trajetoDoc
    ]);
    
} catch (Exception $e) {
    // Clean output buffer and return error response
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
    // Log the error
    error_log("Error loading trajeto: " . $e->getMessage());
}
?> 