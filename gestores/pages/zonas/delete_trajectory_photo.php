<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!$input || !isset($input['photoUrl'])) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Photo URL is required']);
    exit();
}

$photoUrl = $input['photoUrl'];
$trajectoryId = $input['trajectoryId'] ?? '';

// SECURITY: Get current user info for validation
$user_id = $_SESSION['user']['id'] ?? '';
$user_email = $_SESSION['user']['email'] ?? '';
$user_nif = $_SESSION['user']['nif'] ?? '';

if (empty($user_id) || empty($user_email)) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User session invalid - missing user ID or email']);
    exit();
}

// SECURITY: Validate trajectory ID format if provided
if (!empty($trajectoryId) && (!preg_match('/^[a-zA-Z0-9_-]+$/', $trajectoryId) || strlen($trajectoryId) > 100)) {
    error_log("SECURITY VIOLATION: Invalid trajectory ID format attempted: " . $trajectoryId . " by user " . $user_email);
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid trajectory ID format']);
    exit();
}

// SECURITY: Validate photo URL format
if (empty($photoUrl) || strpos($photoUrl, 'firebasestorage.googleapis.com') === false) {
    error_log("SECURITY VIOLATION: Invalid photo URL attempted: " . $photoUrl . " by user " . $user_email);
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid photo URL format']);
    exit();
}

// SECURITY: Rate limiting - prevent rapid deletion attempts
$current_time = time();
$last_deletion_key = "last_photo_deletion_" . $user_id;

if (isset($_SESSION[$last_deletion_key])) {
    $time_since_last = $current_time - $_SESSION[$last_deletion_key];
    if ($time_since_last < 2) { // 2 second cooldown between photo deletions
        ob_end_clean();
        http_response_code(429);
        echo json_encode(['success' => false, 'message' => 'Please wait before performing another deletion operation']);
        exit();
    }
}

$_SESSION[$last_deletion_key] = $current_time;

try {
    // Use the global database instance
    global $database;
    
    // Get admin token for operations
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // SECURITY LAYER 1: Verify trajectory ownership if trajectory ID provided
    if (!empty($trajectoryId)) {
        $trajectoryDoc = $database->getDocument('zonas', $trajectoryId);
        if (!$trajectoryDoc) {
            throw new Exception("Trajectory not found or access denied");
        }
        
        // Check if the trajectory was created by the current user
        if (!isset($trajectoryDoc['createdBy']) || $trajectoryDoc['createdBy'] !== $user_id) {
            error_log("SECURITY VIOLATION: User {$user_email} attempted to delete photo from trajectory {$trajectoryId} owned by " . ($trajectoryDoc['createdBy'] ?? 'unknown'));
            throw new Exception("Access denied: You can only delete photos from your own trajectories");
        }
        
        // SECURITY LAYER 2: Verify the zone associated with this trajectory belongs to the user
        if (isset($trajectoryDoc['zoneId']) && !empty($trajectoryDoc['zoneId'])) {
            $zoneDoc = $database->getDocument('zonasCaca', $trajectoryDoc['zoneId']);
            if ($zoneDoc) {
                // Check if the zone belongs to the user's NIF
                if (!isset($zoneDoc['nifEntidade']) || $zoneDoc['nifEntidade'] !== $user_nif) {
                    error_log("SECURITY VIOLATION: User {$user_email} (NIF: {$user_nif}) attempted to delete photo from zone {$trajectoryDoc['zoneId']} belonging to NIF " . ($zoneDoc['nifEntidade'] ?? 'unknown'));
                    throw new Exception("Access denied: The zone associated with this trajectory does not belong to you");
                }
            }
        }
    }
    
    // SECURITY LAYER 3: Validate that the photo URL belongs to the user's trajectory
    $storagePath = extractStoragePathFromUrl($photoUrl);
    if (!$storagePath) {
        throw new Exception("Invalid photo URL format");
    }
    
    // Check if the storage path contains the trajectory ID (if provided)
    if (!empty($trajectoryId) && strpos($storagePath, $trajectoryId) === false) {
        error_log("SECURITY VIOLATION: User {$user_email} attempted to delete photo {$storagePath} that doesn't belong to trajectory {$trajectoryId}");
        throw new Exception("Access denied: Photo does not belong to the specified trajectory");
    }
    
    // Delete from Firebase Storage
    $deleted = deleteFromFirebaseStorage($storagePath, $adminToken);
    
    if ($deleted) {
        // Log successful deletion for audit trail
        error_log("PHOTO DELETION SUCCESS: User {$user_email} (ID: {$user_id}) successfully deleted photo {$storagePath} from trajectory {$trajectoryId}");
        
        // Clean output buffer and return success response
        ob_end_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Photo deleted successfully and verified',
            'deletedPath' => $storagePath,
            'trajectoryId' => $trajectoryId,
            'deletedBy' => $user_email,
            'verified' => true
        ]);
    } else {
        error_log("PHOTO DELETION FAILED: User {$user_email} failed to delete photo {$storagePath} from trajectory {$trajectoryId}");
        throw new Exception('Failed to delete photo from storage');
    }
    
} catch (Exception $e) {
    // Clean output buffer and return error response
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
    // Log the error
    error_log("Error deleting trajectory photo: " . $e->getMessage());
}

/**
 * Extract storage path from Firebase Storage URL
 */
function extractStoragePathFromUrl($url) {
    // Firebase Storage URLs have format: 
    // https://firebasestorage.googleapis.com/v0/b/BUCKET/o/ENCODED_PATH?alt=media&token=TOKEN
    
    if (strpos($url, 'firebasestorage.googleapis.com') === false) {
        return null;
    }
    
    // Extract the encoded path between /o/ and ?alt=media
    $pattern = '/\/o\/([^?]+)/';
    if (preg_match($pattern, $url, $matches)) {
        // Decode the URL-encoded path
        return urldecode($matches[1]);
    }
    
    return null;
}

/**
 * Delete file from Firebase Storage
 */
function deleteFromFirebaseStorage($storagePath, $adminToken) {
    try {
        $encodedPath = urlencode($storagePath);
        $url = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/{$encodedPath}";
        
        error_log("Deleting from Storage: {$url}");
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        error_log("Storage delete response status: {$status}");
        if ($response) {
            error_log("Storage delete response: " . $response);
        }
        
        // 204 = No Content (successful deletion)
        // 404 = Not Found (file doesn't exist, consider as success)
        return $status === 204 || $status === 404;
        
    } catch (Exception $e) {
        error_log("Error deleting from Firebase Storage: " . $e->getMessage());
        return false;
    }
}
?> 