<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Log the received data for debugging
error_log("💾 SAVE_CONTACTS: Received trajectory ID: " . ($input['trajectoryId'] ?? 'NOT SET'));
error_log("💾 SAVE_CONTACTS: Number of contacts to save: " . (isset($input['contacts']) ? count($input['contacts']) : 0));

// Validate required fields
if (!$input || !isset($input['trajectoryId']) || !isset($input['contacts'])) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

// Get user information
$user_id = $_SESSION['user']['id'] ?? '';
$user_email = $_SESSION['user']['email'] ?? '';

if (empty($user_id)) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User ID not found']);
    exit();
}

try {
    // Use the global database instance
    global $database;
    
    // Debug: Log the input data
    error_log("Contacts input data: " . json_encode($input));
    
    // Get admin token for saving
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // Set admin token
    $database->setAccessToken($adminToken);
    
    // Validate contacts array
    $contacts = $input['contacts'];
    if (!is_array($contacts)) {
        throw new Exception("Contacts must be an array");
    }
    
    $savedContacts = [];
    
    // Process each contact
    foreach ($contacts as $index => $contact) {
        error_log("PROCESSING CONTACT $index: " . json_encode($contact));
        
        if (!is_array($contact)) {
            error_log("ERROR: Invalid contact format at index $index");
            throw new Exception("Invalid contact format at index $index");
        }
        
        // Validate required contact fields (coordinates are required, others can be empty)
        $requiredFields = ['lat', 'lng'];
        foreach ($requiredFields as $field) {
            if (!isset($contact[$field])) {
                error_log("ERROR: Missing required field '$field' in contact at index $index");
                error_log("CONTACT DATA: " . json_encode($contact));
                throw new Exception("Missing required field '$field' in contact at index $index");
            }
        }
        
        // Ensure other fields exist (can be empty)
        $optionalFields = ['time', 'circumstance', 'location'];
        foreach ($optionalFields as $field) {
            if (!isset($contact[$field])) {
                $contact[$field] = ''; // Set to empty string if not provided
            }
        }
        
        $lat = (float)$contact['lat'];
        $lng = (float)$contact['lng'];
        
        error_log("CONTACT $index COORDINATES: lat=$lat, lng=$lng");
        
        // Basic validation for coordinate values
        if ($lat < -90 || $lat > 90 || $lng < -180 || $lng > 180) {
            error_log("ERROR: Invalid coordinate values at index $index: lat=$lat, lng=$lng");
            throw new Exception("Invalid coordinate values at index $index: lat=$lat, lng=$lng");
        }
        
        // Prepare contact data
        error_log("💾 SAVE_CONTACTS: Preparing contact $index with trajectory ID: " . $input['trajectoryId']);
        $contactData = [
            'trajectoryId' => $input['trajectoryId'],
            'coordinates' => [
                'lat' => $lat,
                'lng' => $lng
            ],
            'time' => $contact['time'],
            'circumstance' => $contact['circumstance'],
            'location' => $contact['location'],
            'createdAt' => date('c'), // ISO 8601 format
            'createdBy' => $user_id,
            'createdByEmail' => $user_email,
            'updatedAt' => date('c'),
            'updatedBy' => $user_id
        ];
        
        error_log("PREPARED CONTACT DATA $index: " . json_encode($contactData));
        
        // Save to Firestore - using 'contacts' collection
        try {
            $documentId = $database->addDocument('contacts', $contactData);
            error_log("SUCCESS: Saved contact $index with ID: $documentId");
            
            $savedContacts[] = [
                'id' => $documentId,
                'data' => $contactData
            ];
        } catch (Exception $e) {
            error_log("ERROR: Failed to save contact $index: " . $e->getMessage());
            throw new Exception("Failed to save contact $index: " . $e->getMessage());
        }
    }
    
    // Clean output buffer and return success response
    ob_end_clean();
    echo json_encode([
        'success' => true, 
        'message' => 'Contactos guardados com sucesso',
        'contactCount' => count($savedContacts),
        'contacts' => $savedContacts
    ]);
    
} catch (Exception $e) {
    // Clean output buffer and return error response
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Erro ao guardar os contactos: ' . $e->getMessage()
    ]);
    
    // Log the error
    error_log("Error saving contacts: " . $e->getMessage());
}
?> 