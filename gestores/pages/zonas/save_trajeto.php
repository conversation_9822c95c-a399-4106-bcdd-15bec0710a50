<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!$input || !isset($input['zoneId']) || !isset($input['coordinates'])) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

// Get user information
$user_id = $_SESSION['user']['id'] ?? '';
$user_email = $_SESSION['user']['email'] ?? '';

if (empty($user_id)) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User ID not found']);
    exit();
}

try {
    // Use the global database instance
    global $database;
    
    // Debug: Log the input data
    error_log("Trajeto input data: " . json_encode($input));
    
    // Get admin token for saving
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // Set admin token
    $database->setAccessToken($adminToken);
    
    // Convert coordinates from array of objects to Firestore-friendly format
    $coordinates = $input['coordinates'];
    $coordinatesList = [];
    
    // Validate coordinates array
    if (!is_array($coordinates)) {
        throw new Exception("Coordinates must be an array");
    }
    
    // Convert each coordinate object to a simple array format
    foreach ($coordinates as $index => $coord) {
        if (!is_array($coord) || !isset($coord['lat']) || !isset($coord['lng'])) {
            throw new Exception("Invalid coordinate format at index $index");
        }
        
        $lat = (float)$coord['lat'];
        $lng = (float)$coord['lng'];
        
        // Basic validation for coordinate values
        if ($lat < -90 || $lat > 90 || $lng < -180 || $lng > 180) {
            throw new Exception("Invalid coordinate values at index $index: lat=$lat, lng=$lng");
        }
        
        $coordinatesList[] = [
            'lat' => $lat,
            'lng' => $lng
        ];
    }
    
    error_log("Converted " . count($coordinatesList) . " coordinates successfully");
    
    // Prepare trajeto data
    $trajetoData = [
        'zoneId' => $input['zoneId'],
        'name' => $input['name'] ?? '',
        'description' => $input['description'] ?? '',
        'difficulty' => $input['difficulty'] ?? 'medio',
        'coordinates' => $coordinatesList,
        'distance' => $input['distance'] ?? '0 km',
        'pointsCount' => $input['pointsCount'] ?? 0,
        'startingAddress' => $input['startingAddress'] ?? '',
        'status' => $input['status'] ?? 'draft',
        'createdAt' => date('c'), // ISO 8601 format
        'createdBy' => $user_id,
        'createdByEmail' => $user_email,
        'updatedAt' => date('c'),
        'updatedBy' => $user_id
    ];
    
    // Add optional trajectory setup fields if provided
    if (isset($input['date'])) {
        $trajetoData['date'] = $input['date'];
        error_log("Added date: " . $input['date']);
    } else {
        error_log("Date not provided in input");
    }
    
    if (isset($input['startTime'])) {
        $trajetoData['startTime'] = $input['startTime'];
        error_log("Added startTime: " . $input['startTime']);
    } else {
        error_log("StartTime not provided in input");
    }
    
    if (isset($input['weatherCondition'])) {
        $trajetoData['weatherCondition'] = $input['weatherCondition'];
        error_log("Added weatherCondition: " . $input['weatherCondition']);
    } else {
        error_log("WeatherCondition not provided in input");
    }
    
    if (isset($input['numberOfObservers'])) {
        $trajetoData['numberOfObservers'] = $input['numberOfObservers'];
        error_log("Added numberOfObservers: " . $input['numberOfObservers']);
    } else {
        error_log("NumberOfObservers not provided in input");
    }
    
    // Debug: Log the prepared data
    error_log("Prepared trajeto data: " . json_encode($trajetoData));
    
    // Save to Firestore - using 'zonas' collection (same as in index.php)
    $documentId = $database->addDocument('zonas', $trajetoData);
    
    // Debug: Log the document ID
    error_log("Document saved with ID: " . $documentId);
    error_log("Document ID type: " . gettype($documentId));
    error_log("Document ID empty: " . (empty($documentId) ? 'true' : 'false'));
    
    // Clean output buffer and return success response
    ob_end_clean();
    echo json_encode([
        'success' => true, 
        'message' => 'Trajeto criado com sucesso',
        'documentId' => $documentId
    ]);
    
} catch (Exception $e) {
    // Clean output buffer and return error response
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Erro ao guardar o trajeto: ' . $e->getMessage()
    ]);
    
    // Log the error
    error_log("Error saving trajeto: " . $e->getMessage());
}
?> 