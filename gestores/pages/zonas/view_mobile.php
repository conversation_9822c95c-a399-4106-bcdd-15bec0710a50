<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Handle AJAX request for loading contacts
if (isset($_GET['action']) && $_GET['action'] === 'load_contacts') {
    ob_end_clean(); // Clear output buffer
    header('Content-Type: application/json');
    
    $sessionId = $_GET['sessionId'] ?? '';
    
    if (empty($sessionId)) {
        echo json_encode(['success' => false, 'message' => 'Session ID não fornecido']);
        exit();
    }
    
    try {
        // Get admin access token for Firestore API
        global $database;
        $adminToken = $database->getAdminAccessToken();
        
        if (!$adminToken) {
            throw new Exception('Failed to get admin access token');
        }
        
        // Query gestorMobile_contacts collection for this session - get ALL documents with pagination
        $allFirestoreContacts = [];
        $nextPageToken = null;
        
        do {
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/gestorMobile_contacts";
            if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
            }
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status !== 200) {
                throw new Exception('Failed to fetch contacts from Firebase: HTTP ' . $status);
            }
            
            $pageResult = json_decode($response, true);
            
            if (isset($pageResult['documents'])) {
                $allFirestoreContacts = array_merge($allFirestoreContacts, $pageResult['documents']);
            }
            
            $nextPageToken = $pageResult['nextPageToken'] ?? null;
            
        } while ($nextPageToken);
        
        // Create result structure for compatibility
        $result = ['documents' => $allFirestoreContacts];
        $contacts = [];
        
        if (isset($result['documents'])) {
            foreach ($result['documents'] as $doc) {
                $contact = parseFirestoreDocument($doc);
                
                // Filter by session ID
                if (isset($contact['sessionId']) && $contact['sessionId'] === $sessionId) {
                    $contacts[] = [
                        'id' => basename($doc['name']),
                        'sessionId' => $contact['sessionId'] ?? '',
                        'contactNumber' => $contact['contactNumber'] ?? 0,
                        'timestamp' => $contact['timestamp'] ?? null,
                        'distance' => $contact['distance'] ?? 0,
                        'bearing' => $contact['bearing'] ?? 0,
                        'contactLocation' => $contact['contactLocation'] ?? [],
                        'observerLocation' => $contact['observerLocation'] ?? [],
                        'circumstances' => $contact['circumstances'] ?? [],
                        'contactLocationDetails' => $contact['contactLocationDetails'] ?? [],
                        'images' => $contact['images'] ?? [],
                        'protocol' => $contact['protocol'] ?? 'trajeto',
                        'userId' => $contact['userId'] ?? '',
                        'userName' => $contact['userName'] ?? ''
                    ];
                }
            }
        }
        
        // Sort contacts by contact number
        usort($contacts, function($a, $b) {
            return $a['contactNumber'] - $b['contactNumber'];
        });
        
        echo json_encode([
            'success' => true,
            'contacts' => $contacts,
            'count' => count($contacts)
        ]);
        
    } catch (Exception $e) {
        error_log('Error loading mobile contacts: ' . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao carregar contactos: ' . $e->getMessage()
        ]);
    }
    
    exit();
}

// Handle AJAX request for loading trajectories
if (isset($_GET['action']) && $_GET['action'] === 'load_trajectories') {
    ob_end_clean(); // Clear output buffer
    header('Content-Type: application/json');
    
    try {
        // Get admin access token for Firestore API
        global $database;
        $adminToken = $database->getAdminAccessToken();
        
        if (!$adminToken) {
            throw new Exception('Failed to get admin access token');
        }
        
        // Get current user ID and filtering parameters
        $currentUserId = $_SESSION['user']['id'] ?? null;
        $zoneId = $_GET['zoneId'] ?? null;
        $trajectoryId = $_GET['trajectoryId'] ?? null;
        
        if (!$zoneId && !$trajectoryId) {
            throw new Exception('Zone ID ou Trajectory ID é obrigatório');
        }
        
        // If we have a specific trajectory ID, fetch it directly
        if ($trajectoryId) {
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/gestorMobile_trajetos/" . $trajectoryId;
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status === 404) {
                throw new Exception('Trajeto não encontrado');
            } elseif ($status !== 200) {
                throw new Exception('Failed to fetch trajectory from Firebase: HTTP ' . $status);
            }
            
            $result = json_decode($response, true);
            $trajectory = parseFirestoreDocument($result);
            
            // Check if user has access to this trajectory
            if ($currentUserId && isset($trajectory['userId']) && $trajectory['userId'] !== $currentUserId) {
                throw new Exception('Acesso negado a este trajeto');
            }
            
            // Check if trajectory is completed
            if (!isset($trajectory['status']) || $trajectory['status'] !== 'completed') {
                throw new Exception('Trajeto não está completo');
            }
            
            $trajectoryData = [
                'id' => $trajectoryId,
                'sessionId' => $trajectory['sessionId'] ?? '',
                'userId' => $trajectory['userId'] ?? '',
                'userName' => $trajectory['userName'] ?? 'Utilizador',
                'userEmail' => $trajectory['userEmail'] ?? '',
                'zoneId' => $trajectory['zoneId'] ?? '',
                'zoneName' => $trajectory['zoneName'] ?? 'Zona não especificada',
                'protocol' => $trajectory['protocol'] ?? 'trajeto',
                'startTime' => $trajectory['startTime'] ?? null,
                'endTime' => $trajectory['endTime'] ?? null,
                'sessionDuration' => $trajectory['sessionDuration'] ?? 0,
                'totalDistance' => $trajectory['totalDistance'] ?? 0,
                'contactsCount' => $trajectory['contactsCount'] ?? 0,
                'observersCount' => $trajectory['observersCount'] ?? 0,
                'pathCoordinates' => $trajectory['pathCoordinates'] ?? [],
                'createdAt' => $trajectory['createdAt'] ?? null,
                'weather' => $trajectory['weather'] ?? null
            ];
            
            echo json_encode([
                'success' => true,
                'trajectory' => $trajectoryData
            ]);
            
        } else {
            // Query gestorMobile_trajetos collection by zone ID - get ALL documents with pagination
            $allFirestoreTrajectories = [];
            $nextPageToken = null;
            
            do {
                $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/gestorMobile_trajetos";
                if ($nextPageToken) {
                    $url .= "?pageToken=" . urlencode($nextPageToken);
                }
                
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $adminToken
                ]);
                
                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($status !== 200) {
                    throw new Exception('Failed to fetch trajectories from Firebase: HTTP ' . $status);
                }
                
                $pageResult = json_decode($response, true);
                
                if (isset($pageResult['documents'])) {
                    $allFirestoreTrajectories = array_merge($allFirestoreTrajectories, $pageResult['documents']);
                }
                
                $nextPageToken = $pageResult['nextPageToken'] ?? null;
                
            } while ($nextPageToken);
            
            // Create result structure for compatibility
            $result = ['documents' => $allFirestoreTrajectories];
            $trajectories = [];
            
            if (isset($result['documents'])) {
                foreach ($result['documents'] as $doc) {
                    $trajectory = parseFirestoreDocument($doc);
                    
                    // Filter by current user and zone ID
                    if ($currentUserId && isset($trajectory['userId']) && $trajectory['userId'] !== $currentUserId) {
                        continue;
                    }
                    
                    // Filter by zone ID
                    if (isset($trajectory['zoneId']) && $trajectory['zoneId'] !== $zoneId) {
                        continue;
                    }
                    
                    // Only include completed trajectories
                    if (isset($trajectory['status']) && $trajectory['status'] === 'completed') {
                        $trajectories[] = [
                            'id' => basename($doc['name']),
                            'sessionId' => $trajectory['sessionId'] ?? '',
                            'userId' => $trajectory['userId'] ?? '',
                            'userName' => $trajectory['userName'] ?? 'Utilizador',
                            'userEmail' => $trajectory['userEmail'] ?? '',
                            'zoneId' => $trajectory['zoneId'] ?? '',
                            'zoneName' => $trajectory['zoneName'] ?? 'Zona não especificada',
                            'protocol' => $trajectory['protocol'] ?? 'trajeto',
                            'startTime' => $trajectory['startTime'] ?? null,
                            'endTime' => $trajectory['endTime'] ?? null,
                            'sessionDuration' => $trajectory['sessionDuration'] ?? 0,
                            'totalDistance' => $trajectory['totalDistance'] ?? 0,
                            'contactsCount' => $trajectory['contactsCount'] ?? 0,
                            'observersCount' => $trajectory['observersCount'] ?? 0,
                            'pathCoordinates' => $trajectory['pathCoordinates'] ?? [],
                            'createdAt' => $trajectory['createdAt'] ?? null,
                            'weather' => $trajectory['weather'] ?? null
                        ];
                    }
                }
            }
            
            // Sort trajectories by creation date (newest first)
            usort($trajectories, function($a, $b) {
                $timeA = $a['startTime'] ?? $a['createdAt'];
                $timeB = $b['startTime'] ?? $b['createdAt'];
                
                if (!$timeA && !$timeB) return 0;
                if (!$timeA) return 1;
                if (!$timeB) return -1;
                
                return strtotime($timeB) - strtotime($timeA);
            });
            
            echo json_encode([
                'success' => true,
                'trajectories' => $trajectories,
                'count' => count($trajectories)
            ]);
        }
        
    } catch (Exception $e) {
        error_log('Error loading mobile trajectories: ' . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao carregar trajetos: ' . $e->getMessage()
        ]);
    }
    
    exit();
}

// Get trajectory ID from URL parameters (optional - for viewing specific trajectory)
$trajectoryId = $_GET['id'] ?? '';

// Clean any previous output
ob_end_clean();

/**
 * Parse Firestore document into PHP array
 */
function parseFirestoreDocument($doc) {
    $data = [];
    
    if (isset($doc['fields'])) {
        foreach ($doc['fields'] as $field => $value) {
            $data[$field] = convertFromFirestoreValue($value);
        }
    }
    
    return $data;
}

/**
 * Convert Firestore value to PHP value
 */
function convertFromFirestoreValue($field) {
    if (isset($field['stringValue'])) {
        return $field['stringValue'];
    } elseif (isset($field['integerValue'])) {
        return (int)$field['integerValue'];
    } elseif (isset($field['doubleValue'])) {
        return (float)$field['doubleValue'];
    } elseif (isset($field['booleanValue'])) {
        return $field['booleanValue'];
    } elseif (isset($field['timestampValue'])) {
        return $field['timestampValue'];
    } elseif (isset($field['arrayValue'])) {
        $array = [];
        if (isset($field['arrayValue']['values'])) {
            foreach ($field['arrayValue']['values'] as $value) {
                $array[] = convertFromFirestoreValue($value);
            }
        }
        return $array;
    } elseif (isset($field['mapValue'])) {
        $map = [];
        if (isset($field['mapValue']['fields'])) {
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $map[$key] = convertFromFirestoreValue($value);
            }
        }
        return $map;
    } elseif (isset($field['nullValue'])) {
        return null;
    }
    
    return null;
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trajeto GPS - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(10, 126, 164, 0.1);
            max-width: 400px;
            width: 90%;
        }
        
        .loading-spinner {
            position: relative;
            width: 60px;
            height: 60px;
            margin: 0 auto 1.5rem;
        }
        
        .spinner-ring {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 3px solid rgba(10, 126, 164, 0.1);
            border-top: 3px solid #0a7ea4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Map Container */
        #map {
            height: 600px;
            width: 100%;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        /* Trajectory List */
        .trajectory-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .trajectory-card:hover {
            border-color: #0a7ea4;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }

        .trajectory-card.selected {
            border-color: #0a7ea4;
            background: #f0f9ff;
        }

        .trajectory-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .trajectory-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .trajectory-date {
            font-size: 0.9rem;
            color: #6b7280;
            background: #f3f4f6;
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
        }

        .trajectory-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            background: #0a7ea4;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin: 0;
        }

        .stat-value {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .trajectory-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        .trajectory-zone {
            font-size: 0.9rem;
            color: #0a7ea4;
            font-weight: 500;
        }

        .trajectory-user {
            font-size: 0.85rem;
            color: #6b7280;
        }

        /* No data message */
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .no-data i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }

        /* Image Modal Styles */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            animation: fadeIn 0.3s ease;
        }

        .image-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
            text-align: center;
        }

        .image-modal img {
            max-width: 100%;
            max-height: 80vh;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .image-modal-title {
            color: white;
            font-size: 1.2rem;
            margin-top: 1rem;
            font-weight: 500;
        }

        .image-modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            z-index: 10001;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .image-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Contact image thumbnails in info windows */
        .contact-image-thumbnail {
            transition: all 0.3s ease;
        }

        .contact-image-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.3);
        }

        /* Detail sections */
        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #0a7ea4;
        }

        .detail-title {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .detail-content {
            color: #6b7280;
        }

        .detail-item {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .detail-item strong {
            color: #374151;
            font-weight: 600;
        }

        /* Info Cards Styles (from view.php) */
        .info-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.5rem !important;
            padding-bottom: 0.5rem !important;
            padding-top: 0rem !important;
            padding-left: 0rem !important;
            border-bottom: 1px solid #e5e7eb;
            text-align: left;
        }

        .card-header-icon {
            width: 32px;
            height: 32px;
            background: #0a7ea4;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .card-header-title {
            font-weight: 600;
            color: #374151;
            font-size: 0.95rem;
        }

        .info-content {
            display: flex;
            flex-direction: column;
            gap: 0.4rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .info-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #374151;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            width: 100%;
        }

        .info-row-icon {
            width: 20px;
            height: 20px;
            background: #f3f4f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0a7ea4;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .info-row-text {
            font-weight: 500;
            line-height: 1.3;
        }

        /* Timeline Styles */
        .timeline-container {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            border-left: 3px solid transparent;
        }

        .timeline-item.start {
            border-left-color: #22c55e;
        }

        .timeline-item.end {
            border-left-color: #ef4444;
        }

        .timeline-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .timeline-dot.start {
            background: #22c55e;
        }

        .timeline-dot.end {
            background: #ef4444;
        }

        .timeline-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 0.5rem;
            min-height: 0;
        }

        .timeline-label {
            font-size: 0.65rem;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            line-height: 1;
            margin-bottom: 0.1rem;
        }

        .timeline-time {
            font-size: 0.8rem;
            color: #374151;
            font-weight: 600;
            line-height: 1;
        }

        /* Stats Styles */
        .stats-grid {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            width: 100%;
        }

        .stat-label {
            color: #6b7280;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        .stat-label i {
            color: #0a7ea4;
            font-size: 0.8rem;
        }

        .stat-value {
            color: #0a7ea4;
            font-weight: 700;
            font-size: 1rem;
            background: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            border: 1px solid #0a7ea4;
            min-width: 30px;
            text-align: center;
        }

        /* Map Card Styles */
        .map-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .map-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .map-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: #374151;
            font-size: 1rem;
        }

        .map-stats {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .map-container {
            height: 500px;
            position: relative;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .map-container {
                height: 400px;
            }

            .image-modal-content {
                max-width: 95%;
                max-height: 95%;
            }

            .image-modal img {
                max-height: 70vh;
            }

            .detail-section {
                padding: 0.75rem;
            }

            .info-card {
                padding: 0.75rem;
            }

            .card-header-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .card-header-title {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-mobile-alt"></i>
                <span id="headerTrajetoName">Trajetos GPS</span>
            </h1>
        </div>
        <div class="header-actions">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Voltar
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <!-- Loading State -->
        <div id="loadingState" class="loading-overlay">
            <div class="loading-container">
                <div class="loading-animation">
                    <div class="loading-spinner">
                        <div class="spinner-ring"></div>
                    </div>
                    <div class="loading-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                </div>
                <div class="loading-text">
                    <h4>A carregar trajetos GPS</h4>
                    <p>Por favor aguarde enquanto carregamos os dados...</p>
                </div>
            </div>
        </div>

        <!-- GPS Trajectory View Content -->
        <div id="mainContent" style="display: none;">
            <!-- Info Cards Row -->
            <div class="row g-3 mb-3" style="align-items: stretch;">
                <!-- Basic Info Card -->
                <div class="col-lg-4 col-md-6 d-flex">
                    <div class="info-card w-100">
                        <div class="card-header">
                            <div class="card-header-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="card-header-title">Informações</div>
                        </div>
                        <div class="info-content">
                            <div class="info-row">
                                <div class="info-row-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="info-row-text" id="routeDistanceMain">-</div>
                            </div>
                            <div class="info-row">
                                <div class="info-row-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="info-row-text" id="createdDateMain">-</div>
                            </div>
                            <div class="info-row">
                                <div class="info-row-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="info-row-text">Trajeto GPS</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline Card -->
                <div class="col-lg-2 col-md-3 d-flex">
                    <div class="info-card w-100">
                        <div class="card-header">
                            <div class="card-header-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-header-title">Timeline</div>
                        </div>
                        <div class="timeline-container">
                            <div class="timeline-item start">
                                <div class="timeline-dot start"></div>
                                <div class="timeline-content">
                                    <div class="timeline-label">Início</div>
                                    <div class="timeline-time" id="startTimeCompact">--:--</div>
                                </div>
                            </div>
                            <div class="timeline-item end">
                                <div class="timeline-dot end"></div>
                                <div class="timeline-content">
                                    <div class="timeline-label">Fim</div>
                                    <div class="timeline-time" id="endTimeCompact">--:--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stats Card -->
                <div class="col-lg-3 col-md-6 d-flex">
                    <div class="info-card w-100">
                        <div class="card-header">
                            <div class="card-header-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="card-header-title">Estatísticas</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-label">
                                    <i class="fas fa-map-pin"></i>
                                    Pontos
                                </div>
                                <div class="stat-value" id="routePoints">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">
                                    <i class="fas fa-dove"></i>
                                    Contactos
                                </div>
                                <div class="stat-value" id="contactsCount">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">
                                    <i class="fas fa-binoculars"></i>
                                    Observadores
                                </div>
                                <div class="stat-value" id="observersCount">1</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weather Card -->
                <div class="col-lg-3 col-md-6 d-flex">
                    <div class="info-card w-100" id="weatherCard" style="display: none;">
                        <div class="card-header">
                            <div class="card-header-icon">
                                <i class="fas fa-cloud-sun"></i>
                            </div>
                            <div class="card-header-title">Meteorologia</div>
                        </div>
                        <div class="info-content" id="weatherContent">
                            <!-- Weather info will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map Card -->
            <div class="row">
                <div class="col-12">
                    <div class="map-card">
                        <div class="map-header">
                            <div class="map-title">
                                <i class="fas fa-map"></i>
                                <span>Mapa do Trajeto GPS</span>
                                <span class="map-stats">
                                    <i class="fas fa-map-pin"></i>
                                    <span id="routePointsDisplay">0</span> pontos
                                </span>
                            </div>
                        </div>
                        <div class="map-container">
                            <div id="map"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <div class="image-modal-content" onclick="event.stopPropagation()">
            <img id="modalImage" src="" alt="Contact Image">
            <div id="modalImageTitle" class="image-modal-title"></div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Google Maps API -->
    <script>
        let map;
        let trajectories = [];
        let selectedTrajectory = null;
        let routePath = null;
        let markers = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadMobileTrajectories();
        });

        // Initialize Google Map
        function initMap() {
            console.log('Initializing Google Maps...');
            try {
                // Check if map element exists
                const mapElement = document.getElementById('map');
                if (!mapElement) {
                    console.log('Map element not found, skipping initialization');
                    return;
                }

                // Initialize map with same options as view.php
                const mapOptions = {
                    zoom: 16,
                    center: { lat: 39.5, lng: -8.0 }, // Default center (Portugal)
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    mapTypeControl: true,
                    mapTypeControlOptions: {
                        style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                        position: google.maps.ControlPosition.TOP_RIGHT
                    },
                    zoomControl: true,
                    zoomControlOptions: {
                        position: google.maps.ControlPosition.RIGHT_CENTER
                    },
                    streetViewControl: false,
                    fullscreenControl: true,
                    fullscreenControlOptions: {
                        position: google.maps.ControlPosition.RIGHT_TOP
                    },
                    styles: [
                        {
                            featureType: "poi",
                            elementType: "labels",
                            stylers: [{ visibility: "off" }]
                        },
                        {
                            featureType: "poi.business",
                            stylers: [{ visibility: "off" }]
                        },
                        {
                            featureType: "poi.attraction",
                            stylers: [{ visibility: "off" }]
                        },
                        {
                            featureType: "poi.park",
                            elementType: "labels",
                            stylers: [{ visibility: "off" }]
                        }
                    ]
                };

                // Create map instance
                map = new google.maps.Map(mapElement, mapOptions);

                // Initialize route path with shadow effect (same as view.php)
                const shadowPath = new google.maps.Polyline({
                    path: [],
                    geodesic: true,
                    strokeColor: '#000000',
                    strokeOpacity: 0.4,
                    strokeWeight: 6,
                    zIndex: 1
                });
                shadowPath.setMap(map);

                routePath = new google.maps.Polyline({
                    path: [],
                    geodesic: true,
                    strokeColor: '#0a7ea4',
                    strokeOpacity: 1.0,
                    strokeWeight: 4,
                    zIndex: 2
                });
                routePath.setMap(map);

                window.shadowPath = shadowPath;
                
                console.log('Google Maps initialized successfully');
            } catch (error) {
                console.error('Error initializing Google Maps:', error);
            }
        }

        // Show error message
        function showError(message) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('mainContent').innerHTML = `
                <div class="no-data" style="text-align: center; padding: 3rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ef4444; margin-bottom: 1rem;"></i>
                    <h5>Erro</h5>
                    <p>${message}</p>
                    <a href="index.php" class="btn btn-secondary mt-3">
                        <i class="fas fa-arrow-left me-2"></i>Voltar às Zonas
                    </a>
                </div>
            `;
        }

        // Load GPS trajectories from gestorMobile_trajetos collection
        function loadMobileTrajectories() {
            const urlParams = new URLSearchParams(window.location.search);
            let zoneId = urlParams.get('zoneId');
            const trajectoryId = urlParams.get('id');
            
            console.log('URL Parameters:', { zoneId, trajectoryId });
            console.log('Full URL:', window.location.href);
            
            // If we have a specific trajectory ID but no zoneId, we'll fetch the trajectory first to get the zoneId
            if (trajectoryId && !zoneId) {
                console.log('No zoneId provided, will fetch trajectory first to get zoneId');
                loadSpecificTrajectory(trajectoryId);
                return;
            }
            
            if (!zoneId) {
                console.error('Missing zoneId parameter and no trajectory ID provided');
                showError('Zone ID ou Trajectory ID é obrigatório');
                return;
            }
            
            console.log('Fetching trajectories for zoneId:', zoneId);
            
            fetch('?action=load_trajectories&zoneId=' + encodeURIComponent(zoneId), {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    trajectories = data.trajectories;
                    
                    if (trajectories.length === 0) {
                        showNoTrajectories();
                        return;
                    }
                    
                    // If specific trajectory ID was provided, find and display it
                    let trajectoryToShow = null;
                    if (trajectoryId) {
                        trajectoryToShow = trajectories.find(t => t.id === trajectoryId);
                    }
                    
                    // If no specific trajectory or not found, show the first one
                    if (!trajectoryToShow && trajectories.length > 0) {
                        trajectoryToShow = trajectories[0];
                    }
                    
                    if (trajectoryToShow) {
                        displayTrajectory(trajectoryToShow);
                        loadContacts(trajectoryToShow.sessionId);
                    }
                    
                    // Hide loading, show content
                    document.getElementById('loadingState').style.display = 'none';
                    document.getElementById('mainContent').style.display = 'block';
                } else {
                    console.error('Error loading trajectories:', data.message);
                    showError('Erro ao carregar trajetos: ' + (data.message || 'Erro desconhecido'));
                }
            })
            .catch(error => {
                console.error('Error loading trajectories:', error);
                showError('Erro ao carregar trajetos. Verifique a sua conexão.');
            });
        }

        // Load specific trajectory by ID (when zoneId is not provided)
        function loadSpecificTrajectory(trajectoryId) {
            console.log('Loading specific trajectory:', trajectoryId);
            
            fetch('?action=load_trajectories&trajectoryId=' + encodeURIComponent(trajectoryId), {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success && data.trajectory) {
                    const trajectory = data.trajectory;
                    trajectories = [trajectory]; // Store as array for consistency
                    
                    displayTrajectory(trajectory);
                    loadContacts(trajectory.sessionId);
                    
                    // Hide loading, show content
                    document.getElementById('loadingState').style.display = 'none';
                    document.getElementById('mainContent').style.display = 'block';
                } else {
                    console.error('Error loading trajectory:', data.message);
                    showError('Erro ao carregar trajeto: ' + (data.message || 'Trajeto não encontrado'));
                }
            })
            .catch(error => {
                console.error('Error loading trajectory:', error);
                showError('Erro ao carregar trajeto. Verifique a sua conexão.');
            });
        }

        // Display single trajectory
        function displayTrajectory(trajectory) {
            selectedTrajectory = trajectory;
            
            // Update header title
            document.getElementById('headerTrajetoName').textContent = `Trajeto GPS - ${trajectory.zoneName || 'Zona'}`;
            
            // Update basic info
            document.getElementById('routeDistanceMain').textContent = 
                trajectory.totalDistance ? `${(trajectory.totalDistance / 1000).toFixed(2)} km` : 'N/A';
            
            document.getElementById('createdDateMain').textContent = 
                trajectory.startTime ? formatDate(trajectory.startTime) : 'N/A';
            
            // Update timeline
            if (trajectory.startTime) {
                document.getElementById('startTimeCompact').textContent = formatTime(trajectory.startTime);
            }
            if (trajectory.endTime) {
                document.getElementById('endTimeCompact').textContent = formatTime(trajectory.endTime);
            }
            
            // Update stats
            document.getElementById('routePoints').textContent = trajectory.pathCoordinates ? trajectory.pathCoordinates.length : 0;
            document.getElementById('routePointsDisplay').textContent = trajectory.pathCoordinates ? trajectory.pathCoordinates.length : 0;
            document.getElementById('contactsCount').textContent = trajectory.contactsCount || 0;
            document.getElementById('observersCount').textContent = trajectory.observersCount || 1;
            
            // Update weather if available
            if (trajectory.weather) {
                displayWeather(trajectory.weather);
            }
            
            // Display on map
            displayTrajectoryOnMap(trajectory);
        }

        // Show no trajectories message
        function showNoTrajectories() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('mainContent').innerHTML = `
                <div class="no-data" style="text-align: center; padding: 3rem;">
                    <i class="fas fa-mobile-alt" style="font-size: 3rem; color: #6b7280; margin-bottom: 1rem;"></i>
                    <h5>Nenhum trajeto GPS encontrado</h5>
                    <p>Ainda não foram criados trajetos GPS para esta zona</p>
                    <a href="index.php" class="btn btn-secondary mt-3">
                        <i class="fas fa-arrow-left me-2"></i>Voltar às Zonas
                    </a>
                </div>
            `;
        }

        // Display weather information
        function displayWeather(weather) {
            const weatherCard = document.getElementById('weatherCard');
            const weatherContent = document.getElementById('weatherContent');
            
            if (!weather) {
                weatherCard.style.display = 'none';
                return;
            }
            
            weatherCard.style.display = 'block';
            
            weatherContent.innerHTML = `
                <div class="info-row">
                    <div class="info-row-icon">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <div class="info-row-text">${weather.temperature || 'N/A'}°C</div>
                </div>
                <div class="info-row">
                    <div class="info-row-icon">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div class="info-row-text">${weather.humidity || 'N/A'}%</div>
                </div>
                <div class="info-row">
                    <div class="info-row-icon">
                        <i class="fas fa-wind"></i>
                    </div>
                    <div class="info-row-text">${weather.windSpeed || 'N/A'} km/h</div>
                </div>
            `;
        }

        // Format date for display
        function formatDate(dateString) {
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('pt-PT', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                });
            } catch (e) {
                return 'N/A';
            }
        }

        // Format time for display
        function formatTime(dateString) {
            try {
                const date = new Date(dateString);
                return date.toLocaleTimeString('pt-PT', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (e) {
                return '--:--';
            }
        }
        // Display trajectory on map (same as view.php)
        function displayTrajectoryOnMap(trajectory) {
            if (!map || !trajectory.pathCoordinates || trajectory.pathCoordinates.length === 0) {
                return;
            }

            // Clear existing markers
            clearMarkers();

            // Convert path coordinates to Google Maps format and create markers
            const routeCoordinates = trajectory.pathCoordinates.map((coord, index) => {
                const position = { lat: coord.latitude, lng: coord.longitude };
                
                // Create marker icon based on position (same as view.php)
                let markerIcon;
                if (index === 0) {
                    // Start point - green with floating label
                    markerIcon = {
                        path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                        fillColor: '#22c55e',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                        scale: 0.7,
                        anchor: new google.maps.Point(0, 8),
                        labelOrigin: new google.maps.Point(0, -40)
                    };
                } else if (index === trajectory.pathCoordinates.length - 1 && trajectory.pathCoordinates.length > 1) {
                    // End point - red with floating label
                    markerIcon = {
                        path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                        fillColor: '#dc2626',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2,
                        scale: 0.7,
                        anchor: new google.maps.Point(0, 8),
                        labelOrigin: new google.maps.Point(0, -40)
                    };
                } else {
                    // Regular point - blue circle
                    markerIcon = {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#0a7ea4',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 3
                    };
                }

                // Create marker for each point
                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: index === 0 ? 'Início' : (index === trajectory.pathCoordinates.length - 1 && trajectory.pathCoordinates.length > 1 ? 'Fim' : `Ponto ${index + 1}`),
                    icon: markerIcon,
                    label: index === 0 ? {
                        text: 'INÍCIO',
                        fontSize: '9px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    } : (index === trajectory.pathCoordinates.length - 1 && trajectory.pathCoordinates.length > 1 ? {
                        text: 'FIM',
                        fontSize: '9px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    } : {
                        text: (index + 1).toString(),
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    }),
                    zIndex: index === 0 || (index === trajectory.pathCoordinates.length - 1 && trajectory.pathCoordinates.length > 1) ? 1000 : 100
                });

                // Add ground point for start and end (same as view.php)
                if (index === 0 || (index === trajectory.pathCoordinates.length - 1 && trajectory.pathCoordinates.length > 1)) {
                    const groundMarker = new google.maps.Marker({
                        position: position,
                        map: map,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: index === 0 ? '#22c55e' : '#dc2626',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2
                        },
                        label: {
                            text: (index + 1).toString(),
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        },
                        title: index === 0 ? 'Início Ground' : 'Fim Ground',
                        zIndex: 50
                    });
                    markers.push(groundMarker);
                }
                
                markers.push(marker);
                return position;
            });

            // Update route path
            if (routePath) {
                routePath.setPath(routeCoordinates);
            }
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Fit map to show all points
            if (routeCoordinates.length > 0) {
                const bounds = new google.maps.LatLngBounds();
                routeCoordinates.forEach(coord => bounds.extend(coord));
                map.fitBounds(bounds);
                
                // Add some padding
                setTimeout(() => {
                    if (routeCoordinates.length === 1) {
                        map.setZoom(15);
                    }
                }, 100);
            }
        }

        // Load contacts for trajectory
        function loadContacts(sessionId) {
            if (!sessionId) return;

            fetch(`?action=load_contacts&sessionId=${encodeURIComponent(sessionId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.contacts) {
                        displayContactsOnMap(data.contacts);
                    }
                })
                .catch(error => {
                    console.error('Error loading contacts:', error);
                });
        }

        // Display contacts on map (same as view.php)
        function displayContactsOnMap(contacts) {
            if (!map || !contacts || contacts.length === 0) return;

            contacts.forEach((contact, index) => {
                if (contact.contactLocation && contact.contactLocation.latitude && contact.contactLocation.longitude) {
                    const position = {
                        lat: contact.contactLocation.latitude,
                        lng: contact.contactLocation.longitude
                    };

                    // Create canvas for contact marker (same as view.php)
                    const canvas = document.createElement('canvas');
                    canvas.width = 32;
                    canvas.height = 32;
                    const ctx = canvas.getContext('2d');

                    // Draw green circle background
                    ctx.beginPath();
                    ctx.arc(16, 16, 16, 0, 2 * Math.PI);
                    ctx.fillStyle = '#16a34a';  // Keep the green color for consistency
                    ctx.fill();
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // Load and draw dove icon
                    const doveImg = new Image();
                    doveImg.onload = function() {
                        // Create a temporary canvas to process the dove icon
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = 16;
                        tempCanvas.height = 16;
                        const tempCtx = tempCanvas.getContext('2d');
                        
                        // Draw the dove icon on temp canvas
                        tempCtx.drawImage(doveImg, 0, 0, 16, 16);
                        
                        // Get image data to process pixels
                        const imageData = tempCtx.getImageData(0, 0, 16, 16);
                        const data = imageData.data;
                        
                        // Convert non-transparent pixels to white
                        for (let i = 0; i < data.length; i += 4) {
                            if (data[i + 3] > 0) { // If pixel is not transparent
                                data[i] = 255;     // Red = 255 (white)
                                data[i + 1] = 255; // Green = 255 (white)
                                data[i + 2] = 255; // Blue = 255 (white)
                            }
                        }
                        
                        // Put the processed image data back
                        tempCtx.putImageData(imageData, 0, 0);
                        
                        // Draw the white dove icon on the main canvas
                        ctx.drawImage(tempCanvas, 8, 8);
                        
                        // Create marker with the canvas image
                        const contactMarker = new google.maps.Marker({
                            position: position,
                            map: map,
                            icon: {
                                url: canvas.toDataURL(),
                                scaledSize: new google.maps.Size(32, 32),
                                anchor: new google.maps.Point(16, 16)
                            },
                            title: `Contacto ${contact.contactNumber}: ${contact.circumstances || 'Observação'}`,
                            zIndex: 500
                        });

                        // Add info window for contact details
                        const infoWindow = new google.maps.InfoWindow({
                            content: createContactInfoContent(contact)
                        });

                        // Add click listener to show info window
                        contactMarker.addListener('click', function() {
                            infoWindow.open(map, contactMarker);
                        });

                        markers.push(contactMarker);
                        console.log(`Successfully created contact marker ${contact.contactNumber} at position:`, position);
                    };
                    doveImg.src = '../../assets/images/icons/dove-icon.png';
                } else {
                    console.log(`Contact ${contact.contactNumber} missing coordinates:`, contact);
                }
            });

            console.log(`Added ${contacts.length} contact markers to map`);
            console.log('Total markers on map now:', markers.length);
        }

        // Utility function to normalize bearing values (same as mobile app)
        function normalizeBearing(bearing) {
            if (bearing === undefined || bearing === null) return 'N/A';
            
            let normalizedBearing = Number(bearing);
            if (isNaN(normalizedBearing)) return 'N/A';
            
            // Handle very large numbers that might be precision errors
            if (Math.abs(normalizedBearing) > 100000) {
                // Likely a precision error, try to extract meaningful digits
                normalizedBearing = normalizedBearing % 360;
            }
            
            // If bearing seems to be in radians (> 2π or < -2π), convert to degrees
            if (Math.abs(normalizedBearing) > 6.28) {
                normalizedBearing = (normalizedBearing * 180 / Math.PI);
            }
            
            // Normalize to 0-360 range
            while (normalizedBearing < 0) {
                normalizedBearing += 360;
            }
            while (normalizedBearing >= 360) {
                normalizedBearing -= 360;
            }
            
            return Math.round(normalizedBearing) + '°';
        }

        // Create contact info window content with enhanced details and thumbnails
        function createContactInfoContent(contact) {
            // Parse circumstances (matching mobile app exactly)
            const circumstances = [];
            if (contact.circumstances) {
                if (contact.circumstances.rolaEmVoo) circumstances.push('Rola em voo');
                if (contact.circumstances.rolaAdultaCantando) circumstances.push('Rola adulta cantando');
                if (contact.circumstances.adultoPousado) circumstances.push('Adulto pousado');
                if (contact.circumstances.adultoEmDisplay) circumstances.push('Adulto em display');
                if (contact.circumstances.adultoAIncubar) circumstances.push('Adulto a incubar');
                if (contact.circumstances.ovos) circumstances.push('Ovos');
                if (contact.circumstances.crias) circumstances.push('Crias');
                if (contact.circumstances.juvenile) circumstances.push('Juvenil');
                if (contact.circumstances.nichoOcupado) circumstances.push('Nicho ocupado');
                if (contact.circumstances.ninhoVazio) circumstances.push('Ninho vazio');
                if (contact.circumstances.outraQual && contact.circumstances.outraQualText) {
                    circumstances.push(contact.circumstances.outraQualText);
            }
            }

            // Parse location details (matching mobile app exactly)
            const locationDetails = [];
            if (contact.contactLocationDetails) {
                if (contact.contactLocationDetails.pontoDeAgua) locationDetails.push('Ponto de água');
                if (contact.contactLocationDetails.arvore) locationDetails.push('Árvore');
                if (contact.contactLocationDetails.arbusto) locationDetails.push('Arbusto');
                if (contact.contactLocationDetails.clareira) locationDetails.push('Clareira');
                if (contact.contactLocationDetails.parcelaAgricola) locationDetails.push('Parcela agrícola');
                if (contact.contactLocationDetails.outraQual && contact.contactLocationDetails.outraQualText) {
                    locationDetails.push(contact.contactLocationDetails.outraQualText);
                }
            }

            // Create image thumbnails HTML
            let thumbnailsHtml = '';
            if (contact.images && contact.images.length > 0) {
                thumbnailsHtml = `
                    <div style="margin-top: 10px;">
                        <p style="margin: 5px 0; font-weight: bold; color: #374151;">
                            <i class="fas fa-camera" style="color: #6b7280; margin-right: 5px;"></i>
                            Fotos (${contact.images.length})
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                            ${contact.images.map((imageUrl, index) => `
                                <div style="position: relative; cursor: pointer;" onclick="openImageModal('${imageUrl}', 'Contacto ${contact.contactNumber} - Foto ${index + 1}')">
                                    <img src="${imageUrl}" 
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; border: 1px solid #e5e7eb;"
                                         alt="Foto ${index + 1}"
                                         onerror="this.style.display='none'">
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            return `
                <div style="max-width: 350px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <h6 style="margin: 0 0 12px 0; color: #0a7ea4; font-size: 16px; font-weight: 600;">
                        <i class="fas fa-dove" style="color: #0a7ea4; margin-right: 6px;"></i>
                        Contacto ${contact.contactNumber}
                    </h6>
                    
                    <div style="display: flex; gap: 15px; margin-bottom: 8px;">
                        ${contact.distance ? `
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-road" style="color: #6b7280; font-size: 12px;"></i>
                                <span style="font-size: 13px; color: #374151;">${contact.distance}m</span>
                            </div>
                        ` : ''}
                        ${contact.bearing !== undefined && contact.bearing !== null ? `
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-compass" style="color: #6b7280; font-size: 12px;"></i>
                                <span style="font-size: 13px; color: #374151;">${normalizeBearing(contact.bearing)}</span>
                            </div>
                        ` : ''}
                    </div>
                    
                    ${circumstances.length > 0 ? `
                        <div style="margin-bottom: 8px;">
                            <p style="margin: 0 0 3px 0; font-weight: 600; color: #374151; font-size: 12px;">Circunstâncias:</p>
                            <p style="margin: 0; color: #6b7280; font-size: 13px; line-height: 1.4;">${circumstances.join(', ')}</p>
                        </div>
                    ` : ''}
                    
                    ${locationDetails.length > 0 ? `
                        <div style="margin-bottom: 8px;">
                            <p style="margin: 0 0 3px 0; font-weight: 600; color: #374151; font-size: 12px;">Local:</p>
                            <p style="margin: 0; color: #6b7280; font-size: 13px; line-height: 1.4;">${locationDetails.join(', ')}</p>
                        </div>
                    ` : ''}
                    
                    ${thumbnailsHtml}
                </div>
            `;
        }

        // Clear all markers
        function clearMarkers() {
            markers.forEach(marker => marker.setMap(null));
            markers = [];
        }


        // Image modal functions (for contact photos)
        function openImageModal(imageUrl, title) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalImageTitle');
            
            modalImage.src = imageUrl;
            modalTitle.textContent = title;
            modal.style.display = 'block';
            
            // Prevent body scrolling when modal is open
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
            
            // Restore body scrolling
            document.body.style.overflow = 'auto';
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
    
    <!-- Google Maps API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&libraries=geometry&callback=initMap" async defer></script>
</body>
</html> 