<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Get trajeto ID from URL parameters
$trajetoId = $_GET['id'] ?? '';

if (empty($trajetoId)) {
    ob_end_clean();
    header('Location: index.php');
    exit();
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Trajeto - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        /* Modern Loading Animation - Same as view.php */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(10, 126, 164, 0.1);
            max-width: 400px;
            width: 90%;
        }
        
        .loading-animation {
            position: relative;
            display: inline-block;
            margin-bottom: 1.5rem;
        }
        
        .loading-spinner {
            position: relative;
            width: 60px;
            height: 60px;
            margin: 0 auto;
        }
        
        .spinner-ring {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 3px solid rgba(10, 126, 164, 0.1);
            border-top: 3px solid #0a7ea4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .loading-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2rem;
            color: #0a7ea4;
            animation: pulse 1.5s ease-in-out infinite;
            z-index: 2;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-text h4 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.25rem;
        }
        
        .loading-text p {
            color: #6b7280;
            margin: 0;
            font-size: 0.9rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { 
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            50% { 
                opacity: 0.7;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        /* Info Cards Styles - Same as view.php */
        .info-card {
            background: white;
            border-radius: 8px;
            padding: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 0.5rem;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 120px;
            max-height: 140px;
        }

        .info-card .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 0.4rem;
            margin-bottom: 0.4rem !important;
            padding-bottom: 0.3rem !important;
            padding-top: 0rem !important;
            padding-left: 0rem !important;
            padding-right: 0rem !important;
            border-bottom: 1px solid #e5e7eb;
            text-align: left;
            position: relative;
            background: none !important;
        }

        .info-card .card-header-icon {
            width: 24px;
            height: 24px;
            background: #0a7ea4;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
        }

        .info-card .card-header-title {
            font-weight: 600;
            color: #374151;
            font-size: 0.85rem;
        }

        .card-header-left {
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        /* Editable Card Styles */
        .editable-card {
            cursor: pointer;
            position: relative;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            background: white;
        }

        .editable-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(10, 126, 164, 0.15);
            border-color: #0a7ea4;
            background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
        }

        .edit-hint {
            background: #0a7ea4;
            color: white;
            font-size: 0.65rem;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 12px;
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(10, 126, 164, 0.3);
            margin-left: auto;
        }

        .editable-card:hover .edit-hint {
            background: #0369a1;
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(10, 126, 164, 0.4);
        }

        .editable-card::after {
            content: '✏️';
            position: absolute;
            bottom: 6px;
            right: 6px;
            font-size: 0.8rem;
            opacity: 0.4;
            transition: all 0.3s ease;
        }

        .editable-card:hover::after {
            opacity: 0.8;
            transform: scale(1.2);
        }

        /* Info Content Styles */
        .info-content {
            display: flex;
            flex-direction: column;
            gap: 0.4rem;
            flex: 1;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
        }

        .info-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #374151;
            padding: 0.5rem;
            background: #f9fafb;
            border-radius: 6px;
            width: 100%;
        }

        .info-row-icon {
            width: 20px;
            height: 20px;
            background: #f3f4f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0a7ea4;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .info-row-text {
            font-weight: 500;
            line-height: 1.3;
        }

        /* Timeline Styles */
        .timeline-container {
            display: flex;
            flex-direction: row;
            gap: 0.8rem;
            flex: 1;
            justify-content: space-around;
            align-items: center;
            text-align: center;
            flex-wrap: wrap;
        }

        .timeline-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.75rem;
            flex: 1;
            min-width: 60px;
        }

        .timeline-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .timeline-dot.start {
            background: #16a34a;
        }

        .timeline-dot.middle {
            background: #f59e0b;
        }

        .timeline-dot.end {
            background: #dc2626;
        }

        .timeline-content {
            display: flex;
            flex-direction: column;
            gap: 0.1rem;
            align-items: center;
            text-align: center;
        }

        .timeline-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.65rem;
            line-height: 1;
        }

        .timeline-time {
            color: #0a7ea4;
            font-size: 0.75rem;
            font-weight: 600;
            line-height: 1;
        }

        /* Weather Container */
        .weather-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            text-align: center;
            gap: 0.3rem;
        }

        .weather-icon-large {
            font-size: 1.5rem;
            line-height: 1;
        }

        .weather-text-large {
            font-size: 0.75rem;
            font-weight: 600;
            color: #374151;
            line-height: 1.1;
        }

        /* Observers Container */
        .observers-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            text-align: center;
            gap: 0.15rem;
        }

        .observers-count {
            font-size: 1.8rem;
            font-weight: 700;
            color: #0a7ea4;
            line-height: 1;
        }

        .observers-label {
            font-size: 0.7rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Weather Options Styles */
        .weather-options {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .weather-options .form-check {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            min-height: 52px;
            margin: 0;
        }

        .weather-options .form-check:hover {
            background: rgba(10, 126, 164, 0.02);
            border-color: rgba(10, 126, 164, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .weather-options .form-check-input {
            position: relative;
            margin: 0 0.75rem 0 0;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .weather-options .form-check-input:checked {
            background-color: #0a7ea4 !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }

        .weather-options .form-check-input:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 0.125rem rgba(10, 126, 164, 0.25) !important;
        }

        .weather-options .form-check-input:checked + .form-check-label {
            color: #0a7ea4 !important;
            font-weight: 600 !important;
        }

        .weather-options .form-check:has(.form-check-input:checked) {
            background: rgba(10, 126, 164, 0.05) !important;
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1) !important;
            transform: translateY(-1px) !important;
        }

        .weather-options .form-check-label {
            cursor: pointer;
            display: flex;
            align-items: center;
            margin-bottom: 0;
            font-size: 0.9rem;
            margin-left: 0;
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
    </style>
</head>
<body>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-edit"></i>
                Editar
                <span class="trajeto-name" id="headerTrajetoName">A carregar...</span>
            </h1>
        </div>
        <div class="header-actions">

            <button type="button" class="btn btn-help" onclick="showTrajetoHelp()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div style="padding-top:20px">

            <!-- Loading State -->
            <div id="loadingState" class="loading-overlay">
                <div class="loading-container">
                    <div class="loading-animation">
                        <div class="loading-spinner">
                            <div class="spinner-ring"></div>
                </div>
                    </div>
                    <div class="loading-text">
                        <h4>A carregar trajeto</h4>
                        <p>Por favor aguarde enquanto carregamos os dados...</p>
                    </div>
                </div>
            </div>

            <!-- Edit Choice Modal Content (shown after loading) -->
            <div id="editChoiceContent" style="display: none;">
                <div class="row justify-content-center">
                    <div class="col-md-10 col-lg-8 col-xl-6">
                        <div class="edit-choice-card">
                            <!-- Header with gradient -->
                            <div class="edit-choice-header">
                                <div class="header-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <h3 class="header-title">Como pretende editar o trajeto?</h3>
                                <p class="header-subtitle">Escolha como pretende proceder com a edição do trajeto existente.</p>
                            </div>
                            
                            <!-- Choice buttons -->
                            <div class="edit-choice-body">
                                <div class="choice-options">
                                    <button type="button" class="choice-btn choice-btn-primary" onclick="editCurrentTrajeto()">
                                        <div class="choice-btn-icon">
                                            <i class="fas fa-pencil-alt"></i>
                                        </div>
                                        <div class="choice-btn-content">
                                            <h5 class="choice-btn-title">Editar Trajeto Atual</h5>
                                            <p class="choice-btn-description">Modificar pontos do trajeto existente no mapa</p>
                                        </div>
                                        <div class="choice-btn-arrow">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </button>
                                    
                                    <button type="button" class="choice-btn choice-btn-warning" onclick="startOverTrajeto()">
                                        <div class="choice-btn-icon">
                                            <i class="fas fa-redo"></i>
                                        </div>
                                        <div class="choice-btn-content">
                                            <h5 class="choice-btn-title">Começar do início</h5>
                                            <p class="choice-btn-description">Criar novo trajeto substituindo o atual</p>
                                        </div>
                                        <div class="choice-btn-arrow">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </button>
                                </div>
                                
                                <!-- Safety notice -->
                                <div class="safety-notice">
                                    <div class="safety-notice-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="safety-notice-text">
                                        <strong>Dados protegidos:</strong> Os dados originais são preservados até confirmar as alterações
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trajeto Edit Form -->
            <div id="mainContent" style="display: none;">
                
                <!-- Combined Card - Full Width -->
                <div class="col-12">
                    <div class="trajeto-card">
                        <!-- Stats Header -->
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>
                                Informações do Trajeto
                            </h5>
                            <span class="info-badge">
                                <i class="fas fa-mouse-pointer me-1"></i>
                                Clique no mapa para editar pontos do trajeto
                            </span>
                        </div>
                        
                        <!-- Stats Section -->
                        <div class="stats-section">
                            <div class="stats-flex-container">
                                <div class="stat-item-wrapper stat-address-wrapper">
                                    <div class="stat-item stat-item-address">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <span class="stat-label">INÍCIO:</span>
                                        <span class="stat-value" id="startingAddress">A carregar...</span>
                                    </div>
                                </div>
                                <div class="stat-item-wrapper stat-distance-wrapper">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-route me-1"></i>
                                        <span class="stat-label">DISTÂNCIA:</span>
                                        <span class="stat-value" id="routeDistance">0 km</span>
                                        <div id="distanceValidation" style="display: none;"></div>
                                    </div>
                                </div>
                                <div class="stat-item-wrapper stat-points-wrapper">
                                    <div class="stat-item stat-item-compact">
                                        <i class="fas fa-map-pin me-1"></i>
                                        <span class="stat-label">PONTOS:</span>
                                        <span class="stat-value" id="routePoints">0</span>
                                        <span class="stat-contacts" id="contactsCount" style="display: none;">
                                            <i class="fas fa-dove ms-2 me-1" style="color: #0a7ea4;"></i>
                                            <span class="stat-label">CONTACTOS:</span>
                                            <span class="stat-value" id="contactsCountValue">0</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Map with Floating Controls -->
                        <div class="map-container">
                            <div id="map" class="google-map"></div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="card-actions">
                            <form id="trajetoForm">
                                <input type="hidden" id="trajetoId" value="<?php echo htmlspecialchars($trajetoId); ?>">
                                <input type="hidden" id="zoneId" value="">
                                <input type="hidden" id="routeCoordinates" value="">
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn-outline-secondary" onclick="cancelEdit()">
                                        <i class="fas fa-times"></i>
                                        Cancelar
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearRoute()">
                                        <i class="fas fa-eraser"></i>
                                        Limpar Trajeto
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="undoLastPoint()" id="undoBtn" disabled>
                                        <i class="fas fa-undo"></i>
                                        Desfazer Último
                                    </button>
                                                        <button type="button" class="btn btn-info text-white" onclick="toggleAddContactMode()">
                        <i class="fas fa-dove"></i>
                        Adicionar Contacto
                                    </button>
                                    <button type="submit" class="btn btn-success" id="saveTrajetoBtn" disabled>
                                        <i class="fas fa-save"></i>
                                        Guardar Alterações
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Editable Info Cards Row - Moved Below Map -->
                <div class="row g-3 mt-3" style="align-items: stretch;">
                    <!-- Timeline Card (Editable) -->
                    <div class="col-lg-4 col-md-4 d-flex">
                        <div class="info-card w-100 editable-card" onclick="editTimeline()">
                            <div class="card-header">
                                <div class="card-header-left">
                                    <div class="card-header-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="card-header-title">Timeline</div>
                                    <div class="trajeto-date-display" id="trajetoDateDisplay">
                                        --/--/----
                                    </div>
                                </div>
                                <div class="card-header-right">
                                    <div class="edit-hint">
                                        EDITAR
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-container">
                                <div class="timeline-item start">
                                    <div class="timeline-dot start"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-label">Início</div>
                                        <div class="timeline-time" id="startTimeCompact">--:--</div>
                                    </div>
                                </div>
                                <div class="timeline-item middle">
                                    <div class="timeline-dot middle"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-label">Meio</div>
                                        <div class="timeline-time" id="middleTimeCompact">--:--</div>
                                    </div>
                                </div>
                                <div class="timeline-item end">
                                    <div class="timeline-dot end"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-label">Fim</div>
                                        <div class="timeline-time" id="endTimeCompact">--:--</div>
                                    </div>
                        </div>
                    </div>
                </div>
            </div>

                    <!-- Weather Card (Editable) -->
                    <div class="col-lg-4 col-md-4 d-flex">
                        <div class="info-card w-100 editable-card" onclick="editWeather()">
                            <div class="card-header">
                                <div class="card-header-left">
                                    <div class="card-header-icon">
                                        <i class="fas fa-cloud-sun"></i>
                                    </div>
                                    <div class="card-header-title">Condições</div>
                                </div>
                                <div class="edit-hint">
                                    EDITAR
                                </div>
                            </div>
                            <div class="weather-container">
                                <div class="weather-icon-large" id="weatherIconCompact"><i class="fas fa-cloud-sun text-info"></i></div>
                                <div class="weather-text-large" id="weatherTextCompact">Clique para definir</div>
                            </div>
                        </div>
                    </div>

                    <!-- Observers Card (Editable) -->
                    <div class="col-lg-4 col-md-4 d-flex">
                        <div class="info-card w-100 editable-card" onclick="editObservers()">
                            <div class="card-header">
                                <div class="card-header-left">
                                    <div class="card-header-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="card-header-title">Observadores</div>
                                </div>
                                <div class="edit-hint">
                                    EDITAR
                                </div>
                            </div>
                            <div class="observers-container">
                                <div class="observers-count" id="observersCountDisplay">-</div>
                                <div class="observers-label">observadores</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Edit Details Modal -->
    <div class="modal fade" id="editDetailsModal" tabindex="-1" aria-labelledby="editDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editDetailsModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        Detalhes do Trajeto
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="trajetoName" class="form-label">Nome do Trajeto</label>
                                <input type="text" class="form-control" id="trajetoName" required 
                                       placeholder="Ex: Rota Principal Norte">
                                <div class="form-text">Escolha um nome descritivo para o trajeto</div>
                            </div>

                            <div class="mb-3">
                                <label for="trajetoDifficulty" class="form-label">Dificuldade</label>
                                <select class="form-select" id="trajetoDifficulty" required>
                                    <option value="">Selecione a dificuldade</option>
                                    <option value="facil">Fácil</option>
                                    <option value="medio">Médio</option>
                                    <option value="dificil">Difícil</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="trajetoStatus" class="form-label">Estado</label>
                                <select class="form-select" id="trajetoStatusSelect" required>
                                    <option value="draft">Rascunho</option>
                                    <option value="completed">Concluído</option>
                                    <option value="review">Em Revisão</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="trajetoDescription" class="form-label">Descrição</label>
                                <textarea class="form-control" id="trajetoDescription" rows="5" 
                                          placeholder="Descreva o trajeto, pontos de interesse, etc."></textarea>
                            </div>

                            <div class="route-stats">
                                <div class="stat-item">
                                    <label>Criado em</label>
                                    <span id="createdDate">-</span>
                                </div>
                                <div class="stat-item">
                                    <label>Última atualização</label>
                                    <span id="updatedDate">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="saveTrajetoDetails()">
                        <i class="fas fa-save me-1"></i>
                        Guardar Detalhes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Choice Modal (for Start Over option) -->
    <div class="modal fade" id="locationChoiceModal" tabindex="-1" aria-labelledby="locationChoiceModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content location-choice-modal">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationChoiceModalLabel">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Definir Localização Inicial
                    </h5>
                    <div class="step-indicator">
                        <span class="step-number">1</span>
                        <span class="step-text">de 5</span>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-location-arrow text-primary" style="font-size: 3rem; opacity: 0.8;"></i>
                    </div>
                    <h6 class="mb-3">Como pretende definir a localização inicial do trajeto?</h6>
                    <div class="mx-auto mb-4" style="max-width: 500px;">
                        <p class="text-muted small">
                            Escolha uma das opções abaixo para definir onde pretende começar o trajeto.
                        </p>
                    </div>
                    
                    <div class="d-grid gap-3 mx-auto" style="max-width: 350px;">
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="useCurrentLocation()">
                            <i class="fas fa-crosshairs me-2"></i>
                            <span>Usar Localização Atual</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="showLocationSearch()">
                            <i class="fas fa-search me-2"></i>
                            <span>Pesquisar Localização</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="useManualLocation()">
                            <i class="fas fa-map me-2"></i>
                            <span>Definir Manualmente no Mapa</span>
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Pode sempre ajustar a localização clicando no mapa
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Search Modal -->
    <div class="modal fade" id="locationSearchModal" tabindex="-1" aria-labelledby="locationSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content location-choice-modal">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationSearchModalLabel">
                        <i class="fas fa-search me-2"></i>
                        Pesquisar Localização
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body py-4">
                    <div class="mb-3">
                        <label for="locationSearchInput" class="form-label">Digite o nome da cidade, vila ou localização:</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                            </span>
                            <input type="text" class="form-control form-control-lg" id="locationSearchInput" 
                                   placeholder="Ex: Lisboa, Porto, Coimbra..." 
                                   onkeypress="handleLocationSearchEnter(event)">
                            <button class="btn btn-primary" type="button" onclick="searchLocation()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Pode pesquisar por cidade, código postal ou morada específica
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg location-choice-btn" onclick="searchLocation()">
                            <i class="fas fa-search me-2"></i>
                            <span>Pesquisar e Centrar Mapa</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg location-choice-btn" data-bs-dismiss="modal" onclick="showLocationChoiceModal()">
                            <i class="fas fa-arrow-left me-2"></i>
                            <span>Voltar às Opções</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Edit Modal -->
    <div class="modal fade" id="contactEditModal" tabindex="-1" aria-labelledby="contactEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" style="max-width: 900px;">
            <div class="modal-content" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; border-radius: 16px 16px 0 0; padding: 0.75rem 1.5rem; border: none;">
                    <h5 class="modal-title" id="contactEditModalLabel" style="font-weight: 600; font-size: 1.1rem; margin: 0; display: flex; align-items: center;">
                        <i class="fas fa-dove me-2"></i>
                        Novo Contacto
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" style="opacity: 0.8; font-size: 1rem;" onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.8'"></button>
                </div>
                <div class="modal-body" style="padding: 1.5rem; background-color: #f8fafc;">
                    <form id="contactEditForm">
                        <input type="hidden" id="contactEditIndex" value="">
                        
                        <!-- Time Section -->
                        <div class="form-section mb-3">
                            <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center; font-size: 0.95rem;">
                                <i class="fas fa-clock me-2" style="color: #0a7ea4;"></i>
                                Hora do contacto
                            </label>
                            <div class="time-input-container" style="position: relative;">
                                <input type="text" class="form-control" id="contactTime" placeholder="Selecione a hora..." readonly style="background-color: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 3rem 0.5rem 0.75rem; font-size: 0.85rem; cursor: pointer; transition: all 0.3s ease;">
                                <button type="button" class="btn" id="contactTimePickerBtn" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #0a7ea4; padding: 0.25rem 0.5rem; border-radius: 4px; transition: all 0.3s ease;">
                                    <i class="fas fa-clock" style="font-size: 1rem;"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Circumstance Details Section -->
                        <div class="form-section mb-3">
                            <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center; font-size: 0.95rem;">
                                <i class="fas fa-dove me-2" style="color: #0a7ea4;"></i>
                                Circunstância do contacto
                            </label>
                            <div class="contact-circumstances" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.75rem; margin-top: 0.5rem;">
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_singing" name="contact_circumstances" value="adultSinging" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_singing" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-music me-1" style="color: #f59e0b; font-size: 0.8rem;"></i>
                                        Rola adulta a cantar
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_display" name="contact_circumstances" value="adultDisplay" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_display" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-heart me-1" style="color: #ef4444; font-size: 0.8rem;"></i>
                                        Adulto em display
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_flying" name="contact_circumstances" value="flying" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_flying" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-dove me-1" style="color: #0a7ea4; font-size: 0.8rem;"></i>
                                        Rola em voo
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_empty_nest" name="contact_circumstances" value="emptyNest" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_empty_nest" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-circle me-1" style="color: #6b7280; font-size: 0.8rem;"></i>
                                        Ninho vazio
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_perched" name="contact_circumstances" value="adultPerched" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_perched" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-feather me-1" style="color: #10b981; font-size: 0.8rem;"></i>
                                        Adulto pousado
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_occupied_nest" name="contact_circumstances" value="occupiedNest" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_occupied_nest" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-egg me-1" style="color: #06b6d4; font-size: 0.8rem;"></i>
                                        Ninho ocupado
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_groups" name="contact_circumstances" value="groups" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_groups" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-dove me-1" style="color: #8b5cf6; font-size: 0.8rem;"></i>
                                        Grupos de Rolas
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="circumstance_other" name="contact_circumstances" value="other" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="circumstance_other" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-question-circle me-1" style="color: #6b7280; font-size: 0.8rem;"></i>
                                        Outro. Qual?
                                    </label>
                                </div>
                            </div>
                            <!-- Number input for "Grupos de Rolas" -->
                            <div id="circumstanceGroupsInput" style="display: none; margin-top: 0.5rem;">
                                <input type="number" class="form-control" id="circumstanceGroupsNumber" placeholder="Número de Indivíduos..." min="1" max="999" style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem; font-size: 0.85rem;" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                            </div>
                            <!-- Text input for "Outro. Qual?" in circumstances -->
                            <div id="circumstanceOtherInput" style="display: none; margin-top: 0.5rem;">
                                <input type="text" class="form-control" id="circumstanceOtherText" placeholder="Especifique a circunstância..." style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem; font-size: 0.85rem;">
                            </div>
                        </div>

                        <!-- Location Details Section -->
                        <div class="form-section mb-3">
                            <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center; font-size: 0.95rem;">
                                <i class="fas fa-tree me-2" style="color: #0a7ea4;"></i>
                                Local do contacto
                            </label>
                            <div class="contact-location" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.75rem; margin-top: 0.5rem;">
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="location_tree" name="contact_location" value="tree" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_tree" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-tree me-1" style="color: #10b981; font-size: 0.8rem;"></i>
                                        Árvore
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="location_water" name="contact_location" value="waterPoint" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_water" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-tint me-1" style="color: #0a7ea4; font-size: 0.8rem;"></i>
                                        Ponto de água
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="location_shrub" name="contact_location" value="shrub" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_shrub" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-seedling me-1" style="color: #10b981; font-size: 0.8rem;"></i>
                                        Arbusto
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="location_clearing" name="contact_location" value="clearing" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_clearing" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-circle me-1" style="color: #f59e0b; font-size: 0.8rem;"></i>
                                        Clareira
                                    </label>
                                </div>
                                <div class="form-check" style="background: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem 0.75rem; transition: all 0.3s ease; position: relative; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); display: flex; align-items: center; min-height: 42px;">
                                    <input class="form-check-input" type="radio" id="location_other" name="contact_location" value="other" style="position: relative; margin: 0 0.5rem 0 0; width: 16px; height: 16px; border: 2px solid #d1d5db; border-radius: 50%; flex-shrink: 0;">
                                    <label class="form-check-label" for="location_other" style="cursor: pointer; display: flex; align-items: center; margin-bottom: 0; font-size: 0.85rem; margin-left: 0; flex: 1; font-weight: 500; color: #374151;">
                                        <i class="fas fa-question-circle me-1" style="color: #6b7280; font-size: 0.8rem;"></i>
                                        Outro. Qual?
                                    </label>
                                </div>
                            </div>
                            <!-- Text input for "Outro. Qual?" in location -->
                            <div id="locationOtherInput" style="display: none; margin-top: 0.5rem;">
                                <input type="text" class="form-control" id="locationOtherText" placeholder="Especifique o local..." style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.5rem; font-size: 0.85rem;">
                            </div>
                        </div>

                        <!-- Position Section -->
                        <div class="form-section mb-3">
                            <label class="form-label section-title" style="color: #374151; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center; font-size: 0.95rem;">
                                <i class="fas fa-crosshairs me-2" style="color: #0a7ea4;"></i>
                                Posição
                            </label>
                            <input type="text" class="form-control" id="contactCoords" readonly style="background-color: #f8fafc; border: 2px solid #e5e7eb; border-radius: 8px; font-family: monospace; font-weight: 500; color: #374151; padding: 0.5rem; font-size: 0.85rem;">
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border: none; padding: 1rem 1.5rem; background: white; display: flex; gap: 1rem; justify-content: center;">
                    <button type="button" class="btn" onclick="cancelContactDetails()" style="background-color: #6b7280; border-color: #6b7280; color: white; padding: 0.5rem 1.25rem; font-size: 0.85rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn" onclick="saveContactDetails()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.5rem 1.25rem; font-size: 0.85rem; font-weight: 500; border-radius: 6px; transition: all 0.2s; display: inline-flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-save"></i>
                        Guardar Contacto
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Time Picker Modal -->
    <div class="modal fade" id="timePickerModal" tabindex="-1" aria-labelledby="timePickerModalLabel" aria-hidden="true" data-bs-backdrop="static" style="z-index: 1080;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white; border-radius: 16px 16px 0 0;">
                    <h5 class="modal-title" id="timePickerModalLabel">
                        <i class="fas fa-clock me-2"></i>Selecionar Hora
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label text-center d-block">Horas</label>
                            <select class="form-select" id="hourSelect" size="8">
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label text-center d-block">Minutos</label>
                            <select class="form-select" id="minuteSelect" size="8">
                                <option value="00">00</option>
                                <option value="05">05</option>
                                <option value="10">10</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                                <option value="25">25</option>
                                <option value="30">30</option>
                                <option value="35">35</option>
                                <option value="40">40</option>
                                <option value="45">45</option>
                                <option value="50">50</option>
                                <option value="55">55</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>
                            <span id="selectedTimeDisplay">--:--</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmTimeBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Profile Modal -->
    <?php include '../../includes/profile_modal.php'; ?>

    <!-- Google Maps API -->
    <script>
        // Global flag to prevent multiple initializations
        window.mapsInitialized = window.mapsInitialized || false;
        
        // Make initMap globally accessible for the callback
        window.initMap = function() {
            if (!window.mapsInitialized) {
                window.mapsInitialized = true;
                initializeMap();
            } else {
            }
        };

        // Check if Google Maps is already loaded
        function loadGoogleMaps() {
            if (typeof google !== 'undefined' && google.maps) {
                if (!window.mapsInitialized) {
                    window.mapsInitialized = true;
                    initializeMap();
                }
            } else {
                const script = document.createElement('script');
                script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&callback=initMap&libraries=geometry';
                script.async = true;
                script.defer = true;
                script.onerror = function() {
                    Swal.fire({
                        title: 'Erro ao carregar mapas',
                        text: 'Não foi possível carregar o Google Maps. Verifique a sua ligação à internet.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc2626'
                    });
                };
                document.head.appendChild(script);
            }
        }

        // Load maps when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                loadGoogleMaps();
                setupTimePickerListeners();
            });
        } else {
            loadGoogleMaps();
            setupTimePickerListeners();
        }
    </script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Firebase scripts removed - using PHP endpoints instead -->

    <style>
        /* CSS Variables for dynamic calculations and theme */
        :root {
            --header-height: 48px;
            --content-padding: 60px;
            --card-header-height: 65px;
            --stats-section-height: 70px;
            --card-actions-height: 100px;
            --available-map-height: calc(100vh - var(--header-height) - var(--content-padding) - var(--card-header-height) - var(--stats-section-height) - var(--card-actions-height));
            
            /* Theme Colors */
            --primary-blue: #0a7ea4;
            --primary-blue-light: rgba(10, 126, 164, 0.1);
            --primary-blue-border: rgba(10, 126, 164, 0.2);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-white: #ffffff;
            --bg-gray-50: #f9fafb;
            --bg-gray-100: #f3f4f6;
            --border-gray: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Header and Content Layout */
        .header {
            background-color: #fff;
            padding: 0.5rem 1rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            left: 220px !important;
            z-index: 999 !important;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header.sidebar-collapsed {
            left: 60px !important;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-title {
            color: #374151;
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-title .trajeto-name {
            color: #0a7ea4;
            font-size: 1rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .content {
            margin-left: 220px !important;
            padding: 60px 24px 24px 24px !important;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 100vh !important;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            width: calc(100% - 220px) !important;
            position: relative !important;
            overflow-x: hidden !important;
        }

        .content.sidebar-collapsed {
            margin-left: 60px !important;
            width: calc(100% - 60px) !important;
        }

        /* Help Button */
        .btn-help {
            background: rgba(10, 126, 164, 0.08);
            color: #0a7ea4;
            border: 1px solid rgba(10, 126, 164, 0.15);
            padding: 0.375rem 0.75rem;
            font-size: 0.813rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
            height: 32px;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .btn-help:hover {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Modern Card Container */
        .trajeto-card {
            background: var(--bg-white);
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(229, 231, 235, 0.8);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .trajeto-card:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(10, 126, 164, 0.1);
            border-color: rgba(10, 126, 164, 0.2);
        }

        /* Card Header */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, var(--bg-white) 0%, var(--bg-gray-50) 100%);
            border-bottom: 1px solid var(--border-gray);
            position: relative;
        }

        .card-header h5 {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.125rem;
            margin: 0;
            display: flex;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            letter-spacing: -0.025em;
        }

        .card-header h5 i {
            color: var(--primary-blue);
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        .info-badge {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.08) 0%, rgba(10, 126, 164, 0.03) 100%);
            color: var(--primary-blue);
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.2rem 0.875rem;
            border-radius: 20px;
            border: 1px solid rgba(10, 126, 164, 0.15);
            display: flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 1px 3px 0 rgba(10, 126, 164, 0.1), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(4px);
        }

        .info-badge i {
            color: inherit;
            font-size: 0.7rem;
        }

        .stats-section {
            padding: 0.5rem;
            background: linear-gradient(135deg, var(--bg-gray-50) 0%, var(--bg-white) 100%);
            border-bottom: 1px solid var(--border-gray);
        }

        .stats-flex-container {
            display: flex;
            gap: 0.5rem;
            align-items: stretch;
        }

        .stat-item-wrapper {
            display: flex;
            flex-direction: column;
        }

        .stat-address-wrapper {
            flex: 1;
            min-width: 0;
        }

        .stat-distance-wrapper {
            flex: 0 0 auto;
            min-width: fit-content;
        }

        .stat-points-wrapper {
            flex: 0 0 auto;
            min-width: fit-content;
            width: auto;
        }

        .stat-item {
            display: flex;
            align-items: center;
            padding: 0.35rem 0.75rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 1px solid rgba(229, 231, 235, 0.6);
            height: 32px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: visible;
            backdrop-filter: blur(8px);
        }

        .stat-address-wrapper .stat-item {
            width: 100%;
        }

        .stat-distance-wrapper .stat-item {
            width: auto;
            white-space: nowrap;
            min-width: fit-content;
            padding-right: 0.75rem;
        }
        
        .stat-points-wrapper .stat-item {
            width: fit-content;
            white-space: nowrap;
        }

        .stat-item i {
            color: var(--primary-blue);
            font-size: 0.8rem;
            margin-right: 0.25rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            margin-right: 0.2rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
            margin-top: 0.05rem !important;
        }

        .stat-value {
            font-weight: 700;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
        }

        /* Address stat item */
        .stat-item-address .stat-label {
            font-size: 0.7rem;
            white-space: nowrap;
            font-weight: 600;
        }

        .stat-item-address .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
            min-width: 0;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.3;
        }

        /* Compact stat items for distance and points */
        .stat-item-compact .stat-label {
            font-size: 0.7rem;
            white-space: nowrap;
        }

        .stat-item-compact .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            white-space: nowrap;
        }

        /* Contacts count styling - matches points styling */
        .stat-contacts {
            display: inline-block;
            margin-left: 0.75rem;
            vertical-align: middle;
        }

        .stat-contacts .stat-label {
            font-size: 0.7rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .stat-contacts .stat-value {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Special styling for address - allow natural wrapping */
        #startingAddress {
            width: 100% !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            overflow: visible !important;
            text-overflow: unset !important;
            white-space: normal !important;
            max-width: none !important;
        }

        /* Map Container - Dynamic Height */
        .map-container {
            position: relative;
            height: var(--available-map-height);
            width: 100%;
            border: none;
            min-height: 400px;
            max-height: calc(100vh - 200px);
        }

        .google-map {
            height: 100%;
            width: 100%;
            border: none;
        }

        /* Card Actions */
        .card-actions {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--bg-gray-50) 0%, var(--bg-white) 100%);
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--border-gray);
        }

        /* Form Actions */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
        }

        .form-actions .btn {
            min-width: 120px;
            height: 40px;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        /* Button Styles */
        .btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
        }

        .btn-primary {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
            color: white;
        }

        /* Distance validation styles */
        .distance-insufficient {
            color: #dc2626 !important;
            font-weight: 600 !important;
        }
        
        .distance-sufficient {
            color: #16a34a !important;
            font-weight: 600 !important;
        }
        
        .distance-warning {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border: 1px solid #f87171;
            color: #dc2626;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.15rem;
            white-space: nowrap;
            line-height: 1;
            margin-left: 0.4rem;
            vertical-align: text-top;
            height: 18px;
            transform: translateY(1px);
            animation: flashWarning 1.5s infinite;
        }
        
        @keyframes flashWarning {
            0%, 100% { 
                opacity: 1;
                background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
                border-color: #f87171;
            }
            50% { 
                opacity: 0.7;
                background: linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%);
                border-color: #ef4444;
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 0 0 10px rgba(245, 158, 11, 0.1);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
            }
        }
        
        .distance-success {
            background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
            border: 1px solid #86efac;
            color: #16a34a;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.15rem;
            white-space: nowrap;
            line-height: 1;
            margin-left: 0.4rem;
            vertical-align: text-top;
            height: 18px;
            transform: translateY(1px);
        }

        /* Save button disabled state for distance validation */
        #saveTrajetoBtn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            position: relative;
        }
        
        #saveTrajetoBtn:disabled:hover::after {
            content: "Trajeto deve ter pelo menos 2.5 km";
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 0.25rem;
        }
        
        #saveTrajetoBtn:disabled:hover::before {
            content: "";
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            margin-bottom: 0.125rem;
        }

        /* Distance validation popup styling */
        .distance-validation-popup {
            border-radius: 16px !important;
            overflow: hidden !important;
        }
        
        .distance-validation-popup .swal2-html-container {
            padding: 0 !important;
            margin: 0 !important;
        }

        .btn-primary:hover {
            background-color: #0891b2;
            border-color: #0891b2;
        }

        .btn-success {
            background-color: #16a34a;
            border: 1px solid #16a34a;
            color: white;
        }

        .btn-success:hover {
            background-color: #15803d;
            border-color: #15803d;
            color: white;
        }

        .btn-secondary {
            background-color: #6b7280;
            border: 1px solid #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
            border-color: #4b5563;
            color: white;
        }

        .btn-warning {
            background-color: #f59e0b;
            border: 1px solid #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
            border-color: #d97706;
            color: white;
        }

        .btn-outline-secondary {
            background-color: transparent;
            border: 1px solid #9ca3af;
            color: #9ca3af;
        }

        .btn-outline-secondary:hover {
            background-color: #e5e7eb;
            border-color: #6b7280;
            color: #374151;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .header {
                left: 60px !important;
            }
            
            .content {
                margin-left: 60px !important;
                width: calc(100% - 60px) !important;
            }
        }

        @media (max-width: 768px) {
            .form-actions {
                flex-direction: column;
            }
            
            .map-container {
                height: calc(100vh - 48px - 40px - 100px - 100px);
                min-height: 300px;
            }
        }

        @media (max-width: 576px) {
            .map-container {
                height: calc(100vh - 48px - 40px - 80px - 120px);
                min-height: 250px;
            }
            
            .form-actions .btn {
                min-width: 120px;
                font-size: 0.8rem;
            }
        }

        /* Location Choice Modal Styles */
        .location-choice-modal .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .location-choice-modal .modal-header {
            border-radius: 16px 16px 0 0;
            border-bottom: none;
            position: relative;
        }

        .step-indicator {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .location-choice-btn {
            border-radius: 12px;
            padding: 1rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            text-align: left;
        }

        .location-choice-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .location-choice-btn i {
            font-size: 1.1rem;
        }

        /* Edit Choice Card Styles - App Theme */
        .edit-choice-card {
            background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
            border: none;
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(10, 126, 164, 0.12),
                0 4px 16px rgba(0, 0, 0, 0.08),
                0 0 0 1px rgba(10, 126, 164, 0.08);
            overflow: hidden;
            position: relative;
        }

        .edit-choice-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0a7ea4 0%, #0891b2 50%, #06b6d4 100%);
        }

        /* Header */
        .edit-choice-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1.5rem 2rem 1.25rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .edit-choice-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .header-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        .header-icon i {
            font-size: 1.25rem;
            color: white;
        }

        .header-title {
            font-size: 1.375rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
            position: relative;
            z-index: 2;
        }

        /* Body */
        .edit-choice-body {
            padding: 2.5rem 2rem;
        }

        .choice-options {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Choice Buttons */
        .choice-btn {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            text-align: left;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            width: 100%;
        }

        .choice-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s ease;
        }

        .choice-btn:hover::before {
            left: 100%;
        }

        .choice-btn:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 32px rgba(10, 126, 164, 0.15);
        }

        .choice-btn-primary {
            border-color: rgba(10, 126, 164, 0.2);
        }

        .choice-btn-primary:hover {
            border-color: #0a7ea4;
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.02) 100%);
        }

        .choice-btn-warning {
            border-color: rgba(245, 158, 11, 0.2);
        }

        .choice-btn-warning:hover {
            border-color: #f59e0b;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(249, 115, 22, 0.02) 100%);
        }

        .choice-btn-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .choice-btn-primary .choice-btn-icon {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
        }

        .choice-btn-warning .choice-btn-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
        }

        .choice-btn:hover .choice-btn-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .choice-btn-content {
            flex: 1;
        }

        .choice-btn-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 0.5rem 0;
            transition: color 0.3s ease;
        }

        .choice-btn-description {
            font-size: 0.95rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
        }

        .choice-btn-arrow {
            color: #9ca3af;
            font-size: 1.25rem;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .choice-btn:hover .choice-btn-arrow {
            color: #0a7ea4;
            transform: translateX(4px);
        }

        /* Safety Notice */
        .safety-notice {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid rgba(10, 126, 164, 0.15);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .safety-notice-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .safety-notice-text {
            font-size: 0.9rem;
            color: #0a7ea4;
            line-height: 1.4;
        }

        .safety-notice-text strong {
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .edit-choice-header {
                padding: 1.25rem 1.5rem 1rem;
            }

            .header-icon {
                width: 40px;
                height: 40px;
                margin: 0 auto 0.75rem;
            }

            .header-icon i {
                font-size: 1rem;
            }

            .header-title {
                font-size: 1.25rem;
            }

            .header-subtitle {
                font-size: 0.85rem;
            }

            .edit-choice-body {
                padding: 2rem 1.5rem;
            }

            .choice-btn {
                padding: 1.25rem;
                gap: 1rem;
            }

            .choice-btn-icon {
                width: 56px;
                height: 56px;
                font-size: 1.25rem;
            }

            .choice-btn-title {
                font-size: 1.125rem;
            }

            .choice-btn-description {
                font-size: 0.875rem;
            }
        }

        /* Clear Options Modal Styling */
        .clear-options-popup {
            border-radius: 16px !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        }

        .clear-option-btn {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: white;
            text-align: left;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .clear-option-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: #d1d5db;
        }

        .clear-both-btn:hover {
            border-color: #dc2626;
            background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
        }

        .clear-route-btn:hover {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
        }

        .clear-contacts-btn:hover {
            border-color: #0891b2;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
        }

        .clear-option-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .clear-both-btn .clear-option-icon {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
        }

        .clear-route-btn .clear-option-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .clear-contacts-btn .clear-option-icon {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            color: white;
        }

        .clear-option-content {
            flex: 1;
        }

        .clear-option-title {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin: 0 0 0.25rem 0;
        }

        .clear-option-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
        }

        .clear-cancel-btn {
            border-radius: 8px !important;
            font-weight: 500 !important;
            padding: 0.5rem 1.25rem !important;
        }

        /* Responsive adjustments for clear options */
        @media (max-width: 576px) {
            .clear-option-btn {
                padding: 0.875rem 1rem;
            }

            .clear-option-icon {
                width: 40px;
                height: 40px;
                font-size: 1rem;
                margin-right: 0.75rem;
            }

            .clear-option-title {
                font-size: 0.9rem;
            }

            .clear-option-description {
                font-size: 0.8rem;
            }
        }

        /* Location Choice Modal Styling (from create.php) */
        .location-choice-modal {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .location-choice-modal .modal-header {
            border-radius: 16px 16px 0 0;
            border-bottom: none;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .location-choice-modal .modal-body {
            border-radius: 0 0 16px 16px;
        }
        
        /* Ensure modal is properly centered */
        .modal-dialog-centered {
            display: flex;
            align-items: center;
            min-height: calc(100vh - 1rem);
        }
        
        @media (min-width: 576px) {
            .modal-dialog-centered {
                min-height: calc(100vh - 3.5rem);
            }
        }
        
        /* Location choice button styling */
        .location-choice-btn {
            display: flex !important;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .location-choice-btn i {
            flex-shrink: 0;
        }
        
        .location-choice-btn span {
            flex: 1;
            text-align: center;
        }

        /* Step indicator styling */
        .step-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .step-number {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 700;
        }

        .step-text {
            color: white;
            opacity: 0.9;
        }

        /* Timeline Date Picker Modal Styling */
        #timelineDatePickerModal .modal-body {
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 0 0 16px 16px;
        }

        #timelineDatePickerModal .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.5rem;
            font-size: 0.9rem;
            background: white;
            transition: all 0.3s ease;
        }

        #timelineDatePickerModal .form-select:focus {
            border-color: #0a7ea4;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
            outline: none;
        }

        #timelineDatePickerModal .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        #timelineDatePickerModal .alert {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #93c5fd;
            color: #1e40af;
            border-radius: 8px;
            padding: 0.75rem;
            margin: 0;
        }

        /* Timeline Date Picker Button Hover */
        .timeline-date-picker-btn:hover {
            background-color: rgba(10, 126, 164, 0.1) !important;
            color: #0891b2 !important;
        }

        /* Timeline Card Header with Date Display */
        .card-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            justify-content: flex-end;
        }

        .trajeto-date-display {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.08) 0%, rgba(10, 126, 164, 0.03) 100%);
            color: var(--primary-blue);
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            border: 1px solid rgba(10, 126, 164, 0.15);
            display: flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 1px 3px 0 rgba(10, 126, 164, 0.1);
            backdrop-filter: blur(4px);
            white-space: nowrap;
            margin-left: 0.5rem;
        }

        .trajeto-date-display::before {
            content: '\f073';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            font-size: 0.7rem;
            color: inherit;
        }
    </style>

    <script>
        let map;
        let routePath;
        let routeCoordinates = [];
        let markers = [], contactMarkers = [];
        let trajetoData = null;
        let originalTrajetoData = null;
        
        // Modal protection flags to prevent duplicate modal openings
        window.contactTimePickerOpening = false;
        window.timelineTimePickerOpening = false;
        
        // Date formatting function
        function formatDate(dateString) {
            if (!dateString) return null;
            
            try {
                const date = new Date(dateString);
                return date.toLocaleString('pt-PT', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (e) {
                return dateString;
            }
        }
        
        // Emergency modal cleanup function
        function cleanupModals() {
            
            // Reset all modal protection flags
            window.contactTimePickerOpening = false;
            window.timelineTimePickerOpening = false;
            
            // Close any open Bootstrap modals
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            });
            
            // Remove any leftover modal backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            
            // Remove modal-open class from body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
        }
        
        // Add global escape key listener for emergency modal cleanup
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Check if there are any stuck modals (modals without proper backdrop)
                const openModals = document.querySelectorAll('.modal.show');
                const backdrops = document.querySelectorAll('.modal-backdrop');
                
                if (openModals.length > backdrops.length) {
                    cleanupModals();
                }
            }
        });

        // Initialize Google Map
        function initializeMap() {
            
            // Default center (Portugal)
            const center = { lat: 39.5, lng: -8.0 };
            
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 8,
                center: center,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_CENTER
                },
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            // Initialize route path with shadow effect
            // Shadow path (darker, thicker line underneath)
            const shadowPath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#000000',
                strokeOpacity: 0.4,
                strokeWeight: 6,
                zIndex: 1
            });
            shadowPath.setMap(map);

            // Main route path (colored line on top)
            routePath = new google.maps.Polyline({
                path: [],
                geodesic: true,
                strokeColor: '#0a7ea4',
                strokeOpacity: 1.0,
                strokeWeight: 4,
                zIndex: 2
            });
            routePath.setMap(map);

            // Store shadow path for updates
            window.shadowPath = shadowPath;

            // Add click listener to map
            map.addListener('click', function(event) {
                if (addingNewContact) {
                    // Adding new contact mode
                    addNewContactAt(event.latLng);
                } else {
                    // Normal route point adding
                addRoutePoint(event.latLng);
                }
            });

            // Add double-click listener to finish route
            map.addListener('dblclick', function(event) {
                finishRoute();
            });

            // Load existing trajeto data
            loadTrajetoData();
            
        }

        function loadTrajetoData() {
            const trajetoId = document.getElementById('trajetoId').value;
            // Loading trajectory data
            
            // Call PHP endpoint to load trajeto data
            fetch('load_trajeto.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ trajetoId: trajetoId })
            })
            .then(response => {
                return response.json();
            })
            .then(data => {
                
                if (data.success) {
                    
                    // Store original data as backup
                    originalTrajetoData = JSON.parse(JSON.stringify(data.data));
                    trajetoData = data.data;
                    
                    // Hide loading, show edit choice
                    document.getElementById('loadingState').style.display = 'none';
                    document.getElementById('editChoiceContent').style.display = 'block';
                } else {
                    Swal.fire({
                        title: 'Erro',
                        text: data.message || 'Trajeto não encontrado.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc2626'
                    }).then(() => {
                        window.location.href = '../zonas/index.php';
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: 'Erro',
                    text: 'Erro ao carregar o trajeto.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                }).then(() => {
                    window.location.href = '../zonas/index.php';
                });
            });
        }

        function reloadContactsOnly() {
            const trajetoId = document.getElementById('trajetoId').value;
            
            // Call PHP endpoint to load trajeto data
            fetch('load_trajeto.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ trajetoId: trajetoId })
            })
            .then(response => {
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update only the contacts in the current trajectory data
                    if (trajetoData) {
                        trajetoData.contacts = data.data.contacts || [];
                        
                        // Reload contacts on map
                        loadContactsOnMap(trajetoData.contacts);
                    }
                } else {
                }
            })
            .catch(error => {
            });
        }

        // Edit choice functions
        function editCurrentTrajeto() {
            editMode = 'current';
            
            // Reset deletion tracking arrays
            deletedMapPhotos = [];
            deletedOtherPhotos = [];
            
            // Hide choice, show edit form
            document.getElementById('editChoiceContent').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
            
            // Load current trajectory data
            populateForm(trajetoData);
            loadRouteOnMap(trajetoData.coordinates || []);
            
            // Load contacts on map if available
            if (trajetoData.contacts && trajetoData.contacts.length > 0) {
                setTimeout(() => {
                    loadContactsOnMap(trajetoData.contacts);
                }, 500); // Small delay to ensure map is ready
            } else {
                // Update contacts count even if no contacts
                updateContactsCount();
            }
            
            // Focus map on trajectory if it exists
            if (trajetoData.coordinates && trajetoData.coordinates.length > 0) {
                const bounds = new google.maps.LatLngBounds();
                trajetoData.coordinates.forEach(coord => {
                    bounds.extend(new google.maps.LatLng(coord.lat, coord.lng));
                });
                map.fitBounds(bounds);
                
                // Add some padding
                setTimeout(() => {
                    if (trajetoData.coordinates.length === 1) {
                        map.setZoom(15);
                    }
                }, 100);
            }
        }

        function startOverTrajeto() {
            editMode = 'startOver';
            
            // Reset deletion tracking arrays
            deletedMapPhotos = [];
            deletedOtherPhotos = [];
            
            // Clear any existing route data
            routeCoordinates = [];
            clearAllMarkers(); // Clear both route and contact markers for fresh start
            routePath.setPath([]);
            if (window.shadowPath) window.shadowPath.setPath([]);
            
            // Reset timeline, weather, and observers data
            currentStartTime = '';
            currentMiddleTime = '';
            currentEndTime = '';
            currentTrajetoDate = '';
            currentWeatherCondition = '';
            currentObserversCount = 0;
            
            // Update displays
            updateTrajetoDateDisplay();
            
            // Reset card displays to show they need to be filled
            document.getElementById('startTimeCompact').textContent = '--:--';
            document.getElementById('middleTimeCompact').textContent = '--:--';
            document.getElementById('endTimeCompact').textContent = '--:--';
            document.getElementById('weatherIconCompact').innerHTML = '<i class="fas fa-cloud-sun text-info"></i>';
            document.getElementById('weatherTextCompact').textContent = 'Clique para definir';
            document.getElementById('observersCountDisplay').textContent = '-';
            
            // Reset trajectory data contacts (will be empty for start over)
            if (trajetoData) {
                trajetoData.contacts = [];
            }
            
            // Update contacts count display
            updateContactsCount();
            
            // Hide choice content
            document.getElementById('editChoiceContent').style.display = 'none';
            
            // Show location choice modal (same as create.php)
            const locationModal = new bootstrap.Modal(document.getElementById('locationChoiceModal'));
            locationModal.show();
        }

        // Map animation functions (from create.php)
        function animateMapToLocation(targetLocation, targetZoom = 15, fromCurrentPosition = false) {
            return new Promise((resolve) => {
                if (!map) {
                    resolve();
                    return;
                }
                
                const cinematicZoomOut = 8; // Zoom out to level 8 for subtle effect
                const intermediateZoom = 12; // Intermediate zoom level
                
                if (fromCurrentPosition) {
                    // For search locations: start from current map position, zoom out, then travel to target
                    const currentPosition = map.getCenter();
                    
                    // Step 1: Zoom out from current position
                    smoothZoomTo(cinematicZoomOut, 800).then(() => {
                        // Step 2: Pan from current position to target location (journey effect)
                        map.panTo(targetLocation);
                        
                        setTimeout(() => {
                            // Step 3: Zoom to intermediate level
                            smoothZoomTo(intermediateZoom, 600).then(() => {
                                // Step 4: Final zoom to target level
                                smoothZoomTo(targetZoom, 800).then(() => {
                                    // Final centering
                                    setTimeout(() => {
                                        map.panTo(targetLocation);
                                        setTimeout(() => {
                                            resolve();
                                        }, 300);
                                    }, 200);
                                });
                            });
                        }, 1200); // Wait for pan animation to complete
                    });
                } else {
                    // For GPS locations: center on target first, then zoom out and back in
                    // Step 1: Instantly center on target location (for GPS we want to focus on user location)
                    map.setCenter(targetLocation);
                    
                    // Step 2: Zoom out from target location
                    smoothZoomTo(cinematicZoomOut, 800).then(() => {
                        // Step 3: Small re-center during zoom out
                        map.panTo(targetLocation);
                        
                        setTimeout(() => {
                            // Step 4: Zoom to intermediate level
                            smoothZoomTo(intermediateZoom, 600).then(() => {
                                // Step 5: Final zoom to target level
                                smoothZoomTo(targetZoom, 800).then(() => {
                                    // Final centering
                                    setTimeout(() => {
                                        map.panTo(targetLocation);
                                        setTimeout(() => {
                                            resolve();
                                        }, 300);
                                    }, 200);
                                });
                            });
                        }, 400); // Shorter wait since no long pan needed
                    });
                }
            });
        }
        
        // Function to animate map from one specific location to another (for search)
        function animateMapFromTo(startLocation, startZoom, targetLocation, targetZoom) {
            return new Promise((resolve) => {
                if (!map) {
                    resolve();
                    return;
                }
                
                // Ensure we start from the original position
                map.setCenter(startLocation);
                map.setZoom(startZoom);
                
                const cinematicZoomOut = 8; // Zoom out level
                const intermediateZoom = 12; // Intermediate zoom level
                
                // Step 1: Zoom out from original position
                smoothZoomTo(cinematicZoomOut, 800).then(() => {
                    // Step 2: Pan from original position to target location (true journey effect)
                    map.panTo(targetLocation);
                    
                    setTimeout(() => {
                        // Step 3: Zoom to intermediate level
                        smoothZoomTo(intermediateZoom, 600).then(() => {
                            // Step 4: Final zoom to target level
                            smoothZoomTo(targetZoom, 800).then(() => {
                                // Final centering
                                setTimeout(() => {
                                    map.panTo(targetLocation);
                                    setTimeout(() => {
                                        resolve();
                                    }, 300);
                                }, 200);
                            });
                        });
                    }, 1200); // Wait for pan animation to complete
                });
            });
        }

        // Helper function for smooth zoom animations
        function smoothZoomTo(targetZoom, duration = 1000) {
            return new Promise((resolve) => {
                const startZoom = map.getZoom();
                const zoomDifference = targetZoom - startZoom;
                const steps = 20; // Number of animation steps
                const stepDuration = duration / steps;
                const zoomStep = zoomDifference / steps;
                
                let currentStep = 0;
                
                const animateStep = () => {
                    if (currentStep >= steps) {
                        map.setZoom(targetZoom); // Ensure exact final zoom
                        resolve();
                        return;
                    }
                    
                    const newZoom = startZoom + (zoomStep * currentStep);
                    map.setZoom(newZoom);
                    currentStep++;
                    
                    setTimeout(animateStep, stepDuration);
                };
                
                animateStep();
            });
        }

        // Location choice functions (from create.php)
        function useCurrentLocation() {
            if (navigator.geolocation) {
                // Show loading state
                Swal.fire({
                    title: 'A obter localização...',
                    html: '<div class="text-center">' +
                          '<p class="mb-3">Por favor <strong>permita o acesso à localização</strong> quando o navegador solicitar.</p>' +
                          '<div class="alert alert-info d-inline-block" style="font-size: 0.9rem;">' +
                          '<i class="fas fa-info-circle me-2"></i>' +
                          'Procure por um ícone de localização na barra de endereços do navegador' +
                          '</div>' +
                          '</div>',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        
                        Swal.close();
                        
                        // Hide location modal first
                        const modal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
                        modal.hide();
                        
                        // Show edit form BEFORE animation so map is visible
                        setTimeout(() => {
                            showEditFormBeforeAnimation();
                            
                            // Wait a bit more for map to be fully rendered before animation
                            setTimeout(() => {
                                // Animate map to user location with smooth zoom animation
                                animateMapToLocation(userLocation, 15).then(() => {
                                    // Animation completed
                                });
                            }, 200);
                        }, 300);
                    },
                    function(error) {
                        Swal.close();
                        
                        let errorMessage = 'Não foi possível obter a sua localização.';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'Acesso à localização foi negado. Pode definir a localização manualmente no mapa.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'Informação de localização não disponível. Pode definir a localização manualmente no mapa.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'Tempo limite excedido ao obter localização. Pode definir a localização manualmente no mapa.';
                                break;
                        }
                        
                        Swal.fire({
                            title: 'Erro de Localização',
                            text: errorMessage,
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#0a7ea4'
                        });
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            } else {
                Swal.fire({
                    title: 'Geolocalização não suportada',
                    text: 'O seu navegador não suporta geolocalização. Pode definir a localização manualmente no mapa.',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
            }
        }

        function showLocationSearch() {
            // Hide location choice modal and show search modal
            bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal')).hide();
            const searchModal = new bootstrap.Modal(document.getElementById('locationSearchModal'));
            searchModal.show();
            
            // Focus on search input
            setTimeout(() => {
                document.getElementById('locationSearchInput').focus();
            }, 500);
        }

        function showLocationChoiceModal() {
            const locationModal = new bootstrap.Modal(document.getElementById('locationChoiceModal'));
            locationModal.show();
        }

        function handleLocationSearchEnter(event) {
            if (event.key === 'Enter') {
                searchLocation();
            }
        }

        function searchLocation() {
            const searchTerm = document.getElementById('locationSearchInput').value.trim();
            if (!searchTerm) {
                Swal.fire({
                    title: 'Campo vazio',
                    text: 'Por favor digite uma localização para pesquisar.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                document.getElementById('locationSearchInput').focus();
                return;
            }

            // CAPTURE THE ORIGINAL MAP POSITION BEFORE SEARCHING
            const originalCenter = map.getCenter();
            const originalZoom = map.getZoom();

            // Show loading
            Swal.fire({
                title: 'A pesquisar...',
                text: `A procurar "${searchTerm}"...`,
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Use Google Geocoding API
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({ 
                address: searchTerm + ', Portugal',
                region: 'PT'
            }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    const location = results[0].geometry.location;
                    const targetLocation = {
                        lat: location.lat(),
                        lng: location.lng()
                    };
                    
                    // Hide search modal
                    const searchModal = bootstrap.Modal.getInstance(document.getElementById('locationSearchModal'));
                    searchModal.hide();
                    
                    Swal.close();
                    
                    // Show edit form BEFORE animation so map is visible
                    setTimeout(() => {
                        showEditFormBeforeAnimation();
                        
                        // Wait a bit more for map to be fully rendered before animation
                        setTimeout(() => {
                            // DON'T move the map yet! Animate from original position to found location
                            animateMapFromTo(originalCenter, originalZoom, targetLocation, 12).then(() => {
                                // Animation completed
                            });
                        }, 200);
                    }, 300);
                } else {
                    Swal.close();
                    
                    let errorMessage = 'Não foi possível encontrar essa localização.';
                    if (status === 'ZERO_RESULTS') {
                        errorMessage = `Não foram encontrados resultados para "${searchTerm}". Tente com uma localização diferente.`;
                    } else if (status === 'OVER_QUERY_LIMIT') {
                        errorMessage = 'Limite de pesquisas excedido. Tente novamente mais tarde.';
                    }
                    
                    Swal.fire({
                        title: 'Localização não encontrada',
                        text: errorMessage,
                        icon: 'warning',
                        confirmButtonText: 'Tentar novamente',
                        confirmButtonColor: '#0a7ea4'
                    }).then(() => {
                        // Show search modal again
                        const searchModal = new bootstrap.Modal(document.getElementById('locationSearchModal'));
                        searchModal.show();
                        setTimeout(() => {
                            const input = document.getElementById('locationSearchInput');
                            input.focus();
                            input.select();
                        }, 500);
                    });
                }
            });
        }

        function useManualLocation() {
            // Close location modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('locationChoiceModal'));
            modal.hide();
            
            // For manual location, show edit form immediately (user will click on map)
            setTimeout(() => {
                showEditFormBeforeAnimation();
            }, 300);
        }

        function showEditFormBeforeAnimation() {
            // Show edit form
            document.getElementById('mainContent').style.display = 'block';
            
            // Initialize empty form for start over mode
            populateFormForStartOver();
            
            // Trigger map resize to ensure proper display BEFORE animation
            setTimeout(() => {
                google.maps.event.trigger(map, 'resize');
                
                // Ensure map is properly visible and sized
                if (map) {
                    const mapDiv = document.getElementById('map');
                    if (mapDiv) {
                        // Force a redraw
                        mapDiv.style.display = 'none';
                        mapDiv.offsetHeight; // Trigger reflow
                        mapDiv.style.display = 'block';
                        
                        // Trigger resize again after visibility change
                        setTimeout(() => {
                            google.maps.event.trigger(map, 'resize');
                        }, 50);
                    }
                }
            }, 100);
        }

        function showEditForm(centerLocation) {
            // Show edit form
            document.getElementById('mainContent').style.display = 'block';
            
            // Initialize empty form for start over mode
            populateFormForStartOver();
            
            // Center map on specified location
            map.setCenter(centerLocation);
            map.setZoom(12);
            
            // Trigger map resize
            setTimeout(() => {
                google.maps.event.trigger(map, 'resize');
                map.setCenter(centerLocation);
            }, 100);
        }

        function populateFormForStartOver() {
            // Keep original trajectory metadata but reset coordinates
            let headerHtml = trajetoData.name || 'Trajeto sem nome';
            
            // Add only edited date to header if it exists and is different from created date
            if (trajetoData.updatedAt && trajetoData.updatedAt !== trajetoData.createdAt) {
                const editedDate = formatDate(trajetoData.updatedAt);
                headerHtml += ` <span style="font-size: 0.7em; font-weight: normal; color: #6b7280;">- Editado: ${editedDate}</span>`;
            }
            
            document.getElementById('headerTrajetoName').innerHTML = headerHtml;
            document.getElementById('trajetoName').value = trajetoData.name || '';
            document.getElementById('trajetoDescription').value = trajetoData.description || '';
            document.getElementById('trajetoDifficulty').value = trajetoData.difficulty || '';
            document.getElementById('trajetoStatusSelect').value = trajetoData.status || 'draft';
            document.getElementById('zoneId').value = trajetoData.zoneId || '';
            
            // Reset route-specific data
            document.getElementById('startingAddress').textContent = 'A definir...';
            document.getElementById('routeDistance').textContent = '0 km';
            document.getElementById('routePoints').textContent = '0';
            
            // Disable save button until route is created
            document.getElementById('saveTrajetoBtn').disabled = true;
        }

        function populateForm(data) {
            // Update header with trajeto name and date
            let headerHtml = data.name || 'Trajeto sem nome';
            
            // Add only edited date to header if it exists and is different from created date
            if (data.updatedAt && data.updatedAt !== data.createdAt) {
                const editedDate = formatDate(data.updatedAt);
                headerHtml += ` <span style="font-size: 0.7em; font-weight: normal; color: #6b7280;">- Editado: ${editedDate}</span>`;
            }
            
            document.getElementById('headerTrajetoName').innerHTML = headerHtml;
            
            // Populate form fields
            document.getElementById('trajetoName').value = data.name || '';
            document.getElementById('trajetoDescription').value = data.description || '';
            document.getElementById('trajetoDifficulty').value = data.difficulty || '';
            document.getElementById('trajetoStatusSelect').value = data.status || 'draft';
            document.getElementById('zoneId').value = data.zoneId || '';
            
            // Update starting address
            if (data.startingAddress) {
                document.getElementById('startingAddress').textContent = data.startingAddress;
            }

            // Populate info cards
            populateInfoCards(data);
        }

        function populateInfoCards(data) {
            // Timeline Card - populate global variables and display
            currentStartTime = data.startTime || '';
            currentMiddleTime = data.middleTime || '';
            currentEndTime = data.endTime || '';
            
            // Extract date - prioritize saved trajetoDate, fallback to createdAt timestamp
            if (data.trajetoDate) {
                // Use the saved trajectory date (already in DD/MM/YYYY format)
                currentTrajetoDate = data.trajetoDate;
            } else if (data.createdAt) {
                try {
                    // Fallback to createdAt timestamp and convert to DD/MM/YYYY format
                    const createdDate = new Date(data.createdAt);
                    const day = String(createdDate.getDate()).padStart(2, '0');
                    const month = String(createdDate.getMonth() + 1).padStart(2, '0');
                    const year = String(createdDate.getFullYear());
                    currentTrajetoDate = `${day}/${month}/${year}`;
                } catch (e) {
                    const now = new Date();
                    const day = String(now.getDate()).padStart(2, '0');
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const year = String(now.getFullYear());
                    currentTrajetoDate = `${day}/${month}/${year}`;
                }
            } else {
                // No date available, use current date
                const now = new Date();
                const day = String(now.getDate()).padStart(2, '0');
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const year = String(now.getFullYear());
                currentTrajetoDate = `${day}/${month}/${year}`;
            }
            
            document.getElementById('startTimeCompact').textContent = currentStartTime || '--:--';
            document.getElementById('middleTimeCompact').textContent = currentMiddleTime || '--:--';
            document.getElementById('endTimeCompact').textContent = currentEndTime || '--:--';
            
            // Update the trajectory date display
            updateTrajetoDateDisplay();

            // Update header trajectory name if needed
            updateHeaderTrajetoName();

            // Weather Card - populate global variable and display
            currentWeatherCondition = data.weatherCondition || '';
            
            if (currentWeatherCondition) {
                let weatherIcon = '<i class="fas fa-cloud-sun text-info"></i>';
                let weatherText = currentWeatherCondition;
                
                // Map weather conditions to icons
                if (currentWeatherCondition === 'Céu limpo') {
                    weatherIcon = '<i class="fas fa-sun text-warning"></i>';
                } else if (currentWeatherCondition === 'Nublado') {
                    weatherIcon = '<i class="fas fa-cloud text-secondary"></i>';
                } else if (currentWeatherCondition === 'Chuva') {
                    weatherIcon = '<i class="fas fa-cloud-rain text-primary"></i>';
                } else if (currentWeatherCondition === 'Vento moderado a forte') {
                    weatherIcon = '<i class="fas fa-wind text-info"></i>';
                } else if (currentWeatherCondition.startsWith('Outro:')) {
                    weatherIcon = '<i class="fas fa-question-circle text-muted"></i>';
                }
                
                document.getElementById('weatherIconCompact').innerHTML = weatherIcon;
                document.getElementById('weatherTextCompact').textContent = weatherText;
            } else {
                document.getElementById('weatherIconCompact').innerHTML = '<i class="fas fa-cloud-sun text-info"></i>';
                document.getElementById('weatherTextCompact').textContent = 'Clique para definir';
            }

            // Observers Card - populate global variable and display
            currentObserversCount = data.numberOfObservers || 0;
            document.getElementById('observersCountDisplay').textContent = currentObserversCount || '-';
        }

        function loadRouteOnMap(coordinates) {
            if (coordinates && coordinates.length > 0) {
                // Clear existing route markers only
                markers.forEach(marker => marker.setMap(null));
                markers = [];
                
                routeCoordinates = coordinates;
                
                // Add markers for each coordinate with proper styling
                coordinates.forEach((coord, index) => {
                    let markerIcon;
                    if (index === 0) {
                        // Start point - green with floating label
                        markerIcon = {
                            path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                            fillColor: '#22c55e',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2,
                            scale: 0.7,
                            anchor: new google.maps.Point(0, 8),
                            labelOrigin: new google.maps.Point(0, -40)
                        };
                    } else if (index === coordinates.length - 1) {
                        // End point - red with floating label
                        markerIcon = {
                            path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                            fillColor: '#dc2626',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2,
                            scale: 0.7,
                            anchor: new google.maps.Point(0, 8),
                            labelOrigin: new google.maps.Point(0, -40)
                        };
                    } else {
                        // Regular point - blue circle
                        markerIcon = {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: '#0a7ea4',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 3
                        };
                    }

                    const marker = new google.maps.Marker({
                        position: { lat: coord.lat, lng: coord.lng },
                        map: map,
                        title: index === 0 ? 'Início' : (index === coordinates.length - 1 ? 'Fim' : `Ponto ${index + 1}`),
                        icon: markerIcon,
                        label: index === 0 ? {
                            text: 'INÍCIO',
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        } : (index === coordinates.length - 1 ? {
                            text: 'FIM',
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        } : {
                            text: (index + 1).toString(),
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        }),
                        zIndex: index === 0 || index === coordinates.length - 1 ? 1000 : 100
                    });

                    // Add ground point for start and end
                    if (index === 0 || index === coordinates.length - 1) {
                        const groundMarker = new google.maps.Marker({
                            position: { lat: coord.lat, lng: coord.lng },
                            map: map,
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                scale: 9,
                                fillColor: index === 0 ? '#22c55e' : '#dc2626',
                                fillOpacity: 1,
                                strokeColor: '#ffffff',
                                strokeWeight: 2
                            },
                            label: {
                                text: (index + 1).toString(),
                                fontSize: '12px',
                                fontWeight: 'bold',
                                color: '#ffffff'
                            },
                            title: index === 0 ? 'Início Ground' : 'Fim Ground',
                            zIndex: 50
                        });
                        markers.push(groundMarker);
                    }

                    markers.push(marker);
                });
                
                // Update route path
                routePath.setPath(routeCoordinates);
                if (window.shadowPath) {
                    window.shadowPath.setPath(routeCoordinates);
                }
                
                // Update stats
                updateRouteStats();
                
                // Center map on route
                if (routeCoordinates.length > 0) {
                    const bounds = new google.maps.LatLngBounds();
                    routeCoordinates.forEach(coord => {
                        bounds.extend(new google.maps.LatLng(coord.lat, coord.lng));
                    });
                    map.fitBounds(bounds);
                    
                    // Add some padding
                    setTimeout(() => {
                        if (routeCoordinates.length === 1) {
                            map.setZoom(15);
                        }
                    }, 100);
                }
                
                // Enable save button if we have at least 2 points
                if (routeCoordinates.length >= 2) {
                    document.getElementById('saveTrajetoBtn').disabled = false;
                }
                
                // Enable undo button if we have at least 1 point
                if (routeCoordinates.length >= 1) {
                    document.getElementById('undoBtn').disabled = false;
                }
            }
        }

        function saveTrajetoDetails() {
            // Get form values
            const name = document.getElementById('trajetoName').value;
            const description = document.getElementById('trajetoDescription').value;
            const difficulty = document.getElementById('trajetoDifficulty').value;
            const status = document.getElementById('trajetoStatusSelect').value;
            
            if (!name.trim()) {
                Swal.fire({
                    title: 'Erro',
                    text: 'O nome do trajeto é obrigatório.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }
            
            // Update trajeto data
            if (trajetoData) {
                trajetoData.name = name;
                trajetoData.description = description;
                trajetoData.difficulty = difficulty;
                trajetoData.status = status;
                trajetoData.updatedAt = new Date().toISOString();
            }
            
            // Update header
            document.getElementById('headerTrajetoName').textContent = name;
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editDetailsModal'));
            modal.hide();
            
            Swal.fire({
                title: 'Sucesso!',
                text: 'Detalhes do trajeto atualizados.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a'
            });
        }

        function getAddressFromCoordinates(lat, lng) {
            const geocoder = new google.maps.Geocoder();
            const latlng = { lat: lat, lng: lng };
            
            geocoder.geocode({ 
                location: latlng,
                language: 'pt',  // Request results in Portuguese
                region: 'PT'     // Bias results towards Portugal
            }, (results, status) => {
                if (status === 'OK') {
                    if (results[0]) {
                        // Get a more readable address format
                        let address = results[0].formatted_address;
                        
                        // Try to get a shorter, more relevant address
                        for (let i = 0; i < results.length; i++) {
                            const result = results[i];
                            // Look for route, locality, or administrative_area_level_3 for better context
                            if (result.types.includes('route') || 
                                result.types.includes('locality') || 
                                result.types.includes('administrative_area_level_3')) {
                                address = result.formatted_address;
                                break;
                            }
                        }
                        
                        // Store the full address for saving to database
                        window.fullStartingAddress = address;
                        
                        // Use full address for display since we have enough space
                        document.getElementById('startingAddress').textContent = address;
                    } else {
                        document.getElementById('startingAddress').textContent = 'Endereço não encontrado';
                    }
                } else {
                    document.getElementById('startingAddress').textContent = 'Erro ao obter endereço';
                }
            });
        }

        function addRoutePoint(latLng) {
            const pointIndex = routeCoordinates.length;
            
            // Determine marker icon based on position
            let markerIcon;
            if (pointIndex === 0) {
                // Start point - green with wider marker for full word, floating above
                markerIcon = {
                    path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                    fillColor: '#22c55e',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 0.7,
                    anchor: new google.maps.Point(0, 8),
                    labelOrigin: new google.maps.Point(0, -40)
                };
            } else {
                // Regular point - blue circle with number
                markerIcon = {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 9,
                    fillColor: '#0a7ea4',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 3
                };
            }

            // Add marker
            const marker = new google.maps.Marker({
                position: latLng,
                map: map,
                title: pointIndex === 0 ? 'Início' : `Ponto ${pointIndex + 1}`,
                icon: markerIcon,
                label: pointIndex === 0 ? {
                    text: 'INÍCIO',
                    fontSize: '9px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                } : {
                    text: (pointIndex + 1).toString(),
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                },
                zIndex: pointIndex === 0 ? 1000 : 100
            });

            // Add a ground point marker for INÍCIO to show connection to the line
            if (pointIndex === 0) {
                const groundMarker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#22c55e',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    },
                    label: {
                        text: '1',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    },
                    title: 'Início Ground',
                    zIndex: 50
                });
                markers.push(groundMarker);
            }

            markers.push(marker);
            routeCoordinates.push({
                lat: latLng.lat(),
                lng: latLng.lng()
            });
            
            // Get address for the starting point
            if (pointIndex === 0) {
                getAddressFromCoordinates(latLng.lat(), latLng.lng());
            }

            // Set current marker as finish if it's not the first point
            if (routeCoordinates.length > 1) { // Check actual route points, not marker count
                // Find and remove previous floating finish marker if it exists
                for (let i = markers.length - 1; i >= 0; i--) {
                    if (markers[i].getTitle() === 'Fim') {
                        markers[i].setMap(null);
                        markers.splice(i, 1);
                        break;
                    }
                }
                
                // Change previous final ground point back to blue (if it exists)
                for (let i = markers.length - 1; i >= 0; i--) {
                    const marker = markers[i];
                    // Only change red markers (not green INÍCIO markers) back to blue
                    if (marker.getIcon && marker.getIcon().fillColor === '#dc2626') {
                        marker.setIcon({
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: '#0a7ea4',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2
                        });
                        break;
                    }
                }
                
                // Add ground point for finish (red for final point)
                const finishGroundMarker = new google.maps.Marker({
                    position: latLng,
                    map: map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 9,
                        fillColor: '#dc2626',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    },
                    label: {
                        text: (routeCoordinates.length).toString(),
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    },
                    zIndex: 50
                });
                markers.push(finishGroundMarker);
                
                // Update current marker to floating finish
                marker.setIcon({
                    path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                    fillColor: '#dc2626',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 0.7,
                    anchor: new google.maps.Point(0, 8),
                    labelOrigin: new google.maps.Point(0, -40)
                });
                marker.setLabel({
                    text: 'FIM',
                    fontSize: '9px',
                    fontWeight: 'bold',
                    color: '#ffffff'
                });
                marker.setTitle('Fim');
                marker.setZIndex(1000);
            }

            // Update route path with shadow effect
            routePath.setPath(routeCoordinates);
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Update stats
            updateRouteStats();

            // Enable save button if we have at least 2 points
            if (routeCoordinates.length >= 2) {
                document.getElementById('saveTrajetoBtn').disabled = false;
            }

            // Enable undo button if we have at least 1 point
            document.getElementById('undoBtn').disabled = routeCoordinates.length === 0;
        }

        function undoLastPoint() {
            if (routeCoordinates.length === 0) return;

            // Remove last coordinate first
            routeCoordinates.pop();

            // Clear all markers and rebuild them for the remaining coordinates
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Rebuild markers for remaining coordinates
            if (routeCoordinates.length > 0) {
                routeCoordinates.forEach((coord, index) => {
                    let markerIcon;
                    if (index === 0) {
                        // Start point - green with floating label
                        markerIcon = {
                            path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                            fillColor: '#22c55e',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2,
                            scale: 0.7,
                            anchor: new google.maps.Point(0, 8),
                            labelOrigin: new google.maps.Point(0, -40)
                        };
                    } else if (index === routeCoordinates.length - 1 && routeCoordinates.length > 1) {
                        // End point - red with floating label
                        markerIcon = {
                            path: 'M0-60c-12 0-22 8-22 18 0 16 22 32 22 32s22-16 22-32c0-10-10-18-22-18z',
                            fillColor: '#dc2626',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 2,
                            scale: 0.7,
                            anchor: new google.maps.Point(0, 8),
                            labelOrigin: new google.maps.Point(0, -40)
                        };
                    } else {
                        // Regular point - blue circle
                        markerIcon = {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 9,
                            fillColor: '#0a7ea4',
                            fillOpacity: 1,
                            strokeColor: '#ffffff',
                            strokeWeight: 3
                        };
                    }

                    const marker = new google.maps.Marker({
                        position: { lat: coord.lat, lng: coord.lng },
                        map: map,
                        title: index === 0 ? 'Início' : (index === routeCoordinates.length - 1 && routeCoordinates.length > 1 ? 'Fim' : `Ponto ${index + 1}`),
                        icon: markerIcon,
                        label: index === 0 ? {
                            text: 'INÍCIO',
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        } : (index === routeCoordinates.length - 1 && routeCoordinates.length > 1 ? {
                            text: 'FIM',
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        } : {
                            text: (index + 1).toString(),
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#ffffff'
                        }),
                        zIndex: index === 0 || (index === routeCoordinates.length - 1 && routeCoordinates.length > 1) ? 1000 : 100
                    });

                    // Add ground point for start and end
                    if (index === 0 || (index === routeCoordinates.length - 1 && routeCoordinates.length > 1)) {
                        const groundMarker = new google.maps.Marker({
                            position: { lat: coord.lat, lng: coord.lng },
                            map: map,
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                scale: 9,
                                fillColor: index === 0 ? '#22c55e' : '#dc2626',
                                fillOpacity: 1,
                                strokeColor: '#ffffff',
                                strokeWeight: 2
                            },
                            label: {
                                text: (index + 1).toString(),
                                fontSize: '12px',
                                fontWeight: 'bold',
                                color: '#ffffff'
                            },
                            title: index === 0 ? 'Início Ground' : 'Fim Ground',
                            zIndex: 50
                        });
                        markers.push(groundMarker);
                    }

                    markers.push(marker);
                });
            }

            // Update route path
            routePath.setPath(routeCoordinates);
            if (window.shadowPath) {
                window.shadowPath.setPath(routeCoordinates);
            }

            // Update stats
            updateRouteStats();

            // Update button states
            document.getElementById('undoBtn').disabled = routeCoordinates.length === 0;
            document.getElementById('saveTrajetoBtn').disabled = routeCoordinates.length < 2;
        }

        function updateRouteStats() {
            const pointsCount = routeCoordinates.length;
            const distanceElement = document.getElementById('routeDistance');
            const validationElement = document.getElementById('distanceValidation');
            const pointsElement = document.getElementById('routePoints');
            
            // Update points count
            pointsElement.textContent = pointsCount;

            if (pointsCount >= 2) {
                const distance = google.maps.geometry.spherical.computeLength(routeCoordinates);
                const distanceKm = (distance / 1000).toFixed(2);
                distanceElement.textContent = `${distanceKm} km`;
                
                // Validate distance (minimum 2.5 km) - only for display, not for button state
                validateDistance(parseFloat(distanceKm), distanceElement, validationElement);
            } else {
                distanceElement.textContent = '0 km';
                distanceElement.className = distanceElement.className.replace(/distance-(insufficient|sufficient)/g, '');
                validationElement.style.display = 'none';
            }

            // Update hidden input with coordinates
            document.getElementById('routeCoordinates').value = JSON.stringify(routeCoordinates);
            
            // Update contacts count
            updateContactsCount();
        }
        
        function updateContactsCount() {
            const dataContactCount = trajetoData.contacts ? trajetoData.contacts.length : 0;
            const mapContactCount = contactMarkers ? contactMarkers.length : 0;
            const contactCount = Math.max(dataContactCount, mapContactCount);
            
            const contactsCountElement = document.getElementById('contactsCount');
            const contactsCountValueElement = document.getElementById('contactsCountValue');
            
            if (contactCount > 0) {
                contactsCountValueElement.textContent = contactCount;
                contactsCountElement.style.display = 'inline';
            } else {
                contactsCountElement.style.display = 'none';
            }
        }
        
        function validateDistance(distanceKm, distanceElement, validationElement) {
            const minimumDistance = 2.5;
            
            // Remove existing classes
            distanceElement.className = distanceElement.className.replace(/distance-(insufficient|sufficient)/g, '');
            
            if (distanceKm < minimumDistance) {
                distanceElement.classList.add('distance-insufficient');
                validationElement.innerHTML = `
                    <div class="distance-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span style="font-size: 0.8rem;">Faltam ${(minimumDistance - distanceKm).toFixed(2)} km</span>
                    </div>
                `;
                validationElement.style.display = 'block';
                return false; // Distance is insufficient
            } else {
                distanceElement.classList.add('distance-sufficient');
                validationElement.innerHTML = `
                    <div class="distance-success">
                        <i class="fas fa-check-circle"></i>
                        <span style="font-size: 0.8rem;">Válida</span>
                    </div>
                `;
                validationElement.style.display = 'block';
                return true; // Distance is sufficient
            }
        }

        function clearRoute() {
            // Check if there are contacts (both in trajetoData and on the map)
            const hasContacts = (trajetoData.contacts && trajetoData.contacts.length > 0) || 
                               (contactMarkers && contactMarkers.length > 0);
            
            if (hasContacts) {
                // Show options modal when there are contacts
                showClearOptionsModal();
            } else {
                // Simple confirmation when no contacts
                Swal.fire({
                    title: 'Limpar Trajeto?',
                    text: 'Tem a certeza que pretende limpar todo o trajeto? Esta ação não pode ser desfeita.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '<i class="fas fa-eraser me-2"></i>Sim, Limpar',
                    cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
                    confirmButtonColor: '#dc2626',
                    cancelButtonColor: '#6b7280',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        clearRouteOnly();
                    }
                });
            }
        }

        function showClearOptionsModal() {
            // Get contact count from both data and map markers (use the higher value)
            const dataContactCount = trajetoData.contacts ? trajetoData.contacts.length : 0;
            const mapContactCount = contactMarkers ? contactMarkers.length : 0;
            const contactCount = Math.max(dataContactCount, mapContactCount);
            
            Swal.fire({
                title: 'O que pretende limpar?',
                html: `
                    <div style="text-align: center; padding: 1rem;">
                        <p style="font-size: 1rem; margin-bottom: 1.5rem; color: #374151;">
                            Este trajeto tem <strong>${contactCount} contacto${contactCount > 1 ? 's' : ''}</strong>. 
                            Escolha o que pretende limpar:
                        </p>
                        <div style="display: flex; flex-direction: column; gap: 1rem; max-width: 400px; margin: 0 auto;">
                            <button type="button" class="clear-option-btn clear-both-btn" onclick="clearRouteAndContacts()">
                                <div class="clear-option-icon">
                                    <i class="fas fa-trash-alt"></i>
                                </div>
                                <div class="clear-option-content">
                                    <h6 class="clear-option-title">Limpar Trajeto e Contactos</h6>
                                    <p class="clear-option-description">Remove tudo e recomeça do zero</p>
                                </div>
                            </button>
                            
                            <button type="button" class="clear-option-btn clear-route-btn" onclick="clearRouteOnly()">
                                <div class="clear-option-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="clear-option-content">
                                    <h6 class="clear-option-title">Limpar Apenas Trajeto</h6>
                                    <p class="clear-option-description">Mantém os contactos, remove só o trajeto</p>
                                </div>
                            </button>
                            
                            <button type="button" class="clear-option-btn clear-contacts-btn" onclick="clearContactsOnly()">
                                <div class="clear-option-icon">
                                    <i class="fas fa-dove"></i>
                                </div>
                                <div class="clear-option-content">
                                    <h6 class="clear-option-title">Limpar Apenas Contactos</h6>
                                    <p class="clear-option-description">Mantém o trajeto, remove só os contactos</p>
                                </div>
                            </button>
                        </div>
                    </div>
                `,
                icon: null,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
                cancelButtonColor: '#6b7280',
                customClass: {
                    popup: 'clear-options-popup',
                    cancelButton: 'clear-cancel-btn'
                },
                width: '500px',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp animate__faster'
                }
            });
        }

        function clearRouteAndContacts() {
            Swal.close(); // Close the options modal
            
            Swal.fire({
                title: 'Limpar Tudo?',
                text: 'Tem a certeza que pretende limpar o trajeto e todos os contactos? Esta ação não pode ser desfeita.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-trash-alt me-2"></i>Sim, Limpar Tudo',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280',
                reverseButtons: true
            }).then(async (result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'A Limpar Tudo...',
                        text: 'Por favor aguarde enquanto o trajeto e contactos são removidos.',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    try {
                        // Delete contacts from database first
                        const deleteResponse = await fetch('delete_trajectory_contacts.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                trajectoryId: document.getElementById('trajetoId').value,
                                zoneId: document.getElementById('zoneId').value || trajetoData.zoneId || ''
                            })
                        });
                        
                        if (!deleteResponse.ok) {
                            const errorText = await deleteResponse.text();
                            console.error('Delete response error text:', errorText);
                            throw new Error(`HTTP error! status: ${deleteResponse.status} - ${errorText}`);
                        }
                        
                        const deleteResult = await deleteResponse.json();
                        console.log('Delete result:', deleteResult);
                        
                        if (deleteResult.success) {
                            // Clear route markers
                            markers.forEach(marker => marker.setMap(null));
                            markers = [];

                            // Clear route coordinates
                            routeCoordinates = [];
                            routePath.setPath([]);
                            if (window.shadowPath) {
                                window.shadowPath.setPath([]);
                            }
                            
                            // Remove contact markers
                            contactMarkers.forEach(marker => marker.setMap(null));
                            contactMarkers = [];
                            
                            // Clear contacts data
                            trajetoData.contacts = [];

                            // Reset starting address
                            document.getElementById('startingAddress').textContent = 'Não definida';

                            // Update stats
                            updateRouteStats();
                            
                            // Update contacts count display
                            updateContactsCount();

                            // Disable buttons
                            document.getElementById('saveTrajetoBtn').disabled = true;
                            document.getElementById('undoBtn').disabled = true;
                            
                            // Success message
                            Swal.fire({
                                title: 'Tudo Limpo!',
                                text: `O trajeto e todos os ${deleteResult.deletedCount} contactos foram limpos com sucesso.`,
                                icon: 'success',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#16a34a'
                            });
                        } else {
                            console.error('Delete failed:', deleteResult.message);
                            throw new Error(deleteResult.message || 'Failed to delete contacts');
                        }
                        
                    } catch (error) {
                        console.error('Clear all error:', error);
                        
                        // Still clear local data even if database deletion fails
                        markers.forEach(marker => marker.setMap(null));
                        markers = [];
                        routeCoordinates = [];
                        routePath.setPath([]);
                        if (window.shadowPath) {
                            window.shadowPath.setPath([]);
                        }
                        contactMarkers.forEach(marker => marker.setMap(null));
                        contactMarkers = [];
                        trajetoData.contacts = [];
                        document.getElementById('startingAddress').textContent = 'Não definida';
                        updateRouteStats();
                        updateContactsCount();
                        document.getElementById('saveTrajetoBtn').disabled = true;
                        document.getElementById('undoBtn').disabled = true;
                        
                        // Error message
                        Swal.fire({
                            title: 'Erro ao Limpar',
                            text: 'Houve um erro ao limpar os contactos da base de dados, mas os dados locais foram limpos. Erro: ' + error.message,
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#f59e0b'
                        });
                    }
                }
            });
        }

        function clearRouteOnly() {
            if (Swal.isVisible()) {
                Swal.close(); // Close the options modal if it's open
            }
            
            Swal.fire({
                title: 'Limpar Trajeto?',
                text: 'Tem a certeza que pretende limpar apenas o trajeto? Os contactos serão mantidos.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-route me-2"></i>Sim, Limpar Trajeto',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
                confirmButtonColor: '#f59e0b',
                cancelButtonColor: '#6b7280',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Clear route markers only
                    markers.forEach(marker => marker.setMap(null));
                    markers = [];

                    // Clear route coordinates
                    routeCoordinates = [];
                    routePath.setPath([]);
                    if (window.shadowPath) {
                        window.shadowPath.setPath([]);
                    }

                    // Reset starting address
                    document.getElementById('startingAddress').textContent = 'Não definida';

                    // Update stats
                    updateRouteStats();

                    // Disable buttons
                    document.getElementById('saveTrajetoBtn').disabled = true;
                    document.getElementById('undoBtn').disabled = true;
                    
                    // Success message
                    Swal.fire({
                        title: 'Trajeto Limpo!',
                        text: 'O trajeto foi limpo com sucesso. Os contactos foram mantidos.',
                        icon: 'success',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#16a34a'
                    });
                }
            });
        }

        function clearContactsOnly() {
            Swal.close(); // Close the options modal
            
            // Get contact count from both data and map markers (use the higher value)
            const dataContactCount = trajetoData.contacts ? trajetoData.contacts.length : 0;
            const mapContactCount = contactMarkers ? contactMarkers.length : 0;
            const contactCount = Math.max(dataContactCount, mapContactCount);
            
            Swal.fire({
                title: 'Limpar Contactos?',
                text: `Tem a certeza que pretende limpar todos os ${contactCount} contactos? O trajeto será mantido.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-dove me-2"></i>Sim, Limpar Contactos',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
                confirmButtonColor: '#0891b2',
                cancelButtonColor: '#6b7280',
                reverseButtons: true
            }).then(async (result) => {
                if (result.isConfirmed) {
        
                    
                    // Show loading state
                    Swal.fire({
                        title: 'A Limpar Contactos...',
                        text: 'Por favor aguarde enquanto os contactos são removidos.',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    try {
                        // Delete contacts from database first
                        const deleteResponse = await fetch('delete_trajectory_contacts.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                trajectoryId: document.getElementById('trajetoId').value,
                                zoneId: document.getElementById('zoneId').value || trajetoData.zoneId || ''
                            })
                        });
                        
                        if (!deleteResponse.ok) {
                            const errorText = await deleteResponse.text();
                            console.error('Delete response error text:', errorText);
                            throw new Error(`HTTP error! status: ${deleteResponse.status} - ${errorText}`);
                        }
                        
                        const deleteResult = await deleteResponse.json();
                        console.log('Delete result:', deleteResult);
                        
                        if (deleteResult.success) {
                            // Now clear local data and map markers
                            contactMarkers.forEach(marker => marker.setMap(null));
                            contactMarkers = [];
                            
                            // Clear contacts data
                            trajetoData.contacts = [];
                            
                            // Update contacts count display
                            updateContactsCount();
                            
                            // Success message
                            Swal.fire({
                                title: 'Contactos Limpos!',
                                text: `Todos os ${deleteResult.deletedCount} contactos foram limpos com sucesso. O trajeto foi mantido.`,
                                icon: 'success',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#16a34a'
                            });
                        } else {
                            console.error('Delete failed:', deleteResult.message);
                            throw new Error(deleteResult.message || 'Failed to delete contacts');
                        }
                        
                    } catch (error) {
                        console.error('Delete contacts error:', error);
                        
                        // Still clear local data even if database deletion fails
                        contactMarkers.forEach(marker => marker.setMap(null));
                        contactMarkers = [];
                        trajetoData.contacts = [];
                        
                        // Update contacts count display
                        updateContactsCount();
                        
                        Swal.fire({
                            title: 'Contactos Limpos Localmente',
                            text: 'Os contactos foram removidos do mapa, mas pode ter ocorrido um erro na base de dados. Recarregue a página para verificar.',
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#f59e0b'
                        });
                    }
                }
            });
        }

        function clearAllMarkers() {
            // Clear both route and contact markers
            markers.forEach(marker => marker.setMap(null));
            markers = [];
            contactMarkers.forEach(marker => marker.setMap(null));
            contactMarkers = [];
        }

        function clearContactMarkers() {
            // Clear contact markers only
            contactMarkers.forEach(marker => marker.setMap(null));
            contactMarkers = [];
            
            // Update contacts count display
            updateContactsCount();
        }

        function finishRoute() {
            if (routeCoordinates.length >= 2) {
                Swal.fire({
                    title: 'Rota Atualizada!',
                    text: `Rota com ${routeCoordinates.length} pontos atualizada.`,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
            }
        }

        function loadContactsOnMap(contacts) {
            if (!contacts || contacts.length === 0) {
                return;
            }

            // Clear existing contact markers first
            clearContactMarkers();
            
            // Check if all contacts belong to the current trajectory
            const currentTrajectoryId = document.getElementById('trajetoId').value;
            const wrongContacts = contacts.filter(contact => contact.trajectoryId !== currentTrajectoryId);

            let visibleContactIndex = 0;
            contacts.forEach((contact, index) => {
                if (contact.coordinates && contact.coordinates.lat && contact.coordinates.lng) {
                    visibleContactIndex++; // Increment only for contacts that will be displayed
                    const currentContactNumber = visibleContactIndex; // Capture the current number
                    const position = { 
                        lat: parseFloat(contact.coordinates.lat), 
                        lng: parseFloat(contact.coordinates.lng) 
                    };

                    // Create canvas for dove marker
                    const canvas = document.createElement('canvas');
                    canvas.width = 32;
                    canvas.height = 32;
                    const ctx = canvas.getContext('2d');
                    
                    // Draw circle background
                    ctx.beginPath();
                    ctx.arc(16, 16, 14, 0, 2 * Math.PI);
                    ctx.fillStyle = '#16a34a';  // Keep the green color for consistency
                    ctx.fill();
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // Load and draw dove icon
                    const doveImg = new Image();
                    doveImg.onload = function() {
                        // Create a temporary canvas to process the dove icon
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = 16;
                        tempCanvas.height = 16;
                        const tempCtx = tempCanvas.getContext('2d');
                        
                        // Draw the dove icon on temp canvas
                        tempCtx.drawImage(doveImg, 0, 0, 16, 16);
                        
                        // Get image data to process pixels
                        const imageData = tempCtx.getImageData(0, 0, 16, 16);
                        const data = imageData.data;
                        
                        // Convert non-transparent pixels to white
                        for (let i = 0; i < data.length; i += 4) {
                            if (data[i + 3] > 0) { // If pixel is not transparent
                                data[i] = 255;     // Red = 255 (white)
                                data[i + 1] = 255; // Green = 255 (white)
                                data[i + 2] = 255; // Blue = 255 (white)
                            }
                        }
                        
                        // Put the processed image data back
                        tempCtx.putImageData(imageData, 0, 0);
                        
                        // Draw the white dove icon on the main canvas
                        ctx.drawImage(tempCanvas, 8, 8);
                        
                        // Create marker with the canvas image
                        const contactMarker = new google.maps.Marker({
                            position: position,
                            map: map,
                            icon: {
                                url: canvas.toDataURL(),
                                scaledSize: new google.maps.Size(32, 32),
                                anchor: new google.maps.Point(16, 16)
                            },
                            title: `Contacto ${currentContactNumber}: ${contact.circumstance || 'Observação'}`,
                            zIndex: 500
                        });

                        // Add info window for contact details
                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 0.5rem; min-width: 220px;">
                                    <h6 style="margin: 0 0 0.5rem 0; color: #16a34a; font-weight: 600;">
                                        <i class="fas fa-dove" style="margin-right: 0.5rem;"></i>
                                        Contacto ${currentContactNumber}
                                    </h6>
                                    <div style="font-size: 0.9rem; line-height: 1.4;">
                                        <div style="margin-bottom: 0.25rem;">
                                            <strong>Circunstância:</strong> ${translateCircumstance(contact.circumstance) || 'Não especificada'}
                                        </div>
                                        <div style="margin-bottom: 0.25rem;">
                                            <strong>Local:</strong> ${translateLocation(contact.location) || 'Não especificado'}
                                        </div>
                                        <div style="margin-bottom: 0.25rem;">
                                            <strong>Hora:</strong> ${contact.time || 'Não especificada'}
                                        </div>
                                        <div style="color: #6b7280; font-size: 0.8rem; margin-bottom: 0.75rem;">
                                            Lat: ${position.lat.toFixed(6)}, Lng: ${position.lng.toFixed(6)}
                                        </div>
                                        <div style="display: flex; gap: 0.5rem; justify-content: center;">
                                            <button onclick="editContact(${index})" class="btn btn-sm" style="background: #0a7ea4; color: white; border: none; padding: 0.25rem 0.75rem; border-radius: 4px; font-size: 0.8rem;">
                                                <i class="fas fa-edit" style="margin-right: 0.25rem;"></i>Editar
                                            </button>
                                            <button onclick="deleteContact(${index})" class="btn btn-sm" style="background: #dc2626; color: white; border: none; padding: 0.25rem 0.75rem; border-radius: 4px; font-size: 0.8rem;">
                                                <i class="fas fa-trash" style="margin-right: 0.25rem;"></i>Eliminar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `
                        });

                        // Add click listener to show info window
                        contactMarker.addListener('click', function() {
                            infoWindow.open(map, contactMarker);
                        });

                        // Store contact data in marker for later retrieval during save
                        contactMarker.contactData = contact;
                        contactMarker.contactIndex = index;

                        contactMarkers.push(contactMarker);
                    };
                    doveImg.src = '../../assets/images/icons/dove-icon.png';
                }
            });
            
            // Update contacts count display
            updateContactsCount();
        }

        function translateCircumstance(circumstance) {
            if (!circumstance) return 'Não especificada';
            
            const translations = {
                // Main circumstances
                'adultSinging': 'Rola adulta a cantar',
                'adultDisplay': 'Adulto em display',
                'flying': 'Rola em voo',
                'landed': 'Pousada',
                'feeding': 'Alimentando-se',
                'resting': 'Descansando',
                'emptyNest': 'Ninho vazio',
                'adultPerched': 'Adulto pousado',
                'occupiedNest': 'Ninho ocupado',
                'groups': 'Grupos de Rolas',
                'other': 'Outro',
                
                // Alternative spellings/formats
                'Voando': 'Rola em voo',
                'Pousada': 'Pousada',
                'Alimentando-se': 'Alimentando-se',
                'Descansando': 'Descansando',
                'Ninho Vazio': 'Ninho vazio',
                'Ninho vazio': 'Ninho vazio',
                'Adulto pousado': 'Adulto pousado',
                'Ninho ocupado': 'Ninho ocupado',
                'Grupos de Rolas': 'Grupos de Rolas',
                'Outro': 'Outro',
                'Outra': 'Outro'
            };
            
            return translations[circumstance] || circumstance;
        }

        function translateLocation(location) {
            if (!location) return 'Não especificado';
            
            const translations = {
                // Main locations
                'field': 'Campo',
                'water': 'Água',
                'waterPoint': 'Ponto de água',
                'tree': 'Árvore',
                'trees': 'Árvores',
                'shrub': 'Arbusto',
                'clearing': 'Clareira',
                'building': 'Edifício',
                'other': 'Outro',
                
                // Alternative spellings/formats
                'Campo': 'Campo',
                'Água': 'Água',
                'Ponto de água': 'Ponto de água',
                'Árvore': 'Árvore',
                'Árvores': 'Árvores',
                'Arbusto': 'Arbusto',
                'Clareira': 'Clareira',
                'Edifício': 'Edifício',
                'Outro': 'Outro'
            };
            
            return translations[location] || location;
        }

        // Contact editing functions
        function editContact(contactIndex, isNewContact = false) {
            
            if (!trajetoData.contacts || !trajetoData.contacts[contactIndex]) {
                return;
            }

            const contact = trajetoData.contacts[contactIndex];

            // Store the contact index for saving
            document.getElementById('contactEditIndex').value = contactIndex;

            // Update modal title based on whether it's a new contact
            const modalTitle = document.getElementById('contactEditModalLabel');
            if (isNewContact) {
                modalTitle.innerHTML = '<i class="fas fa-dove me-2"></i>Novo Contacto';
            } else {
                modalTitle.innerHTML = '<i class="fas fa-dove me-2"></i>Editar Contacto';
            }

            // Reset all radio buttons
            document.querySelectorAll('input[name="contact_circumstances"]').forEach(radio => radio.checked = false);
            document.querySelectorAll('input[name="contact_location"]').forEach(radio => radio.checked = false);
            
            // Hide all conditional inputs
            document.getElementById('circumstanceGroupsInput').style.display = 'none';
            document.getElementById('circumstanceOtherInput').style.display = 'none';
            document.getElementById('locationOtherInput').style.display = 'none';
            
            // Clear conditional input values
            document.getElementById('circumstanceGroupsNumber').value = '';
            document.getElementById('circumstanceOtherText').value = '';
            document.getElementById('locationOtherText').value = '';

            // Populate form fields
            if (contact.circumstance) {
                // Handle special cases first
                if (contact.circumstance.includes('Grupos de Rolas')) {
                    document.getElementById('circumstance_groups').checked = true;
                    const match = contact.circumstance.match(/\((\d+) indivíduos\)/);
                    if (match) {
                        document.getElementById('circumstanceGroupsInput').style.display = 'block';
                        document.getElementById('circumstanceGroupsNumber').value = match[1];
                    }
                } else {
                    // Try to find matching radio button
                    const circumstanceRadio = document.querySelector(`input[name="contact_circumstances"][value="${contact.circumstance}"]`);
                    if (circumstanceRadio) {
                        circumstanceRadio.checked = true;
                    } else {
                        // It's a custom "other" value
                        document.getElementById('circumstance_other').checked = true;
                        document.getElementById('circumstanceOtherInput').style.display = 'block';
                        document.getElementById('circumstanceOtherText').value = contact.circumstance;
                    }
                }
            }

            if (contact.location) {
                const locationRadio = document.querySelector(`input[name="contact_location"][value="${contact.location}"]`);
                if (locationRadio) {
                    locationRadio.checked = true;
                } else {
                    // It's a custom "other" value
                    document.getElementById('location_other').checked = true;
                    document.getElementById('locationOtherInput').style.display = 'block';
                    document.getElementById('locationOtherText').value = contact.location;
                }
            }

            document.getElementById('contactTime').value = contact.time || '';
            
            if (contact.coordinates) {
                document.getElementById('contactCoords').value = 
                    `${contact.coordinates.lat.toFixed(6)}, ${contact.coordinates.lng.toFixed(6)}`;
            }

            // Setup event listeners for the modal
            setupContactModalListeners();

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('contactEditModal'));
            
            // Store if it's a new contact for cleanup on cancel
            modal._isNewContact = isNewContact;
            modal._contactIndex = contactIndex;
            
            modal.show();
        }

        function deleteContact(contactIndex) {
            
            if (!trajetoData.contacts || !trajetoData.contacts[contactIndex]) {
                return;
            }

            const contact = trajetoData.contacts[contactIndex];
            
            Swal.fire({
                title: 'Eliminar Contacto?',
                text: `Tem a certeza que pretende eliminar o contacto ${contactIndex + 1}?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-trash me-2"></i>Sim, Eliminar',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'A eliminar...',
                        text: 'Por favor aguarde...',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // If contact has an ID (exists in database), delete it from database first
                    if (contact.id) {

                        
                        fetch('delete_contact.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                contactId: contact.id
                            })
                        })
                        .then(response => response.json())
                        .then(data => {

                            
                            if (data.success) {
                                // Remove the specific contact from local arrays without reloading all

                                
                                // Remove from contacts array
                                trajetoData.contacts.splice(contactIndex, 1);
                                
                                // Remove the specific marker from map
                                if (contactMarkers[contactIndex]) {
                                    contactMarkers[contactIndex].setMap(null);
                                    contactMarkers.splice(contactIndex, 1);
                                }
                                
                                // Reload contacts on map to update indices and numbering
                                loadContactsOnMap(trajetoData.contacts);
                                
                                Swal.fire({
                                    title: 'Contacto Eliminado!',
                                    text: 'O contacto foi eliminado com sucesso.',
                                    icon: 'success',
                                    confirmButtonText: 'OK',
                                    confirmButtonColor: '#16a34a'
                                });
                            } else {
                                Swal.fire({
                                    title: 'Erro',
                                    text: 'Erro ao eliminar o contacto: ' + data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                    confirmButtonColor: '#dc2626'
                                });
                            }
                        })
                        .catch(error => {
                            Swal.fire({
                                title: 'Erro',
                                text: 'Erro ao eliminar o contacto. Tente novamente.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#dc2626'
                            });
                        });
                    } else {
                        // Contact doesn't exist in database yet, just remove from local array

                        
                        // Remove from contacts array
                        trajetoData.contacts.splice(contactIndex, 1);
                        
                        // Remove the marker from map
                        if (contactMarkers[contactIndex]) {
                            contactMarkers[contactIndex].setMap(null);
                            contactMarkers.splice(contactIndex, 1);
                        }
                        
                        // Reload contacts on map to update indices
                        loadContactsOnMap(trajetoData.contacts);
                        
                        Swal.fire({
                            title: 'Contacto Eliminado!',
                            text: 'O contacto foi eliminado com sucesso.',
                            icon: 'success',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#16a34a'
                        });
                    }
                }
            });
        }

        window.saveContactDetails =         function saveContactDetails() {
            
            const contactIndex = parseInt(document.getElementById('contactEditIndex').value);
            
            if (isNaN(contactIndex) || !trajetoData.contacts || !trajetoData.contacts[contactIndex]) {
                return;
            }
            
            // Validate time field first
            const timeValue = document.getElementById('contactTime').value;
            if (!timeValue) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione a hora do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }
            
            // Get selected radio button values
            const selectedCircumstance = document.querySelector('input[name="contact_circumstances"]:checked');
            const selectedLocation = document.querySelector('input[name="contact_location"]:checked');

            // Validate that both are selected
            if (!selectedCircumstance) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione uma circunstância do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            if (!selectedLocation) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor, selecione um local do contacto.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#0a7ea4'
                });
                return;
            }

            // Validate "Outro. Qual?" and "Grupos de Rolas" fields if selected
            let circumstanceValue = selectedCircumstance.value;
            let locationValue = selectedLocation.value;
            
            if (selectedCircumstance.value === 'other') {
                const otherText = document.getElementById('circumstanceOtherText').value.trim();
                if (!otherText) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor, especifique a circunstância do contacto.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                circumstanceValue = otherText;
            } else if (selectedCircumstance.value === 'groups') {
                const groupsNumber = document.getElementById('circumstanceGroupsNumber').value.trim();
                if (!groupsNumber || parseInt(groupsNumber) < 1) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor, especifique o número de indivíduos (mínimo 1).',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                circumstanceValue = `Grupos de Rolas (${groupsNumber} indivíduos)`;
            }
            
            if (selectedLocation.value === 'other') {
                const otherText = document.getElementById('locationOtherText').value.trim();
                if (!otherText) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor, especifique o local do contacto.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                locationValue = otherText;
            }

            // Get coordinates from the contact data
            const contact = trajetoData.contacts[contactIndex];
            if (!contact.coordinates) {
                Swal.fire({
                    title: 'Erro',
                    text: 'Coordenadas do contacto não encontradas.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }

            // Update contact data (preserve trajectory ID and metadata)
            const actualTrajectoryId = document.getElementById('trajetoId').value;
            trajetoData.contacts[contactIndex] = {
                ...trajetoData.contacts[contactIndex],
                circumstance: circumstanceValue,
                location: locationValue,
                time: timeValue,
                trajectoryId: actualTrajectoryId, // Ensure correct trajectory ID
                updatedAt: new Date().toISOString(),
                updatedBy: '<?php echo $_SESSION["user"]["uid"] ?? ""; ?>'
            };

            // CRITICAL FIX: Also update the corresponding contact marker's contactData
            if (contactMarkers && contactMarkers[contactIndex]) {
                contactMarkers[contactIndex].contactData = trajetoData.contacts[contactIndex];
            }

            // Reload contacts on map to reflect changes
            loadContactsOnMap(trajetoData.contacts);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactEditModal'));
            modal.hide();

            // Show success message
            Swal.fire({
                title: 'Contacto Atualizado!',
                text: 'As alterações foram guardadas com sucesso.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a'
            });
        }

        function setupContactModalListeners() {
            // Add event listeners for time picker
            const timeInput = document.getElementById('contactTime');
            const timePickerBtn = document.getElementById('contactTimePickerBtn');
            
            if (timeInput && timePickerBtn) {
                // Only add click listener to the button to avoid duplicate calls
                timePickerBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    showContactTimePicker();
                });
                
                // Make input readonly to prevent direct editing and focus on button click
                timeInput.setAttribute('readonly', 'readonly');
                timeInput.style.cursor = 'pointer';
                
                // If user clicks on input, redirect to button
                timeInput.addEventListener('click', function(e) {
                    e.preventDefault();
                    timePickerBtn.click();
                });
            }

            // Add event listeners for "Outro. Qual?" and "Grupos de Rolas" options
            
            // Circumstance options
            const circumstanceOtherRadio = document.getElementById('circumstance_other');
            const circumstanceOtherInput = document.getElementById('circumstanceOtherInput');
            const circumstanceGroupsRadio = document.getElementById('circumstance_groups');
            const circumstanceGroupsInput = document.getElementById('circumstanceGroupsInput');
            const circumstanceRadios = document.querySelectorAll('input[name="contact_circumstances"]');
            
            if (circumstanceRadios.length > 0) {
                circumstanceRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        // Handle "Outro. Qual?" option
                        if (this.value === 'other') {
                            if (circumstanceOtherInput) {
                                circumstanceOtherInput.style.display = 'block';
                                document.getElementById('circumstanceOtherText').focus();
                            }
                        } else {
                            if (circumstanceOtherInput) {
                                circumstanceOtherInput.style.display = 'none';
                                document.getElementById('circumstanceOtherText').value = '';
                            }
                        }
                        
                        // Handle "Grupos de Rolas" option
                        if (this.value === 'groups') {
                            if (circumstanceGroupsInput) {
                                circumstanceGroupsInput.style.display = 'block';
                                document.getElementById('circumstanceGroupsNumber').focus();
                            }
                        } else {
                            if (circumstanceGroupsInput) {
                                circumstanceGroupsInput.style.display = 'none';
                                document.getElementById('circumstanceGroupsNumber').value = '';
                            }
                        }
                    });
                });
            }
            
            // Location "Outro. Qual?" option
            const locationOtherRadio = document.getElementById('location_other');
            const locationOtherInput = document.getElementById('locationOtherInput');
            const locationRadios = document.querySelectorAll('input[name="contact_location"]');
            
            if (locationOtherRadio && locationOtherInput) {
                locationRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'other') {
                            locationOtherInput.style.display = 'block';
                            document.getElementById('locationOtherText').focus();
                        } else {
                            locationOtherInput.style.display = 'none';
                            document.getElementById('locationOtherText').value = '';
                        }
                    });
                });
            }
        }

        function updateTimeDisplay() {
            const hour = document.getElementById('hourSelect').value;
            const minute = document.getElementById('minuteSelect').value;
            const timeString = `${hour}:${minute}`;
            document.getElementById('selectedTimeDisplay').textContent = timeString;
        }

        function setupTimePickerListeners() {
            // Update display when selections change
            document.getElementById('hourSelect').addEventListener('change', updateTimeDisplay);
            document.getElementById('minuteSelect').addEventListener('change', updateTimeDisplay);
            
            // Confirm button
            document.getElementById('confirmTimeBtn').addEventListener('click', function() {
                const hour = document.getElementById('hourSelect').value;
                const minute = document.getElementById('minuteSelect').value;
                const timeString = `${hour}:${minute}`;
                
                // Set the time input value based on current target
                const targetInput = document.getElementById(window.currentTimeTarget || 'contactTime');
                if (targetInput) {
                    targetInput.value = timeString;
                    
                    // Trigger change event
                    targetInput.dispatchEvent(new Event('change', { bubbles: true }));
                    targetInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
                
                // Close the time picker modal
                const timeModal = bootstrap.Modal.getInstance(document.getElementById('timePickerModal'));
                if (timeModal) {
                    timeModal.hide();
                }
            });
            
            // Timeline time picker buttons
            setupTimelineTimePickerListeners();
        }
        
        function setupTimelineTimePickerListeners() {
            // Add event listeners for timeline time picker buttons
            const timelinePickerButtons = document.querySelectorAll('.timeline-time-picker-btn');
            
            timelinePickerButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    showTimelineTimePicker(targetId);
                });
            });
            
            // Also add click listeners to the input fields themselves
            const timelineInputs = ['editStartTime', 'editMiddleTime', 'editEndTime'];
            timelineInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('click', function() {
                        showTimelineTimePicker(inputId);
                    });
                }
            });
        }
        
        function showTimelineTimePicker(targetId) {
            
            // Prevent multiple simultaneous calls
            if (window.timelineTimePickerOpening) {
                return;
            }
            
            window.timelineTimePickerOpening = true;
            
            // Set up the time picker for timeline
            window.currentTimeTarget = targetId;
            
            // Get current time value if any
            const currentTime = document.getElementById(targetId).value;
            
            if (currentTime) {
                const [hours, minutes] = currentTime.split(':');
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();
            } else {
                // Default to current time
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0'); // Round to nearest 5 minutes
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();
            }
            
            // Show the time picker modal
            const timePickerModal = new bootstrap.Modal(document.getElementById('timePickerModal'));
            
            // Reset the opening flag when modal is shown
            timePickerModal._element.addEventListener('shown.bs.modal', function() {
                window.timelineTimePickerOpening = false;
            });
            
            // Reset the opening flag when modal is hidden (safety)
            timePickerModal._element.addEventListener('hidden.bs.modal', function() {
                window.timelineTimePickerOpening = false;
            });
            
            timePickerModal.show();
        }

        function showContactTimePicker() {
            // Prevent multiple simultaneous calls
            if (window.contactTimePickerOpening) {
                return;
            }
            
            window.contactTimePickerOpening = true;
            
            // Set up the time picker for contact time
            window.currentTimeTarget = 'contactTime';
            
            // Get current contact time value if any
            const currentTime = document.getElementById('contactTime').value;
            
            if (currentTime) {
                const [hours, minutes] = currentTime.split(':');
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();
            } else {
                // Default to current time
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(Math.floor(now.getMinutes() / 5) * 5).padStart(2, '0'); // Round to nearest 5 minutes
                document.getElementById('hourSelect').value = hours;
                document.getElementById('minuteSelect').value = minutes;
                updateTimeDisplay();
            }
            
            // Show the time picker modal
            const timePickerModal = new bootstrap.Modal(document.getElementById('timePickerModal'));
            
            // Reset the opening flag when modal is shown
            timePickerModal._element.addEventListener('shown.bs.modal', function() {
                window.contactTimePickerOpening = false;
            });
            
            // Reset the opening flag when modal is hidden (safety)
            timePickerModal._element.addEventListener('hidden.bs.modal', function() {
                window.contactTimePickerOpening = false;
            });
            
            timePickerModal.show();
        }

        window.cancelContactDetails = function cancelContactDetails() {
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('contactEditModal'));
            modal.hide();
        }

        // Add new contact functionality
        let addingNewContact = false;

        function toggleAddContactMode() {
            addingNewContact = !addingNewContact;
            const button = document.querySelector('[onclick="toggleAddContactMode()"]');
            
            if (addingNewContact) {
                button.innerHTML = '<i class="fas fa-times"></i>Cancelar Adição';
                button.className = 'btn btn-warning';
                
                // Show instruction
                Swal.fire({
                    title: 'Modo Adicionar Contacto',
                    text: 'Clique no mapa onde pretende adicionar um novo contacto.',
                    icon: 'info',
                    confirmButtonText: 'Entendido',
                    confirmButtonColor: '#0a7ea4'
                });
            } else {
                button.innerHTML = '<i class="fas fa-dove"></i>Adicionar Contacto';
                button.className = 'btn btn-info text-white';
            }
        }

        function addNewContactAt(latLng) {
            if (!addingNewContact) return;

            // Turn off add mode
            addingNewContact = false;
            const button = document.querySelector('[onclick="toggleAddContactMode()"]');
            button.innerHTML = '<i class="fas fa-dove"></i>Adicionar Contacto';
            button.className = 'btn btn-info text-white';

            // Create new contact object with correct trajectory ID
            const actualTrajectoryId = document.getElementById('trajetoId').value;
            const newContact = {
                circumstance: '',
                location: '',
                time: '',
                coordinates: {
                    lat: latLng.lat(),
                    lng: latLng.lng()
                },
                trajectoryId: actualTrajectoryId,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: '<?php echo $_SESSION["user"]["uid"] ?? ""; ?>',
                updatedBy: '<?php echo $_SESSION["user"]["uid"] ?? ""; ?>',
                createdByEmail: '<?php echo $_SESSION["user"]["email"] ?? ""; ?>'
            };

            // Add to contacts array
            if (!trajetoData.contacts) {
                trajetoData.contacts = [];
            }
            trajetoData.contacts.push(newContact);

            // Get the new contact index
            const newContactIndex = trajetoData.contacts.length - 1;
            
            // Reload contacts on map
            loadContactsOnMap(trajetoData.contacts);

            // Open edit modal for the new contact
            setTimeout(() => {
                editContact(newContactIndex, true); // Pass true to indicate it's a new contact
            }, 500);
        }



        // Form submission
        document.getElementById('trajetoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (routeCoordinates.length < 2) {
                Swal.fire({
                    title: 'Erro',
                    text: 'É necessário ter pelo menos 2 pontos para guardar o trajeto.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }

            // Validate distance (minimum 2.5 km) - make sure we have fresh coordinates
            
            // Check if Google Maps geometry library is loaded
            if (!google?.maps?.geometry?.spherical) {
                Swal.fire({
                    title: 'Erro',
                    text: 'Erro ao calcular distância. Recarregue a página.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
                return;
            }
            
            // Recalculate distance fresh from current coordinates
            const distance = google.maps.geometry.spherical.computeLength(routeCoordinates);
            const distanceKm = (distance / 1000).toFixed(2);
            const minimumDistance = 2.5;
            
            
            if (parseFloat(distanceKm) < minimumDistance) {
                
                const shortfall = (minimumDistance - parseFloat(distanceKm)).toFixed(2);
                
                Swal.fire({
                    title: 'Distância Insuficiente',
                    html: `
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 3rem; color: #dc2626; margin-bottom: 1rem;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <p style="font-size: 1.1rem; margin-bottom: 1rem; color: #374151;">
                                O trajeto deve ter <strong>pelo menos 2.5 km</strong> de distância.
                            </p>
                            <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                                <div style="color: #dc2626; font-weight: 600; margin-bottom: 0.5rem;">
                                    <i class="fas fa-ruler me-2"></i>Distância Atual: ${distanceKm} km
                                </div>
                                <div style="color: #dc2626; font-weight: 600;">
                                    <i class="fas fa-plus me-2"></i>Faltam: ${shortfall} km
                                </div>
                            </div>
                            <p style="color: #6b7280; font-size: 0.9rem;">
                                Continue a adicionar pontos ao trajeto no mapa para atingir a distância mínima.
                            </p>
                        </div>
                    `,
                    icon: null,
                    confirmButtonText: '<i class="fas fa-edit me-2"></i>Continuar a Editar',
                    confirmButtonColor: '#0a7ea4',
                    customClass: {
                        popup: 'distance-validation-popup'
                    },
                    width: '500px',
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown animate__faster'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp animate__faster'
                    }
                });
                return;
            }

            // Validate required fields (timeline, weather, observers)
            const validationErrors = [];
            
            // Timeline validation - all times should be filled
            if (!currentStartTime || currentStartTime.trim() === '') {
                validationErrors.push('Timeline: Deve definir todas as horas (início, meio, fim)');
            }
            
            // Weather validation - weather condition is required
            if (!currentWeatherCondition || currentWeatherCondition.trim() === '' || currentWeatherCondition === 'Clique para definir') {
                validationErrors.push('Condições Meteorológicas: Deve selecionar as condições climáticas');
            }
            
            // Observers validation - number of observers is required and must be at least 1
            if (!currentObserversCount || currentObserversCount < 1) {
                validationErrors.push('Observadores: Deve indicar pelo menos 1 observador');
            }
            
            // If there are validation errors, show them
            if (validationErrors.length > 0) {
                
                const errorList = validationErrors.map(error => `<li style="text-align: left; margin-bottom: 0.5rem;">${error}</li>`).join('');
                
                Swal.fire({
                    title: 'Campos Obrigatórios',
                    html: `
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 3rem; color: #f59e0b; margin-bottom: 1rem;">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <p style="font-size: 1.1rem; margin-bottom: 1rem; color: #374151;">
                                Por favor complete os seguintes campos obrigatórios:
                            </p>
                            <div style="background: #fffbeb; border: 1px solid #fcd34d; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                                <ul style="color: #92400e; font-weight: 500; margin: 0; padding-left: 1.5rem;">
                                    ${errorList}
                                </ul>
                            </div>
                            <p style="color: #6b7280; font-size: 0.9rem;">
                                <i class="fas fa-info-circle me-2"></i>
                                Clique nos cartões abaixo do mapa para preencher estas informações.
                            </p>
                        </div>
                    `,
                    icon: null,
                    confirmButtonText: '<i class="fas fa-edit me-2"></i>Preencher Dados',
                    confirmButtonColor: '#f59e0b',
                    width: '550px',
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown animate__faster'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp animate__faster'
                    }
                }).then(() => {
                    // Smooth scroll to the cards section
                    const cardsSection = document.querySelector('.row.g-3.mt-3');
                    if (cardsSection) {
                        cardsSection.scrollIntoView({ 
                            behavior: 'smooth', 
                            block: 'start',
                            inline: 'nearest' 
                        });
                    }
                    
                    // Add a small delay then highlight the missing fields by adding a pulsing effect
                    setTimeout(() => {
                        if (!currentStartTime || currentStartTime.trim() === '') {
                            const timelineCard = document.querySelector('.editable-card[onclick="editTimeline()"]');
                            if (timelineCard) {
                                timelineCard.style.animation = 'pulse 1.5s infinite';
                                timelineCard.style.borderColor = '#f59e0b';
                                setTimeout(() => {
                                    timelineCard.style.animation = '';
                                    timelineCard.style.borderColor = '#e5e7eb';
                                }, 5000);
                            }
                        }
                        
                        if (!currentWeatherCondition || currentWeatherCondition.trim() === '' || currentWeatherCondition === 'Clique para definir') {
                            const weatherCard = document.querySelector('.editable-card[onclick="editWeather()"]');
                            if (weatherCard) {
                                weatherCard.style.animation = 'pulse 1.5s infinite';
                                weatherCard.style.borderColor = '#f59e0b';
                                setTimeout(() => {
                                    weatherCard.style.animation = '';
                                    weatherCard.style.borderColor = '#e5e7eb';
                                }, 5000);
                            }
                        }
                        
                        if (!currentObserversCount || currentObserversCount < 1) {
                            const observersCard = document.querySelector('.editable-card[onclick="editObservers()"]');
                            if (observersCard) {
                                observersCard.style.animation = 'pulse 1.5s infinite';
                                observersCard.style.borderColor = '#f59e0b';
                                setTimeout(() => {
                                    observersCard.style.animation = '';
                                    observersCard.style.borderColor = '#e5e7eb';
                                }, 5000);
                            }
                        }
                    }, 800); // Wait for scroll to complete before highlighting
                });
                return;
            }
            

            // Show the "Finalizar Trajeto" modal for image uploads
            showFinalizarTrajetoModal();
        });

        // Global variables for image handling
        let mapPhotosFiles = [];
        let otherPhotosFiles = [];
        
        // Track deleted photos for proper cleanup
        let deletedMapPhotos = [];
        let deletedOtherPhotos = [];
        
        // Debug function to check deletion arrays state
        function debugDeletionArrays(context) {
        }

        function showFinalizarTrajetoModal() {
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'finalizarTrajetoModal';
            modal.tabIndex = -1;
            modal.setAttribute('aria-labelledby', 'finalizarTrajetoModalLabel');
            modal.setAttribute('aria-hidden', 'true');
            modal.setAttribute('data-bs-backdrop', 'static');
            modal.setAttribute('data-bs-keyboard', 'false');
            modal.style.zIndex = '1060';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-xl">
                    <div class="modal-content" style="border: none; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); overflow: hidden;">
                        <div class="modal-header" style="border: none; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; padding: 1rem 1.5rem;">
                            <h5 class="modal-title" style="color: white; font-weight: 600; margin-bottom: 0; font-size: 1.1rem; display: flex; align-items: center;">
                                <i class="fas fa-clipboard-check me-2" style="font-size: 1rem;"></i>
                                Finalizar Edição do Trajeto
                            </h5>
                        </div>
                        <div class="modal-body" style="padding: 1.5rem; background: white; max-height: 60vh; overflow-y: auto;">
                            <form id="trajectoryDetailsForm">
                                <!-- Photos Section -->
                                <div class="mb-3">
                                    <h6 style="color: #374151; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; font-size: 1rem;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                            <i class="fas fa-images" style="color: white; font-size: 0.9rem;"></i>
                                        </div>
                                        Fotografias 
                                        <span style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-weight: 500; font-size: 0.75rem; margin-left: 0.5rem; display: inline-flex; align-items: center; gap: 0.25rem;">
                                            <i class="fas fa-info-circle" style="font-size: 0.7rem;"></i>
                                            opcional
                                        </span>
                                    </h6>
                                    
                                    <div class="row g-3">
                                        <!-- Map Photos Card -->
                                        <div class="col-md-6">
                                            <div class="photo-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; transition: all 0.3s ease; position: relative;">
                                                <div class="photo-card-header" style="display: flex; align-items: center; margin-bottom: 0.75rem;">
                                                    <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                        <i class="fas fa-map" style="color: white; font-size: 0.8rem;"></i>
                                                    </div>
                                                    <div>
                                                        <h6 style="margin: 0; color: #374151; font-weight: 600; font-size: 0.9rem;">Fotos dos Mapas</h6>
                                                        <p style="margin: 0; color: #6b7280; font-size: 0.75rem;">Mapas criados durante o trajeto</p>
                                                    </div>
                                                </div>
                                                <div class="dropzone-container" id="editMapPhotosDropzone" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border: 2px dashed #0a7ea4; color: #0a7ea4; font-weight: 500; padding: 1rem; border-radius: 6px; transition: all 0.3s ease; font-size: 0.85rem; text-align: center; cursor: pointer; position: relative;">
                                                    <div class="dropzone-content">
                                                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #0a7ea4; margin-bottom: 0.5rem; display: block;"></i>
                                                        <div style="font-weight: 600; margin-bottom: 0.25rem;">Arraste as fotos aqui</div>
                                                        <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">ou clique para selecionar</div>
                                                        <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                                                    </div>
                                                    <input type="file" id="editMapPhotosInput" multiple accept="image/*" style="display: none;">
                                                </div>
                                                <div id="editMapPhotosPreview" class="photos-preview mt-2"></div>
                                            </div>
                                        </div>
                                        
                                        <!-- Other Photos Card -->
                                        <div class="col-md-6">
                                            <div class="photo-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; transition: all 0.3s ease; position: relative;">
                                                <div class="photo-card-header" style="display: flex; align-items: center; margin-bottom: 0.75rem;">
                                                    <div style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); display: flex; align-items: center; justify-content: center; margin-right: 0.5rem;">
                                                        <i class="fas fa-camera" style="color: white; font-size: 0.8rem;"></i>
                                                    </div>
                                                    <div>
                                                        <h6 style="margin: 0; color: #374151; font-weight: 600; font-size: 0.9rem;">Outras Fotos</h6>
                                                        <p style="margin: 0; color: #6b7280; font-size: 0.75rem;">Fotos relativas ao percurso</p>
                                                    </div>
                                                </div>
                                                <div class="dropzone-container" id="editOtherPhotosDropzone" style="background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%); border: 2px dashed #0a7ea4; color: #0a7ea4; font-weight: 500; padding: 1rem; border-radius: 6px; transition: all 0.3s ease; font-size: 0.85rem; text-align: center; cursor: pointer; position: relative;">
                                                    <div class="dropzone-content">
                                                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #0a7ea4; margin-bottom: 0.5rem; display: block;"></i>
                                                        <div style="font-weight: 600; margin-bottom: 0.25rem;">Arraste as fotos aqui</div>
                                                        <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">ou clique para selecionar</div>
                                                        <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                                                    </div>
                                                    <input type="file" id="editOtherPhotosInput" multiple accept="image/*" style="display: none;">
                                                </div>
                                                <div id="editOtherPhotosPreview" class="photos-preview mt-2"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer" style="border: none; padding: 1.25rem; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); display: flex; justify-content: center; align-items: center; gap: 1rem;">
                            <button type="button" class="btn" onclick="cancelEditTrajectory()" id="cancelEditBtn" style="background: #6b7280; border: none; color: white; padding: 0.75rem 1.5rem; font-size: 1rem; font-weight: 600; border-radius: 8px; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease; transform: translateY(0);">
                                <i class="fas fa-arrow-left" style="font-size: 1rem;"></i>
                                Voltar
                            </button>
                            <button type="button" class="btn" onclick="saveTrajectoryDetails()" id="saveEditDetailsBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: none; color: white; padding: 0.75rem 2rem; font-size: 1rem; font-weight: 600; border-radius: 8px; display: inline-flex; align-items: center; gap: 0.5rem; box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3); transition: all 0.3s ease; transform: translateY(0);">
                                <i class="fas fa-check-circle" style="font-size: 1rem;"></i>
                                Concluir Edição
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            const bsModal = new bootstrap.Modal(modal, {
                backdrop: 'static',
                keyboard: false
            });
            bsModal.show();

            // Initialize dropzone functionality
            setupEditDropzones();
            
            // Load existing images if editing current trajectory
            if (editMode === 'current') {
                debugDeletionArrays('Modal opened - before loading existing images');
                loadExistingImages();
            } else {
                debugDeletionArrays('Modal opened - start over mode');
            }
        }

        function setupEditDropzones() {
            // Setup map photos dropzone
            const mapDropzone = document.getElementById('editMapPhotosDropzone');
            const mapInput = document.getElementById('editMapPhotosInput');
            
            if (mapDropzone && mapInput) {
                mapDropzone.onclick = () => mapInput.click();
                
                mapDropzone.ondragover = function(e) {
                    e.preventDefault();
                    this.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.1) 0%, rgba(8, 145, 178, 0.08) 100%)';
                    this.style.borderColor = '#0891b2';
                    this.style.transform = 'scale(1.02)';
                };
                
                mapDropzone.ondragleave = function(e) {
                    e.preventDefault();
                    this.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                };
                
                mapDropzone.ondrop = function(e) {
                    e.preventDefault();
                    this.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                    
                    const files = e.dataTransfer.files;
                    handleEditMapPhotosSelect(files);
                };
                
                mapInput.onchange = function() {
                    handleEditMapPhotosSelect(this.files);
                };
            }
            
            // Setup other photos dropzone
            const otherDropzone = document.getElementById('editOtherPhotosDropzone');
            const otherInput = document.getElementById('editOtherPhotosInput');
            
            if (otherDropzone && otherInput) {
                otherDropzone.onclick = () => otherInput.click();
                
                otherDropzone.ondragover = function(e) {
                    e.preventDefault();
                    this.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.1) 0%, rgba(8, 145, 178, 0.08) 100%)';
                    this.style.borderColor = '#0891b2';
                    this.style.transform = 'scale(1.02)';
                };
                
                otherDropzone.ondragleave = function(e) {
                    e.preventDefault();
                    this.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                };
                
                otherDropzone.ondrop = function(e) {
                    e.preventDefault();
                    this.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%)';
                    this.style.borderColor = '#0a7ea4';
                    this.style.transform = 'scale(1)';
                    
                    const files = e.dataTransfer.files;
                    handleEditOtherPhotosSelect(files);
                };
                
                otherInput.onchange = function() {
                    handleEditOtherPhotosSelect(this.files);
                };
            }
        }

        function handleEditMapPhotosSelect(files) {
            
            const maxFiles = 6;
            const maxSize = 5 * 1024 * 1024; // 5MB
            
            const existingPhotos = (editMode === 'current' && trajetoData.mapPhotos) ? trajetoData.mapPhotos.length : 0;
            const totalPhotos = existingPhotos + mapPhotosFiles.length + files.length;
            
            
            if (totalPhotos > maxFiles) {
                Swal.fire({
                    title: 'Demasiadas fotos',
                    text: `Pode ter no máximo ${maxFiles} fotos dos mapas. Atualmente tem ${existingPhotos + mapPhotosFiles.length} foto${(existingPhotos + mapPhotosFiles.length) !== 1 ? 's' : ''} e está a tentar adicionar ${files.length} mais.`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#f59e0b'
                });
                return;
            }
            
            Array.from(files).forEach(file => {
                
                if (file.size > maxSize) {
                    Swal.fire({
                        title: 'Ficheiro demasiado grande',
                        text: `A foto "${file.name}" excede o limite de 5MB.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#f59e0b'
                    });
                    return;
                }
                
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Tipo de ficheiro inválido',
                        text: `O ficheiro "${file.name}" não é uma imagem válida.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#f59e0b'
                    });
                    return;
                }
                
                mapPhotosFiles.push(file);
            });
            
            updateEditMapPhotosPreview();
        }

        function handleEditOtherPhotosSelect(files) {
            const maxFiles = 6;
            const maxSize = 5 * 1024 * 1024; // 5MB
            
            const existingPhotos = (editMode === 'current' && trajetoData.otherPhotos) ? trajetoData.otherPhotos.length : 0;
            const totalPhotos = existingPhotos + otherPhotosFiles.length + files.length;
            
            if (totalPhotos > maxFiles) {
                Swal.fire({
                    title: 'Demasiadas fotos',
                    text: `Pode ter no máximo ${maxFiles} outras fotos. Atualmente tem ${existingPhotos + otherPhotosFiles.length} foto${(existingPhotos + otherPhotosFiles.length) !== 1 ? 's' : ''} e está a tentar adicionar ${files.length} mais.`,
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#f59e0b'
                });
                return;
            }
            
            Array.from(files).forEach(file => {
                if (file.size > maxSize) {
                    Swal.fire({
                        title: 'Ficheiro demasiado grande',
                        text: `A foto "${file.name}" excede o limite de 5MB.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#f59e0b'
                    });
                    return;
                }
                
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Tipo de ficheiro inválido',
                        text: `O ficheiro "${file.name}" não é uma imagem válida.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#f59e0b'
                    });
                    return;
                }
                
                otherPhotosFiles.push(file);
            });
            
            updateEditOtherPhotosPreview();
        }

        function updateEditMapPhotosPreview() {
            const container = document.getElementById('editMapPhotosPreview');
            if (!container) return;
            
            
            container.innerHTML = '';
            
            // First, add existing photos if any
            if (editMode === 'current' && trajetoData.mapPhotos && trajetoData.mapPhotos.length > 0) {
                trajetoData.mapPhotos.forEach((photoUrl, index) => {
                    const photoDiv = document.createElement('div');
                    photoDiv.className = 'photo-thumbnail existing-photo';
                    photoDiv.style.cssText = 'display: inline-block; position: relative; margin: 0.25rem; border-radius: 6px; overflow: visible; border: 2px solid #e5e7eb; background: white;';
                    
                    const img = document.createElement('img');
                    img.style.cssText = 'width: 60px; height: 60px; object-fit: cover; display: block;';
                    img.src = photoUrl;
                    img.onerror = function() {
                        this.style.background = '#f3f4f6';
                        this.alt = 'Erro ao carregar';
                    };
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.style.cssText = 'position: absolute; top: -8px; right: -8px; width: 20px; height: 20px; border-radius: 50%; background: #dc2626; color: white; border: none; font-size: 0.7rem; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 100; transition: all 0.2s ease;';
                    removeBtn.onmouseover = () => removeBtn.style.transform = 'scale(1.1)';
                    removeBtn.onmouseout = () => removeBtn.style.transform = 'scale(1)';
                    removeBtn.onclick = () => {
                        debugDeletionArrays('Before map photo removal');
                        
                        // Track the deleted photo URL for cleanup
                        const deletedPhotoUrl = trajetoData.mapPhotos[index];
                        
                        // Add to deletion tracking if not already there
                        if (deletedPhotoUrl && !deletedMapPhotos.includes(deletedPhotoUrl)) {
                            deletedMapPhotos.push(deletedPhotoUrl);
                        }
                        
                        
                        // Remove from current array
                        trajetoData.mapPhotos.splice(index, 1);
                        
                        debugDeletionArrays('After map photo removal');
                        updateEditMapPhotosPreview();
                    };
                    
                    // Add existing photo indicator
                    const existingBadge = document.createElement('div');
                    existingBadge.innerHTML = '<i class="fas fa-check-circle"></i>';
                    existingBadge.style.cssText = 'position: absolute; bottom: -8px; left: -8px; width: 20px; height: 20px; border-radius: 50%; background: #16a34a; color: white; border: 2px solid white; font-size: 0.7rem; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 50;';
                    existingBadge.title = 'Foto existente';
                    
                    photoDiv.appendChild(img);
                    photoDiv.appendChild(removeBtn);
                    photoDiv.appendChild(existingBadge);
                    container.appendChild(photoDiv);
                });
            }
            
            // Then, add new selected files
            mapPhotosFiles.forEach((file, index) => {
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-thumbnail new-photo';
                photoDiv.style.cssText = 'display: inline-block; position: relative; margin: 0.25rem; border-radius: 6px; overflow: visible; border: 2px solid #e5e7eb; background: white;';
                
                const img = document.createElement('img');
                img.style.cssText = 'width: 60px; height: 60px; object-fit: cover; display: block;';
                img.src = URL.createObjectURL(file);
                
                const removeBtn = document.createElement('button');
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.style.cssText = 'position: absolute; top: -8px; right: -8px; width: 20px; height: 20px; border-radius: 50%; background: #dc2626; color: white; border: none; font-size: 0.7rem; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 100; transition: all 0.2s ease;';
                removeBtn.onmouseover = () => removeBtn.style.transform = 'scale(1.1)';
                removeBtn.onmouseout = () => removeBtn.style.transform = 'scale(1)';
                removeBtn.onclick = () => {
                    mapPhotosFiles.splice(index, 1);
                    updateEditMapPhotosPreview();
                };
                
                // Add new photo indicator
                const newBadge = document.createElement('div');
                newBadge.innerHTML = '<i class="fas fa-plus-circle"></i>';
                newBadge.style.cssText = 'position: absolute; bottom: -8px; left: -8px; width: 20px; height: 20px; border-radius: 50%; background: #0a7ea4; color: white; border: 2px solid white; font-size: 0.7rem; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 50;';
                newBadge.title = 'Nova foto';
                
                photoDiv.appendChild(img);
                photoDiv.appendChild(removeBtn);
                photoDiv.appendChild(newBadge);
                container.appendChild(photoDiv);
            });
            
            // Update dropzone appearance
            const totalPhotos = (editMode === 'current' && trajetoData.mapPhotos ? trajetoData.mapPhotos.length : 0) + mapPhotosFiles.length;
            const dropzone = document.getElementById('editMapPhotosDropzone');
            if (dropzone && totalPhotos > 0) {
                const dropzoneContent = dropzone.querySelector('.dropzone-content');
                if (dropzoneContent) {
                    dropzoneContent.innerHTML = `
                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #16a34a; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.25rem; color: #16a34a;">${totalPhotos} foto${totalPhotos !== 1 ? 's' : ''} selecionada${totalPhotos !== 1 ? 's' : ''}</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Clique para adicionar mais</div>
                    `;
                }
                dropzone.style.background = 'linear-gradient(135deg, rgba(22, 163, 74, 0.05) 0%, rgba(34, 197, 94, 0.03) 100%)';
                dropzone.style.borderColor = '#16a34a';
            }
        }

        function updateEditOtherPhotosPreview() {
            const container = document.getElementById('editOtherPhotosPreview');
            if (!container) return;
            
            
            container.innerHTML = '';
            
            // First, add existing photos if any
            if (editMode === 'current' && trajetoData.otherPhotos && trajetoData.otherPhotos.length > 0) {
                trajetoData.otherPhotos.forEach((photoUrl, index) => {
                    const photoDiv = document.createElement('div');
                    photoDiv.className = 'photo-thumbnail existing-photo';
                    photoDiv.style.cssText = 'display: inline-block; position: relative; margin: 0.25rem; border-radius: 6px; overflow: visible; border: 2px solid #e5e7eb; background: white;';
                    
                    const img = document.createElement('img');
                    img.style.cssText = 'width: 60px; height: 60px; object-fit: cover; display: block;';
                    img.src = photoUrl;
                    img.onerror = function() {
                        this.style.background = '#f3f4f6';
                        this.alt = 'Erro ao carregar';
                    };
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.style.cssText = 'position: absolute; top: -8px; right: -8px; width: 20px; height: 20px; border-radius: 50%; background: #dc2626; color: white; border: none; font-size: 0.7rem; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 100; transition: all 0.2s ease;';
                    removeBtn.onmouseover = () => removeBtn.style.transform = 'scale(1.1)';
                    removeBtn.onmouseout = () => removeBtn.style.transform = 'scale(1)';
                    removeBtn.onclick = () => {
                        debugDeletionArrays('Before other photo removal');
                        
                        // Track the deleted photo URL for cleanup
                        const deletedPhotoUrl = trajetoData.otherPhotos[index];
                        
                        // Add to deletion tracking if not already there
                        if (deletedPhotoUrl && !deletedOtherPhotos.includes(deletedPhotoUrl)) {
                            deletedOtherPhotos.push(deletedPhotoUrl);
                        }
                        
                        
                        // Remove from current array
                        trajetoData.otherPhotos.splice(index, 1);
                        
                        debugDeletionArrays('After other photo removal');
                        updateEditOtherPhotosPreview();
                    };
                    
                    // Add existing photo indicator
                    const existingBadge = document.createElement('div');
                    existingBadge.innerHTML = '<i class="fas fa-check-circle"></i>';
                    existingBadge.style.cssText = 'position: absolute; bottom: -8px; left: -8px; width: 20px; height: 20px; border-radius: 50%; background: #16a34a; color: white; border: 2px solid white; font-size: 0.7rem; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 50;';
                    existingBadge.title = 'Foto existente';
                    
                    photoDiv.appendChild(img);
                    photoDiv.appendChild(removeBtn);
                    photoDiv.appendChild(existingBadge);
                    container.appendChild(photoDiv);
                });
            }
            
            // Then, add new selected files
            otherPhotosFiles.forEach((file, index) => {
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-thumbnail new-photo';
                photoDiv.style.cssText = 'display: inline-block; position: relative; margin: 0.25rem; border-radius: 6px; overflow: visible; border: 2px solid #e5e7eb; background: white;';
                
                const img = document.createElement('img');
                img.style.cssText = 'width: 60px; height: 60px; object-fit: cover; display: block;';
                img.src = URL.createObjectURL(file);
                
                const removeBtn = document.createElement('button');
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.style.cssText = 'position: absolute; top: -8px; right: -8px; width: 20px; height: 20px; border-radius: 50%; background: #dc2626; color: white; border: none; font-size: 0.7rem; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 100; transition: all 0.2s ease;';
                removeBtn.onmouseover = () => removeBtn.style.transform = 'scale(1.1)';
                removeBtn.onmouseout = () => removeBtn.style.transform = 'scale(1)';
                removeBtn.onclick = () => {
                    otherPhotosFiles.splice(index, 1);
                    updateEditOtherPhotosPreview();
                };
                
                // Add new photo indicator
                const newBadge = document.createElement('div');
                newBadge.innerHTML = '<i class="fas fa-plus-circle"></i>';
                newBadge.style.cssText = 'position: absolute; bottom: -8px; left: -8px; width: 20px; height: 20px; border-radius: 50%; background: #0a7ea4; color: white; border: 2px solid white; font-size: 0.7rem; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.3); z-index: 50;';
                newBadge.title = 'Nova foto';
                
                photoDiv.appendChild(img);
                photoDiv.appendChild(removeBtn);
                photoDiv.appendChild(newBadge);
                container.appendChild(photoDiv);
            });
            
            // Update dropzone appearance
            const totalPhotos = (editMode === 'current' && trajetoData.otherPhotos ? trajetoData.otherPhotos.length : 0) + otherPhotosFiles.length;
            const dropzone = document.getElementById('editOtherPhotosDropzone');
            if (dropzone && totalPhotos > 0) {
                const dropzoneContent = dropzone.querySelector('.dropzone-content');
                if (dropzoneContent) {
                    dropzoneContent.innerHTML = `
                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #16a34a; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.25rem; color: #16a34a;">${totalPhotos} foto${totalPhotos !== 1 ? 's' : ''} selecionada${totalPhotos !== 1 ? 's' : ''}</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Clique para adicionar mais</div>
                    `;
                }
                dropzone.style.background = 'linear-gradient(135deg, rgba(22, 163, 74, 0.05) 0%, rgba(34, 197, 94, 0.03) 100%)';
                dropzone.style.borderColor = '#16a34a';
            }
        }

        function loadExistingImages() {
            debugDeletionArrays('loadExistingImages - start');
            
            // Load existing map photos
            if (trajetoData.mapPhotos && trajetoData.mapPhotos.length > 0) {
                loadExistingPhotosIntoPreview(trajetoData.mapPhotos, 'editMapPhotosPreview', 'map');
            } else {
            }
            
            // Load existing other photos
            if (trajetoData.otherPhotos && trajetoData.otherPhotos.length > 0) {
                loadExistingPhotosIntoPreview(trajetoData.otherPhotos, 'editOtherPhotosPreview', 'other');
            } else {
            }
            
            // Update dropzone appearances if images exist
            if (trajetoData.mapPhotos && trajetoData.mapPhotos.length > 0) {
                updateDropzoneAppearance('editMapPhotosDropzone', trajetoData.mapPhotos.length, 'map');
            }
            
            if (trajetoData.otherPhotos && trajetoData.otherPhotos.length > 0) {
                updateDropzoneAppearance('editOtherPhotosDropzone', trajetoData.otherPhotos.length, 'other');
            }
            
        }

        function loadExistingPhotosIntoPreview(photos, containerId, type) {
            
            const container = document.getElementById(containerId);
            if (!container) {
                return;
            }
            
            container.innerHTML = '';
            
            photos.forEach((photo, index) => {
                
                // Handle both URL string and photo object formats
                let photoUrl;
                if (typeof photo === 'string') {
                    photoUrl = photo;
                } else if (photo && photo.url) {
                    photoUrl = photo.url;
                } else if (photo && photo.downloadUrl) {
                    photoUrl = photo.downloadUrl;
                } else {
                    return;
                }
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-thumbnail existing-photo';
                photoDiv.style.cssText = 'display: inline-block; position: relative; margin: 0.25rem; border-radius: 6px; overflow: visible; border: 2px solid #e5e7eb; background: white; z-index: 1;';
                
                const img = document.createElement('img');
                img.style.cssText = 'width: 60px; height: 60px; object-fit: cover; display: block;';
                img.src = photoUrl;
                img.onerror = function() {
                    this.style.background = '#f3f4f6';
                    this.alt = 'Erro ao carregar';
                };
                
                const removeBtn = document.createElement('button');
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.style.cssText = 'position: absolute; top: -8px; right: -8px; width: 20px; height: 20px; border-radius: 50%; background: #dc2626; color: white; border: none; font-size: 0.7rem; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.2); z-index: 10; transition: all 0.2s ease;';
                removeBtn.onmouseover = () => removeBtn.style.transform = 'scale(1.1)';
                removeBtn.onmouseout = () => removeBtn.style.transform = 'scale(1)';
                removeBtn.onclick = () => {
                    // Get the photo URL before removing it
                    const photoUrl = type === 'map' ? trajetoData.mapPhotos[index] : trajetoData.otherPhotos[index];
                    
                    
                    // Add to deletion tracking arrays
                    if (type === 'map') {
                        if (photoUrl && !deletedMapPhotos.includes(photoUrl)) {
                            deletedMapPhotos.push(photoUrl);
                        }
                        // Remove from existing photos array
                        trajetoData.mapPhotos.splice(index, 1);
                        loadExistingPhotosIntoPreview(trajetoData.mapPhotos, 'editMapPhotosPreview', 'map');
                        updateDropzoneAppearance('editMapPhotosDropzone', trajetoData.mapPhotos.length, 'map');
                    } else {
                        if (photoUrl && !deletedOtherPhotos.includes(photoUrl)) {
                            deletedOtherPhotos.push(photoUrl);
                        }
                        // Remove from existing photos array
                        trajetoData.otherPhotos.splice(index, 1);
                        loadExistingPhotosIntoPreview(trajetoData.otherPhotos, 'editOtherPhotosPreview', 'other');
                        updateDropzoneAppearance('editOtherPhotosDropzone', trajetoData.otherPhotos.length, 'other');
                    }
                    
                };
                
                // Add existing photo indicator
                const existingBadge = document.createElement('div');
                existingBadge.innerHTML = '<i class="fas fa-check-circle"></i>';
                existingBadge.style.cssText = 'position: absolute; bottom: -8px; left: -8px; width: 20px; height: 20px; border-radius: 50%; background: #16a34a; color: white; border: 2px solid white; font-size: 0.7rem; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.2); z-index: 10;';
                existingBadge.title = 'Foto existente';
                
                photoDiv.appendChild(img);
                photoDiv.appendChild(removeBtn);
                photoDiv.appendChild(existingBadge);
                container.appendChild(photoDiv);
            });
        }

        function updateDropzoneAppearance(dropzoneId, photoCount, type) {
            const dropzone = document.getElementById(dropzoneId);
            if (!dropzone) return;
            
            const dropzoneContent = dropzone.querySelector('.dropzone-content');
            if (!dropzoneContent) return;
            
            if (photoCount === 0) {
                // Reset to original dropzone appearance when no photos
                dropzoneContent.innerHTML = `
                    <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #0a7ea4; margin-bottom: 0.5rem; display: block;"></i>
                    <div style="font-weight: 600; margin-bottom: 0.25rem;">Arraste as fotos aqui</div>
                    <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">ou clique para selecionar</div>
                    <div style="font-size: 0.7rem; color: #9ca3af;">Máx. 6 fotos • 5MB cada</div>
                `;
                dropzone.style.background = 'linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(8, 145, 178, 0.03) 100%)';
                dropzone.style.borderColor = '#0a7ea4';
            } else {
                // Show existing photos count
                dropzoneContent.innerHTML = `
                    <i class="fas fa-check-circle" style="font-size: 2rem; color: #16a34a; margin-bottom: 0.5rem; display: block;"></i>
                    <div style="font-weight: 600; margin-bottom: 0.25rem; color: #16a34a;">${photoCount} foto${photoCount !== 1 ? 's' : ''} existente${photoCount !== 1 ? 's' : ''}</div>
                    <div style="font-size: 0.75rem; color: #6b7280;">Clique para adicionar mais</div>
                `;
                dropzone.style.background = 'linear-gradient(135deg, rgba(22, 163, 74, 0.05) 0%, rgba(34, 197, 94, 0.03) 100%)';
                dropzone.style.borderColor = '#16a34a';
            }
        }

        function cancelEditTrajectory() {
            
            // Simply close the modal without clearing any photos or data
            // This preserves any uploaded photos for when the user reopens the modal
            const modal = document.getElementById('finalizarTrajetoModal');
            if (modal) {
                
                // Try to get existing Bootstrap modal instance
                let bsModal = bootstrap.Modal.getInstance(modal);
                
                if (bsModal) {
                    bsModal.hide();
                } else {
                    // Create new modal instance and hide it
                    bsModal = new bootstrap.Modal(modal);
                    bsModal.hide();
                }
                
                // Add event listener to remove modal from DOM after it's hidden
                modal.addEventListener('hidden.bs.modal', function() {
                    modal.remove();
                }, { once: true });
                
            } else {
            }
        }

        function ensureContactsDataSynchronized() {
            const dataContactCount = trajetoData.contacts ? trajetoData.contacts.length : 0;
            const markerContactCount = contactMarkers ? contactMarkers.length : 0;
            
            // CRITICAL FIX: For "Edit Current" mode, only extract NEW contacts from markers
            // Don't include existing contacts that were loaded from database
            if (editMode === 'current') {
                if (markerContactCount > 0) {
                    
                    // Get all contact data from markers
                    const contactsFromMarkers = contactMarkers.map(marker => marker.contactData).filter(contact => contact);
                    
                    // Separate existing contacts (have database ID) from new contacts (no ID)
                    const existingContacts = contactsFromMarkers.filter(contact => contact.id);
                    const newContacts = contactsFromMarkers.filter(contact => !contact.id);
                    
                    // CRITICAL: Only update trajetoData with NEW contacts
                    // Existing contacts are already in database and don't need to be saved again
                    trajetoData.contacts = newContacts;
                    
                    return Promise.resolve(newContacts.length);
                } else {
                    trajetoData.contacts = [];
                    return Promise.resolve(0);
                }
            }
            
            // For "Start Over" mode, contacts should be empty
            if (editMode === 'startOver') {
                trajetoData.contacts = [];
                return Promise.resolve(0);
            }
            
            // Handle mismatch cases for other modes
            if (dataContactCount !== markerContactCount) {
                if (markerContactCount > 0 && dataContactCount === 0) {
                    return reloadContactsFromServer().then(() => {
                        const recoveredCount = trajetoData.contacts ? trajetoData.contacts.length : 0;
                        return recoveredCount;
                    }).catch(error => {
                        return 0;
                    });
                }
            }
            
            return Promise.resolve(dataContactCount);
        }

        function reloadContactsFromServer() {
            const trajectoryId = document.getElementById('trajetoId').value;
            
            return fetch('load_trajeto.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    trajetoId: trajectoryId
                })
            })
            .then(response => response.json())
            .then(data => {
                
                if (data.success && data.data.contacts) {
                    
                    // Update trajectory data with recovered contacts
                    trajetoData.contacts = data.data.contacts;
                    
                    // Reload contacts on map to synchronize
                    loadContactsOnMap(trajetoData.contacts);
                    
                    return data.data.contacts;
                } else {
                    throw new Error('Failed to reload contacts from server');
                }
            })
            .catch(error => {
                throw error;
            });
        }

        function saveTrajectoryDetails() {

            
            // CRITICAL FIX: Ensure contacts data is synchronized before saving
            // This prevents the bug where contacts disappear during editing
            const saveBtn = document.getElementById('saveEditDetailsBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A verificar contactos...';
            
            ensureContactsDataSynchronized().then((contactCount) => {
                // Update button to show saving
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A guardar...';
                
                // Continue with save process
                proceedWithSave(originalText);
            }).catch(error => {
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                
                Swal.fire({
                    title: 'Erro de Sincronização',
                    text: 'Erro ao sincronizar contactos. Tente novamente.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc2626'
                });
            });
        }

        function proceedWithSave(originalButtonText) {
            // Prepare form data based on edit mode
            let formData;
            if (editMode === 'startOver') {
                // For start over, create completely new trajectory data
                formData = {
                    id: trajetoData.id, // Keep original ID
                    name: document.getElementById('trajetoName').value || trajetoData.name,
                    description: document.getElementById('trajetoDescription').value || trajetoData.description,
                    difficulty: document.getElementById('trajetoDifficulty').value || trajetoData.difficulty,
                    status: document.getElementById('trajetoStatusSelect').value || trajetoData.status,
                    zoneId: trajetoData.zoneId,
                coordinates: routeCoordinates,
                distance: document.getElementById('routeDistance').textContent,
                pointsCount: routeCoordinates.length,
                    startingAddress: document.getElementById('startingAddress').textContent,
                    // Timeline data
                    startTime: currentStartTime,
                    middleTime: currentMiddleTime,
                    endTime: currentEndTime,
                    trajetoDate: currentTrajetoDate,
                    // Weather data
                    weatherCondition: currentWeatherCondition,
                    // Observers data
                    numberOfObservers: currentObserversCount,
                    // Photo counts (new only for start over)
                    mapPhotosCount: mapPhotosFiles.length,
                    otherPhotosCount: otherPhotosFiles.length,
                    // Metadata
                    createdBy: trajetoData.createdBy,
                    createdAt: trajetoData.createdAt,
                updatedAt: new Date().toISOString(),
                    updatedBy: '<?php echo $_SESSION["user"]["email"] ?? ""; ?>',
                    editMode: 'startOver'
                };
            } else {
                // For edit current, update existing data - only include necessary fields
                formData = {
                    id: trajetoData.id,
                    name: document.getElementById('trajetoName').value || trajetoData.name,
                    description: document.getElementById('trajetoDescription').value || trajetoData.description,
                    difficulty: document.getElementById('trajetoDifficulty').value || trajetoData.difficulty,
                    status: document.getElementById('trajetoStatusSelect').value || trajetoData.status,
                    zoneId: trajetoData.zoneId,
                    coordinates: routeCoordinates,
                    distance: document.getElementById('routeDistance').textContent,
                    pointsCount: routeCoordinates.length,
                    startingAddress: document.getElementById('startingAddress').textContent,
                    // Timeline data
                    startTime: currentStartTime,
                    middleTime: currentMiddleTime,
                    endTime: currentEndTime,
                    trajetoDate: currentTrajetoDate,
                    // Weather data
                    weatherCondition: currentWeatherCondition,
                    // Observers data
                    numberOfObservers: currentObserversCount,
                    // Contacts are stored separately in contacts collection, not in trajectory document
                    // Photo counts (existing - deleted + new)
                    mapPhotosCount: Math.max(0, ((trajetoData.mapPhotos && trajetoData.mapPhotos.length) || 0) + mapPhotosFiles.length),
                    otherPhotosCount: Math.max(0, ((trajetoData.otherPhotos && trajetoData.otherPhotos.length) || 0) + otherPhotosFiles.length),
                    // Metadata
                    createdBy: trajetoData.createdBy,
                    createdAt: trajetoData.createdAt,
                    updatedAt: new Date().toISOString(),
                    updatedBy: '<?php echo $_SESSION["user"]["email"] ?? ""; ?>',
                    editMode: 'current'
                };
            }

            // Update trajectory in Firestore first
            updateTrajetoInFirestore(formData)
                .then(() => {
        
                    
                    // Save contacts to contacts collection (even if empty to clear existing ones)
                    
                    // SIMPLE FIX: Get current contacts and filter by trajectory ID
                    const actualTrajectoryId = document.getElementById('trajetoId').value;
                    let currentContacts = [];
                    
                    
                    
                    if (contactMarkers && contactMarkers.length > 0) {
                        // Use contact data from map markers (most current)
                        
                        
                        try {
                            const markerContacts = contactMarkers.map((marker, index) => {
                                if (marker && marker.contactData) {
                                    return marker.contactData;
                                } else {
                                    return null;
                                }
                            }).filter(contact => {
                                return contact !== null;
                            });
                            
                            currentContacts = markerContacts.filter(contact => {
                                return contact && contact.trajectoryId === actualTrajectoryId;
                            });
                        } catch (error) {
                            console.error('❌ CONTACT_COLLECTION: Error processing markers:', error);
                            throw error;
                        }
                    } else if (trajetoData.contacts && trajetoData.contacts.length > 0) {
                        // Fallback to server data, but filter by trajectory ID
                        currentContacts = trajetoData.contacts.filter(contact => contact.trajectoryId === actualTrajectoryId);

                    } else {
                        // No contacts to save
                        currentContacts = [];

                    }
                    
                    // CONTACT SAVE STRATEGY: Log current state

                    
                    // Validate trajectory ID
                    if (!actualTrajectoryId) {
                        console.error('❌ CONTACT SAVE: No trajectory ID found!');
                        throw new Error('Trajectory ID not found');
                    }
                    
                    // Use the appropriate contact saving approach based on edit mode
                    if (editMode === 'startOver') {
                        // For "Start Over" mode, completely replace all contacts
                        return saveContactsCompleteReplace(actualTrajectoryId, currentContacts);
                    } else {
                        // For "Edit Current" mode, use incremental approach
                        return saveContactsWithValidation(actualTrajectoryId, currentContacts);
                    }
                })
                .then(() => {
                    // Delete removed photos from Firebase Storage
                    
                    // COMPREHENSIVE DELETION LOGIC
                    // This ensures complete cleanup of photos:
                    // 1. Photos explicitly removed by user (clicking red X buttons)
                    // 2. Photos that become "orphaned" when user removes all photos of a type
                    // 3. Safety checks to only delete photos belonging to this trajectory
                    let photosToDelete = [...deletedMapPhotos, ...deletedOtherPhotos];
                    
                    // If editing current trajectory and user has no photos selected,
                    // we need to delete any remaining existing photos that weren't explicitly removed
                    if (editMode === 'current') {
                        const currentMapPhotos = trajetoData.mapPhotos || [];
                        const currentOtherPhotos = trajetoData.otherPhotos || [];
                        const hasNewMapPhotos = mapPhotosFiles.length > 0;
                        const hasNewOtherPhotos = otherPhotosFiles.length > 0;
                        

                        
                        // If user has no map photos (neither existing nor new), delete all original map photos
                        if (currentMapPhotos.length === 0 && !hasNewMapPhotos) {
                            const originalMapPhotos = originalTrajetoData?.mapPhotos || [];
                            originalMapPhotos.forEach(photoUrl => {
                                // Safety check: only delete photos that belong to this trajectory
                                const trajectoryId = document.getElementById('trajetoId').value;
                                if (photoUrl && photoUrl.includes(trajectoryId) && !photosToDelete.includes(photoUrl)) {
                                    photosToDelete.push(photoUrl);
                                }
                            });
                        }
                        
                        // If user has no other photos (neither existing nor new), delete all original other photos
                        if (currentOtherPhotos.length === 0 && !hasNewOtherPhotos) {
                            const originalOtherPhotos = originalTrajetoData?.otherPhotos || [];
                            originalOtherPhotos.forEach(photoUrl => {
                                // Safety check: only delete photos that belong to this trajectory
                                const trajectoryId = document.getElementById('trajetoId').value;
                                if (photoUrl && photoUrl.includes(trajectoryId) && !photosToDelete.includes(photoUrl)) {
                                    photosToDelete.push(photoUrl);
                                }
                            });
                        }
                    }
                    

                    
                    // Safety verification: ensure all photos belong to current trajectory
                    const trajectoryId = document.getElementById('trajetoId').value;
                    const safePhotos = photosToDelete.filter(photoUrl => photoUrl && photoUrl.includes(trajectoryId));
                    const unsafePhotos = photosToDelete.filter(photoUrl => photoUrl && !photoUrl.includes(trajectoryId));
                    
                    if (unsafePhotos.length > 0) {
                        photosToDelete = safePhotos;
                    } else {
                    }
                    
                    if (photosToDelete.length > 0) {
                        return deletePhotosFromFirebase(photosToDelete);
                    } else {
                        return Promise.resolve();
                    }
                })
                .then(() => {
                    
                    // Upload new images to Firebase Storage if any were selected
                    if (mapPhotosFiles.length > 0 || otherPhotosFiles.length > 0) {
                        const actualTrajectoryId = document.getElementById('trajetoId').value;
                        return uploadEditTrajectoryPhotos(actualTrajectoryId);
                    } else {
                        return Promise.resolve();
                    }
                })
                .then(() => {
                    
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('finalizarTrajetoModal'));
                    modal.hide();
                    
                    Swal.fire({
                        title: 'Sucesso!',
                        text: 'Trajeto atualizado com sucesso.',
                        icon: 'success',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#16a34a'
                    }).then(() => {
                        window.location.href = 'index.php';
                    });
                })
                .catch(error => {
                    
                    // Check if this is our contact safety block
                    if (error.message && error.message.includes('Contact data mismatch')) {
                        Swal.fire({
                            title: 'Erro de Sincronização de Contactos',
                            html: `
                                <div style="text-align: left; padding: 1rem;">
                                    <p><strong>Detectado um problema que poderia causar perda de contactos.</strong></p>
                                    <p>O sistema bloqueou a operação para proteger os seus dados.</p>
                                    <br>
                                    <p><strong>Para resolver:</strong></p>
                                    <ol>
                                        <li>Clique em "OK" para fechar esta mensagem</li>
                                        <li>Clique no botão "Voltar" no modal</li>
                                        <li>Recarregue a página (F5)</li>
                                        <li>Tente novamente</li>
                                    </ol>
                                    <br>
                                    <p style="color: #16a34a;"><i class="fas fa-shield-alt"></i> Os seus contactos estão protegidos e não foram perdidos.</p>
                                </div>
                            `,
                            icon: 'warning',
                            confirmButtonText: 'OK, Entendi',
                            confirmButtonColor: '#f59e0b',
                            width: '500px'
                        });
                    } else {
                        Swal.fire({
                            title: 'Erro',
                            text: 'Erro ao atualizar o trajeto. Tente novamente.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#dc2626'
                        });
                    }
                })
                .finally(() => {
                    const saveBtn = document.getElementById('saveEditDetailsBtn');
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = originalButtonText;
                });
        }

        function updateTrajetoInFirestore(trajetoData) {
            return new Promise((resolve, reject) => {
                const trajetoId = document.getElementById('trajetoId').value;
                

                
                // Call PHP endpoint to update trajeto
                fetch('update_trajeto.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        trajetoId: trajetoId,
                        trajetoData: trajetoData
                    })
                })
                .then(response => {
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        resolve();
                    } else {
                        console.error('❌ UPDATE_TRAJETO: Trajectory update failed:', data.message);
                        reject(new Error(data.message));
                    }
                })
                .catch(error => {
                    console.error('❌ UPDATE_TRAJETO: Error during trajectory update:', error);
                    reject(error);
                });
            });
        }

        // Simple function to save contacts directly (without delete)
        function saveContactsDirectly(trajectoryId, contacts) {
            return new Promise((resolve, reject) => {
                if (!contacts || contacts.length === 0) {
                    resolve({ success: true, contactCount: 0 });
                    return;
                }

                // Prepare contacts data for the API
                const contactsData = contacts.map(contact => ({
                    lat: contact.coordinates ? contact.coordinates.lat : contact.lat,
                    lng: contact.coordinates ? contact.coordinates.lng : contact.lng,
                    time: contact.time,
                    circumstance: contact.circumstance,
                    location: contact.location
                }));

                // Call the save_contacts.php endpoint
                fetch('save_contacts.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        trajectoryId: trajectoryId,
                        contacts: contactsData
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resolve(data);
                    } else {
                        reject(new Error(data.message || 'Failed to save contacts'));
                    }
                })
                .catch(error => {
                    reject(error);
                });
            });
        }

        // Save contacts to the contacts collection
        function saveContactsToCollection(trajectoryId, contacts) {
            return new Promise((resolve, reject) => {
                
                
                // Check if we have contacts to save
                if (!contacts || contacts.length === 0) {
    
                    resolve({ success: true, contactCount: 0 });
                    return;
                }

                // Prepare contacts data for the API
                const contactsData = contacts.map((contact, index) => {
                    return {
                        lat: contact.coordinates ? contact.coordinates.lat : contact.lat,
                        lng: contact.coordinates ? contact.coordinates.lng : contact.lng,
                        time: contact.time,
                        circumstance: contact.circumstance,
                        location: contact.location
                    };
                });

                // Call the save_contacts.php endpoint
                fetch('save_contacts.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        trajectoryId: trajectoryId,
                        contacts: contactsData
                    })
                })
                .then(response => {
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // VERIFICATION: Check if the number of saved contacts matches expected
                        const expectedCount = contacts.length;
                        const actualCount = data.contactCount;
                        
                        if (expectedCount !== actualCount) {
                            console.warn(`⚠️ SAVE_CONTACTS_TO_COLLECTION: Contact count mismatch - Expected: ${expectedCount}, Actual: ${actualCount}`);
                        }
                        
                        resolve(data);
                    } else {
                        console.error('❌ SAVE_CONTACTS_TO_COLLECTION: Save failed:', data.message);
                        reject(new Error(data.message || 'Failed to save contacts'));
                    }
                })
                .catch(error => {
                    console.error('❌ SAVE_CONTACTS_TO_COLLECTION: Error during save:', error);
                    reject(error);
                });
            });
        }

        // NEW: Incremental contact save - only saves new contacts, doesn't touch existing ones
        

        // Complete contact replacement for "Start Over" mode
        function saveContactsCompleteReplace(trajectoryId, contacts) {
            return new Promise((resolve, reject) => {
                // STEP 1: Delete ALL existing contacts for this trajectory
                fetch('delete_trajectory_contacts.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        trajectoryId: trajectoryId,
                        zoneId: document.getElementById('zoneId').value || trajetoData.zoneId || ''
                    })
                })
                .then(response => response.json())
                .then(deleteResult => {
                    if (!deleteResult.success) {
                        throw new Error('Failed to delete existing contacts: ' + deleteResult.message);
                    }
                    
                    // STEP 2: Save new contacts if any exist
                    if (!contacts || contacts.length === 0) {
                        // No new contacts to save, just return success
                        resolve({
                            success: true,
                            contactCount: 0,
                            message: 'All existing contacts deleted, no new contacts to save',
                            operations: {
                                deleted: deleteResult.deletedCount || 0,
                                added: 0,
                                total: 0
                            }
                        });
                        return;
                    }
                    
                    // Prepare contacts data for saving
                    const contactsData = contacts.map(contact => ({
                        lat: contact.coordinates ? contact.coordinates.lat : contact.lat,
                        lng: contact.coordinates ? contact.coordinates.lng : contact.lng,
                        time: contact.time,
                        circumstance: contact.circumstance,
                        location: contact.location
                    }));
                    
                    // STEP 3: Save new contacts
                    return fetch('save_contacts.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            trajectoryId: trajectoryId,
                            contacts: contactsData
                        })
                    });
                })
                .then(response => response.json())
                .then(saveResult => {
                    if (saveResult.success) {
                        resolve({
                            success: true,
                            contactCount: saveResult.contactCount || 0,
                            message: 'Contacts completely replaced successfully',
                            operations: {
                                deleted: 'all existing',
                                added: saveResult.contactCount || 0,
                                total: saveResult.contactCount || 0
                            }
                        });
                    } else {
                        throw new Error('Failed to save new contacts: ' + saveResult.message);
                    }
                })
                .catch(error => {
                    console.error('❌ COMPLETE REPLACE: Error replacing contacts:', error);
                    reject(error);
                });
            });
        }

        // Simple contact saving with trajectory ID enforcement and deduplication
        function saveContactsWithValidation(trajectoryId, contacts) {

            
            // CRITICAL: Only save contacts that don't have database IDs (new contacts only)
            const newContactsOnly = contacts.filter(contact => !contact.id);
            const existingContacts = contacts.filter(contact => contact.id);
            

            
            // If no new contacts, return early
            if (newContactsOnly.length === 0) {

                return Promise.resolve({
                    success: true,
                    contactCount: 0,
                    message: 'No new contacts to save',
                    operations: {
                        added: 0,
                        existing: existingContacts.length,
                        skipped: existingContacts.length
                    }
                });
            }
            
            // Ensure all NEW contacts have the correct trajectory ID
            const safeContacts = newContactsOnly.map((contact, index) => {
                return {
                    ...contact,
                    trajectoryId: trajectoryId, // Force correct trajectory ID
                    updatedAt: new Date().toISOString(),
                    updatedBy: '<?php echo $_SESSION["user"]["uid"] ?? ""; ?>'
                };
            });
            return saveContactsIncrementally(trajectoryId, safeContacts);
        }

        function saveContactsIncrementally(trajectoryId, contacts) {
            return new Promise((resolve, reject) => {
                
                if (!contacts || contacts.length === 0) {
                    resolve({ success: true, contactCount: 0, message: 'No contacts to save' });
                    return;
                }
                
                // STEP 1: Get existing contacts from database
                fetch('load_trajeto.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        trajetoId: trajectoryId
                    })
                })
                .then(response => response.json())
                .then(existingData => {
                    const existingContacts = existingData.contacts || [];
                    
                    // STEP 2: Identify NEW contacts (those without database IDs)
                    const newContacts = contacts.filter(contact => !contact.id);
                    const existingContactsInMemory = contacts.filter(contact => contact.id);
                    
                    
                    if (newContacts.length === 0) {
                        resolve({ 
                            success: true, 
                            contactCount: contacts.length, 
                            message: 'No new contacts to save',
                            operations: {
                                added: 0,
                                existing: contacts.length
                            }
                        });
                        return;
                    }
                    
                    // STEP 3: Save only NEW contacts using incremental endpoint
                    
                    const contactsData = newContacts.map(contact => ({
                        lat: contact.coordinates ? contact.coordinates.lat : contact.lat,
                        lng: contact.coordinates ? contact.coordinates.lng : contact.lng,
                        time: contact.time,
                        circumstance: contact.circumstance,
                        location: contact.location
                    }));
                    
                    
                    return fetch('save_contacts_incremental.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            trajectoryId: trajectoryId,
                            contacts: contactsData
                        })
                    });
                })
                .then(response => response.json())
                .then(data => {
                    
                    if (data.success) {
                        const totalContacts = (contacts.length - (contacts.filter(c => !c.id).length)) + data.contactCount;
                        
                        resolve({
                            success: true,
                            contactCount: totalContacts,
                            message: data.message,
                            operations: {
                                added: data.contactCount,
                                existing: contacts.filter(c => c.id).length
                            }
                        });
                    } else {
                        reject(new Error(data.message || 'Failed to save new contacts'));
                    }
                })
                .catch(error => {
                    reject(error);
                });
            });
        }

        async function uploadEditTrajectoryPhotos(trajectoryId) {
            
            const totalPhotos = mapPhotosFiles.length + otherPhotosFiles.length;
            let uploadedPhotos = 0;
            
            if (totalPhotos === 0) {
                return Promise.resolve();
            }
            
            // Show upload progress modal
            Swal.fire({
                title: 'A carregar fotografias...',
                html: `
                    <div class="upload-progress-container">
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%; background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);" 
                                 id="uploadProgressBar">
                                0%
                            </div>
                        </div>
                        <p id="uploadStatus">A preparar carregamento...</p>
                        <small class="text-muted" id="uploadDetails">0 de ${totalPhotos} fotografias carregadas</small>
                    </div>
                `,
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            const updateProgress = () => {
                const percentage = Math.round((uploadedPhotos / totalPhotos) * 100);
                const progressBar = document.getElementById('uploadProgressBar');
                const statusText = document.getElementById('uploadStatus');
                const detailsText = document.getElementById('uploadDetails');
                
                if (progressBar) {
                    progressBar.style.width = percentage + '%';
                    progressBar.textContent = percentage + '%';
                }
                
                if (statusText) {
                    statusText.textContent = `A carregar fotografia ${uploadedPhotos + 1} de ${totalPhotos}...`;
                }
                
                if (detailsText) {
                    detailsText.textContent = `${uploadedPhotos} de ${totalPhotos} fotografias carregadas`;
                }
            };
            
            const uploadPromises = [];
            
            // Upload map photos
            mapPhotosFiles.forEach((file, index) => {
                uploadPromises.push(
                    uploadPhotoToFirebase(file, trajectoryId, 'maps', index)
                        .then(result => {
                            uploadedPhotos++;
                            updateProgress();
                            return result;
                        })
                        .catch(error => {
                            throw error;
                        })
                );
            });
            
            // Upload other photos
            otherPhotosFiles.forEach((file, index) => {
                uploadPromises.push(
                    uploadPhotoToFirebase(file, trajectoryId, 'other', index)
                        .then(result => {
                            uploadedPhotos++;
                            updateProgress();
                            return result;
                        })
                        .catch(error => {
                            throw error;
                        })
                );
            });
            
            return Promise.all(uploadPromises);
        }

        async function uploadPhotoToFirebase(file, trajectoryId, type, index) {
            return new Promise((resolve, reject) => {
                
                const formData = new FormData();
                formData.append('photo', file);
                formData.append('trajectoryId', trajectoryId);
                formData.append('type', type);
                formData.append('index', index);
                
                fetch('upload_trajectory_photo.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    
                    // Get the raw response text first
                    return response.text().then(text => {
                        
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            throw new Error('Server returned invalid JSON: ' + text.substring(0, 200));
                        }
                    });
                })
                .then(data => {
                    
                    if (data.success) {
                        resolve(data);
                    } else {
                        reject(new Error(data.message || 'Upload failed'));
                    }
                })
                .catch(error => {
                    reject(error);
                });
            });
        }

        async function deletePhotosFromFirebase(deletedPhotos) {
            
            if (!deletedPhotos || deletedPhotos.length === 0) {
                return Promise.resolve();
            }
            
            
            const deletePromises = deletedPhotos.map(photoUrl => {
                return new Promise((resolve, reject) => {
                    
                    fetch('delete_trajectory_photo.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ photoUrl: photoUrl })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            resolve(data);
                        } else {
                            // Don't reject - we want to continue with other deletions
                            resolve({ success: false, message: data.message });
                        }
                    })
                    .catch(error => {
                        // Don't reject - we want to continue with other deletions
                        resolve({ success: false, error: error.message });
                    });
                });
            });
            
            return Promise.all(deletePromises);
        }

        function cancelEdit() {
            Swal.fire({
                title: 'Cancelar Edição?',
                text: 'Tem a certeza que pretende sair sem guardar as alterações?',
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Sim, Sair',
                cancelButtonText: '<i class="fas fa-edit me-2"></i>Continuar a Editar',
                confirmButtonColor: '#0a7ea4',
                cancelButtonColor: '#6b7280'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'index.php';
                }
            });
        }

        function showTrajetoHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Editar Trajeto</div>',
                html: `
                    <div class="help-modal-content">
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-route text-primary me-2"></i>
                                <span>Opções de Edição do Trajeto</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-edit text-primary me-2"></i>
                                    <span><strong>Editar Trajeto Atual:</strong> Modifica os pontos do trajeto existente, mantendo os dados originais</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-redo text-warning me-2"></i>
                                    <span><strong>Começar do início:</strong> Inicia um novo trajeto com seleção de localização (GPS, pesquisa ou manual)</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-mouse-pointer text-primary me-2"></i>
                                    <span><strong>Adicionar pontos:</strong> Clique no mapa para adicionar novos pontos ao trajeto</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-undo text-warning me-2"></i>
                                    <span><strong>Desfazer Último:</strong> Remove o último ponto adicionado</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-eraser text-secondary me-2"></i>
                                    <span><strong>Limpar Trajeto:</strong> Remove todos os pontos para recomeçar</span>
                                </div>
                            </div>
                        </div>

                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-cogs text-primary me-2"></i>
                                <span>Cartões de Informação Editáveis</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <span><strong>Timeline:</strong> Clique para definir horários de início, meio e fim do trajeto</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-cloud-sun text-primary me-2"></i>
                                    <span><strong>Condições Meteorológicas:</strong> Clique para selecionar as condições climáticas (céu limpo, nublado, chuva, vento)</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    <span><strong>Observadores:</strong> Clique para definir o número de observadores presentes no trajeto</span>
                                </div>
                            </div>
                        </div>

                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-dove text-primary me-2"></i>
                                <span>Gestão de Contactos</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-plus text-success me-2"></i>
                                    <span><strong>Adicionar Contacto:</strong> Clique no botão para ativar o modo de adição de contactos</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-map-marker-alt text-info me-2"></i>
                                    <span><strong>Marcar no Mapa:</strong> Com o modo ativo, clique no mapa para adicionar um contacto nessa localização</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-edit text-primary me-2"></i>
                                    <span><strong>Editar Contacto:</strong> Clique no marcador do contacto para editar detalhes (hora, circunstância, local)</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-trash text-danger me-2"></i>
                                    <span><strong>Remover Contacto:</strong> Use o botão de eliminar no formulário de edição do contacto</span>
                                </div>
                            </div>
                        </div>

                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-map-marked-alt text-primary me-2"></i>
                                <span>Seleção de Localização (Começar do início)</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-crosshairs text-success me-2"></i>
                                    <span><strong>Localização Atual:</strong> Usa o GPS para definir a posição atual como ponto de partida</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-search text-info me-2"></i>
                                    <span><strong>Pesquisar Localização:</strong> Permite pesquisar por cidade, código postal ou morada específica</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-map text-primary me-2"></i>
                                    <span><strong>Definir Manualmente:</strong> Permite escolher a localização clicando diretamente no mapa</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-camera text-primary me-2"></i>
                                <span>Finalizar Trajeto - Upload de Fotos</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-map text-primary me-2"></i>
                                    <span><strong>Fotos dos Mapas:</strong> Arraste e solte ou clique para adicionar fotos dos mapas (máximo 6 fotos, 5MB cada)</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-images text-primary me-2"></i>
                                    <span><strong>Outras Fotos:</strong> Arraste e solte ou clique para adicionar outras fotos relevantes (máximo 6 fotos, 5MB cada)</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-eye text-info me-2"></i>
                                    <span><strong>Pré-visualização:</strong> As fotos selecionadas aparecem como miniaturas abaixo de cada área</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    <span><strong>Remover Fotos:</strong> Clique no X vermelho no canto da miniatura para remover uma foto</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span><strong>Fotos Existentes:</strong> Fotos já guardadas aparecem com marca verde, novas fotos com ícone azul</span>
                                </div>
                            </div>
                        </div>

                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-clipboard-check text-primary me-2"></i>
                                <span>Fluxo de Trabalho</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-play text-success me-2"></i>
                                    <span><strong>1. Escolha o Modo:</strong> Selecione "Editar Trajeto Atual" ou "Começar do início"</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-map-marked-alt text-primary me-2"></i>
                                    <span><strong>2. Defina o Trajeto:</strong> Adicione ou modifique pontos no mapa até atingir 2.5 km mínimos</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-edit text-warning me-2"></i>
                                    <span><strong>3. Preencha os Dados:</strong> Clique em "Preencher Dados" quando o trajeto estiver válido</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-check-circle text-info me-2"></i>
                                    <span><strong>4. Complete os Cartões:</strong> Preencha timeline, condições meteorológicas e observadores</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-camera text-primary me-2"></i>
                                    <span><strong>5. Adicione Fotos:</strong> Faça upload das fotos dos mapas e outras fotos no modal final</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-save text-success me-2"></i>
                                    <span><strong>6. Guarde:</strong> Clique em "Guardar Alterações" para finalizar a edição</span>
                                </div>
                            </div>
                        </div>

                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-lightbulb text-primary me-2"></i>
                                <span>Dicas Importantes</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-circle text-success me-2"></i>
                                    <span><strong>Distância mínima:</strong> O trajeto deve ter pelo menos 2.5 km para ser válido</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-info me-2"></i>
                                    <span><strong>Validação automática:</strong> A distância é calculada em tempo real e validada automaticamente</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-warning me-2"></i>
                                    <span><strong>Guardar alterações:</strong> Não esqueça de guardar antes de sair da página</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Endereço automático:</strong> O endereço de partida é atualizado automaticamente</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-danger me-2"></i>
                                    <span><strong>Segurança:</strong> Os dados originais são preservados até confirmar as alterações</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-warning me-2"></i>
                                    <span><strong>Formatos aceites:</strong> Apenas imagens (JPG, PNG, GIF, WebP) são permitidas</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span>Precisa de Ajuda?</span>
                            </div>
                            <div class="help-section-body">
                                <div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">
                                    <div style="color: #0a7ea4; font-size: 0.9rem;">
                                        Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>
                                        <strong><EMAIL></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }

        // Timeline editing functions
        function editTimeline() {
            // Populate current values
            document.getElementById('editStartTime').value = currentStartTime || '';
            document.getElementById('editMiddleTime').value = currentMiddleTime || '';
            document.getElementById('editEndTime').value = currentEndTime || '';
            document.getElementById('editTrajetoDate').value = currentTrajetoDate || '';

            // Setup date picker event listeners if not already done
            setupTimelineDatePicker();

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('timelineEditModal'));
            modal.show();
        }

        function saveTimeline() {
            const startTime = document.getElementById('editStartTime').value;
            const middleTime = document.getElementById('editMiddleTime').value;
            const endTime = document.getElementById('editEndTime').value;
            const trajetoDate = document.getElementById('editTrajetoDate').value;

            // Validate required fields
            if (!startTime) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'A hora de início é obrigatória.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#f59e0b'
                });
                return;
            }

            if (!trajetoDate) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'A data do trajeto é obrigatória.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#f59e0b'
                });
                return;
            }

            // Update global variables
            currentStartTime = startTime;
            currentMiddleTime = middleTime;
            currentEndTime = endTime;
            currentTrajetoDate = trajetoDate;

            // Update display
            document.getElementById('startTimeCompact').textContent = startTime || '--:--';
            document.getElementById('middleTimeCompact').textContent = middleTime || '--:--';
            document.getElementById('endTimeCompact').textContent = endTime || '--:--';

            // Update trajectory date display
            updateTrajetoDateDisplay();

            // Update header trajectory name with new date/time
            updateHeaderTrajetoName();

            // Add visual feedback
            const timelineCard = document.querySelector('.editable-card[onclick="editTimeline()"]');
            timelineCard.style.background = 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
            timelineCard.style.borderColor = '#16a34a';
            setTimeout(() => {
                timelineCard.style.background = 'white';
                timelineCard.style.borderColor = '#e5e7eb';
            }, 2000);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('timelineEditModal'));
            modal.hide();

            // Show success message
            Swal.fire({
                title: 'Timeline Atualizada!',
                text: 'A data e horários foram atualizados com sucesso.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a',
                timer: 2000,
                timerProgressBar: true
            });
        }

        // Custom Date Picker Functions for Timeline
        function openTimelineDatePicker() {
            // Get current date value if any
            const currentDate = document.getElementById('editTrajetoDate').value;
            
            if (currentDate && currentDate.includes('/')) {
                // Parse DD/MM/YYYY format
                const dateParts = currentDate.split('/');
                if (dateParts.length === 3) {
                    const [day, month, year] = dateParts;
                    if (day && month && year) {
                        document.getElementById('timelineDaySelect').value = day.padStart(2, '0');
                        document.getElementById('timelineMonthSelect').value = month.padStart(2, '0');
                        document.getElementById('timelineYearSelect').value = year;
                        updateTimelineDateDisplay();
                    } else {
                        // If date parts are invalid, use current date
                        setCurrentDateAsDefault();
                    }
                } else {
                    // If date format is invalid, use current date
                    setCurrentDateAsDefault();
                }
            } else {
                // Default to current date
                setCurrentDateAsDefault();
            }
            
            // Apply date restrictions
            filterTimelineAvailableMonths();
            filterTimelineAvailableDays();
            
            // Show the date picker modal
            const dateModal = new bootstrap.Modal(document.getElementById('timelineDatePickerModal'));
            dateModal.show();
        }

        function setCurrentDateAsDefault() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = String(now.getFullYear());
            document.getElementById('timelineDaySelect').value = day;
            document.getElementById('timelineMonthSelect').value = month;
            document.getElementById('timelineYearSelect').value = year;
            updateTimelineDateDisplay();
        }

        // Update the trajectory date display in the Timeline card
        function updateTrajetoDateDisplay() {
            const dateDisplay = document.getElementById('trajetoDateDisplay');
            if (dateDisplay) {
                dateDisplay.textContent = currentTrajetoDate || '--/--/----';
            }
        }

        // Update the header trajectory name with new date
        function updateHeaderTrajetoName() {
            if (trajetoData && currentTrajetoDate) {
                // Extract base name without date and time
                let baseName = trajetoData.name || 'Trajeto';
                
                // Remove old date pattern (- DD/MM/YYYY HH:MM)
                baseName = baseName.replace(/ - \d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/, '');
                
                // Create new name with updated date and start time
                const startTime = currentStartTime || '00:00';
                const newName = `${baseName} - ${currentTrajetoDate} ${startTime}`;
                
                // Update header display
                document.getElementById('headerTrajetoName').textContent = newName;
                
                // Update form field
                document.getElementById('trajetoName').value = newName;
                
            }
        }

        let timelineDatePickerSetup = false;
        function setupTimelineDatePicker() {
            if (timelineDatePickerSetup) return; // Only setup once
            timelineDatePickerSetup = true;
            
            // Populate days (1-31) - will be filtered based on month/year selection
            const daySelect = document.getElementById('timelineDaySelect');
            daySelect.innerHTML = ''; // Clear existing options
            for (let i = 1; i <= 31; i++) {
                const option = document.createElement('option');
                option.value = String(i).padStart(2, '0');
                option.textContent = String(i).padStart(2, '0');
                daySelect.appendChild(option);
            }
            
            // Populate years (only past years and current year)
            const yearSelect = document.getElementById('timelineYearSelect');
            yearSelect.innerHTML = ''; // Clear existing options
            const currentYear = new Date().getFullYear();
            for (let i = currentYear - 5; i <= currentYear; i++) {
                const option = document.createElement('option');
                option.value = String(i);
                option.textContent = String(i);
                yearSelect.appendChild(option);
            }
            
            // Update display when selections change and filter available options
            document.getElementById('timelineDaySelect').addEventListener('change', updateTimelineDateDisplay);
            document.getElementById('timelineMonthSelect').addEventListener('change', function() {
                filterTimelineAvailableDays();
                updateTimelineDateDisplay();
            });
            document.getElementById('timelineYearSelect').addEventListener('change', function() {
                filterTimelineAvailableMonths();
                filterTimelineAvailableDays();
                updateTimelineDateDisplay();
            });
            
            // Add click listeners for date picker button and input
            document.addEventListener('click', function(e) {
                if (e.target && (e.target.classList.contains('timeline-date-picker-btn') || e.target.closest('.timeline-date-picker-btn'))) {
                    e.preventDefault();
                    e.stopPropagation();
                    openTimelineDatePicker();
                }
            });
            
            // Date input click (readonly, so open picker)
            document.getElementById('editTrajetoDate').addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                openTimelineDatePicker();
            });
            
            // Confirm button
            document.getElementById('timelineConfirmDateBtn').addEventListener('click', function() {
                const day = document.getElementById('timelineDaySelect').value;
                const month = document.getElementById('timelineMonthSelect').value;
                const year = document.getElementById('timelineYearSelect').value;
                
                // Validate date exists
                const date = new Date(year, month - 1, day);
                if (date.getDate() != day || date.getMonth() != month - 1 || date.getFullYear() != year) {
                    Swal.fire({
                        title: 'Data inválida',
                        text: 'Por favor selecione uma data válida.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                // Validate date is not in the future
                const today = new Date();
                today.setHours(23, 59, 59, 999); // Set to end of today for comparison
                if (date > today) {
                    Swal.fire({
                        title: 'Data futura não permitida',
                        text: 'Não é possível selecionar uma data futura. Por favor selecione a data de hoje ou uma data anterior.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#0a7ea4'
                    });
                    return;
                }
                
                const dateString = `${day}/${month}/${year}`;
                
                // Update the global variable
                currentTrajetoDate = dateString;
                
                // Set the date input value
                const dateInput = document.getElementById('editTrajetoDate');
                if (dateInput) {
                    dateInput.value = dateString;
                    
                    // Trigger change event to ensure any listeners are notified
                    dateInput.dispatchEvent(new Event('change', { bubbles: true }));
                    dateInput.dispatchEvent(new Event('input', { bubbles: true }));
                } else {
                }
                
                // Update the trajectory date display in the Timeline card
                updateTrajetoDateDisplay();
                
                // Update the header trajectory name with new date
                updateHeaderTrajetoName();
                
                // Close the date picker modal
                const dateModal = bootstrap.Modal.getInstance(document.getElementById('timelineDatePickerModal'));
                if (dateModal) {
                    dateModal.hide();
                } else {
                }
            });
        }

        function updateTimelineDateDisplay() {
            const day = document.getElementById('timelineDaySelect').value || '--';
            const month = document.getElementById('timelineMonthSelect').value || '--';
            const year = document.getElementById('timelineYearSelect').value || '----';
            const dateString = `${day}/${month}/${year}`;
            document.getElementById('timelineSelectedDateDisplay').textContent = dateString;
        }

        function filterTimelineAvailableMonths() {
            const yearSelect = document.getElementById('timelineYearSelect');
            const monthSelect = document.getElementById('timelineMonthSelect');
            const selectedYear = parseInt(yearSelect.value);
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11
            
            // Get all month options
            const monthOptions = monthSelect.querySelectorAll('option');
            
            monthOptions.forEach(option => {
                const monthValue = parseInt(option.value);
                
                if (selectedYear === currentYear) {
                    // For current year, only allow current month and previous months
                    if (monthValue > currentMonth) {
                        option.disabled = true;
                        option.style.display = 'none';
                    } else {
                        option.disabled = false;
                        option.style.display = 'block';
                    }
                } else {
                    // For past years, allow all months
                    option.disabled = false;
                    option.style.display = 'block';
                }
            });
        }

        function filterTimelineAvailableDays() {
            const daySelect = document.getElementById('timelineDaySelect');
            const monthSelect = document.getElementById('timelineMonthSelect');
            const yearSelect = document.getElementById('timelineYearSelect');
            const selectedMonth = parseInt(monthSelect.value);
            const selectedYear = parseInt(yearSelect.value);
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            const currentDay = currentDate.getDate();
            
            // Get number of days in selected month
            const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
            
            // Get all day options
            const dayOptions = daySelect.querySelectorAll('option');
            
            dayOptions.forEach(option => {
                const dayValue = parseInt(option.value);
                
                // Hide days that don't exist in the selected month
                if (dayValue > daysInMonth) {
                    option.disabled = true;
                    option.style.display = 'none';
                } else if (selectedYear === currentYear && selectedMonth === currentMonth) {
                    // For current month of current year, only allow current day and previous days
                    if (dayValue > currentDay) {
                        option.disabled = true;
                        option.style.display = 'none';
                    } else {
                        option.disabled = false;
                        option.style.display = 'block';
                    }
                } else {
                    // For past months/years, allow all valid days
                    option.disabled = false;
                    option.style.display = 'block';
                }
            });
        }

        // Weather editing functions
        function editWeather() {
            // Clear previous selection
            document.querySelectorAll('input[name="edit_weather_conditions"]').forEach(radio => {
                radio.checked = false;
            });
            document.getElementById('editWeatherOtherInput').style.display = 'none';
            document.getElementById('editWeatherOtherText').value = '';

            // Set current selection if exists
            if (currentWeatherCondition) {
                if (currentWeatherCondition.startsWith('Outro:')) {
                    document.getElementById('edit_weather_other').checked = true;
                    document.getElementById('editWeatherOtherInput').style.display = 'block';
                    document.getElementById('editWeatherOtherText').value = currentWeatherCondition.replace('Outro: ', '');
                } else {
                    // Find matching radio button by label text
                    const weatherLabels = {
                        'Céu limpo': 'edit_weather_clear',
                        'Nublado': 'edit_weather_cloudy',
                        'Chuva': 'edit_weather_rain',
                        'Vento moderado a forte': 'edit_weather_wind'
                    };
                    
                    for (const [label, radioId] of Object.entries(weatherLabels)) {
                        if (currentWeatherCondition === label) {
                            document.getElementById(radioId).checked = true;
                            break;
                        }
                    }
                }
            }

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('weatherEditModal'));
            modal.show();
        }

        function saveWeather() {
            const weatherRadio = document.querySelector('input[name="edit_weather_conditions"]:checked');
            
            if (!weatherRadio) {
                Swal.fire({
                    title: 'Campo Obrigatório',
                    text: 'Por favor selecione as condições meteorológicas.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#f59e0b'
                });
                return;
            }

            let weatherCondition;
            let weatherIcon;
            
            if (weatherRadio.value === 'other') {
                const otherText = document.getElementById('editWeatherOtherText').value.trim();
                if (!otherText) {
                    Swal.fire({
                        title: 'Campo Obrigatório',
                        text: 'Por favor especifique as condições meteorológicas.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#f59e0b'
                    });
                    return;
                }
                weatherCondition = `Outro: ${otherText}`;
                weatherIcon = '<i class="fas fa-question-circle text-muted"></i>';
            } else {
                const weatherOptions = {
                    'clear': { condition: 'Céu limpo', icon: '<i class="fas fa-sun text-warning"></i>' },
                    'cloudy': { condition: 'Nublado', icon: '<i class="fas fa-cloud text-secondary"></i>' },
                    'rain': { condition: 'Chuva', icon: '<i class="fas fa-cloud-rain text-primary"></i>' },
                    'wind': { condition: 'Vento moderado a forte', icon: '<i class="fas fa-wind text-info"></i>' }
                };
                
                const selected = weatherOptions[weatherRadio.value];
                weatherCondition = selected.condition;
                weatherIcon = selected.icon;
            }

            // Update global variables
            currentWeatherCondition = weatherCondition;

            // Update display
            document.getElementById('weatherIconCompact').innerHTML = weatherIcon;
            document.getElementById('weatherTextCompact').textContent = weatherCondition;

            // Add visual feedback
            const weatherCard = document.querySelector('.editable-card[onclick="editWeather()"]');
            weatherCard.style.background = 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
            weatherCard.style.borderColor = '#16a34a';
            setTimeout(() => {
                weatherCard.style.background = 'white';
                weatherCard.style.borderColor = '#e5e7eb';
            }, 2000);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('weatherEditModal'));
            modal.hide();

            // Show success message
            Swal.fire({
                title: 'Condições Atualizadas!',
                text: 'As condições meteorológicas foram atualizadas com sucesso.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a',
                timer: 2000,
                timerProgressBar: true
            });
        }

        // Observers editing functions
        function editObservers() {
            // Set current value
            document.getElementById('editNumberOfObservers').value = currentObserversCount || '';

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('observersEditModal'));
            modal.show();
        }

        function saveObservers() {
            const observersCount = parseInt(document.getElementById('editNumberOfObservers').value);
            
            if (isNaN(observersCount) || observersCount < 1) {
                Swal.fire({
                    title: 'Valor Inválido',
                    text: 'Por favor indique um número válido de observadores (mínimo 1).',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#f59e0b'
                });
                return;
            }

            // Update global variable
            currentObserversCount = observersCount;

            // Update display
            document.getElementById('observersCountDisplay').textContent = observersCount;

            // Add visual feedback
            const observersCard = document.querySelector('.editable-card[onclick="editObservers()"]');
            observersCard.style.background = 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
            observersCard.style.borderColor = '#16a34a';
            setTimeout(() => {
                observersCard.style.background = 'white';
                observersCard.style.borderColor = '#e5e7eb';
            }, 2000);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('observersEditModal'));
            modal.hide();

            // Show success message
            Swal.fire({
                title: 'Observadores Atualizados!',
                text: `Número de observadores definido para ${observersCount}.`,
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a',
                timer: 2000,
                timerProgressBar: true
            });
        }

        // Handle weather "other" option
        document.addEventListener('DOMContentLoaded', function() {
            const weatherOtherCheckbox = document.getElementById('edit_weather_other');
            const weatherOtherInput = document.getElementById('editWeatherOtherInput');
            
            if (weatherOtherCheckbox) {
                weatherOtherCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        weatherOtherInput.style.display = 'block';
                        document.getElementById('editWeatherOtherText').focus();
                    } else {
                        weatherOtherInput.style.display = 'none';
                        document.getElementById('editWeatherOtherText').value = '';
                    }
                });
            }
        });

        // Global variables to store current values
        let currentStartTime = '';
        let currentMiddleTime = '';
        let currentEndTime = '';
        let currentTrajetoDate = '';
        let currentWeatherCondition = '';
        let currentObserversCount = 0;

        // Handle contact modal close for new contacts
        document.addEventListener('DOMContentLoaded', function() {
            const contactModal = document.getElementById('contactEditModal');
            if (contactModal) {
                contactModal.addEventListener('hidden.bs.modal', function() {
                    const modal = bootstrap.Modal.getInstance(contactModal);
                    if (modal && modal._isNewContact) {
                        // Check if the contact was saved (has required fields filled)
                        const contactIndex = modal._contactIndex;
                        const contact = trajetoData.contacts[contactIndex];
                        
                        if (contact && (!contact.circumstance || !contact.location || !contact.time)) {
                            // Contact wasn't saved, remove it
                            trajetoData.contacts.splice(contactIndex, 1);
                            
                            // Remove the marker from map
                            if (contactMarkers[contactIndex]) {
                                contactMarkers[contactIndex].setMap(null);
                                contactMarkers.splice(contactIndex, 1);
                            }
                            
                            // Reload contacts on map to update indices
                            loadContactsOnMap(trajetoData.contacts);
                        }
                        
                        // Reset flags
                        modal._isNewContact = false;
                        modal._contactIndex = null;
                    }
                });
            }
        });
    </script>

    <!-- Timeline Edit Modal -->
    <div class="modal fade" id="timelineEditModal" tabindex="-1" aria-labelledby="timelineEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white;">
                    <h5 class="modal-title" id="timelineEditModalLabel">
                        <i class="fas fa-clock me-2"></i>Editar Timeline
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Date Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="editTrajetoDate" class="form-label">
                                    <i class="fas fa-calendar text-primary me-1"></i>
                                    Data do Trajeto
                                </label>
                                <div class="date-input-container" style="position: relative;">
                                    <input type="text" class="form-control" id="editTrajetoDate" placeholder="Selecione a data..." readonly style="background-color: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem 3rem 0.75rem 0.75rem; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;" required>
                                    <button type="button" class="btn timeline-date-picker-btn" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #0a7ea4; padding: 0.25rem 0.5rem; border-radius: 4px; transition: all 0.3s ease;">
                                        <i class="fas fa-calendar" style="font-size: 1.1rem;"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Time Section -->
                    <div class="row">
                        <div class="col-4">
                            <div class="mb-3">
                                <label for="editStartTime" class="form-label">
                                    <i class="fas fa-play text-success me-1"></i>
                                    Início
                                </label>
                                <div class="time-input-container" style="position: relative;">
                                    <input type="text" class="form-control" id="editStartTime" placeholder="Selecione a hora..." readonly style="background-color: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem 3rem 0.75rem 0.75rem; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;" required>
                                    <button type="button" class="btn timeline-time-picker-btn" data-target="editStartTime" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #0a7ea4; padding: 0.25rem 0.5rem; border-radius: 4px; transition: all 0.3s ease;">
                                        <i class="fas fa-clock" style="font-size: 1.1rem;"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="mb-3">
                                <label for="editMiddleTime" class="form-label">
                                    <i class="fas fa-pause text-warning me-1"></i>
                                    Meio
                                </label>
                                <div class="time-input-container" style="position: relative;">
                                    <input type="text" class="form-control" id="editMiddleTime" placeholder="Selecione a hora..." readonly style="background-color: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem 3rem 0.75rem 0.75rem; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;">
                                    <button type="button" class="btn timeline-time-picker-btn" data-target="editMiddleTime" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #0a7ea4; padding: 0.25rem 0.5rem; border-radius: 4px; transition: all 0.3s ease;">
                                        <i class="fas fa-clock" style="font-size: 1.1rem;"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="mb-3">
                                <label for="editEndTime" class="form-label">
                                    <i class="fas fa-stop text-danger me-1"></i>
                                    Fim
                                </label>
                                <div class="time-input-container" style="position: relative;">
                                    <input type="text" class="form-control" id="editEndTime" placeholder="Selecione a hora..." readonly style="background-color: white; border: 2px solid #e5e7eb; border-radius: 8px; padding: 0.75rem 3rem 0.75rem 0.75rem; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;">
                                    <button type="button" class="btn timeline-time-picker-btn" data-target="editEndTime" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #0a7ea4; padding: 0.25rem 0.5rem; border-radius: 4px; transition: all 0.3s ease;">
                                        <i class="fas fa-clock" style="font-size: 1.1rem;"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveTimeline()">
                        <i class="fas fa-save me-2"></i>Guardar Timeline
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Date Picker Modal for Timeline -->
    <div class="modal fade" id="timelineDatePickerModal" tabindex="-1" aria-labelledby="timelineDatePickerModalLabel" aria-hidden="true" data-bs-backdrop="static" style="z-index: 1080;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white; border-radius: 16px 16px 0 0;">
                    <h5 class="modal-title" id="timelineDatePickerModalLabel">
                        <i class="fas fa-calendar me-2"></i>Selecionar Data
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-4">
                            <label class="form-label text-center d-block">Dia</label>
                            <select class="form-select" id="timelineDaySelect" size="8">
                                <!-- Days will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="col-4">
                            <label class="form-label text-center d-block">Mês</label>
                            <select class="form-select" id="timelineMonthSelect" size="8">
                                <option value="01">Janeiro</option>
                                <option value="02">Fevereiro</option>
                                <option value="03">Março</option>
                                <option value="04">Abril</option>
                                <option value="05">Maio</option>
                                <option value="06">Junho</option>
                                <option value="07">Julho</option>
                                <option value="08">Agosto</option>
                                <option value="09">Setembro</option>
                                <option value="10">Outubro</option>
                                <option value="11">Novembro</option>
                                <option value="12">Dezembro</option>
                            </select>
                        </div>
                        <div class="col-4">
                            <label class="form-label text-center d-block">Ano</label>
                            <select class="form-select" id="timelineYearSelect" size="8">
                                <!-- Years will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-calendar me-2"></i>
                            <span id="timelineSelectedDateDisplay">--/--/----</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="timelineConfirmDateBtn" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Edit Modal -->
    <div class="modal fade" id="weatherEditModal" tabindex="-1" aria-labelledby="weatherEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white;">
                    <h5 class="modal-title" id="weatherEditModalLabel">
                        <i class="fas fa-cloud-sun me-2"></i>Editar Condições Meteorológicas
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="weather-options">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="edit_weather_clear" name="edit_weather_conditions" value="clear">
                            <label class="form-check-label" for="edit_weather_clear">
                                <i class="fas fa-sun text-warning me-2"></i>
                                Céu limpo
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="edit_weather_cloudy" name="edit_weather_conditions" value="cloudy">
                            <label class="form-check-label" for="edit_weather_cloudy">
                                <i class="fas fa-cloud text-secondary me-2"></i>
                                Nublado
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="edit_weather_rain" name="edit_weather_conditions" value="rain">
                            <label class="form-check-label" for="edit_weather_rain">
                                <i class="fas fa-cloud-rain text-primary me-2"></i>
                                Chuva
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="edit_weather_wind" name="edit_weather_conditions" value="wind">
                            <label class="form-check-label" for="edit_weather_wind">
                                <i class="fas fa-wind text-info me-2"></i>
                                Vento moderado a forte
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="edit_weather_other" name="edit_weather_conditions" value="other">
                            <label class="form-check-label" for="edit_weather_other">
                                <i class="fas fa-question-circle text-muted me-2"></i>
                                Outro. Qual?
                            </label>
                        </div>
                        <div class="mt-2" id="editWeatherOtherInput" style="display: none;">
                            <input type="text" class="form-control" id="editWeatherOtherText" placeholder="Especifique as condições meteorológicas...">
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveWeather()">
                        <i class="fas fa-save me-2"></i>Guardar Condições
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="observersEditModal" tabindex="-1" aria-labelledby="observersEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 400px;">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white;">
                    <h5 class="modal-title" id="observersEditModalLabel">
                        <i class="fas fa-users me-2"></i>Editar Observadores
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editNumberOfObservers" class="form-label">
                            <i class="fas fa-users me-1"></i>
                            Número de Observadores
                        </label>
                        <input type="number" class="form-control" id="editNumberOfObservers" min="1" required>
                        <div class="form-text">Mínimo: 1 observador</div>
                    </div>
                </div>
                <div class="modal-footer" style="justify-content: space-between;">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveObservers()">
                        <i class="fas fa-save me-2"></i>Guardar Observadores
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize the necessary JavaScript functions that were missing
        
        function setupTimePickerListeners() {
            // Setting up time picker listeners
            
            // Timeline time picker buttons
            document.querySelectorAll('.timeline-time-picker-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    openTimePicker(targetId);
                });
            });
            
            // Timeline time picker input fields - make them clickable too
            document.querySelectorAll('#editStartTime, #editMiddleTime, #editEndTime').forEach(input => {
                input.addEventListener('click', function() {
                    const targetId = this.getAttribute('id');
                    openTimePicker(targetId);
                });
            });
            
            // Timeline date picker button
            document.querySelector('.timeline-date-picker-btn')?.addEventListener('click', function() {
                openDatePicker();
            });
            
            // Contact time picker functionality
            setupContactTimePickerListeners();
        }
        
        function setupContactTimePickerListeners() {
            // Set up contact time picker modal functionality
            const timePickerModal = document.getElementById('timePickerModal');
            if (timePickerModal) {
                // Handle confirm button click using the correct ID
                const confirmBtn = document.getElementById('confirmTimeBtn');
                if (confirmBtn) {
                    // Remove any existing listeners
                    confirmBtn.removeEventListener('click', confirmTimeSelection);
                    // Add new listener
                    confirmBtn.addEventListener('click', confirmTimeSelection);
                    // Time picker listener added
                }
                
                // Also set up hour/minute selection listeners for live preview
                const hourSelect = document.getElementById('hourSelect');
                const minuteSelect = document.getElementById('minuteSelect');
                const timeDisplay = document.getElementById('selectedTimeDisplay');
                
                if (hourSelect && minuteSelect && timeDisplay) {
                    const updateTimeDisplay = () => {
                        const hour = hourSelect.value || '00';
                        const minute = minuteSelect.value || '00';
                        timeDisplay.textContent = `${hour}:${minute}`;
                    };
                    
                    hourSelect.addEventListener('change', updateTimeDisplay);
                    minuteSelect.addEventListener('change', updateTimeDisplay);
                    
                    // Live preview set up
                }
            }
        }
        
        function confirmTimeSelection() {
            // Get selected hour and minute using correct IDs
            const hourSelect = document.getElementById('hourSelect');
            const minuteSelect = document.getElementById('minuteSelect');
            
            if (hourSelect && minuteSelect) {
                const hour = hourSelect.value || '00';
                const minute = minuteSelect.value || '00';
                const timeValue = `${hour}:${minute}`;
                
                // Set the time in the contact modal
                const contactTimeInput = document.getElementById('contactTime');
                if (contactTimeInput) {
                    contactTimeInput.value = timeValue;
                    // Contact time set
                }
                
                // Close the time picker modal
                const timePickerModal = document.getElementById('timePickerModal');
                const modal = bootstrap.Modal.getInstance(timePickerModal);
                if (modal) {
                    modal.hide();
                    // Modal closed
                }
            }
        }
        
        function openTimePicker(targetId) {
            // Store the target input ID for later use
            window.currentTimelineTarget = targetId;
            
            // Get current value to pre-populate
            const currentValue = document.getElementById(targetId).value;
            let hour = '09', minute = '00';
            
            if (currentValue && currentValue.includes(':')) {
                const parts = currentValue.split(':');
                hour = parts[0] || '09';
                minute = parts[1] || '00';
            }
            
            // Set the selectors to current values
            document.getElementById('timelineHourSelect').value = hour;
            document.getElementById('timelineMinuteSelect').value = minute;
            
            // Update display
            updateTimelineTimeDisplay();
            
            // Show the timeline time picker modal
            const modal = new bootstrap.Modal(document.getElementById('timelineTimePickerModal'));
            modal.show();
        }
        
        function updateTimelineTimeDisplay() {
            const hour = document.getElementById('timelineHourSelect').value || '09';
            const minute = document.getElementById('timelineMinuteSelect').value || '00';
            document.getElementById('timelineSelectedTimeDisplay').textContent = `${hour}:${minute}`;
        }
        
        function confirmTimelineTimeSelection() {
            const hour = document.getElementById('timelineHourSelect').value || '09';
            const minute = document.getElementById('timelineMinuteSelect').value || '00';
            const timeValue = `${hour}:${minute}`;
            
            // Set the time in the target input
            if (window.currentTimelineTarget) {
                document.getElementById(window.currentTimelineTarget).value = timeValue;
            }
            
            // Close the modal and clean up backdrop
            const timePickerModal = document.getElementById('timelineTimePickerModal');
            const modal = bootstrap.Modal.getInstance(timePickerModal);
            if (modal) {
                modal.hide();
                
                // Ensure backdrop is removed after modal closes
                timePickerModal.addEventListener('hidden.bs.modal', function() {
                    // Remove any lingering backdrops
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => {
                        if (backdrop.style.zIndex === '1079' || backdrop.style.zIndex === '1080') {
                            backdrop.remove();
                        }
                    });
                }, { once: true });
            }
        }

        function openDatePicker() {
            // Simple date picker implementation
            const date = prompt('Digite a data (DD/MM/YYYY):');
            if (date && /^\d{2}\/\d{2}\/\d{4}$/.test(date)) {
                document.getElementById('editTrajetoDate').value = date;
            }
        }
        
        function saveTimeline() {
            const startTime = document.getElementById('editStartTime').value;
            const middleTime = document.getElementById('editMiddleTime').value;
            const endTime = document.getElementById('editEndTime').value;
            const date = document.getElementById('editTrajetoDate').value;
            
            if (startTime) currentStartTime = startTime;
            if (middleTime) currentMiddleTime = middleTime;
            if (endTime) currentEndTime = endTime;
            if (date) currentTrajetoDate = date;
            
            // Update display
            document.getElementById('startTimeCompact').textContent = currentStartTime || '--:--';
            document.getElementById('middleTimeCompact').textContent = currentMiddleTime || '--:--';
            document.getElementById('endTimeCompact').textContent = currentEndTime || '--:--';
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('timelineEditModal'));
            modal.hide();
        }
        
        function saveWeather() {
            const selectedWeather = document.querySelector('input[name="edit_weather_conditions"]:checked');
            if (selectedWeather) {
                let weatherText = '';
                let weatherIcon = '';
                
                switch(selectedWeather.value) {
                    case 'clear': 
                        weatherText = 'Céu limpo'; 
                        weatherIcon = '<i class="fas fa-sun text-warning"></i>';
                        break;
                    case 'cloudy': 
                        weatherText = 'Nublado'; 
                        weatherIcon = '<i class="fas fa-cloud text-secondary"></i>';
                        break;
                    case 'rain': 
                        weatherText = 'Chuva'; 
                        weatherIcon = '<i class="fas fa-cloud-rain text-primary"></i>';
                        break;
                    case 'wind': 
                        weatherText = 'Vento moderado a forte'; 
                        weatherIcon = '<i class="fas fa-wind text-info"></i>';
                        break;
                    case 'other': 
                        const otherText = document.getElementById('editWeatherOtherText').value;
                        weatherText = otherText ? `Outro: ${otherText}` : 'Outro';
                        weatherIcon = '<i class="fas fa-question-circle text-muted"></i>';
                        break;
                }
                currentWeatherCondition = weatherText;
                
                // Update display - FIXED: Now updates both text AND icon
                document.getElementById('weatherTextCompact').textContent = weatherText;
                document.getElementById('weatherIconCompact').innerHTML = weatherIcon;
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('weatherEditModal'));
                modal.hide();
            }
        }
        
        function saveObservers() {
            const observers = document.getElementById('editNumberOfObservers').value;
            if (observers && observers > 0) {
                currentObserversCount = parseInt(observers);
                
                // Update display
                document.getElementById('observersCountDisplay').textContent = currentObserversCount;
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('observersEditModal'));
                modal.hide();
            }
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setupTimePickerListeners();
        });
        
        // Make sure initMap function exists for Google Maps callback
        function initMap() {
            // Google Maps callback: initMap called
            if (typeof initializeMap === 'function') {
                initializeMap();
            } else {
                console.error('initializeMap function not found');
            }
        }
        
        // Global function declarations to prevent reference errors
        window.initMap = initMap;
        window.setupTimePickerListeners = setupTimePickerListeners;
        window.saveTimeline = saveTimeline;
        window.saveWeather = saveWeather;
        window.saveObservers = saveObservers;
    </script>

    <!-- Timeline Time Picker Modal -->
    <div class="modal fade" id="timelineTimePickerModal" tabindex="-1" aria-labelledby="timelineTimePickerModalLabel" aria-hidden="true" style="z-index: 1080;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%); color: white; border-radius: 16px 16px 0 0;">
                    <h5 class="modal-title" id="timelineTimePickerModalLabel">
                        <i class="fas fa-clock me-2"></i>Selecionar Hora
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label text-center d-block">Hora</label>
                            <select class="form-select" id="timelineHourSelect" size="8" onchange="updateTimelineTimeDisplay()">
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09" selected>09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label text-center d-block">Minuto</label>
                            <select class="form-select" id="timelineMinuteSelect" size="8" onchange="updateTimelineTimeDisplay()">
                                <option value="00" selected>00</option>
                                <option value="05">05</option>
                                <option value="10">10</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                                <option value="25">25</option>
                                <option value="30">30</option>
                                <option value="35">35</option>
                                <option value="40">40</option>
                                <option value="45">45</option>
                                <option value="50">50</option>
                                <option value="55">55</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>
                            <span id="timelineSelectedTimeDisplay">09:00</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: white; border-top: 1px solid #e5e7eb; border-radius: 0 0 16px 16px; justify-content: center; gap: 1rem;">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border: 2px solid #e5e7eb; color: #6b7280; padding: 0.5rem 1rem; border-radius: 8px;" onclick="cleanupTimePickerModal()">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="timelineConfirmTimeBtn" onclick="confirmTimelineTimeSelection()" style="background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%); border: 2px solid #0a7ea4; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add cleanup function for time picker modal
        function cleanupTimePickerModal() {
            const timePickerModal = document.getElementById('timelineTimePickerModal');
            const modal = bootstrap.Modal.getInstance(timePickerModal);
            if (modal) {
                modal.hide();
            }
            
            // Immediate cleanup
            cleanupModalBackdrops();
            
            // Additional cleanup after modal animation
            setTimeout(cleanupModalBackdrops, 200);
        }
        
        function cleanupModalBackdrops() {
            // Remove all modal backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            
            // Check if there are still open modals
            const openModals = document.querySelectorAll('.modal.show');
            
            // If timeline modal is still open, keep body modal-open class
            const timelineModal = document.getElementById('timelineEditModal');
            const isTimelineOpen = timelineModal && timelineModal.classList.contains('show');
            
            if (!isTimelineOpen && openModals.length === 0) {
                // No modals open, clean up body completely
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            } else if (isTimelineOpen) {
                // Timeline modal is still open, ensure proper backdrop
                const existingBackdrop = document.querySelector('.modal-backdrop');
                if (!existingBackdrop) {
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.style.zIndex = '1040';
                    document.body.appendChild(backdrop);
                }
            }
        }
        
        // Also add cleanup when the modal is hidden via other means
        document.getElementById('timelineTimePickerModal').addEventListener('hidden.bs.modal', function() {
            cleanupModalBackdrops();
        });
        
        // Add cleanup when timeline modal is closed to ensure no stuck backdrops
        document.getElementById('timelineEditModal')?.addEventListener('hidden.bs.modal', function() {
            setTimeout(() => {
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }, 100);
        });
    </script>

</body>
</html>
