<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!$input || !isset($input['trajetoId']) || !isset($input['trajetoData'])) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

$trajetoId = $input['trajetoId'];
$trajetoData = $input['trajetoData'];

// Get user information
$user_id = $_SESSION['user']['id'] ?? '';

if (empty($user_id)) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User ID not found']);
    exit();
}

try {
    // Use the global database instance
    global $database;
    
    // Get admin token for updating
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // Set admin token
    $database->setAccessToken($adminToken);
    
    // Add update metadata
    $trajetoData['updatedAt'] = date('c'); // ISO 8601 format
    $trajetoData['updatedBy'] = $user_id;
    
    // Update trajeto document in 'zonas' collection (same as save_trajeto.php)
    $result = $database->updateDocument('zonas', $trajetoId, $trajetoData);
    
    if ($result) {
        // Clean output buffer and return success response
        ob_end_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Trajeto updated successfully'
        ]);
    } else {
        throw new Exception('Failed to update trajeto');
    }
    
} catch (Exception $e) {
    // Clean output buffer and return error response
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
    // Log the error
    error_log("Error updating trajeto: " . $e->getMessage());
}
?> 