<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Get user information
$user_email = $_SESSION['user']['email'] ?? '';
$user_nif = $_SESSION['user']['nif'] ?? '';
$user_id = $_SESSION['user']['id'] ?? '';

// Use the global database instance (already initialized in config.php)
global $database;

// Set the user's auth token for authenticated requests
if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Get gestor zones and trajetos data
$gestorZones = [];
$gestorTrajetos = [];
$gestorMobileTrajetos = [];
$error_message = '';

try {
    // Get admin token once and reuse it for all operations - OPTIMIZED
    $adminToken = $database->getAdminAccessToken();
    if ($adminToken) {
        $database->setAccessToken($adminToken);
    }
    
    // Get zones managed by this gestor using NIF
    $gestorZones = getGestorZones($database, $user_nif);
    
    // Get trajetos created by this gestor
    $gestorTrajetos = getGestorTrajetos($database, $user_id);
    
    // Get GPS trajetos created by this gestor
    $gestorMobileTrajetos = getGestorMobileTrajetos($database, $user_id);
    
} catch (Exception $e) {
    error_log("Trajetos page data error: " . $e->getMessage());
    $error_message = "Erro ao carregar dados dos trajetos.";
}

/**
 * Get count of used seals for ALL zones at once - OPTIMIZED VERSION
 */
function getAllUsedSealsCounts($database, $zoneIds) {
    $sealCounts = [];
    
    // Initialize all zones with 0 seals
    foreach ($zoneIds as $zoneId) {
        $sealCounts[$zoneId] = 0;
    }
    
    try {
        // Get admin token (will be cached from previous call)
        $adminToken = $database->getAdminAccessToken();
        
        // Fetch ALL jornadas at once with pagination
        $allJornadas = [];
        $nextPageToken = null;
        
        do {
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/jornadasCaca";
            if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
            }
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status === 200) {
                $pageResult = json_decode($response, true);
                
                if (isset($pageResult['documents'])) {
                    $allJornadas = array_merge($allJornadas, $pageResult['documents']);
                }
                
                $nextPageToken = $pageResult['nextPageToken'] ?? null;
            } else {
                break; // Stop on error
            }
            
        } while ($nextPageToken);
        
        // Process all jornadas and count seals by zone
        foreach ($allJornadas as $doc) {
            $jornada = parseFirestoreDocument($doc);
            $zonaId = $jornada['zonaId'] ?? '';
            
            // Only process if this zone belongs to our user
            if (in_array($zonaId, $zoneIds)) {
                $selosAtribuidos = $jornada['selosAtribuidos'] ?? '';
                
                if (!empty($selosAtribuidos)) {
                    if (is_string($selosAtribuidos)) {
                        // Try to decode JSON
                        $selosJson = json_decode($selosAtribuidos, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($selosJson)) {
                            $sealCounts[$zonaId] += count($selosJson);
                        } else {
                            // If not JSON, count comma-separated values
                            $selosArray = array_filter(explode(',', $selosAtribuidos));
                            $sealCounts[$zonaId] += count($selosArray);
                        }
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Error getting all used seals counts: " . $e->getMessage());
    }
    
    return $sealCounts;
}

/**
 * Get hunting zones for the current gestor - OPTIMIZED VERSION
 */
function getGestorZones($database, $userNif) {
    try {
        if (empty($userNif)) {
            return [];
        }
        
        // Use Firestore query to filter by NIF directly
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        $allZones = [];
        
        if ($result && is_array($result)) {
            // First, collect all zone IDs
            $zoneIds = array_keys($result);
            
            // Get used seals count for ALL zones at once (single batch query)
            $usedSealsCounts = getAllUsedSealsCounts($database, $zoneIds);
            
            // Now build the zones array with the pre-calculated seal counts
            foreach ($result as $docId => $data) {
                $usedSealsCount = $usedSealsCounts[$docId] ?? 0;
                $totalQuota = $data['quotaZona'] ?? 0;
                $availableQuota = max(0, $totalQuota - $usedSealsCount);
                
                $allZones[] = [
                    'id' => $docId,
                    'zona' => $data['zona'] ?? 'N/A',
                    'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                    'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                    'email' => $data['email'] ?? 'N/A',
                    'quotaZona' => $totalQuota,
                    'availableQuota' => $availableQuota,
                    'usedSeals' => $usedSealsCount,
                    'minSelo' => $data['minSelo'] ?? 0,
                    'maxSelo' => $data['maxSelo'] ?? 0,
                    'localidade' => $data['localidade'] ?? '',
                    'status' => $data['status'] ?? 'not registered',
                    'registeredBy' => $data['registeredBy'] ?? null
                ];
            }
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getGestorZones: " . $e->getMessage());
        return [];
    }
}

/**
 * Get trajetos created by the current gestor
 */
function getGestorTrajetos($database, $userId) {
    try {
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId
        $result = $database->queryDocuments('zonas', 'createdBy', 'EQUAL', $userId);
        
        $allTrajetos = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allTrajetos[] = [
                    'id' => $docId,
                    'name' => $data['name'] ?? 'Trajeto GPS',
                    'description' => $data['description'] ?? '',
                    'zoneId' => $data['zoneId'] ?? '',
                    'status' => $data['status'] ?? 'draft',
                    'difficulty' => $data['difficulty'] ?? 'medio',
                    'distance' => $data['distance'] ?? '0 km',
                    'createdBy' => $data['createdBy'] ?? '',
                    'createdAt' => $data['createdAt'] ?? null,
                    'updatedAt' => $data['updatedAt'] ?? null,
                    'source' => 'manual' // Mark as manual trajectory
                ];
            }
        }
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getGestorTrajetos: " . $e->getMessage());
        return [];
    }
}

/**
 * Get GPS trajetos created by the current gestor
 */
function getGestorMobileTrajetos($database, $userId) {
    try {
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId (GPS trajectories use 'userId' field)
        $result = $database->queryDocuments('gestorMobile_trajetos', 'userId', 'EQUAL', $userId);
        
        $allTrajetos = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                // Calculate distance from totalDistance (stored in meters)
                $totalDistance = $data['totalDistance'] ?? 0;
                $distanceKm = $totalDistance > 0 ? number_format($totalDistance / 1000, 2) . ' km' : '0 km';
                
                $allTrajetos[] = [
                    'id' => $docId,
                    'name' => $data['name'] ?? 'Trajeto GPS',
                    'description' => $data['description'] ?? '',
                    'zoneId' => $data['zoneId'] ?? '',
                    'status' => $data['status'] ?? 'active',
                    'difficulty' => $data['difficulty'] ?? 'medio',
                    'distance' => $distanceKm,
                    'createdBy' => $data['createdBy'] ?? '',
                    'createdAt' => $data['createdAt'] ?? null,
                    'updatedAt' => $data['updatedAt'] ?? null,
                    'source' => 'gps' // Mark as GPS trajectory
                ];
            }
        }
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getGestorMobileTrajetos: " . $e->getMessage());
        return [];
    }
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zonas de Caça - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- WebAdmin CSS (for sidebar styling) -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        /* World-Class Premium Design */
        .zones-list {
            margin-top: 2rem;
        }
        
        /* Disabled button styling */
        .btn-premium:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .btn-outline-secondary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .zone-item {
            background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
            border: none;
            border-radius: 16px;
            margin-bottom: 1.5rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 1px 3px rgba(0, 0, 0, 0.05),
                0 4px 8px rgba(0, 0, 0, 0.02),
                0 8px 16px rgba(0, 0, 0, 0.02);
        }

        /* Quota Badge Styling - Exact Match from Dashboard */
        .quota-box {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 0.8rem 0.8rem;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
            min-width: 120px;
        }

        .quota-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .quota-header i {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .quota-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.9;
            margin-left: 0.25rem;
        }

        .quota-number {
            font-size: 1.5rem;
            font-weight: 800;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .quota-progress-container {
            position: relative;
            margin-bottom: 0.4rem;
        }

        .quota-progress-bar {
            width: 100%;
            height: 16px;
            background-color: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            display: flex;
        }

        .quota-progress-available {
            height: 100%;
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            transition: width 0.3s ease;
            position: relative;
        }

        .quota-progress-used {
            height: 100%;
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
            transition: width 0.3s ease;
            position: relative;
        }

        .quota-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
            border-radius: 8px;
            transition: width 0.3s ease;
            position: relative;
        }

        .quota-percentage-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.65rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            letter-spacing: 0.3px;
            z-index: 2;
        }

        .quota-details {
            font-size: 0.7rem;
            font-weight: 500;
            opacity: 0.8;
            line-height: 1.2;
            text-align: center;
        }

        .quota-unavailable {
            background: #f1f5f9;
            color: #64748b;
            padding: 1.25rem 1.5rem;
            border-radius: 16px;
            text-align: center;
            border: 2px solid #e2e8f0;
            min-width: 120px;
        }

        .quota-progress-disabled {
            background-color: rgba(100, 116, 139, 0.2) !important;
        }

        .quota-unavailable .quota-progress-fill {
            background: rgba(100, 116, 139, 0.3) !important;
        }

        .quota-unavailable .quota-percentage-text {
            color: #64748b !important;
        }

        .quota-unavailable .quota-details {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .quota-unavailable .quota-header {
            justify-content: center;
        }
        
        .zone-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0a7ea4 0%, #0891b2 50%, #06b6d4 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .zone-item:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow: 
                0 8px 25px rgba(10, 126, 164, 0.15),
                0 16px 40px rgba(0, 0, 0, 0.08),
                0 32px 64px rgba(0, 0, 0, 0.05);
        }
        
        .zone-item:hover::before {
            opacity: 1;
        }
        
        .zone-item-content {
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
        }
        
        .zone-left {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .zone-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
            position: relative;
        }
        
        .zone-icon::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            pointer-events: none;
        }
        
        .zone-details {
            flex: 1;
        }
        
        .zone-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .zone-number {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #0a7ea4;
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid #e2e8f0;
        }
        
        .zone-name {
            color: #1e293b;
            font-size: 1.375rem;
            font-weight: 700;
            margin: 0;
            letter-spacing: -0.025em;
        }
        
        .zone-status-section {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .zone-meta-inline {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }
        
        .zone-meta-inline .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            background: #f8fafc;
            color: #374151;
            border: 1px solid #e2e8f0;
        }
        
        .zone-meta-inline .meta-item i {
            color: #0a7ea4;
            font-size: 0.875rem;
        }
        
        .zone-meta-inline .meta-item span {
            font-weight: 500;
            color: #374151;
        }
        
        .meta-item-address-full {
            white-space: normal !important;
            min-height: auto !important;
            height: auto !important;
            padding: 0.75rem 1rem !important;
        }
        
        .meta-item-address-full span {
            white-space: normal !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            line-height: 1.4 !important;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
        }
        
        .status-indicator.status-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .status-indicator.status-complete {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status-indicator i {
            font-size: 0.875rem;
        }
        
        .status-info-label {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 400;
            background: #f9fafb;
            color: #6b7280;
            border: 1px solid #e5e7eb;
            margin-left: 0.75rem;
        }
        
        .status-info-label i {
            font-size: 0.7rem;
            color: #9ca3af;
        }
        
        .zone-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            min-width: 160px;
        }

        .btn-premium {
            padding: 0.875rem 1.5rem;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-premium::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .btn-premium:hover::before {
            opacity: 1;
        }
        
        .btn-premium.btn-primary {
            background: linear-gradient(135deg, #075985 0%, #0369a1 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(7, 89, 133, 0.3);
        }
        
        .btn-premium.btn-primary:hover {
            background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(7, 89, 133, 0.4);
        }
        
        .btn-premium.btn-success {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.3);
        }
        
        .btn-premium.btn-success:hover {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(5, 150, 105, 0.4);
        }
        
        .btn-premium.btn-warning {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
        }
        
        .btn-premium.btn-warning:hover {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(10, 126, 164, 0.4);
        }
        
        .btn-premium i {
            font-size: 0.875rem;
        }
        
        /* Premium Empty State */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 24px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }
        
        .empty-state-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: #64748b;
        }
        
        .empty-state-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .empty-state-text {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 1.5rem;
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
        }
        
        .zone-left {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .zone-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
            position: relative;
        }
        
        .zone-icon::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            pointer-events: none;
        }
        
        .zone-details {
            flex: 1;
        }
        
        .zone-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .zone-number {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #0a7ea4;
            padding: 0rem 0.3rem;
            border-radius: 20px;
            font-size: 0.6rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid #e2e8f0;
        }
        
        .zone-name {
            color: #1e293b;
            font-size: 1.375rem;
            font-weight: 700;
            margin: 0;
            letter-spacing: -0.025em;
        }
        
        .zone-status-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
        }
        
        .status-indicator.status-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .status-indicator.status-complete {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status-indicator i {
            font-size: 0.875rem;
        }
        
        .trajeto-message {
            margin-top: 0.5rem;
            padding: 1rem;
            border-radius: 12px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .trajeto-message.warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 30%);
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        
        .trajeto-message.success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 30%);
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .trajeto-message i {
            font-size: 1.1rem;
        }
        
        .zone-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            min-width: 160px;
        }
        
        .btn-premium {
            padding: 0.875rem 1.5rem;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-premium::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .btn-premium:hover::before {
            opacity: 1;
        }
        
        .btn-premium.btn-primary {
            background: linear-gradient(135deg, #075985 0%, #0369a1 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(7, 89, 133, 0.3);
        }
        
        .btn-premium.btn-primary:hover {
            background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(7, 89, 133, 0.4);
        }
        
        .btn-premium.btn-success {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.3);
        }
        
        .btn-premium.btn-success:hover {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(5, 150, 105, 0.4);
        }
        
        .btn-premium.btn-warning {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
        }
        
        .btn-premium.btn-warning:hover {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(10, 126, 164, 0.4);
        }
        
        .btn-premium i {
            font-size: 0.875rem;
        }
        
        /* Premium Empty State */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 24px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }
        
        .empty-state-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: #64748b;
        }
        
        .empty-state-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .empty-state-text {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        /* Dark mode enhancements */
        .dark-mode .zone-item {
            background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
            box-shadow: 
                0 1px 3px rgba(0, 0, 0, 0.3),
                0 4px 8px rgba(0, 0, 0, 0.2),
                0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .dark-mode .zone-item:hover {
            box-shadow: 
                0 8px 25px rgba(10, 126, 164, 0.3),
                0 16px 40px rgba(0, 0, 0, 0.2),
                0 32px 64px rgba(0, 0, 0, 0.15);
        }
        
        .dark-mode .zone-number {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            color: #60a5fa;
            border-color: #4b5563;
        }
        
        .dark-mode .zone-name {
            color: #f8fafc;
        }
        
        .dark-mode .trajeto-message.warning {
            background: linear-gradient(135deg, #451a03 0%, #78350f 30%);
            border-color: #d97706;
            color: #fbbf24;
        }
        
        .dark-mode .trajeto-message.success {
            background: linear-gradient(135deg, #064e3b 0%, #065f46 30%);
            border-color: #059669;
            color: #34d399;
        }
        
        .dark-mode .empty-state {
            background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
            border-color: #374151;
        }
        
        .dark-mode .empty-state-title {
            color: #f8fafc;
        }
        
        .dark-mode .empty-state-text {
            color: #9ca3af;
        }
        
        /* Ultra-smooth animations */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .zone-item {
            animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
        
        .zone-item:nth-child(1) { animation-delay: 0.1s; }
        .zone-item:nth-child(2) { animation-delay: 0.2s; }
        .zone-item:nth-child(3) { animation-delay: 0.3s; }
        .zone-item:nth-child(4) { animation-delay: 0.4s; }
        
        /* Responsive excellence */
        @media (max-width: 768px) {
            .zone-item-content {
                flex-direction: column;
                align-items: stretch;
                gap: 1.5rem;
                padding: 1.5rem;
            }
            
            .zone-left {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 1rem;
            }
            
            .zone-icon {
                width: 56px;
                height: 56px;
                font-size: 1.25rem;
            }
            
            .zone-header {
                flex-direction: column;
                gap: 0.5rem;
                align-items: center;
            }
            
            .zone-name {
                font-size: 1.25rem;
            }
            
            .zone-actions {
                min-width: auto;
                width: 100%;
            }
            
            .btn-premium {
                width: 100%;
            }
        }
    </style>
</head>
<body class="<?php echo isset($_COOKIE['darkMode']) && $_COOKIE['darkMode'] === 'true' ? 'dark-mode' : ''; ?>">
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-route"></i>
                                        Zonas de Caça
            </h1>
        </div>
        <div class="header-actions">
            <button class="btn btn-help" onclick="showTrajetoHelp()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div>
            

                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #0a7ea4;">
                            <i class="fas fa-binoculars"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo count($gestorZones); ?></div>
                            <div class="stat-label">Total de Zonas</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #16a34a;">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo count($gestorTrajetos) + count($gestorMobileTrajetos); ?></div>
                            <div class="stat-label">Trajetos Criados</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #9333ea;">
                            <i class="fas fa-check-circle"></i>
                                </div>
                        <div class="stat-content">
                            <div class="stat-number">
                                <?php 
                                $operationalZones = 0;
                                foreach ($gestorZones as $zone) {
                                    $hasTrajetoAssociated = false;
                                    
                                    // Check manual trajectories
                                    foreach ($gestorTrajetos as $trajeto) {
                                        if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                                            $hasTrajetoAssociated = true;
                                            break;
                                        }
                                    }
                                    
                                    // Check mobile trajectories if no manual trajectory found
                                    if (!$hasTrajetoAssociated) {
                                        foreach ($gestorMobileTrajetos as $trajeto) {
                                            if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                                                $hasTrajetoAssociated = true;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    if ($hasTrajetoAssociated) {
                                        $operationalZones++;
                                    }
                                }
                                echo $operationalZones;
                                ?>
                                                </div>
                            <div class="stat-label">Zonas Operacionais</div>
                                                    </div>
                                                    </div>
                                                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #ea580c;">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">
                                <?php 
                                $needTrajeto = 0;
                                foreach ($gestorZones as $zone) {
                                    $hasTrajetoAssociated = false;
                                    
                                    // Check manual trajectories
                                    foreach ($gestorTrajetos as $trajeto) {
                                        if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                                            $hasTrajetoAssociated = true;
                                            break;
                                        }
                                    }
                                    
                                    // Check mobile trajectories if no manual trajectory found
                                    if (!$hasTrajetoAssociated) {
                                        foreach ($gestorMobileTrajetos as $trajeto) {
                                            if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                                                $hasTrajetoAssociated = true;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    if (!$hasTrajetoAssociated) {
                                        $needTrajeto++;
                                    }
                                }
                                echo $needTrajeto;
                                ?>
                            </div>
                            <div class="stat-label">Precisam Trajeto</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

            <!-- Zones List -->
            <div class="zones-list">
                <?php if (empty($gestorZones)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-binoculars"></i>
                        </div>
                        <div class="empty-state-title">Nenhuma zona encontrada</div>
                        <div class="empty-state-text">
                            Não foram encontradas zonas de caça associadas ao seu NIF.
                        </div>
                        <?php if (empty($user_nif)): ?>
                            <div class="empty-state-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Certifique-se de que o seu NIF está registado no sistema.
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <?php foreach ($gestorZones as $zone): ?>
                        <?php 
                        // Find trajeto for this zone (check both manual and GPS trajectories)
                        $trajeto = null;
                        
                        // First check manual trajectories
                        foreach ($gestorTrajetos as $t) {
                            if (isset($t['zoneId']) && $t['zoneId'] === $zone['id']) {
                                $trajeto = $t;
                                break;
                            }
                        }
                        
                        // If no manual trajectory found, check GPS trajectories
                        if (!$trajeto) {
                            foreach ($gestorMobileTrajetos as $t) {
                                if (isset($t['zoneId']) && $t['zoneId'] === $zone['id']) {
                                    $trajeto = $t;
                                    break;
                                }
                            }
                        }
                        ?>
                        <div class="zone-item">
                            <div class="zone-item-content">
                                <div class="zone-left">
                                    <!-- Quota Badge on the left -->
                                    <?php 
                                    // Calculate quota details
                                    $totalQuota = $zone['quotaZona'] ?? 0;
                                    $usedSeals = $zone['usedSeals'] ?? 0;
                                    $availableQuota = $totalQuota - $usedSeals;
                                    $percentageUsed = $totalQuota > 0 ? round(($usedSeals / $totalQuota) * 100) : 0;
                                    $percentageAvailable = 100 - $percentageUsed;
                                    ?>
                                    
                                    <?php if ($trajeto): ?>
                                        <div class="quota-box">
                                            <div class="quota-header">
                                                <i class="fas fa-dove"></i>
                                                <span class="quota-title">QUOTA</span>
                                                <div class="quota-number"><?php echo number_format($availableQuota); ?></div>
                                            </div>
                                            <div class="quota-progress-container">
                                                <div class="quota-progress-bar">
                                                    <div class="quota-progress-available" style="width: <?php echo $percentageAvailable; ?>%"></div>
                                                    <div class="quota-progress-used" style="width: <?php echo $percentageUsed; ?>%"></div>
                                                </div>
                                                <div class="quota-percentage-text"><?php echo $percentageAvailable; ?>%</div>
                                            </div>
                                            <div class="quota-details">
                                                <?php echo number_format($usedSeals); ?> de <?php echo number_format($totalQuota); ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="quota-unavailable">
                                            <div class="quota-header">
                                                <i class="fas fa-dove"></i>
                                                <span class="quota-title">QUOTA</span>
                                            </div>
                                            <div class="quota-progress-container">
                                                <div class="quota-progress-bar quota-progress-disabled">
                                                    <div class="quota-progress-fill" style="width: 0%"></div>
                                                </div>
                                                <div class="quota-percentage-text">0%</div>
                                            </div>
                                            <div class="quota-details">
                                                INDISPONÍVEL
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="zone-details">
                                        <div class="zone-header" style="display: flex; align-items: center; gap: 1rem;">
                                            <h3 class="zone-name"><?php echo htmlspecialchars($zone['nomeZona']); ?></h3>
                                            <div class="zone-number">Zona <?php echo htmlspecialchars($zone['zona']); ?></div>
                                        </div>
                                        <div class="zone-status-section">
                                            <div class="status-indicator <?php echo $trajeto ? 'status-complete' : 'status-pending'; ?>">
                                                <i class="<?php echo $trajeto ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'; ?>"></i>
                                                <span><?php echo $trajeto ? 'Trajeto Ativo' : 'Necessita Trajeto'; ?></span>
                                            </div>
                                            <?php if (!$trajeto): ?>
                                            <div class="status-info-label">
                                                <i class="fas fa-info-circle"></i>
                                                <span>Esta zona necessita de um trajeto para estar operacional</span>
                                            </div>
                                            <?php else: ?>
                                            <div class="zone-meta-inline">
                                                <div class="meta-item">
                                                    <i class="fas fa-route"></i>
                                                    <span>Distância: <?php echo $trajeto['distance'] ?: '—'; ?></span>
                                                </div>
                                                <div class="meta-item">
                                                    <i class="fas fa-calendar"></i>
                                                    <span><?php 
                                                        // Show updated date if it exists and is different from created date, otherwise show created date
                                                        $createdAt = $trajeto['createdAt'] ?? null;
                                                        $updatedAt = $trajeto['updatedAt'] ?? null;
                                                        
                                                        if ($updatedAt && $updatedAt !== $createdAt) {
                                                            $date = new DateTime($updatedAt);
                                                            echo 'Editado: ' . $date->format('d/m/Y');
                                                        } else if ($createdAt) {
                                                            $date = new DateTime($createdAt);
                                                            echo 'Criado: ' . $date->format('d/m/Y');
                                                        } else {
                                                            echo 'Data: —';
                                                        }
                                                    ?></span>
                                                </div>
                                            </div>
                                            <?php if (!empty($trajeto['startingAddress']) && $trajeto['startingAddress'] !== 'Não definida'): ?>
                                            <div class="zone-meta-inline" style="margin-top: 0.5rem;">
                                                <div class="meta-item meta-item-address-full" style="flex: 1;">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>Início: <?php echo htmlspecialchars($trajeto['startingAddress']); ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            <?php endif; ?>

                                        </div>
                                    </div>
                                </div>
                                
                                                                <div class="zone-actions">
                                    <?php if ($trajeto): ?>
                                        <?php if ($trajeto['source'] === 'gps'): ?>
                                            <!-- GPS trajectory - show only GPS view button -->
                                            <button type="button" class="btn-premium btn-primary" onclick="viewGpsTrajeto('<?php echo $trajeto['id']; ?>', '<?php echo $zone['id']; ?>')">
                                                <i class="fas fa-mobile-alt"></i>
                                                Ver Trajeto GPS
                                            </button>
                                        <?php else: ?>
                                            <!-- Manual trajectory - show normal view -->
                                            <button type="button" class="btn-premium btn-primary" onclick="viewTrajeto('<?php echo $trajeto['id']; ?>')">
                                                <i class="fas fa-eye"></i>
                                                Ver Trajeto
                                            </button>
                                                                        <button type="button" class="btn-premium btn-warning" onclick="editTrajeto('<?php echo $trajeto['id']; ?>', '<?php echo $zone['id']; ?>', '<?php echo htmlspecialchars($zone['nomeZona']); ?>')">
                                <i class="fas fa-edit"></i>
                                Editar Trajeto
                            </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <!-- Disabled button for all users -->
                                        <button type="button" class="btn-premium btn-secondary" disabled title="Funcionalidade temporariamente desabilitada">
                                            <i class="fas fa-plus"></i>
                                            Criar Trajeto
                                        </button>
                                        
                                        <?php if ($user_email === '<EMAIL>'): ?>
                                            <!-- Temporary button on top for test user only -->
                                            <button type="button" class="btn-premium btn-success" onclick="createTrajeto('<?php echo $zone['id']; ?>', '<?php echo htmlspecialchars($zone['nomeZona'], ENT_QUOTES, 'UTF-8'); ?>')" style="margin-top: 5px;">
                                                <i class="fas fa-plus"></i>
                                                Criar Trajeto (TESTE)
                                            </button>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

        </div>
    </div>

    <!-- Include Profile Modal -->
    <?php include '../../includes/profile_modal.php'; ?>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Firebase v8 Compatibility -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <!-- Custom JS -->
    <script src="../../../webadmin/assets/js/main.js"></script>
    <script src="../../../webadmin/assets/js/dark-mode.js"></script>

    <script>
        // Page loaded - no dynamic loading needed since we use server-side rendering
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Trajetos page loaded');
            
            <?php if (!empty($error_message)): ?>
            showError('<?php echo addslashes($error_message); ?>');
            <?php endif; ?>
        });

        // Essential functions for trajetos page
        
        function createTrajeto(zoneId, zoneName) {
            window.location.href = `create.php?zoneId=${encodeURIComponent(zoneId)}&zoneName=${encodeURIComponent(zoneName)}`;
        }
        
        function editTrajeto(trajetoId, zoneId, zoneName) {
            window.location.href = `edit.php?id=${encodeURIComponent(trajetoId)}&zoneId=${encodeURIComponent(zoneId)}&zoneName=${encodeURIComponent(zoneName)}`;
        }
        

        
        function showTrajetoHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Gestão de Zonas</div>',
                html: `
                    <div class="help-modal-content">
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-route text-primary me-2"></i>
                                <span>Ações disponíveis</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-plus text-success me-2"></i>
                                    <span><strong>Criar Trajeto:</strong> Adicione um trajeto a zonas que ainda não têm</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-eye text-primary me-2"></i>
                                    <span><strong>Ver Trajeto:</strong> Visualize trajetos já existentes com todos os detalhes</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-edit text-warning me-2"></i>
                                    <span><strong>Editar Trajeto:</strong> Modifique trajetos existentes, alterando pontos no mapa</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                <span>Estados das zonas</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-status-item">
                                    <span class="help-status-badge help-status-active">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Trajeto Ativo
                                    </span>
                                    <span class="help-status-desc">Zona tem um trajeto configurado e operacional</span>
                                </div>
                                <div class="help-status-item">
                                    <span class="help-status-badge help-status-needed">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Necessita Trajeto
                                    </span>
                                    <span class="help-status-desc">Zona ainda não tem trajeto definido</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-lightbulb text-primary me-2"></i>
                                <span>Informações mostradas</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-circle text-info me-2"></i>
                                    <span><strong>Distância:</strong> Distância total do trajeto calculada automaticamente</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-warning me-2"></i>
                                    <span><strong>Data:</strong> Mostra "Criado" ou "Editado" com a data da última alteração</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-success me-2"></i>
                                    <span><strong>Localização inicial:</strong> Endereço de partida do trajeto (quando disponível)</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-map-marked-alt text-primary me-2"></i>
                                <span>Criar trajetos</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Localização automática:</strong> Use GPS para centrar o mapa na sua localização</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Pesquisar localização:</strong> Digite uma cidade ou morada para centrar o mapa</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Definir manualmente:</strong> Use o mapa diretamente sem localização automática</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span>Precisa de Ajuda?</span>
                            </div>
                            <div class="help-section-body">
                                <div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">
                                    <div style="color: #0a7ea4; font-size: 0.9rem;">
                                        Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>
                                        <strong><EMAIL></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }
        
        function showError(message) {
            Swal.fire({
                title: 'Erro',
                text: message,
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc2626'
            });
        }

        function getStatusClass(status) {
            switch (status) {
                case 'completed': return 'bg-success';
                case 'review': return 'bg-warning';
                case 'draft':
                default: return 'bg-secondary';
            }
        }

        function getStatusLabel(status) {
            switch (status) {
                case 'completed': return 'Concluído';
                case 'review': return 'Em Revisão';
                case 'draft':
                default: return 'Rascunho';
            }
        }



        function createTrajeto(zoneId, zoneName) {
            window.location.href = `create.php?zoneId=${encodeURIComponent(zoneId)}&zoneName=${encodeURIComponent(zoneName)}`;
        }

        function viewDetails(zoneId) {
            // Find the zone data
            const zone = zones.find(z => z.id === zoneId);
            const zoneTrajetos = trajetos.filter(t => t.zoneId === zoneId);
            
            if (!zone) return;
            
            let detailsHtml = '<div class="zone-details">' +
                '<h5><i class="fas fa-map-marker-alt me-2"></i>Zona ' + (zone.zona || 'N/A') + ' - ' + escapeHtml(zone.nomeZona) + '</h5>' +
                '<div class="row mt-3">' +
                    '<div class="col-md-6">' +
                        '<p><strong>Quota:</strong> ' + (zone.quotaZona || 'Não definida') + '</p>' +
                        '<p><strong>Selos:</strong> ' + (zone.minSelo && zone.maxSelo ? zone.minSelo + ' - ' + zone.maxSelo : 'Não definidos') + '</p>' +
                    '</div>' +
                    '<div class="col-md-6">' +
                        '<p><strong>Email:</strong> ' + (zone.email || 'Não definido') + '</p>' +
                        '<p><strong>NIF:</strong> ' + (zone.nifEntidade || 'Não definido') + '</p>' +
                    '</div>' +
                '</div>';
            
            if (zoneTrajetos.length > 0) {
                detailsHtml += '<hr><h6><i class="fas fa-route me-2"></i>Trajeto</h6>';
                zoneTrajetos.forEach(trajeto => {
                    detailsHtml += '<div class="trajeto-details mt-2">' +
                        '<p><strong>Nome:</strong> ' + escapeHtml(trajeto.name || 'Sem nome') + '</p>' +
                        '<p><strong>Descrição:</strong> ' + escapeHtml(trajeto.description || 'Sem descrição') + '</p>' +
                        '<p><strong>Estado:</strong> <span class="badge ' + getStatusClass(trajeto.status) + '">' + getStatusLabel(trajeto.status) + '</span></p>' +
                    '</div>';
                });
            } else {
                detailsHtml += '<hr><div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Esta zona ainda não tem trajeto criado.</div>';
            }
            
            detailsHtml += '</div>';
            
            Swal.fire({
                title: 'Detalhes da Zona',
                html: detailsHtml,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Fechar',
                confirmButtonColor: '#0a7ea4',
                width: '600px'
            });
        }

        function viewTrajetos(zoneId, zoneName) {
            // Show modal with trajetos for this zone
            const zoneTrajetos = trajetos.filter(t => t.zoneId === zoneId);
            
            let trajetosHtml = '';
            zoneTrajetos.forEach(trajeto => {
                trajetosHtml += '<div class="trajeto-item mb-3 p-3 border rounded">' +
                    '<div class="d-flex justify-content-between align-items-start">' +
                        '<div>' +
                            '<h6 class="mb-1">' + escapeHtml(trajeto.name || 'Sem nome') + '</h6>' +
                            '<small class="text-muted">' + (trajeto.description || 'Sem descrição') + '</small>' +
                            '<div class="mt-2">' +
                                '<span class="badge ' + getStatusClass(trajeto.status) + ' me-2">' +
                                    getStatusLabel(trajeto.status) +
                                '</span>' +
                                '<span class="badge bg-light text-dark">' +
                                    (trajeto.distance || '0 km') +
                                '</span>' +
                            '</div>' +
                        '</div>' +
                        '<div class="btn-group-vertical btn-group-sm">' +
                            '<button class="btn btn-outline-primary btn-sm" onclick="viewTrajeto(\'' + trajeto.id + '\')">' +
                                '<i class="fas fa-eye"></i> Ver Trajeto' +
                            '</button>' +
                            '<button class="btn btn-outline-warning btn-sm" onclick="editTrajeto(\'' + trajeto.id + '\')">' +
                                '<i class="fas fa-edit"></i> Editar Trajeto' +
                            '</button>' +
                            '<button class="btn btn-outline-danger btn-sm" onclick="deleteTrajeto(\'' + trajeto.id + '\', \'' + escapeHtml(trajeto.name || 'trajeto') + '\')">' +
                                '<i class="fas fa-trash"></i> Eliminar' +
                            '</button>' +
                        '</div>' +
                    '</div>' +
                '</div>';
            });

            Swal.fire({
                title: '<i class="fas fa-route me-2"></i>Trajetos - ' + zoneName,
                html: '<div class="trajetos-list">' +
                        trajetosHtml +
                        '<div class="mt-3">' +
                            '<button class="btn btn-success" onclick="createTrajeto(\'' + zoneId + '\', \'' + escapeHtml(zoneName) + '\')">' +
                                '<i class="fas fa-plus me-2"></i>Criar Novo Trajeto' +
                            '</button>' +
                        '</div>' +
                    '</div>',
                showConfirmButton: false,
                showCloseButton: true,
                width: '600px',
                customClass: {
                    popup: 'trajetos-modal-popup'
                }
            });
        }

        function createTrajeto(zoneId, zoneName) {
            window.location.href = `create.php?zoneId=${encodeURIComponent(zoneId)}&zoneName=${encodeURIComponent(zoneName)}`;
        }



        function viewTrajeto(trajetoId) {
            window.location.href = `view.php?id=${encodeURIComponent(trajetoId)}`;
        }

        function viewGpsTrajeto(trajetoId, zoneId) {
            window.location.href = `view_mobile.php?id=${encodeURIComponent(trajetoId)}&zoneId=${encodeURIComponent(zoneId)}`;
        }

        function viewZoneDetails(zoneId) {
            Swal.fire({
                title: 'Detalhes da Zona',
                text: 'Esta funcionalidade estará disponível em breve.',
                icon: 'info',
                confirmButtonText: 'OK',
                confirmButtonColor: '#0a7ea4'
            });
        }

        function deleteTrajeto(trajetoId, trajetoName) {
            Swal.fire({
                title: 'Eliminar Trajeto',
                text: `Tem a certeza que deseja eliminar o trajeto "${trajetoName}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Sim, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Delete from Firestore
                    db.collection('zonas').doc(trajetoId)
                        .delete()
                        .then(() => {
                            Swal.fire({
                                title: 'Eliminado!',
                                text: 'Trajeto eliminado com sucesso.',
                                icon: 'success',
                                confirmButtonColor: '#16a34a'
                            });
                            // Reload page to refresh data
                            window.location.reload();
                        })
                        .catch((error) => {
                            console.error('Error deleting trajeto:', error);
                            Swal.fire({
                                title: 'Erro',
                                text: 'Erro ao eliminar o trajeto.',
                                icon: 'error',
                                confirmButtonColor: '#dc2626'
                            });
                        });
                }
            });
        }

        function showError(message) {
            Swal.fire({
                title: 'Erro',
                text: message,
                icon: 'error',
                confirmButtonColor: '#dc2626'
            });
        }

        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }

        function showTrajetoHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Gestão de Zonas</div>',
                html: '<div class="help-modal-content">' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-route text-primary me-2"></i>' +
                                '<span>Funcionalidades disponíveis</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-plus text-success me-2"></i>' +
                                    '<span><strong>Criar Trajeto:</strong> Adicione um novo trajeto para uma zona específica</span>' +
                                '</div>' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-eye text-primary me-2"></i>' +
                                    '<span><strong>Ver Trajeto:</strong> Visualize trajetos já existentes com todos os detalhes</span>' +
                                '</div>' +
                                '<div class="help-item">' +
                                    '<i class="fas fa-edit text-warning me-2"></i>' +
                                    '<span><strong>Editar Trajeto:</strong> Modifique trajetos existentes, alterando pontos no mapa</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-info-circle text-primary me-2"></i>' +
                                '<span>Estados das zonas</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-check-circle text-success me-2"></i>' +
                                    '<span><strong>Trajeto Ativo:</strong> Zona tem trajeto configurado e operacional</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-exclamation-triangle text-warning me-2"></i>' +
                                    '<span><strong>Necessita Trajeto:</strong> Zona ainda não tem trajeto definido</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-chart-bar text-primary me-2"></i>' +
                                '<span>Informações exibidas</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-ruler text-primary me-2"></i>' +
                                    '<span><strong>Distância:</strong> Comprimento total do trajeto em quilómetros</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-calendar text-primary me-2"></i>' +
                                    '<span><strong>Data:</strong> Mostra "Criado" ou "Editado" com a data correspondente</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-map-marker-alt text-primary me-2"></i>' +
                                    '<span><strong>Localização:</strong> Endereço aproximado do ponto inicial do trajeto</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-location-arrow text-primary me-2"></i>' +
                                '<span>Opções de localização</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-crosshairs text-primary me-2"></i>' +
                                    '<span><strong>GPS:</strong> Use a sua localização atual para começar o trajeto</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-search text-primary me-2"></i>' +
                                    '<span><strong>Pesquisar:</strong> Procure por uma localização específica em Portugal</span>' +
                                '</div>' +
                                '<div class="help-tip">' +
                                    '<i class="fas fa-mouse-pointer text-primary me-2"></i>' +
                                    '<span><strong>Manual:</strong> Clique diretamente no mapa para definir o ponto inicial</span>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="help-section">' +
                            '<div class="help-section-header">' +
                                '<i class="fas fa-envelope text-primary me-2"></i>' +
                                '<span>Precisa de Ajuda?</span>' +
                            '</div>' +
                            '<div class="help-section-body">' +
                                '<div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">' +
                                    '<div style="color: #0a7ea4; font-size: 0.9rem;">' +
                                        'Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>' +
                                        '<strong><EMAIL></strong>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>',
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }


    </script>

</body>
</html> 