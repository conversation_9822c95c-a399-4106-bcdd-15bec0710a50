<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set HTML response header
header('Content-Type: text/html; charset=utf-8');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo '<h1>Unauthorized</h1><p>You must be logged in as a gestor to access this page.</p>';
    exit();
}

/**
 * Parse Firestore document format
 */
function parseFirestoreDocument($doc) {
    if (!isset($doc['fields'])) {
        return [];
    }
    
    $result = [];
    foreach ($doc['fields'] as $key => $field) {
        $result[$key] = convertFromFirestoreValue($field);
    }
    
    return $result;
}

/**
 * Convert Firestore field value to PHP value
 */
function convertFromFirestoreValue($field) {
    if (isset($field['stringValue'])) {
        return $field['stringValue'];
    } elseif (isset($field['integerValue'])) {
        return (int)$field['integerValue'];
    } elseif (isset($field['doubleValue'])) {
        return (float)$field['doubleValue'];
    } elseif (isset($field['booleanValue'])) {
        return $field['booleanValue'];
    } elseif (isset($field['timestampValue'])) {
        return $field['timestampValue'];
    } elseif (isset($field['nullValue'])) {
        return null;
    } elseif (isset($field['arrayValue'])) {
        $result = [];
        if (isset($field['arrayValue']['values'])) {
            foreach ($field['arrayValue']['values'] as $value) {
                $result[] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    } elseif (isset($field['mapValue'])) {
        $result = [];
        if (isset($field['mapValue']['fields'])) {
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $result[$key] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    }
    
    return null;
}

try {
    // Use the global database instance
    global $database;
    
    // Get admin token for loading
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // Check if this is a global duplicates request
    $isGlobalDuplicatesRequest = false;
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['action']) && $input['action'] === 'findGlobalDuplicates') {
            $isGlobalDuplicatesRequest = true;
        }
    }
    
    // Handle global duplicates request with JSON response
    if ($isGlobalDuplicatesRequest) {
        $startTime = microtime(true);
        
        // Get ALL contacts with pagination for global analysis
    $allFirestoreContacts = [];
    $nextPageToken = null;
    $pageCount = 0;
    
    do {
        $pageCount++;
        
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts";
        if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
        }
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status === 200) {
                $pageResult = json_decode($response, true);
                
                if (isset($pageResult['documents'])) {
                    $allFirestoreContacts = array_merge($allFirestoreContacts, $pageResult['documents']);
                }
                
                $nextPageToken = $pageResult['nextPageToken'] ?? null;
            } else {
                // Handle rate limiting or other errors
                if ($status === 429) {
                    error_log("Rate limit hit during global duplicates search, stopping at page {$pageCount}");
                }
                break;
            }
            
            // Rate limiting protection: Add small delay between requests
            if ($nextPageToken && $pageCount > 1) {
                usleep(250000); // 250ms delay between requests
            }
            
            // Safety check to prevent infinite loops and excessive API usage
            if ($pageCount > 10) {
                error_log("Reached maximum page limit (10) for global duplicates search");
                break;
            }
            
        } while ($nextPageToken && count($allFirestoreContacts) < 10000);
        
        // Process all contacts for global duplicate detection
        $allContacts = [];
        $coordinateGroups = [];
        
        foreach ($allFirestoreContacts as $doc) {
            $contactData = parseFirestoreDocument($doc);
            
            // Extract document ID
            if (isset($doc['name'])) {
                $nameParts = explode('/', $doc['name']);
                $contactData['id'] = end($nameParts);
            }
            
            $allContacts[] = $contactData;
            
            // Group by coordinates
            if (isset($contactData['coordinates']['lat']) && isset($contactData['coordinates']['lng'])) {
                $coordKey = $contactData['coordinates']['lat'] . ',' . $contactData['coordinates']['lng'];
                if (!isset($coordinateGroups[$coordKey])) {
                    $coordinateGroups[$coordKey] = [];
                }
                $coordinateGroups[$coordKey][] = $contactData;
            }
        }
        
        // Find duplicates (same coordinates)
        $globalDuplicates = [];
        $totalDuplicateContacts = 0;
        
        foreach ($coordinateGroups as $coordKey => $group) {
            if (count($group) > 1) {
                $globalDuplicates[] = [
                    'coordinates' => $coordKey,
                    'contacts' => $group
                ];
                $totalDuplicateContacts += count($group);
            }
        }
        
        $endTime = microtime(true);
        $analysisTime = round($endTime - $startTime, 2) . 's';
        
        // Return JSON response
        ob_end_clean();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'duplicates' => $globalDuplicates,
            'totalContacts' => count($allContacts),
            'totalDuplicateContacts' => $totalDuplicateContacts,
            'uniquePositions' => count($coordinateGroups),
            'analysisTime' => $analysisTime
        ]);
        exit();
    }
    
    // Regular page load - get contacts for current user only
    $allFirestoreContacts = [];
                $nextPageToken = null;
    $pageCount = 0;
    
    do {
        $pageCount++;
        error_log("Fetching contacts page {$pageCount} for <EMAIL>");
        
        $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts";
        if ($nextPageToken) {
            $url .= "?pageToken=" . urlencode($nextPageToken);
        }
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status === 200) {
            $pageResult = json_decode($response, true);
            
            if (isset($pageResult['documents'])) {
                $allFirestoreContacts = array_merge($allFirestoreContacts, $pageResult['documents']);
            }
            
            $nextPageToken = $pageResult['nextPageToken'] ?? null;
            } else {
            if ($status === 429) {
                error_log("Rate limit hit during user contacts fetch, stopping at page {$pageCount}");
        } else {
                error_log("Contacts query failed with status: {$status}");
            }
            if ($response) {
                error_log("Error response: " . substr($response, 0, 500));
            }
            break;
        }
        
        // Rate limiting protection: Add small delay between requests for user contacts too
        if ($nextPageToken && $pageCount > 1) {
            usleep(200000); // 200ms delay between requests
        }
        
        // Safety check to prevent infinite loops
        if ($pageCount > 5) {
            error_log("Reached maximum page limit (5) for user contact fetch");
            break;
        }
        
    } while ($nextPageToken && count($allFirestoreContacts) < 5000);
    
         // Create result structure for compatibility
    $result = ['documents' => $allFirestoreContacts];
    $myContacts = [];
    $totalContacts = 0;
    $trajectoryGroups = [];
    
    if (isset($result['documents'])) {
        $totalContacts = count($result['documents']);
        
        foreach ($result['documents'] as $doc) {
            $contactData = parseFirestoreDocument($doc);
            
            // Extract document ID from the document name
            if (isset($doc['name'])) {
                $nameParts = explode('/', $doc['name']);
                $contactData['id'] = end($nameParts);
            }
            
            // Check if this contact was <NAME_EMAIL>
            if (isset($contactData['createdByEmail']) && $contactData['createdByEmail'] === '<EMAIL>') {
                $myContacts[] = $contactData;
                
                // Group by trajectory ID for analysis
                $trajectoryId = $contactData['trajectoryId'] ?? 'UNKNOWN';
                if (!isset($trajectoryGroups[$trajectoryId])) {
                    $trajectoryGroups[$trajectoryId] = [];
                }
                $trajectoryGroups[$trajectoryId][] = $contactData;
            }
        }
    }
    
    // Sort contacts by creation date (newest first)
    usort($myContacts, function($a, $b) {
        $dateA = $a['createdAt'] ?? '';
        $dateB = $b['createdAt'] ?? '';
        return strcmp($dateB, $dateA);
    });
    
    // Analyze duplicates by coordinates
    $coordinateGroups = [];
    foreach ($myContacts as $contact) {
        if (isset($contact['coordinates']['lat']) && isset($contact['coordinates']['lng'])) {
            $coordKey = $contact['coordinates']['lat'] . ',' . $contact['coordinates']['lng'];
            if (!isset($coordinateGroups[$coordKey])) {
                $coordinateGroups[$coordKey] = [];
            }
            $coordinateGroups[$coordKey][] = $contact;
        }
    }
    
    // Find duplicates (same coordinates)
    $duplicateGroups = array_filter($coordinateGroups, function($group) {
        return count($group) > 1;
    });
    
    ob_end_clean();
    
    // Generate HTML output
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Análise de Contactos - <EMAIL></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .contact-card {
                border-left: 4px solid #0a7ea4;
                margin-bottom: 0.5rem;
                min-height: auto;
            }
            .contact-card .card-body {
                padding: 0.75rem 1rem;
            }
            .duplicate-group {
                border: 2px solid #dc2626;
                border-radius: 8px;
                margin-bottom: 1.5rem;
                background: #fef2f2;
            }
            .stats-card {
                background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
                color: white;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
            }
            .trajectory-group {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                margin-bottom: 1rem;
                background: #f9fafb;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid py-4">
            <h1 class="mb-4">
                <i class="fas fa-dove text-primary me-3"></i>
                Análise de Contactos - <EMAIL>
            </h1>
            
            <!-- Global Duplicate Detection Button -->
            <div class="mb-4">
                <button type="button" class="btn btn-warning btn-lg" id="findGlobalDuplicatesBtn">
                    <i class="fas fa-search me-2"></i>
                    Procurar Duplicados Globais
                </button>
                <small class="text-muted d-block mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    Procura contactos duplicados em todos os trajetos de todas as zonas
                </small>
            </div>
            
            <!-- Statistics -->
            <div class="stats-card">
                <div class="row">
                    <div class="col-md-3">
                        <h3><?php echo $totalContacts; ?></h3>
                        <p class="mb-0">Total de Contactos na Base de Dados</p>
                    </div>
                    <div class="col-md-3">
                        <h3><?php echo count($myContacts); ?></h3>
                        <p class="mb-0">Contactos Criados por Mim</p>
                    </div>
                    <div class="col-md-3">
                        <h3><?php echo count($coordinateGroups); ?></h3>
                        <p class="mb-0">Posições Únicas</p>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-warning"><?php echo count($duplicateGroups); ?></h3>
                        <p class="mb-0">Posições com Duplicados</p>
                    </div>
                </div>
            </div>

            <!-- Trajectory Groups -->
            <h2 class="mb-3">
                <i class="fas fa-route text-primary me-2"></i>
                Contactos por Trajeto
            </h2>
            <?php foreach ($trajectoryGroups as $trajectoryId => $contacts): ?>
                <div class="trajectory-group">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Trajeto: <?php echo htmlspecialchars($trajectoryId); ?>
                            <span class="badge bg-primary ms-2"><?php echo count($contacts); ?> contactos</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($contacts as $i => $contact): ?>
                            <div class="card contact-card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <h6 class="card-title mb-0">
                                                <i class="fas fa-dove me-1"></i>
                                                Contacto <?php echo $i + 1; ?>
                                                <?php if (isset($contact['images']) && is_array($contact['images']) && count($contact['images']) > 0): ?>
                                                    <span class="badge bg-info ms-1" title="<?php echo count($contact['images']); ?> imagem(ns)">
                                                        <i class="fas fa-camera"></i> <?php echo count($contact['images']); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </h6>
                                            <small class="text-muted">
                                                <strong>Coleção:</strong> contacts<br>
                                                <strong>Documento:</strong> <?php echo htmlspecialchars($contact['id'] ?? 'NO_ID'); ?><br>
                                                <strong>Email:</strong> <?php echo htmlspecialchars($contact['createdByEmail'] ?? 'N/A'); ?>
                                                <?php if (isset($contact['images']) && is_array($contact['images']) && count($contact['images']) > 0): ?>
                                                    <br><strong>Imagens:</strong> <?php echo count($contact['images']); ?> foto(s)
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <div class="col-md-2">
                                            <p class="card-text small mb-1">
                                                <strong>Coordenadas:</strong><br>
                                                <?php 
                                                if (isset($contact['coordinates']['lat']) && isset($contact['coordinates']['lng'])) {
                                                    echo number_format($contact['coordinates']['lat'], 6) . ', ' . number_format($contact['coordinates']['lng'], 6);
                                                } else {
                                                    echo 'N/A';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                        <div class="col-md-1">
                                            <p class="card-text small mb-1">
                                                <strong>Hora:</strong><br>
                                                <?php echo htmlspecialchars($contact['time'] ?? 'N/A'); ?>
                                            </p>
                                        </div>
                                        <div class="col-md-2">
                                            <p class="card-text small mb-1">
                                                <strong>Circunstância:</strong><br>
                                                <?php echo htmlspecialchars($contact['circumstance'] ?? 'N/A'); ?>
                                            </p>
                                        </div>
                                        <div class="col-md-2">
                                            <p class="card-text small mb-1">
                                                <strong>Local:</strong><br>
                                                <?php echo htmlspecialchars($contact['location'] ?? 'N/A'); ?>
                                            </p>
                                        </div>
                                        <div class="col-md-2">
                                            <p class="card-text small mb-1">
                                                <strong>Criado:</strong><br>
                                                <?php 
                                                if (isset($contact['createdAt'])) {
                                                    $date = new DateTime($contact['createdAt']);
                                                    echo $date->format('d/m/Y H:i:s');
                                                } else {
                                                    echo 'N/A';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                                                <div class="col-md-1">
                            <button class="btn btn-danger btn-sm delete-contact-btn" 
                                    data-contact-id="<?php echo htmlspecialchars($contact['id'] ?? ''); ?>"
                                    data-trajectory-id="<?php echo htmlspecialchars($trajectoryId); ?>"
                                    data-coordinates="<?php 
                                    if (isset($contact['coordinates']['lat']) && isset($contact['coordinates']['lng'])) {
                                        echo htmlspecialchars(number_format($contact['coordinates']['lat'], 6) . ', ' . number_format($contact['coordinates']['lng'], 6));
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>"
                                    data-time="<?php echo htmlspecialchars($contact['time'] ?? 'N/A'); ?>"
                                    data-circumstance="<?php echo htmlspecialchars($contact['circumstance'] ?? 'N/A'); ?>"
                                    data-image-count="<?php echo isset($contact['images']) && is_array($contact['images']) ? count($contact['images']) : 0; ?>"
                                    title="Eliminar Contacto">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Duplicate Analysis -->
            <?php if (count($duplicateGroups) > 0): ?>
                <h2 class="mb-3 mt-5">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Análise de Duplicados
                </h2>
                <div class="alert alert-warning">
                    <strong>Atenção!</strong> Foram encontradas <?php echo count($duplicateGroups); ?> posições com contactos duplicados.
                </div>
                
                <?php foreach ($duplicateGroups as $coordKey => $group): ?>
                    <div class="duplicate-group">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-clone me-2"></i>
                                Posição Duplicada: <?php echo htmlspecialchars($coordKey); ?>
                                <span class="badge bg-light text-dark ms-2"><?php echo count($group); ?> contactos</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($group as $i => $contact): ?>
                                <div class="card border-danger contact-card mb-2">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-2">
                                                <h6 class="card-title text-danger mb-0">
                                                    <i class="fas fa-dove me-1"></i>
                                                    Duplicado <?php echo $i + 1; ?>
                                                    <?php if (isset($contact['images']) && is_array($contact['images']) && count($contact['images']) > 0): ?>
                                                        <span class="badge bg-info ms-1" title="<?php echo count($contact['images']); ?> imagem(ns)">
                                                            <i class="fas fa-camera"></i> <?php echo count($contact['images']); ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <strong>Coleção:</strong> contacts<br>
                                                    <strong>Documento:</strong> <code><?php echo htmlspecialchars($contact['id'] ?? 'NO_ID'); ?></code><br>
                                                    <strong>Email:</strong> <?php echo htmlspecialchars($contact['createdByEmail'] ?? 'N/A'); ?>
                                                    <?php if (isset($contact['images']) && is_array($contact['images']) && count($contact['images']) > 0): ?>
                                                        <br><strong>Imagens:</strong> <?php echo count($contact['images']); ?> foto(s)
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="col-md-1">
                                                <p class="card-text small mb-1">
                                                    <strong>Trajeto:</strong><br>
                                                    <small><?php echo htmlspecialchars($contact['trajectoryId'] ?? 'NO_TRAJECTORY'); ?></small>
                                                </p>
                                            </div>
                                            <div class="col-md-1">
                                                <p class="card-text small mb-1">
                                                    <strong>Hora:</strong><br>
                                                    <?php echo htmlspecialchars($contact['time'] ?? 'N/A'); ?>
                                                </p>
                                            </div>
                                            <div class="col-md-2">
                                                <p class="card-text small mb-1">
                                                    <strong>Circunstância:</strong><br>
                                                    <?php echo htmlspecialchars($contact['circumstance'] ?? 'N/A'); ?>
                                                </p>
                                            </div>
                                            <div class="col-md-2">
                                                <p class="card-text small mb-1">
                                                    <strong>Local:</strong><br>
                                                    <?php echo htmlspecialchars($contact['location'] ?? 'N/A'); ?>
                                                </p>
                                            </div>
                                            <div class="col-md-2">
                                                <p class="card-text small mb-1">
                                                    <strong>Criado:</strong><br>
                                                    <?php 
                                                    if (isset($contact['createdAt'])) {
                                                        $date = new DateTime($contact['createdAt']);
                                                        echo $date->format('d/m/Y H:i:s');
                                                    } else {
                                                        echo 'N/A';
                                                    }
                                                    ?>
                                                </p>
                                            </div>
                                            <div class="col-md-1">
                                                <button class="btn btn-danger btn-sm delete-contact-btn" 
                                                        data-contact-id="<?php echo htmlspecialchars($contact['id'] ?? ''); ?>"
                                                        data-trajectory-id="<?php echo htmlspecialchars($contact['trajectoryId'] ?? 'NO_TRAJECTORY'); ?>"
                                                        data-coordinates="<?php 
                                                        if (isset($contact['coordinates']['lat']) && isset($contact['coordinates']['lng'])) {
                                                            echo htmlspecialchars(number_format($contact['coordinates']['lat'], 6) . ', ' . number_format($contact['coordinates']['lng'], 6));
                                                        } else {
                                                            echo 'N/A';
                                                        }
                                                        ?>"
                                                        data-time="<?php echo htmlspecialchars($contact['time'] ?? 'N/A'); ?>"
                                                        data-circumstance="<?php echo htmlspecialchars($contact['circumstance'] ?? 'N/A'); ?>"
                                                        data-image-count="<?php echo isset($contact['images']) && is_array($contact['images']) ? count($contact['images']) : 0; ?>"
                                                        title="Eliminar Contacto">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="alert alert-success mt-5">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Excelente!</strong> Não foram encontrados contactos duplicados.
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Global Duplicates Modal -->
        <div class="modal fade" id="globalDuplicatesModal" tabindex="-1" aria-labelledby="globalDuplicatesModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title" id="globalDuplicatesModalLabel">
                            <i class="fas fa-globe me-2"></i>
                            Duplicados Globais - Todos os Trajetos e Zonas
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="globalDuplicatesContent">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">A procurar...</span>
                            </div>
                            <p class="mt-3">A procurar duplicados em toda a base de dados...</p>
                            <small class="text-muted">Isto pode demorar alguns segundos</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        <button type="button" class="btn btn-primary" id="exportDuplicatesBtn" style="display: none;">
                            <i class="fas fa-download me-1"></i>
                            Exportar Lista
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteContactModal" tabindex="-1" aria-labelledby="deleteContactModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="deleteContactModalLabel">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Confirmar Eliminação
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Tem a certeza que pretende eliminar este contacto?</p>
                        <div class="alert alert-warning">
                            <strong>Atenção:</strong> Esta ação não pode ser desfeita.
                        </div>
                        <div id="contactInfo">
                            <p><strong>ID do Contacto:</strong> <span id="modalContactId"></span></p>
                            <p><strong>Trajeto:</strong> <span id="modalTrajectoryId"></span></p>
                            <p><strong>Coordenadas:</strong> <span id="modalCoordinates"></span></p>
                            <p><strong>Hora:</strong> <span id="modalTime"></span></p>
                            <p><strong>Circunstância:</strong> <span id="modalCircumstance"></span></p>
                            <p id="modalImagesInfo" style="display: none;"><strong>Imagens:</strong> <span id="modalImageCount"></span> foto(s) - <span class="text-warning">também serão eliminadas</span></p>
                        </div>
                        <div class="alert alert-danger mt-3">
                            <strong><i class="fas fa-exclamation-triangle me-1"></i>ATENÇÃO:</strong> 
                            Esta operação irá eliminar permanentemente este contacto da base de dados em produção. 
                            Verifique cuidadosamente os dados acima antes de confirmar.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                            <i class="fas fa-trash me-1"></i>
                            Eliminar Contacto
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteContactModal'));
                const globalDuplicatesModal = new bootstrap.Modal(document.getElementById('globalDuplicatesModal'));
                const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
                const findGlobalDuplicatesBtn = document.getElementById('findGlobalDuplicatesBtn');
                const globalDuplicatesContent = document.getElementById('globalDuplicatesContent');
                const exportDuplicatesBtn = document.getElementById('exportDuplicatesBtn');
                let currentContactId = null;
                let currentTrajectoryId = null;
                let globalDuplicatesData = null;

                // Handle global duplicates button
                findGlobalDuplicatesBtn.addEventListener('click', function() {
                    // Reset modal content to loading state
                    globalDuplicatesContent.innerHTML = `
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">A procurar...</span>
                            </div>
                            <p class="mt-3">A procurar duplicados em toda a base de dados...</p>
                            <small class="text-muted">Isto pode demorar alguns segundos</small>
                        </div>
                    `;
                    exportDuplicatesBtn.style.display = 'none';
                    
                    // Show modal
                    globalDuplicatesModal.show();
                    
                    // Fetch global duplicates
                    fetch('list_my_contacts.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'findGlobalDuplicates'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            globalDuplicatesData = data;
                            displayGlobalDuplicates(data);
                            exportDuplicatesBtn.style.display = 'inline-block';
                        } else {
                            globalDuplicatesContent.innerHTML = `
                                <div class="alert alert-danger">
                                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Erro</h5>
                                    <p>${data.message || 'Erro ao procurar duplicados'}</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        globalDuplicatesContent.innerHTML = `
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Erro de Conexão</h5>
                                <p>Erro ao conectar com o servidor: ${error.message}</p>
                            </div>
                        `;
                    });
                });

                // Function to display global duplicates
                function displayGlobalDuplicates(data) {
                    if (!data.duplicates || data.duplicates.length === 0) {
                        globalDuplicatesContent.innerHTML = `
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Nenhum Duplicado Encontrado</h5>
                                <p>Não foram encontrados contactos duplicados em toda a base de dados.</p>
                                <hr>
                                <p class="mb-0"><strong>Estatísticas:</strong></p>
                                <ul class="mb-0">
                                    <li>Total de contactos analisados: ${data.totalContacts || 0}</li>
                                    <li>Posições únicas: ${data.uniquePositions || 0}</li>
                                    <li>Tempo de análise: ${data.analysisTime || 'N/A'}</li>
                                </ul>
                            </div>
                        `;
                        return;
                    }

                    let html = `
                        <div class="alert alert-warning mb-4">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Duplicados Encontrados</h5>
                            <p>Foram encontradas <strong>${data.duplicates.length}</strong> posições com contactos duplicados.</p>
                            <hr>
                            <p class="mb-0"><strong>Estatísticas:</strong></p>
                            <ul class="mb-0">
                                <li>Total de contactos analisados: ${data.totalContacts || 0}</li>
                                <li>Contactos duplicados: ${data.totalDuplicateContacts || 0}</li>
                                <li>Posições com duplicados: ${data.duplicates.length}</li>
                                <li>Tempo de análise: ${data.analysisTime || 'N/A'}</li>
                            </ul>
                        </div>
                    `;

                    data.duplicates.forEach((duplicateGroup, groupIndex) => {
                        html += `
                            <div class="card border-danger mb-3">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        Posição ${groupIndex + 1}: ${duplicateGroup.coordinates}
                                        <span class="badge bg-light text-dark ms-2">${duplicateGroup.contacts.length} contactos</span>
                                    </h6>
                                </div>
                                <div class="card-body">
                        `;

                                                 duplicateGroup.contacts.forEach((contact, contactIndex) => {
                             const coordinates = contact.coordinates ? 
                                 `${parseFloat(contact.coordinates.lat).toFixed(6)}, ${parseFloat(contact.coordinates.lng).toFixed(6)}` : 
                                 'N/A';
                             const imageCount = contact.images && Array.isArray(contact.images) ? contact.images.length : 0;
                             
                             html += `
                                 <div class="card mb-2 ${contactIndex > 0 ? 'border-warning' : ''}">
                                     <div class="card-body py-2">
                                         <div class="row align-items-center small">
                                             <div class="col-md-2">
                                                 <strong>Contacto ${contactIndex + 1}</strong><br>
                                                 <span class="text-muted">ID: ${contact.id || 'N/A'}</span>
                                                 ${imageCount > 0 ? `<br><span class="badge bg-info"><i class="fas fa-camera"></i> ${imageCount}</span>` : ''}
                                             </div>
                                             <div class="col-md-2">
                                                 <strong>Coordenadas:</strong><br>
                                                 <code class="small">${coordinates}</code>
                                             </div>
                                             <div class="col-md-2">
                                                 <strong>Trajeto:</strong><br>
                                                 ${contact.trajectoryId || 'N/A'}
                                             </div>
                                             <div class="col-md-1">
                                                 <strong>Hora:</strong><br>
                                                 ${contact.time || 'N/A'}
                                             </div>
                                             <div class="col-md-2">
                                                 <strong>Circunstância:</strong><br>
                                                 ${contact.circumstance || 'N/A'}
                                             </div>
                                             <div class="col-md-3">
                                                 <strong>Criado por:</strong><br>
                                                 ${contact.createdByEmail || 'N/A'}<br>
                                                 <small class="text-muted">${contact.createdAt ? new Date(contact.createdAt).toLocaleDateString('pt-PT') : 'N/A'}</small>
                                             </div>

                                         </div>
                                     </div>
                                 </div>
                             `;
                         });

                        html += `
                                </div>
                            </div>
                        `;
                    });

                    globalDuplicatesContent.innerHTML = html;
                    
                    // Reattach delete listeners to new buttons in modal
                    attachDeleteListeners();
                }

                // Handle export duplicates
                exportDuplicatesBtn.addEventListener('click', function() {
                    if (!globalDuplicatesData) return;
                    
                    // Create CSV content
                    let csvContent = "data:text/csv;charset=utf-8,";
                    csvContent += "Posição,Coordenadas,ID Contacto,ID Trajeto,Hora,Circunstância,Local,Criado Por,Data Criação\\n";
                    
                    globalDuplicatesData.duplicates.forEach((duplicateGroup, groupIndex) => {
                        duplicateGroup.contacts.forEach(contact => {
                            csvContent += [
                                `Posição ${groupIndex + 1}`,
                                duplicateGroup.coordinates,
                                contact.id || 'N/A',
                                contact.trajectoryId || 'N/A',
                                contact.time || 'N/A',
                                contact.circumstance || 'N/A',
                                contact.location || 'N/A',
                                contact.createdByEmail || 'N/A',
                                contact.createdAt ? new Date(contact.createdAt).toLocaleDateString('pt-PT') : 'N/A'
                            ].map(field => `"${field}"`).join(',') + '\\n';
                        });
                    });
                    
                    // Download CSV
                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute("download", `duplicados_globais_${new Date().toISOString().split('T')[0]}.csv`);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                });

                // Function to attach delete listeners (for both main list and modal)
                function attachDeleteListeners() {
                document.querySelectorAll('.delete-contact-btn').forEach(button => {
                        // Remove existing listeners to prevent duplicates
                        button.removeEventListener('click', handleDeleteClick);
                        button.addEventListener('click', handleDeleteClick);
                    });
                }

                // Handle delete button clicks
                function handleDeleteClick() {
                        currentContactId = this.getAttribute('data-contact-id');
                        currentTrajectoryId = this.getAttribute('data-trajectory-id');
                        
                        // SAFETY: Validate contact ID format before proceeding
                        if (!currentContactId || currentContactId.length < 15 || !/^[a-zA-Z0-9]+$/.test(currentContactId)) {
                            alert('ERRO: ID de contacto inválido. Operação cancelada por segurança.');
                            return;
                        }
                        
                        // Get additional contact details for verification
                        const coordinates = this.getAttribute('data-coordinates') || 'N/A';
                        const time = this.getAttribute('data-time') || 'N/A';
                        const circumstance = this.getAttribute('data-circumstance') || 'N/A';
                        const imageCount = parseInt(this.getAttribute('data-image-count') || '0');
                        
                        // Update modal content with detailed information
                        document.getElementById('modalContactId').textContent = currentContactId;
                        document.getElementById('modalTrajectoryId').textContent = currentTrajectoryId;
                        document.getElementById('modalCoordinates').textContent = coordinates;
                        document.getElementById('modalTime').textContent = time;
                        document.getElementById('modalCircumstance').textContent = circumstance;
                        
                        // Show/hide image information
                        const imageInfo = document.getElementById('modalImagesInfo');
                        if (imageCount > 0) {
                            document.getElementById('modalImageCount').textContent = imageCount;
                            imageInfo.style.display = 'block';
                        } else {
                            imageInfo.style.display = 'none';
                        }
                        
                        // Show modal
                        deleteModal.show();
                    }

                // Initial attachment of delete listeners
                attachDeleteListeners();

                // Handle confirm delete
                confirmDeleteBtn.addEventListener('click', function() {
                    if (currentContactId) {
                        // Disable button and show loading
                        confirmDeleteBtn.disabled = true;
                        confirmDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Eliminando...';
                        
                        // Make delete request
                        fetch('delete_contact.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                contactId: currentContactId,
                                trajectoryId: currentTrajectoryId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Success - show summary if there were images
                                let message = 'Contacto eliminado com sucesso!';
                                if (data.totalImages > 0) {
                                    message += `\n\nImagens: ${data.imagesDeleted}/${data.totalImages} eliminadas`;
                                    if (data.imageErrors && data.imageErrors.length > 0) {
                                        message += `\nAvisos: ${data.imageErrors.length} erro(s) ao eliminar imagens`;
                                    }
                                }
                                alert(message);
                                
                                // Check if global duplicates modal is open and refresh it
                                if (globalDuplicatesModal._isShown) {
                                    // Refresh global duplicates data
                                    globalDuplicatesContent.innerHTML = `
                                        <div class="text-center">
                                            <div class="spinner-border text-warning" role="status">
                                                <span class="visually-hidden">A atualizar...</span>
                                            </div>
                                            <p class="mt-3">A atualizar lista de duplicados...</p>
                                        </div>
                                    `;
                                    
                                    // Re-fetch global duplicates
                                    fetch('list_my_contacts.php', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({
                                            action: 'findGlobalDuplicates'
                                        })
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            globalDuplicatesData = data;
                                            displayGlobalDuplicates(data);
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error refreshing duplicates:', error);
                                    });
                                } else {
                                    // Regular page reload if modal not open
                                window.location.reload();
                                }
                            } else {
                                // Error
                                alert('Erro ao eliminar contacto: ' + (data.error || 'Erro desconhecido'));
                                confirmDeleteBtn.disabled = false;
                                confirmDeleteBtn.innerHTML = '<i class="fas fa-trash me-1"></i>Eliminar Contacto';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Erro ao eliminar contacto: ' + error.message);
                            confirmDeleteBtn.disabled = false;
                            confirmDeleteBtn.innerHTML = '<i class="fas fa-trash me-1"></i>Eliminar Contacto';
                        });
                    }
                });
            });
        </script>
    </body>
    </html>
    <?php
    
} catch (Exception $e) {
    ob_end_clean();
    http_response_code(500);
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Erro - Análise de Contactos</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle me-2"></i>Erro</h4>
                <p><?php echo htmlspecialchars($e->getMessage()); ?></p>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?> 