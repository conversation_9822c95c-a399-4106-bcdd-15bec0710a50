<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json; charset=utf-8');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Clean any previous output
ob_end_clean();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit();
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$trajectoryId = $input['trajectoryId'] ?? '';
$zoneId = $input['zoneId'] ?? ''; // Additional zone validation

// SECURITY: Validate trajectory ID format (should be alphanumeric Firestore document ID)
if (empty($trajectoryId)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Trajectory ID is required'
    ]);
    exit();
}

// SECURITY: Validate trajectory ID format to prevent injection attacks
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $trajectoryId) || strlen($trajectoryId) > 100) {
    error_log("SECURITY VIOLATION: Invalid trajectory ID format attempted: " . $trajectoryId . " by user " . ($_SESSION['user']['email'] ?? 'unknown'));
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid trajectory ID format'
    ]);
    exit();
}

// SECURITY: Validate zone ID format if provided (additional validation layer)
if (!empty($zoneId) && (!preg_match('/^[a-zA-Z0-9_-]+$/', $zoneId) || strlen($zoneId) > 100)) {
    error_log("SECURITY VIOLATION: Invalid zone ID format attempted: " . $zoneId . " by user " . ($_SESSION['user']['email'] ?? 'unknown'));
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid zone ID format'
    ]);
    exit();
}

// SECURITY: Rate limiting - prevent rapid deletion attempts
$user_id = $_SESSION['user']['id'] ?? '';
$last_deletion_key = "last_contact_deletion_{$user_id}";
$current_time = time();

if (isset($_SESSION[$last_deletion_key])) {
    $time_since_last = $current_time - $_SESSION[$last_deletion_key];
    if ($time_since_last < 2) { // 2 second cooldown between deletions
        http_response_code(429);
        echo json_encode([
            'success' => false,
            'message' => 'Please wait before performing another deletion operation'
        ]);
        exit();
    }
}

$_SESSION[$last_deletion_key] = $current_time;

/**
 * Parse Firestore document format
 */
function parseFirestoreDocument($doc) {
    if (!isset($doc['fields'])) {
        return [];
    }
    
    $result = [];
    foreach ($doc['fields'] as $key => $field) {
        $result[$key] = convertFromFirestoreValue($field);
    }
    
    return $result;
}

/**
 * Convert Firestore field value to PHP value
 */
function convertFromFirestoreValue($field) {
    if (isset($field['stringValue'])) {
        return $field['stringValue'];
    } elseif (isset($field['integerValue'])) {
        return (int)$field['integerValue'];
    } elseif (isset($field['doubleValue'])) {
        return (float)$field['doubleValue'];
    } elseif (isset($field['booleanValue'])) {
        return $field['booleanValue'];
    } elseif (isset($field['timestampValue'])) {
        return $field['timestampValue'];
    } elseif (isset($field['nullValue'])) {
        return null;
    } elseif (isset($field['arrayValue'])) {
        $result = [];
        if (isset($field['arrayValue']['values'])) {
            foreach ($field['arrayValue']['values'] as $value) {
                $result[] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    } elseif (isset($field['mapValue'])) {
        $result = [];
        if (isset($field['mapValue']['fields'])) {
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $result[$key] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    }
    
    return null;
}

/**
 * Delete a specific contact by ID
 */
function deleteContactById($contactId, $adminToken) {
    $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts/{$contactId}";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $adminToken
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    
    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $status === 200;
}

try {
    // Use the global database instance
    global $database;
    
    // Get current user info for security validation
    $user_id = $_SESSION['user']['id'] ?? '';
    $user_email = $_SESSION['user']['email'] ?? '';
    $user_nif = $_SESSION['user']['nif'] ?? '';
    
    // Debug logging
    error_log("DELETE_TRAJECTORY_CONTACTS: Starting deletion for trajectory {$trajectoryId}, zone {$zoneId} by user {$user_email}");
    error_log("DELETE_TRAJECTORY_CONTACTS: Session data - user_id: '{$user_id}', user_email: '{$user_email}', user_nif: '{$user_nif}'");
    
    if (empty($user_id) || empty($user_email)) {
        error_log("DELETE_TRAJECTORY_CONTACTS: Invalid session - user_id: " . ($user_id ?: 'empty') . ", user_email: " . ($user_email ?: 'empty'));
        throw new Exception("User session invalid - missing user ID or email");
    }
    
    // Get admin token for operations
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // SECURITY LAYER 1: Optional trajectory validation (like single contact deletion)
    $trajectoryDoc = null;
    $trajectoryZoneId = '';
    
    if (!empty($trajectoryId)) {
        error_log("DELETE_TRAJECTORY_CONTACTS: Attempting to get trajectory document from 'zonas' collection with ID: {$trajectoryId}");
        $trajectoryDoc = $database->getDocument('zonas', $trajectoryId);
        
        if ($trajectoryDoc) {
            error_log("DELETE_TRAJECTORY_CONTACTS: Successfully retrieved trajectory document. CreatedBy: " . ($trajectoryDoc['createdBy'] ?? 'not set'));
            
            // Check if the trajectory was created by the current user
            if (!isset($trajectoryDoc['createdBy']) || $trajectoryDoc['createdBy'] !== $user_id) {
                error_log("SECURITY VIOLATION: User {$user_email} attempted to delete contacts from trajectory {$trajectoryId} owned by " . ($trajectoryDoc['createdBy'] ?? 'unknown'));
                throw new Exception("Access denied: You can only delete contacts from your own trajectories");
            }
            
            // SECURITY LAYER 2: Verify the zone associated with this trajectory belongs to the user
            $trajectoryZoneId = $trajectoryDoc['zoneId'] ?? '';
            if (!empty($trajectoryZoneId)) {
                $zoneDoc = $database->getDocument('zonasCaca', $trajectoryZoneId);
                if ($zoneDoc) {
                    // Check if the zone belongs to the user's NIF
                    if (!isset($zoneDoc['nifEntidade']) || $zoneDoc['nifEntidade'] !== $user_nif) {
                        error_log("SECURITY VIOLATION: User {$user_email} (NIF: {$user_nif}) attempted to delete contacts from zone {$trajectoryZoneId} belonging to NIF " . ($zoneDoc['nifEntidade'] ?? 'unknown'));
                        throw new Exception("Access denied: The zone associated with this trajectory does not belong to you");
                    }
                }
            }
            
            // SECURITY LAYER 2.5: Cross-validate zone ID if provided in request
            if (!empty($zoneId) && !empty($trajectoryZoneId) && $zoneId !== $trajectoryZoneId) {
                error_log("SECURITY VIOLATION: User {$user_email} attempted to delete contacts with mismatched zone IDs - Request: {$zoneId}, Trajectory: {$trajectoryZoneId}");
                throw new Exception("Access denied: Zone ID mismatch detected");
            }
        } else {
            error_log("DELETE_TRAJECTORY_CONTACTS: Trajectory document not found, but continuing with contact-level validation only");
        }
    } else {
        error_log("DELETE_TRAJECTORY_CONTACTS: No trajectory ID provided, using contact-level validation only");
    }
    
    // SECURITY LAYER 3: Query contacts with additional user validation
    $allFirestoreContacts = [];
    $nextPageToken = null;
    
    do {
        $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/contacts";
        if ($nextPageToken) {
            $url .= "?pageToken=" . urlencode($nextPageToken);
        }
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $adminToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status !== 200) {
            throw new Exception("Failed to fetch contacts, status: {$status}");
        }
        
        $pageResult = json_decode($response, true);
        
        if (isset($pageResult['documents'])) {
            $allFirestoreContacts = array_merge($allFirestoreContacts, $pageResult['documents']);
        }
        
        $nextPageToken = $pageResult['nextPageToken'] ?? null;
        
    } while ($nextPageToken);
    
    // SECURITY LAYER 4: Find and validate contacts with multiple security checks
    $contactsToDelete = [];
    $totalContacts = 0;
    $securityViolations = [];
    
    if (!empty($allFirestoreContacts)) {
        $totalContacts = count($allFirestoreContacts);
        
        foreach ($allFirestoreContacts as $doc) {
            $contactData = parseFirestoreDocument($doc);
            
            // Extract document ID from the document name
            if (isset($doc['name'])) {
                $nameParts = explode('/', $doc['name']);
                $contactData['id'] = end($nameParts);
            }
            
            // SECURITY CHECK 1: Contact must belong to the specified trajectory
            if (!isset($contactData['trajectoryId']) || $contactData['trajectoryId'] !== $trajectoryId) {
                continue; // Skip contacts from other trajectories
            }
            
            // SECURITY CHECK 2: Contact must be created by the current user (if field exists)
            if (isset($contactData['createdByEmail']) && $contactData['createdByEmail'] !== $user_email) {
                $securityViolations[] = "Contact {$contactData['id']} was created by {$contactData['createdByEmail']}, not by current user {$user_email}";
                continue; // Skip contacts created by other users
            }
            
            // SECURITY CHECK 3: Contact must be created by the current user ID (if field exists)
            if (isset($contactData['createdBy']) && $contactData['createdBy'] !== $user_id) {
                $securityViolations[] = "Contact {$contactData['id']} was created by user ID {$contactData['createdBy']}, not by current user {$user_id}";
                continue; // Skip contacts created by other users
            }
            
            // SECURITY CHECK 4: Double-verify trajectory ownership before adding to deletion list
            if ($contactData['trajectoryId'] === $trajectoryId) {
                // SECURITY CHECK 5: Additional zone validation if zone ID provided
                if (!empty($zoneId)) {
                    // Check if contact has zone information and matches
                    $contactZoneId = $contactData['zoneId'] ?? '';
                    if (!empty($contactZoneId) && $contactZoneId !== $zoneId) {
                        $securityViolations[] = "Contact {$contactData['id']} belongs to zone {$contactZoneId}, not requested zone {$zoneId}";
                        continue; // Skip contacts from other zones
                    }
                }
                
                // SECURITY CHECK 6: Verify contact timestamp is reasonable (not too old for current session)
                $contactTimestamp = $contactData['timestamp'] ?? '';
                if (!empty($contactTimestamp)) {
                    // Allow contacts from the last 24 hours (86400 seconds) to prevent deletion of very old contacts
                    $contactTime = strtotime($contactTimestamp);
                    $currentTime = time();
                    if ($contactTime && ($currentTime - $contactTime) > 86400) {
                        error_log("SECURITY WARNING: User {$user_email} attempting to delete old contact {$contactData['id']} from " . date('Y-m-d H:i:s', $contactTime));
                        // Note: We don't skip old contacts, just log for monitoring
                    }
                }
                
                $contactsToDelete[] = $contactData;
            }
        }
    }
    
    // Log any security violations found
    if (!empty($securityViolations)) {
        error_log("SECURITY VIOLATIONS detected during contact deletion for user {$user_email}: " . implode('; ', $securityViolations));
    }
    
    // Delete the contacts
    $deletedCount = 0;
    $errors = [];
    
    foreach ($contactsToDelete as $contact) {
        if (deleteContactById($contact['id'], $adminToken)) {
            $deletedCount++;
        } else {
            $errors[] = "Failed to delete contact ID: " . $contact['id'];
        }
    }
    
    // Log successful deletion for audit trail
    error_log("CONTACT DELETION SUCCESS: User {$user_email} (ID: {$user_id}) successfully deleted {$deletedCount} contacts from trajectory {$trajectoryId}, zone {$trajectoryZoneId}. Total contacts scanned: {$totalContacts}");
    
    // Return success response with security audit info
    echo json_encode([
        'success' => true,
        'message' => "Successfully deleted {$deletedCount} contacts for trajectory {$trajectoryId}" . (!empty($trajectoryZoneId) ? " in zone {$trajectoryZoneId}" : ""),
        'deletedCount' => $deletedCount,
        'totalContactsScanned' => $totalContacts,
        'securityViolationsFound' => count($securityViolations),
        'trajectoryOwner' => $trajectoryDoc ? ($trajectoryDoc['createdBy'] ?? 'unknown') : 'not_validated',
        'trajectoryZoneId' => $trajectoryZoneId,
        'requestedZoneId' => $zoneId,
        'requestedByUser' => $user_id,
        'errors' => $errors
    ]);
    
} catch (Exception $e) {
    error_log("Error deleting trajectory contacts: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error deleting contacts: ' . $e->getMessage(),
        'deletedCount' => 0
    ]);
}
?> 