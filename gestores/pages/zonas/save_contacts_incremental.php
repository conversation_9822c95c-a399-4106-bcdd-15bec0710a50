<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Log the received data for debugging
error_log("💾 SAVE_CONTACTS_INCREMENTAL: Received trajectory ID: " . ($input['trajectoryId'] ?? 'NOT SET'));
error_log("💾 SAVE_CONTACTS_INCREMENTAL: Number of contacts to save: " . (isset($input['contacts']) ? count($input['contacts']) : 0));

// Validate required fields
if (!$input || !isset($input['trajectoryId']) || !isset($input['contacts'])) {
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

// Get user information
$user_id = $_SESSION['user']['id'] ?? '';
$user_email = $_SESSION['user']['email'] ?? '';

if (empty($user_id)) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User ID not found']);
    exit();
}

try {
    // Use the global database instance
    global $database;
    
    // Debug: Log the input data
    error_log("INCREMENTAL SAVE - Input data: " . json_encode($input));
    
    // Get admin token for saving
    $adminToken = $database->getAdminAccessToken();
    if (!$adminToken) {
        throw new Exception("Failed to get admin access token");
    }
    
    // Set admin token
    $database->setAccessToken($adminToken);
    
    // Validate contacts array
    $newContacts = $input['contacts'];
    if (!is_array($newContacts)) {
        throw new Exception("Contacts must be an array");
    }
    
    error_log("INCREMENTAL SAVE - Processing " . count($newContacts) . " new contacts");
    
    $savedContacts = [];
    $errors = [];
    
    // Process each NEW contact (incremental approach - only add new ones)
    foreach ($newContacts as $index => $contact) {
        error_log("INCREMENTAL SAVE - Processing NEW contact $index: " . json_encode($contact));
        
        if (!is_array($contact)) {
            $error = "Invalid contact format at index $index";
            error_log("INCREMENTAL SAVE - ERROR: $error");
            $errors[] = $error;
            continue;
        }
        
        // Validate required contact fields
        $requiredFields = ['lat', 'lng', 'time', 'circumstance', 'location'];
        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (!isset($contact[$field])) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            $error = "Missing required fields in contact $index: " . implode(', ', $missingFields);
            error_log("INCREMENTAL SAVE - ERROR: $error");
            $errors[] = $error;
            continue;
        }
        
        $lat = (float)$contact['lat'];
        $lng = (float)$contact['lng'];
        
        error_log("INCREMENTAL SAVE - Contact $index coordinates: lat=$lat, lng=$lng");
        
        // Basic validation for coordinate values
        if ($lat < -90 || $lat > 90 || $lng < -180 || $lng > 180) {
            $error = "Invalid coordinate values at index $index: lat=$lat, lng=$lng";
            error_log("INCREMENTAL SAVE - ERROR: $error");
            $errors[] = $error;
            continue;
        }
        
        // Prepare contact data
        error_log("💾 SAVE_CONTACTS_INCREMENTAL: Preparing contact $index with trajectory ID: " . $input['trajectoryId']);
        $contactData = [
            'trajectoryId' => $input['trajectoryId'],
            'coordinates' => [
                'lat' => $lat,
                'lng' => $lng
            ],
            'time' => $contact['time'],
            'circumstance' => $contact['circumstance'],
            'location' => $contact['location'],
            'createdAt' => date('c'), // ISO 8601 format
            'createdBy' => $user_id,
            'createdByEmail' => $user_email,
            'updatedAt' => date('c'),
            'updatedBy' => $user_id
        ];
        
        error_log("INCREMENTAL SAVE - Prepared contact data $index: " . json_encode($contactData));
        
        // Save to Firestore - using 'contacts' collection
        try {
            $documentId = $database->addDocument('contacts', $contactData);
            error_log("INCREMENTAL SAVE - SUCCESS: Saved contact $index with ID: $documentId");
            
            $savedContacts[] = [
                'id' => $documentId,
                'data' => $contactData
            ];
        } catch (Exception $e) {
            $error = "Failed to save contact $index: " . $e->getMessage();
            error_log("INCREMENTAL SAVE - ERROR: $error");
            $errors[] = $error;
        }
    }
    
    // Prepare response
    $successCount = count($savedContacts);
    $errorCount = count($errors);
    
    error_log("INCREMENTAL SAVE - SUMMARY: $successCount saved, $errorCount errors");
    
    if ($errorCount > 0) {
        error_log("INCREMENTAL SAVE - ERRORS: " . json_encode($errors));
    }
    
    // Clean output buffer and return success response
    ob_end_clean();
    
    if ($successCount > 0) {
        echo json_encode([
            'success' => true, 
            'message' => "Successfully saved $successCount contacts" . ($errorCount > 0 ? " ($errorCount errors)" : ""),
            'contactCount' => $successCount,
            'contacts' => $savedContacts,
            'errors' => $errors
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'message' => 'No contacts were saved: ' . implode('; ', $errors),
            'errors' => $errors
        ]);
    }
    
} catch (Exception $e) {
    // Clean output buffer and return error response
    ob_end_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error saving contacts: ' . $e->getMessage()
    ]);
    
    // Log the error
    error_log("INCREMENTAL SAVE - CRITICAL ERROR: " . $e->getMessage());
}
?> 