<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Clean any previous output
ob_end_clean();

// Use the global database instance (already initialized in config.php)
global $database;

// Set the user's auth token for authenticated requests
if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Get gestor data
$user_id = $_SESSION['user']['id'];
$user_email = $_SESSION['user']['email'];
$user_nif = $_SESSION['user']['nif'] ?? '';
$error_message = '';
$success_message = '';



$gestorZones = [];
$selosData = [];



// Handle restore seal action (only if user originally used it)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'restore_seal') {
    $sealNumber = $_POST['seal_number'] ?? '';
    
    if (!empty($sealNumber)) {
        try {
            $result = restoreSeloNumber($sealNumber, $user_email, $database);
            if ($result) {
                // Set a flag for JavaScript to show toast
                echo "<script>
                    window.addEventListener('DOMContentLoaded', function() {
                        Swal.fire({
                            icon: 'success',
                            title: 'Selo Restaurado!',
                            text: 'Selo $sealNumber restaurado com sucesso.',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true
                        });
                    });
                </script>";
                // Refresh the page after a short delay
                echo "<script>setTimeout(function() { window.location.reload(); }, 1000);</script>";
            } else {
                $error_message = 'Falha ao restaurar selo.';
            }
        } catch (Exception $e) {
            $error_message = 'Erro ao restaurar selo: ' . $e->getMessage();
        }
    }
}

try {
    // Get admin token to read data
    $adminToken = $database->getAdminAccessToken();
    if ($adminToken) {
        $database->setAccessToken($adminToken);
        $userDoc = $database->getDocument('gestoresZonaCaca', $user_id);
        
        // If user NIF is empty in session but exists in Firestore, update the session
        if (empty($user_nif) && !empty($userDoc['nif'])) {
            $_SESSION['user']['nif'] = $userDoc['nif'];
            $user_nif = $userDoc['nif'];
        }
    }
    
    // Get all zones managed by this gestor (for warning message)
    $allGestorZones = getAllGestorZones($database, $user_nif);
    
    // Get all trajetos created by this gestor (manual + GPS)
    $gestorTrajetos = getAllGestorTrajetos($database, $user_id);
    
    // Get zones managed by this gestor that have trajectories (filter based on trajetos)
    $gestorZones = getZonesWithTrajetos($allGestorZones, $gestorTrajetos);
    
    // Get seals data for gestor's zones (only those with trajectories)
    $selosData = getSelosForGestorZones($database, $gestorZones);
    
} catch (Exception $e) {
    error_log("Selos data error: " . $e->getMessage());
    $error_message = "Erro ao carregar dados dos selos.";
}

/**
 * Get all trajetos created by the current gestor (manual + GPS)
 */
function getAllGestorTrajetos($database, $userId) {
    try {
        $allTrajetos = [];
        
        // Get manual trajectories from 'zonas' collection
        $manualTrajetos = getGestorTrajetos($database, $userId);
        $allTrajetos = array_merge($allTrajetos, $manualTrajetos);
        
        // Get GPS trajectories from 'gestorMobile_trajetos' collection
        $gpsTrajetos = getGestorMobileTrajetos($database, $userId);
        $allTrajetos = array_merge($allTrajetos, $gpsTrajetos);
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getAllGestorTrajetos: " . $e->getMessage());
        return [];
    }
}

/**
 * Get manual trajetos created by the current gestor
 */
function getGestorTrajetos($database, $userId) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId (using correct collection 'zonas')
        $result = $database->queryDocuments('zonas', 'createdBy', 'EQUAL', $userId);
        
        $allTrajetos = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allTrajetos[] = [
                    'id' => $docId,
                    'name' => $data['name'] ?? 'Trajeto Manual',
                    'description' => $data['description'] ?? '',
                    'zoneId' => $data['zoneId'] ?? '',
                    'status' => $data['status'] ?? 'draft',
                    'difficulty' => $data['difficulty'] ?? 'medio',
                    'distance' => $data['distance'] ?? '0 km',
                    'createdBy' => $data['createdBy'] ?? '',
                    'createdAt' => $data['createdAt'] ?? null,
                    'updatedAt' => $data['updatedAt'] ?? null,
                    'source' => 'manual' // Mark as manual trajectory
                ];
            }
        }
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getGestorTrajetos: " . $e->getMessage());
        return [];
    }
}

/**
 * Get GPS trajetos created by the current gestor
 */
function getGestorMobileTrajetos($database, $userId) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId (GPS trajectories use 'userId' field)
        $result = $database->queryDocuments('gestorMobile_trajetos', 'userId', 'EQUAL', $userId);
        
        $allTrajetos = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                // Only include completed trajectories
                if (isset($data['status']) && $data['status'] === 'completed') {
                    // Calculate distance from totalDistance (stored in meters)
                    $totalDistance = $data['totalDistance'] ?? 0;
                    $distanceKm = $totalDistance > 0 ? number_format($totalDistance / 1000, 2) . ' km' : '0 km';
                    
                    $allTrajetos[] = [
                        'id' => $docId,
                        'name' => $data['name'] ?? 'Trajeto GPS',
                        'description' => $data['description'] ?? '',
                        'zoneId' => $data['zoneId'] ?? '',
                        'status' => $data['status'] ?? 'completed',
                        'difficulty' => $data['difficulty'] ?? 'medio',
                        'distance' => $distanceKm,
                        'createdBy' => $data['userId'] ?? '',
                        'createdAt' => $data['createdAt'] ?? null,
                        'updatedAt' => $data['updatedAt'] ?? null,
                        'source' => 'gps' // Mark as GPS trajectory
                    ];
                }
            }
        }
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getGestorMobileTrajetos: " . $e->getMessage());
        return [];
    }
}

/**
 * Filter zones to only include those with trajectories
 */
function getZonesWithTrajetos($allZones, $trajetos) {
    $zonesWithTrajetos = [];
    
    foreach ($allZones as $zone) {
        $hasTrajetoAssociated = false;
        foreach ($trajetos as $trajeto) {
            if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                $hasTrajetoAssociated = true;
                break;
            }
        }
        if ($hasTrajetoAssociated) {
            $zonesWithTrajetos[] = $zone;
        }
    }
    
    return $zonesWithTrajetos;
}

/**
 * Get all hunting zones for the current gestor (including those without trajectories)
 */
function getAllGestorZones($database, $userNif) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userNif)) {
            return [];
        }
        
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        $allZones = [];
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allZones[] = [
                    'id' => $docId,
                    'zona' => $data['zona'] ?? 'N/A',
                    'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                    'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                    'email' => $data['email'] ?? 'N/A',
                    'quotaZona' => $data['quotaZona'] ?? 0,
                    'minSelo' => $data['minSelo'] ?? 0,
                    'maxSelo' => $data['maxSelo'] ?? 0,
                    'status' => $data['status'] ?? 'not registered',
                    'registeredBy' => $data['registeredBy'] ?? null
                ];
            }
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getAllGestorZones: " . $e->getMessage());
        return [];
    }
}

/**
 * Get user name from user ID
 */
function getUserName($database, $userId) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userId)) {
            return 'N/A';
        }
        
        // Try to get from gestoresZonaCaca first
        $user = $database->getDocument('gestoresZonaCaca', $userId);
        if ($user && isset($user['name'])) {
            return $user['name'];
        }
        
        // Try to get from users collection
        $user = $database->getDocument('users', $userId);
        if ($user && isset($user['name'])) {
            return $user['name'];
        }
        
        return 'N/A';
    } catch (Exception $e) {
        error_log("Error getting user name for $userId: " . $e->getMessage());
        return 'N/A';
    }
}

/**
 * Get jornada information
 */
function getJornadaInfo($database, $jornadaId) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($jornadaId)) {
            return 'N/A';
        }
        
        $jornada = $database->getDocument('jornadasCaca', $jornadaId);
        if ($jornada) {
            $date = $jornada['data'] ?? '';
            $zoneId = $jornada['zonaId'] ?? '';
            
            // Try to get zone name
            $zoneName = 'N/A';
            if (!empty($zoneId)) {
                $zone = $database->getDocument('zonasCaca', $zoneId);
                if ($zone && isset($zone['nomeZona'])) {
                    $zoneName = $zone['nomeZona'];
                }
            }
            
            if (!empty($date)) {
                return "Jornada de " . date('d/m/Y', strtotime($date)) . " - " . $zoneName;
            } else {
                return "Jornada - " . $zoneName;
            }
        }
        
        return 'N/A';
    } catch (Exception $e) {
        error_log("Error getting jornada info for $jornadaId: " . $e->getMessage());
        return 'N/A';
    }
}

/**
 * Get seals data for gestor's zones - generate from zone ranges and get used seals
 */
function getSelosForGestorZones($database, $gestorZones) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($gestorZones)) {
            return [];
        }
        
        // Get used seals from jornadas (actual source of truth)
        $usedSeals = [];
        
        // Query all jornadas to find which seals are actually used
        foreach ($gestorZones as $zone) {
            $result = $database->queryDocuments('jornadasCaca', 'zonaId', 'EQUAL', $zone['id']);
            
            if ($result && is_array($result)) {
                foreach ($result as $docId => $jornada) {
                    $selosAtribuidos = $jornada['selosAtribuidos'] ?? '';
                    $jornadaDate = $jornada['data'] ?? '';
                    $createdBy = $jornada['createdBy'] ?? '';
                    
                    if (!empty($selosAtribuidos)) {
                        if (is_string($selosAtribuidos)) {
                            // Try to decode JSON first
                            $selosJson = json_decode($selosAtribuidos, true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($selosJson)) {
                                // Handle JSON format
                                foreach ($selosJson as $selo) {
                                    if (isset($selo['number'])) {
                                        $seloNumber = intval($selo['number']);
                                        $usedSeals[$seloNumber] = [
                                            'seloNumber' => $seloNumber,
                                            'zoneId' => $zone['id'],
                                            'usedAt' => $jornadaDate,
                                            'usedBy' => $createdBy,
                                            'notes' => 'Usado em jornada ' . $docId,
                                            'jornadaId' => $docId,
                                            'age' => $selo['age'] ?? null,
                                            'photoUrl' => $selo['photoUrl'] ?? $selo['fotoAsaDireita'] ?? null,
                                            'photoName' => $selo['photoName'] ?? null
                                        ];
                                    }
                                }
                            } else {
                                // Handle comma-separated format
                                $selosArray = array_filter(array_map('trim', explode(',', $selosAtribuidos)));
                                foreach ($selosArray as $seloStr) {
                                    if (is_numeric($seloStr)) {
                                        $seloNumber = intval($seloStr);
                                        $usedSeals[$seloNumber] = [
                                            'seloNumber' => $seloNumber,
                                            'zoneId' => $zone['id'],
                                            'usedAt' => $jornadaDate,
                                            'usedBy' => $createdBy,
                                            'notes' => 'Usado em jornada ' . $docId,
                                            'jornadaId' => $docId
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Generate all seals from zone ranges
        $allSelos = [];
        foreach ($gestorZones as $zone) {
            $minSelo = intval($zone['minSelo']);
            $maxSelo = intval($zone['maxSelo']);
            
            if ($minSelo > 0 && $maxSelo >= $minSelo) {
                for ($seloNum = $minSelo; $seloNum <= $maxSelo; $seloNum++) {
                    $isUsed = isset($usedSeals[$seloNum]) && $usedSeals[$seloNum]['zoneId'] === $zone['id'];
                    
                    // Get user name and jornada info for used seals
                    $userName = null;
                    $jornadaInfo = null;
                    if ($isUsed) {
                        $userName = getUserName($database, $usedSeals[$seloNum]['usedBy']);
                        $jornadaInfo = getJornadaInfo($database, $usedSeals[$seloNum]['jornadaId']);
                    }
                    
                    $allSelos[] = [
                        'id' => 'selo_' . $seloNum,
                        'seloNumber' => $seloNum,
                        'zoneId' => $zone['id'],
                        'status' => $isUsed ? 'used' : 'available',
                        'year' => date('Y'),
                        'usedAt' => $isUsed ? $usedSeals[$seloNum]['usedAt'] : null,
                        'usedBy' => $isUsed ? $usedSeals[$seloNum]['usedBy'] : null,
                        'usedByName' => $userName,
                        'notes' => $isUsed ? $usedSeals[$seloNum]['notes'] : '',
                        'jornadaId' => $isUsed ? ($usedSeals[$seloNum]['jornadaId'] ?? null) : null,
                        'jornadaInfo' => $jornadaInfo,
                        'age' => $isUsed ? ($usedSeals[$seloNum]['age'] ?? null) : null,
                        'photoUrl' => $isUsed ? ($usedSeals[$seloNum]['photoUrl'] ?? null) : null,
                        'photoName' => $isUsed ? ($usedSeals[$seloNum]['photoName'] ?? null) : null,
                        'createdAt' => null
                    ];
                }
            }
        }
        
        return $allSelos;
    } catch (Exception $e) {
        error_log("Exception in getSelosForGestorZones: " . $e->getMessage());
        return [];
    }
}



/**
 * Restore a seal number (remove from jornadas - only if current user used it)
 * Note: This function is now deprecated since seals are managed through jornadas.
 * To "restore" a seal, you need to edit or delete the corresponding jornada.
 */
function restoreSeloNumber($seloNumber, $userEmail, $database) {
    try {
        // Since seals are now managed through jornadas, we need to find which jornada contains this seal
        // and allow the user to edit that jornada instead of directly "restoring" the seal
        throw new Exception('Para liberar este selo, deve editar ou eliminar a jornada correspondente na página de Jornadas.');
    } catch (Exception $e) {
        throw new Exception('Error restoring seal: ' . $e->getMessage());
    }
}

// Calculate statistics for the user's zones
$totalSeals = count($selosData);
$availableSeals = count(array_filter($selosData, function($s) { return $s['status'] === 'available'; }));
$usedSeals = count(array_filter($selosData, function($s) { return $s['status'] === 'used'; }));
$quotaPercentage = $totalSeals > 0 ? round(($usedSeals / $totalSeals) * 100, 1) : 0;
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Listagem de Selos - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/dark-mode.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>

        


        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .stat-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(10, 126, 164, 0.15);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .stat-content h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
        }

        .stat-content p {
            margin: 0;
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        .bg-blue {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
        }

        .bg-green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .bg-orange {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .bg-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        /* Seal Cards */
        .seal-card {
            background: white;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            padding: 1rem;
            margin: 0.5rem;
            display: inline-block;
            min-width: 120px;
            height: 100px;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .seal-card.available {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            cursor: default;
        }

        .seal-card.used {
            border-color: #0a7ea4;
            border-width: 3px;
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.2), 0 4px 12px rgba(10, 126, 164, 0.15);
        }

        .seal-card.my-used {
            border-color: #0a7ea4;
            border-width: 3px;
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.2), 0 4px 12px rgba(10, 126, 164, 0.15);
        }

        .seal-card.used:hover,
        .seal-card.my-used:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .seal-number {
            font-size: 1.25rem;
            font-weight: bold;
            color: #374151;
            position: relative;
        }

        /* Add dove icon for used seals */
        .seal-card.used .seal-number::after {
            content: '\f4ba';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: -15px;
            right: -15px;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #0a7ea4 0%, #06b6d4 100%);
            color: #fff;
            border-radius: 50%;
            border: 1px solid white;
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            animation: doveAppear 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes doveAppear {
            0% {
                transform: scale(0) rotate(-180deg);
                opacity: 0;
            }
            50% {
                transform: scale(1.3) rotate(-90deg);
                opacity: 0.8;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }



        .seal-status {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .seal-date {
            font-size: 0.7rem;
            color: #6b7280;
            margin-top: 0.25rem;
            position: absolute;
            bottom: 0.5rem;
            left: 50%;
            transform: translateX(-50%);
        }

        .seal-card.available .seal-status {
            color: #10b981;
        }

        .seal-card.used .seal-status {
            color: #0a7ea4;
        }

        .seal-card.my-used .seal-status {
            color: #0a7ea4;
        }

        .seals-grid {
            display: flex;
            flex-wrap: wrap;
            padding: 1rem;
            gap: 0.5rem;
            justify-content: center;
        }

        .zone-section {
            margin-bottom: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .zone-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .zone-stats {
            display: flex;
            gap: 2rem;
            font-size: 0.9rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-state h5 {
            margin-bottom: 1rem;
            color: #374151;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(4px);
            align-items: center;
            justify-content: center;
        }

        .modal-dialog {
            position: relative;
            width: auto;
            max-width: 700px;
            margin: 0;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h5 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        .form-field {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-label i {
            color: #0a7ea4;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.15s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #0a7ea4;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            transform: translateY(-1px);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: white;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.15s ease;
        }

        .close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .info-display {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 0.75rem;
            font-weight: 500;
            color: #374151;
            min-height: 20px;
        }

        #seal_image_container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.5rem;
            background: #f8fafc;
            border: 2px dashed #e2e8f0;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-height: 140px;
        }

        #seal_image_container img {
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        #seal_image_container img:hover {
            transform: scale(1.05);
        }

        .compact-field {
            margin-bottom: 0.75rem;
        }

        .compact-label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .compact-label i {
            color: #0a7ea4;
        }

        .compact-info {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            min-height: auto;
        }

        /* Lightbox styles */
        .lightbox {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(4px);
        }

        .lightbox-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-image {
            max-width: 100%;
            max-height: 90%;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .lightbox-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            z-index: 10001;
            background: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s ease;
        }

        .lightbox-close:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        /* Header Actions */
        .header-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        /* Help Button */
        .btn-help {
            background: rgba(10, 126, 164, 0.08);
            color: #0a7ea4;
            border: 1px solid rgba(10, 126, 164, 0.15);
            padding: 0.375rem 0.75rem;
            font-size: 0.813rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
            height: 32px;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .btn-help:hover {
            background: #0a7ea4;
            color: white;
            border-color: #0a7ea4;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Help Modal Styles */
        .help-modal-content {
            text-align: left;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 2rem;
        }

        .help-section:last-child {
            margin-bottom: 0;
        }

        .help-section-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: #374151;
        }

        .help-section-body {
            margin-left: 1.75rem;
        }

        .help-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.875rem;
            line-height: 1.5;
        }

        .help-item:last-child {
            margin-bottom: 0;
        }

        .help-item i {
            margin-top: 0.125rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .help-tip {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.75rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #0a7ea4;
        }

        .help-tip:last-child {
            margin-bottom: 0;
        }

        .help-tip i {
            margin-top: 0.125rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .help-status-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.875rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
        }

        .help-status-item:last-child {
            margin-bottom: 0;
        }

        .help-status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 0.75rem;
            min-width: 80px;
            justify-content: center;
        }

        .help-status-available {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            color: #10b981;
            border: 1px solid #10b981;
        }

        .help-status-used {
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
            color: #0a7ea4;
            border: 1px solid #0a7ea4;
        }

        .help-status-desc {
            color: #6b7280;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-tag"></i>
                                        Listagem de Selos
            </h1>
        </div>
        <div class="header-actions">
            <button class="btn btn-help" onclick="showSelosHelp()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>



        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-blue">
                    <i class="fas fa-tag"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo number_format($totalSeals); ?></h4>
                    <p>Total de Selos</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon bg-green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo number_format($availableSeals); ?></h4>
                    <p>Selos Disponíveis</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon bg-orange">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo number_format($usedSeals); ?></h4>
                    <p>Selos Utilizados</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon bg-purple">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo $quotaPercentage; ?>%</h4>
                    <p>Quota Utilizada</p>
                </div>
            </div>
        </div>

        <!-- Seals by Zone -->
        <?php
        // Group seals by zone
        $selosByZone = [];
        foreach ($selosData as $selo) {
            if (!isset($selosByZone[$selo['zoneId']])) {
                $selosByZone[$selo['zoneId']] = [];
            }
            $selosByZone[$selo['zoneId']][] = $selo;
        }

        // Get zone names
        $zoneNames = [];
        foreach ($gestorZones as $zone) {
            $zoneNames[$zone['id']] = $zone['nomeZona'] . ' (Zona ' . $zone['zona'] . ')';
        }

        if (empty($selosByZone)): ?>
            <div style="width: 100%; background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
    <div style="padding: 1.5rem; text-align: center;">
        <div class="empty-state">
            <i class="fas fa-tag" style="font-size: 2rem; color: #6b7280;"></i>
            <h5 style="margin-top: 0.5rem;">Nenhum selo disponível</h5>

            <?php if (count($allGestorZones) > 0 && count($gestorZones) === 0): ?>
                <div style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.08) 100%);
                            border: 1px solid rgba(245, 158, 11, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; text-align: center;">

                    <!-- Centered icon + text -->
                    <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 0.75rem;">
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        <strong style="color: #92400e;">Aviso</strong>
                    </div>

                    <p style="margin: 0; color: #92400e; font-size: 0.9rem; line-height: 1.5;">
                        Os selos das zonas de caça apenas aparecem para zonas que tenham trajetos associados.
                        Atualmente tem <strong><?php echo count($allGestorZones); ?> zona<?php echo count($allGestorZones) > 1 ? 's' : ''; ?> registada<?php echo count($allGestorZones) > 1 ? 's' : ''; ?></strong>,
                        mas nenhuma tem trajetos criados (manuais ou GPS).
                    </p>

                    <p style="margin: 0.5rem 0 0 0; color: #92400e; font-size: 0.85rem;">
                        <i class="fas fa-lightbulb"></i>
                        <strong>Dica:</strong> Crie trajetos manuais ou grave trajetos GPS para as suas zonas na secção "Zonas de Caça" para ver os selos disponíveis.
                    </p>
                </div>
            <?php else: ?>
                <p>Os selos para as suas zonas ainda não foram configurados pelo administrador.</p>
                <p style="color: #6b7280; font-size: 0.9rem;">Entre em contacto com o administrador para solicitar a configuração dos selos.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

        <?php else: ?>
            <?php foreach ($selosByZone as $zoneId => $selos):
                $zoneName = $zoneNames[$zoneId] ?? 'Zona Desconhecida';
                $availableCount = count(array_filter($selos, function($s) { return $s['status'] === 'available'; }));
                $usedCount = count(array_filter($selos, function($s) { return $s['status'] === 'used'; }));
                ?>
                <div class="zone-section">
                    <div class="zone-header">
                        <span><i class="fas fa-binoculars"></i> <?php echo htmlspecialchars($zoneName); ?></span>
                        <div class="zone-stats">
                            <span>Disponíveis: <?php echo $availableCount; ?></span>
                            <span>Utilizados: <?php echo $usedCount; ?></span>
                            <span>Total: <?php echo count($selos); ?></span>
                        </div>
                    </div>
                    <div class="seals-grid">
                        <?php
                        // Sort seals by number
                        usort($selos, function($a, $b) {
                            return $a['seloNumber'] - $b['seloNumber'];
                        });
                        
                        foreach ($selos as $selo): 
                            $isMyUsed = ($selo['status'] === 'used' && $selo['usedBy'] === $user_email);
                            $cardClass = $selo['status'];
                            if ($isMyUsed) {
                                $cardClass = 'my-used';
                            }
                            ?>
                            <div class="seal-card <?php echo $cardClass; ?>" 
                                 onclick="selectSelo(<?php echo $selo['seloNumber']; ?>, '<?php echo $selo['status']; ?>', '<?php echo $zoneId; ?>', <?php echo $isMyUsed ? 'true' : 'false'; ?>, '<?php echo htmlspecialchars($selo['usedBy'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['usedAt'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['notes'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['usedByName'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['jornadaInfo'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['photoUrl'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['photoName'] ?? ''); ?>', '<?php echo htmlspecialchars($selo['age'] ?? ''); ?>')">
                                <div class="seal-number"><?php echo $selo['seloNumber']; ?></div>
                                <div class="seal-status">
                                    <?php 
                                    if ($selo['status'] === 'available') {
                                        echo 'Disponível';
                                    } else {
                                        echo 'Utilizado';
                                    }
                                    ?>
                                </div>
                                <?php if ($selo['status'] === 'used' && !empty($selo['usedAt'])): ?>
                                    <div class="seal-date">
                                        <?php echo date('d/m/Y', strtotime($selo['usedAt'])); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>



    <!-- Lightbox for seal images -->
    <div class="lightbox" id="sealLightbox">
        <div class="lightbox-content">
            <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
            <img class="lightbox-image" id="lightboxImage" src="" alt="Seal Image">
        </div>
    </div>

    <!-- Seal Info Modal -->
    <div class="modal" id="seloInfoModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-info-circle"></i>
                        Informações do Selo
                    </h5>
                    <button type="button" class="close" onclick="closeSeloInfoModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-field text-center">
                                <label class="form-label">
                                    <i class="fas fa-image"></i>
                                    Imagem do Selo
                                </label>
                                <div id="seal_image_container" style="margin: 0.5rem 0;"></div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-field compact-field">
                                        <label class="form-label compact-label">
                                            <i class="fas fa-tag"></i>
                                            Número do Selo
                                        </label>
                                        <div class="info-display compact-info" id="info_seal_number"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-field compact-field">
                                        <label class="form-label compact-label">
                                            <i class="fas fa-user"></i>
                                            Utilizado por
                                        </label>
                                        <div class="info-display compact-info" id="info_used_by"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-field compact-field">
                                        <label class="form-label compact-label">
                                            <i class="fas fa-calendar"></i>
                                            Data de Utilização
                                        </label>
                                        <div class="info-display compact-info" id="info_used_at"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-field compact-field" id="age_field" style="display: none;">
                                        <label class="form-label compact-label">
                                            <i class="fas fa-dove"></i>
                                            Classificação Etária
                                        </label>
                                        <div class="info-display compact-info" id="info_age"></div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-field compact-field" id="notes_field" style="display: none;">
                                        <label class="form-label compact-label">
                                            <i class="fas fa-info-circle"></i>
                                            Informações da Jornada
                                        </label>
                                        <div class="info-display compact-info" id="info_notes"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeSeloInfoModal()">
                        <i class="fas fa-times"></i>
                        Fechar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        function selectSelo(seloNumber, status, zoneId, isMyUsed, usedBy, usedAt, notes, usedByName, jornadaInfo, photoUrl, photoName, age) {
            if (status === 'used') {
                // Show seal info modal for all used seals
                document.getElementById('info_seal_number').textContent = seloNumber;
                document.getElementById('info_used_by').textContent = usedByName || usedBy || 'N/A';
                
                // Format the date
                let formattedDate = 'N/A';
                if (usedAt) {
                    const date = new Date(usedAt);
                    formattedDate = date.toLocaleDateString('pt-PT', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
                document.getElementById('info_used_at').textContent = formattedDate;
                
                // Show jornada info instead of raw notes
                const notesField = document.getElementById('notes_field');
                const notesDisplay = document.getElementById('info_notes');
                if (jornadaInfo && jornadaInfo.trim()) {
                    notesDisplay.textContent = jornadaInfo;
                    notesField.style.display = 'block';
                } else if (notes && notes.trim()) {
                    notesDisplay.textContent = notes;
                    notesField.style.display = 'block';
                } else {
                    notesField.style.display = 'none';
                }
                
                // Show age classification if available
                const ageField = document.getElementById('age_field');
                const ageDisplay = document.getElementById('info_age');
                if (age && age.trim()) {
                    if (ageDisplay) ageDisplay.textContent = age === 'adulto' ? 'Adulto' : 'Juvenil';
                    if (ageField) ageField.style.display = 'block';
                } else {
                    if (ageField) ageField.style.display = 'none';
                }
                
                // Add seal image (real photo or fallback)
                addSealImage(seloNumber, photoUrl, photoName);
                
                openSeloInfoModal();
            }
            // Available seals do nothing when clicked
        }

        function addSealImage(seloNumber, photoUrl, photoName) {
            const imageContainer = document.getElementById('seal_image_container');
            if (!imageContainer) return;
            
            if (photoUrl && photoUrl.trim() !== '') {
                // Show actual seal photo as thumbnail
                const imageHtml = `
                    <div style="position: relative; cursor: pointer;" onclick="openLightbox('${photoUrl}', 'Foto do Selo ${seloNumber}')">
                        <img id="seal_photo" 
                             src="${photoUrl}" 
                             alt="Foto do Selo ${seloNumber}"
                             style="width: 120px; height: 120px; object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.15);"
                             onload="this.style.opacity='1';"
                             onerror="showSealImageError(${seloNumber});"
                             />
                        <div style="position: absolute; top: 4px; right: 4px; background: rgba(10, 126, 164, 0.9); color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold;">
                            ${seloNumber}
                        </div>
                        <div style="position: absolute; bottom: 4px; left: 4px; right: 4px; background: rgba(0, 0, 0, 0.7); color: white; padding: 2px 4px; border-radius: 3px; font-size: 9px; text-align: center;">
                            <i class="fas fa-expand" style="margin-right: 2px;"></i>Clique para ampliar
                        </div>
                    </div>
                `;
                imageContainer.innerHTML = imageHtml;
            } else {
                // Show placeholder when no photo available
                const placeholderHtml = `
                    <div style="display: flex; flex-direction: column; align-items: center; padding: 1rem; color: #6b7280;">
                        <div style="width: 120px; height: 120px; border: 2px dashed #d1d5db; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                            <i class="fas fa-image" style="font-size: 1.5rem; margin-bottom: 0.25rem; color: #9ca3af;"></i>
                            <span style="font-size: 0.7rem;">Selo ${seloNumber}</span>
                        </div>
                        <span style="font-size: 0.75rem; margin-top: 0.5rem;">Sem foto</span>
                    </div>
                `;
                imageContainer.innerHTML = placeholderHtml;
            }
        }

        function showSealImageError(seloNumber) {
            const imageContainer = document.getElementById('seal_image_container');
            if (imageContainer) {
                const errorHtml = `
                    <div style="display: flex; flex-direction: column; align-items: center; padding: 1rem; color: #ef4444;">
                        <div style="width: 120px; height: 120px; border: 2px dashed #fca5a5; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; background: #fef2f2;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem; margin-bottom: 0.25rem;"></i>
                            <span style="font-size: 0.7rem;">Selo ${seloNumber}</span>
                        </div>
                        <span style="font-size: 0.75rem; margin-top: 0.5rem;">Erro ao carregar</span>
                    </div>
                `;
                imageContainer.innerHTML = errorHtml;
            }
        }

        // Lightbox functions
        function openLightbox(imageUrl, altText) {
            const lightbox = document.getElementById('sealLightbox');
            const lightboxImage = document.getElementById('lightboxImage');
            
            lightboxImage.src = imageUrl;
            lightboxImage.alt = altText;
            lightbox.style.display = 'block';
            
            // Prevent body scroll when lightbox is open
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox() {
            const lightbox = document.getElementById('sealLightbox');
            lightbox.style.display = 'none';
            
            // Restore body scroll
            document.body.style.overflow = 'auto';
        }

        // Close lightbox when clicking outside the image
        document.addEventListener('click', function(event) {
            const lightbox = document.getElementById('sealLightbox');
            if (event.target === lightbox) {
                closeLightbox();
            }
        });

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeLightbox();
            }
        });



        function openSeloInfoModal() {
            document.getElementById('seloInfoModal').style.display = 'flex';
        }

        function closeSeloInfoModal() {
            document.getElementById('seloInfoModal').style.display = 'none';
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(event) {
            const modals = ['seloInfoModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modals = ['seloInfoModal'];
                modals.forEach(modalId => {
                    document.getElementById(modalId).style.display = 'none';
                });
            }
        });

        function showSelosHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Listagem de Selos</div>',
                html: `
                    <div class="help-modal-content">
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-tag text-primary me-2"></i>
                                <span>Como funciona a listagem de selos</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-eye text-primary me-2"></i>
                                    <span><strong>Visualização:</strong> Esta página permite consultar todos os selos atribuídos às suas zonas de caça</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    <span><strong>Informações:</strong> Clique em selos utilizados para ver detalhes sobre quem e quando os utilizou</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-chart-bar text-success me-2"></i>
                                    <span><strong>Estatísticas:</strong> Visualize o resumo de selos disponíveis, utilizados e percentagem de quota usada</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-palette text-primary me-2"></i>
                                <span>Estados dos selos</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-status-item">
                                    <span class="help-status-badge help-status-available">
                                        Disponível
                                    </span>
                                    <span class="help-status-desc">Selo ainda não foi utilizado e está disponível para uso</span>
                                </div>
                                <div class="help-status-item">
                                    <span class="help-status-badge help-status-used">
                                        Utilizado
                                    </span>
                                    <span class="help-status-desc">Selo já foi usado por um gestor e não pode ser reutilizado</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-mouse-pointer text-primary me-2"></i>
                                <span>Interações disponíveis</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-hand-paper text-muted me-2"></i>
                                    <span><strong>Selos Disponíveis:</strong> Não são clicáveis - apenas para visualização do estado</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-cursor text-primary me-2"></i>
                                    <span><strong>Selos Utilizados:</strong> Clique para ver informações detalhadas (utilizador, data, notas)</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-lightbulb text-primary me-2"></i>
                                <span>Informações importantes</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-binoculars text-info me-2"></i>
                                    <span><strong>Organização:</strong> Os selos estão agrupados por zona de caça para facilitar a consulta</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-sort-numeric-up text-info me-2"></i>
                                    <span><strong>Ordenação:</strong> Os selos são apresentados por ordem numérica crescente</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-shield-alt text-warning me-2"></i>
                                    <span><strong>Apenas Consulta:</strong> Esta página é apenas para visualização - não permite marcar selos como usados</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span>Precisa de Ajuda?</span>
                            </div>
                            <div class="help-section-body">
                                <div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">
                                    <div style="color: #0a7ea4; font-size: 0.9rem;">
                                        Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>
                                        <strong><EMAIL></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }


    </script>
</body>
</html> 