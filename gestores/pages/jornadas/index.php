<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Get user information
$user_email = $_SESSION['user']['email'] ?? '';
$user_nif = $_SESSION['user']['nif'] ?? '';
$user_id = $_SESSION['user']['id'] ?? '';

// Use the global database instance (already initialized in config.php)
global $database;

// Set the user's auth token for authenticated requests
if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Get gestor zones and jornadas data
$gestorZones = [];
$gestorJornadas = [];
$error_message = '';
$success_message = '';

// Check for success message from session
if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

// Check for jornada update toast flag
$show_update_toast = false;
if (isset($_SESSION['jornada_updated'])) {
    $show_update_toast = true;
    unset($_SESSION['jornada_updated']);
}

// Handle delete action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $jornadaId = $_POST['jornada_id'] ?? '';
    if (!empty($jornadaId)) {
        try {
            $result = deleteJornadaFromFirestore($jornadaId, $database);
            if ($result) {
                $_SESSION['success_message'] = 'Jornada de caça eliminada com sucesso.';
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();
            } else {
                $error_message = 'Erro ao eliminar jornada de caça.';
            }
        } catch (Exception $e) {
            $error_message = 'Erro ao eliminar jornada: ' . $e->getMessage();
        }
    }
}

try {
    // Get zones managed by this gestor using NIF
    $gestorZones = getGestorZones($database, $user_nif);
    
    // Get jornadas created by this gestor
    $gestorJornadas = getGestorJornadas($database, $user_id);
    
    // Match zone names with jornadas
    foreach ($gestorJornadas as &$jornada) {
        if (empty($jornada['nomeZona']) && !empty($jornada['zonaId'])) {
            // Look for zone name in gestorZones
            foreach ($gestorZones as $zone) {
                if ($zone['id'] == $jornada['zonaId']) {
                    $jornada['nomeZona'] = $zone['nomeZona'];
                    break;
                }
            }
        }
    }
    unset($jornada); // Break the reference
    
} catch (Exception $e) {
    error_log("Jornadas page data error: " . $e->getMessage());
    $error_message = "Erro ao carregar dados das jornadas.";
}

/**
 * Get hunting zones for the current gestor
 */
function getGestorZones($database, $userNif) {
    try {
        // Use admin token for reading zones
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userNif)) {
            return [];
        }
        
        // Use Firestore query to filter by NIF directly
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        $allZones = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allZones[] = [
                    'id' => $docId,
                    'zona' => $data['zona'] ?? 'N/A',
                    'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                    'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                    'email' => $data['email'] ?? 'N/A',
                    'quotaZona' => $data['quotaZona'] ?? 0,
                    'minSelo' => $data['minSelo'] ?? 0,
                    'maxSelo' => $data['maxSelo'] ?? 0,
                    'localidade' => $data['localidade'] ?? '',
                    'status' => $data['status'] ?? 'not registered',
                    'registeredBy' => $data['registeredBy'] ?? null
                ];
            }
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getGestorZones: " . $e->getMessage());
        return [];
    }
}

/**
 * Get jornadas created by the current gestor
 */
function getGestorJornadas($database, $userId) {
    try {
        // Use admin token for reading jornadas
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId
        $result = $database->queryDocuments('jornadasCaca', 'createdBy', 'EQUAL', $userId);
        
        $allJornadas = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                // Parse selosAtribuidos if it's JSON
                $selosAtribuidos = $data['selosAtribuidos'] ?? '';
                $selosFormatted = '';
                $numeroAdultos = 0;
                $numeroJuvenis = 0;
                
                if (!empty($selosAtribuidos)) {
                    if (is_string($selosAtribuidos)) {
                        // Try to decode JSON
                        $selosJson = json_decode($selosAtribuidos, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($selosJson)) {
                            $selosNumbers = [];
                            foreach ($selosJson as $selo) {
                                if (isset($selo['number'])) {
                                    $selosNumbers[] = $selo['number'];
                                }
                                // Count adults and juveniles from seals data
                                if (isset($selo['age'])) {
                                    if ($selo['age'] === 'adulto') {
                                        $numeroAdultos++;
                                    } elseif ($selo['age'] === 'juvenil') {
                                        $numeroJuvenis++;
                                    }
                                }
                            }
                            $selosFormatted = implode(', ', array_filter($selosNumbers));
                        } else {
                            $selosFormatted = $selosAtribuidos;
                        }
                    } else {
                        $selosFormatted = $selosAtribuidos;
                    }
                }
                
                $allJornadas[] = [
                    'id' => $docId,
                    'data' => $data['data'] ?? '',
                    'zonaId' => $data['zonaId'] ?? '',
                    'nomeZona' => $data['nomeZona'] ?? '',
                    'numeroCacadores' => $data['numeroCacadores'] ?? 0,
                    'numeroAdultos' => $numeroAdultos, // Count from seals data
                    'numeroJuvenis' => $numeroJuvenis, // Count from seals data
                    'selosAtribuidos' => $selosFormatted,
                    'numeroAsas' => $data['numeroAsas'] ?? 0,
                    'numeroEsfregacos' => $data['numeroEsfregacos'] ?? 0,
                    'numeroZaragatoas' => $data['numeroZaragatoas'] ?? 0,
                    'outros' => $data['outros'] ?? '',
                    'fotoAsaDireita' => $data['fotoAsaDireita'] ?? '',
                    'createdBy' => $data['createdBy'] ?? '',
                    'createdAt' => $data['createdAt'] ?? null,
                    'updatedAt' => $data['updatedAt'] ?? null
                ];
            }
        }
        
        return $allJornadas;
    } catch (Exception $e) {
        error_log("Exception in getGestorJornadas: " . $e->getMessage());
        return [];
    }
}

/**
 * Delete jornada from Firestore
 */
function deleteJornadaFromFirestore($jornadaId, $database) {
    try {
        // Use admin token for deleting
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        return $database->deleteDocument('jornadasCaca', $jornadaId);
    } catch (Exception $e) {
        error_log("Exception in deleteJornadaFromFirestore: " . $e->getMessage());
        return false;
    }
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jornadas de Caça - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/dark-mode.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        .content {
            padding-top: 5rem !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
            padding-bottom: 0 !important;
        }

        .page-header {
            background: white;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 1.5rem;
            color: #374151;
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin: 0 1.5rem 1.5rem 1.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        /* Responsive stats grid */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
            display: flex;
                flex-wrap: nowrap;
                overflow-x: auto;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
                scrollbar-color: #cbd5e1 #f1f5f9;
                margin: 0 1rem 1.5rem 1rem;
                padding: 0 0.5rem 0.5rem 0.5rem;
        }
        
            .stats-grid::-webkit-scrollbar {
                height: 6px;
        }
        
            .stats-grid::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
            }

            .stats-grid::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
            }

            .stats-grid::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                margin: 0 0.5rem 1.5rem 0.5rem;
                padding: 0 0.25rem 0.5rem 0.25rem;
            }
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Responsive stat card adjustments */
        @media (max-width: 768px) {
            .stat-card {
                min-width: 280px;
                flex-shrink: 0;
                padding: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .stat-card {
                min-width: 260px;
                padding: 1rem;
            }
            
            .stat-card .icon {
                width: 36px;
                height: 36px;
            }
            
            .stat-card .icon i {
                font-size: 16px;
            }
            
            .stat-card .title {
                font-size: 0.8rem;
            }
            
            .stat-card .value {
                font-size: 1.25rem;
            }
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-card .icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card .icon i {
            font-size: 18px;
            color: white;
        }
        
        .stat-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-card .title {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 600;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        
        .stat-card .value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #374151;
            margin: 0;
        }

        .bg-blue {
            background-color: #0a7ea4;
        }
        
        .bg-purple {
            background-color: #9333ea;
        }

        .bg-green {
            background-color: #16a34a;
        }
        
        .bg-orange {
            background-color: #ea580c;
        }
        
        /* Table card styling */
        .zones-table-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin: 2rem 1.5rem 1.5rem 1.5rem;
            overflow: hidden;
        }

        .zones-table-header {
            padding: 2rem 1.5rem 1.5rem 1.5rem;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .zones-table-header h2 {
            margin: 0;
            font-size: 1.125rem;
            color: #374151;
            font-weight: 500;
        }

        .zones-table-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Add spacing to DataTables controls */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            margin-top: 1.5rem !important;
            margin-bottom: 1rem !important;
        }
        
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 1rem !important;
        }

        /* DataTable responsive wrapper */
        .zones-table-body table {
            min-width: 1200px; /* Minimum width to trigger horizontal scroll */
        }

        /* Custom scrollbar for table */
        .zones-table-body::-webkit-scrollbar {
            height: 8px;
        }
        
        .zones-table-body::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .zones-table-body::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        .zones-table-body::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Firefox scrollbar */
        .zones-table-body {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        /* Responsive table adjustments */
        @media (max-width: 768px) {
            .zones-table-card {
                margin: 0 1rem 1.5rem 1rem;
            }

            .zones-table-header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
        }
        
            .zones-table-body {
                padding: 0 0.5rem 1rem 0.5rem;
            }

            .zones-table-body table {
                min-width: 800px;
        }
        }

        @media (max-width: 480px) {
            .zones-table-card {
                margin: 0 0.5rem 1.5rem 0.5rem;
            }

            .zones-table-header {
                padding: 0.75rem;
            }

            .zones-table-header h2 {
                font-size: 1rem;
            }

            .zones-table-body {
                padding: 0 0.25rem 0.75rem 0.25rem;
            }
        }
        
        .text-center {
            text-align: center !important;
        }

        /* Prevent line breaking in table cells */
        table td {
            white-space: nowrap;
        }
        
        table td:nth-child(2) {
            white-space: normal; /* Allow wrapping only for zone name */
            min-width: 200px;
        }

        .text-muted {
            color: #9ca3af !important;
            font-size: 0.875rem;
        }

        .me-2 {
            margin-right: 0.5rem !important;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #15803d;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fecaca);
            color: #dc2626;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem 1rem 4rem 1rem;
            color: #6b7280;
        }
        
        .empty-state-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3);
        }
        
        .empty-state-icon i {
            font-size: 2rem;
            color: white;
        }
        
        .empty-state-title {
            color: #374151;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }
        
        .empty-state-text {
            color: #6b7280;
            font-size: 1rem;
            margin-bottom: 2rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .empty-state .btn {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .empty-state .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        /* Status badges */
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .badge-success {
            background-color: #d1fae5;
            color: #065f46;
        }

        .badge-warning {
            background-color: #fef3cd;
            color: #92400e;
        }

        .badge-info {
            background-color: #dbeafe;
            color: #1d4ed8;
        }

        .badge-primary {
            background-color: #e0f2fe;
            color: #0a7ea4;
        }

        .badge-small {
            padding: 0.2rem 0.5rem;
            font-size: 0.7rem;
            margin: 0.1rem 0;
        }

        /* Action buttons */
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 6px;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0a7ea4, #0891b2);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0369a1, #0a7ea4);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #16a34a, #22c55e);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #15803d, #16a34a);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #b91c1c, #dc2626);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
            color: white;
        }

        /* DataTables specific styling */
        .dataTables_length select {
            padding: 0.375rem 2rem 0.375rem 0.75rem !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
            background-position: right 0.5rem center !important;
            background-repeat: no-repeat !important;
            background-size: 1em 1em !important;
            border: 1px solid #d1d5db !important;
            border-radius: 6px !important;
            font-size: 0.875rem !important;
            line-height: 1.5 !important;
            min-width: 60px !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }

        .dataTables_length select:focus {
            border-color: #0a7ea4 !important;
            box-shadow: 0 0 0 2px rgba(10, 126, 164, 0.1) !important;
            outline: none !important;
        }

        .dataTables_length label {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            font-size: 0.875rem !important;
            color: #374151 !important;
            font-weight: 500 !important;
        }

        .dataTables_info {
            font-size: 0.875rem !important;
            color: #6b7280 !important;
        }

        /* DataTables Pagination - Exact copy from webadmin */
        .dataTables_paginate {
            margin-top: 1rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.375rem;
        }

        .dataTables_paginate .paginate_button {
            padding: 0.5rem 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            background: #fff;
            font-size: 0.875rem;
            color: #11181C !important;
        }

        .dataTables_paginate .paginate_button.current {
            background-color: #0a7ea4;
            color: white !important;
            border-color: #0a7ea4;
        }

        .dataTables_paginate .paginate_button:hover:not(.current) {
            background-color: rgba(10, 126, 164, 0.1);
            border-color: #0a7ea4;
        }

        /* Fix responsive adjustments */
        @media (max-width: 768px) {
            .zones-table-card {
                margin: 1.5rem 1rem 1.5rem 1rem;
            }
        }

        @media (max-width: 480px) {
            .zones-table-card {
                margin: 1rem 0.5rem 1.5rem 0.5rem;
            }
        }

        /*
        * DataTables Pagination Styling for Bootstrap 5
        * Goal: Replicate the layout and style from webadmin/zonas-caca
        */

        /* Restore the default two-column layout for the info/paginate row */
        #jornadasTable_wrapper .row:last-of-type {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-top: 1rem;
        }

        /* Ensure info text stays on the left */
        #jornadasTable_wrapper .dataTables_info {
            text-align: left;
            width: auto;
        }

        /* Ensure pagination stays on the right */
        #jornadasTable_wrapper .dataTables_paginate {
            text-align: right;
            width: auto;
        }
        
        #jornadasTable_wrapper .dataTables_paginate ul.pagination {
            justify-content: flex-end; /* Align buttons to the right within their container */
            gap: 0.375rem;
            margin: 0;
        }

        /* --- Individual Button Styling --- */
        #jornadasTable_wrapper .pagination .page-link {
            margin: 0;
            border-radius: 8px !important;
            box-shadow: none !important;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            color: #374151;
            transition: all 0.2s ease;
        }

        /* Hover state */
        #jornadasTable_wrapper .pagination .page-item:not(.active) .page-link:hover {
            background-color: #f3f4f6;
            border-color: #adb5bd;
            color: #1f2937;
        }

        /* Active/current page button */
        #jornadasTable_wrapper .pagination .page-item.active .page-link {
            background-color: #0a7ea4;
            border-color: #0a7ea4;
            color: white;
            z-index: 1;
        }
        #jornadasTable_wrapper .pagination .page-item.active .page-link:hover {
             background-color: #0a7ea4;
             border-color: #0a7ea4;
             color: white;
        }

        /* Disabled buttons (Anterior/Seguinte) */
        #jornadasTable_wrapper .pagination .page-item.disabled .page-link {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
            opacity: 0.7;
        }
    </style>
</head>
<body class="<?php echo isset($_COOKIE['darkMode']) && $_COOKIE['darkMode'] === 'true' ? 'dark-mode' : ''; ?>">
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-calendar-alt"></i>
                Jornadas de Caça
            </h1>
        </div>
        <div class="header-actions">
            <button class="btn btn-help" onclick="showJornadaHelp()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div>
            <!-- Alert Messages -->
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <div class="stats-grid">
                    <div class="stat-card">
                    <div class="icon bg-blue">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-content">
                        <div>
                            <div class="title">Total de Jornadas</div>
                            <div class="value"><?php echo count($gestorJornadas); ?></div>
                        </div>
                    </div>
                </div>
                    <div class="stat-card">
                    <div class="icon bg-green">
                            <i class="fas fa-binoculars"></i>
                        </div>
                        <div class="stat-content">
                        <div>
                            <div class="title">Zonas Disponíveis</div>
                            <div class="value"><?php echo count($gestorZones); ?></div>
                        </div>
                    </div>
                </div>
                    <div class="stat-card">
                    <div class="icon bg-green">
                        <i class="fas fa-dove"></i>
                        </div>
                        <div class="stat-content">
                        <div>
                            <div class="title">Rolas Adultos</div>
                            <div class="value"><?php echo array_sum(array_column($gestorJornadas, 'numeroAdultos')); ?></div>
                        </div>
                    </div>
                </div>
                    <div class="stat-card">
                    <div class="icon bg-orange">
                            <i class="fas fa-dove"></i>
                        </div>
                        <div class="stat-content">
                        <div>
                            <div class="title">Rolas Juvenis</div>
                            <div class="value"><?php echo array_sum(array_column($gestorJornadas, 'numeroJuvenis')); ?></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Jornadas Table -->
            <div class="zones-table-card">
                <div class="zones-table-header">
                    <h2>
                        <i class="fas fa-list-alt" style="color: #0a7ea4; margin-right: 0.5rem;"></i>
                        Lista de Jornadas de Caça
                    </h2>
                    <div class="header-actions">
                        <!-- Criar Jornada button hidden until August 24th -->
                        <!-- <a href="create.php" class="btn btn-success">
                            <i class="fas fa-plus"></i>
                            Criar Nova Jornada
                        </a> -->
                        <div style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.08) 100%); border: 1px solid rgba(245, 158, 11, 0.3); border-radius: 8px; padding: 0.75rem 1rem; font-size: 0.875rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                                <strong style="color: #92400e;">Aviso:</strong>
                                <span style="color: #92400e;">A criação de jornadas estará disponível a partir de 24 de Agosto</span>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (empty($gestorJornadas)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="empty-state-title">Nenhuma jornada registada</div>
                        <div class="empty-state-text">Ainda não tem jornadas de caça registadas.</div>
                        <!-- Criar Primeira Jornada button hidden until August 24th -->
                        <!-- <a href="create.php" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            Criar Primeira Jornada
                        </a> -->
<div style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.08) 100%);
            border: 1px solid rgba(245, 158, 11, 0.3); border-radius: 8px;
            padding: 1rem; margin: 1rem auto; text-align: center; max-width: 650px;">

    <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 0.75rem;">
        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
        <strong style="color: #92400e;">Aviso</strong>
    </div>

    <p style="margin: 0; color: #92400e; font-size: 0.9rem; line-height: 1.5;">
        A criação de jornadas estará disponível a partir de <strong>24 de Agosto</strong>.
    </p>

    <p style="margin: 0.5rem 0 0 0; color: #92400e; font-size: 0.85rem;">
        <i class="fas fa-lightbulb"></i>
        <strong>Dica:</strong> Certifique-se de que tem trajetos criados nas suas zonas de caça para poder criar jornadas.
    </p>
</div>

                    </div>
                <?php else: ?>
                    <div class="zones-table-body">
                        <table id="jornadasTable" class="table">
                            <thead>
                                <tr>
                                    <th class="text-center">Data</th>
                                    <th>Zona de Caça</th>
                                    <th class="text-center">Nº Caçadores</th>
                                    <th class="text-center">Adultos</th>
                                    <th class="text-center">Juvenis</th>
                                    <th class="text-center">Selos</th>
                                    <th class="text-center"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($gestorJornadas as $jornada): ?>
                                <tr>
                                    <td class="text-center align-middle">
                                        <strong><?php echo date('d/m/Y', strtotime($jornada['data'])); ?></strong>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-binoculars" style="color: #0a7ea4; margin-right: 0.5rem;"></i>
                                            <?php echo htmlspecialchars($jornada['nomeZona'] ?: 'N/A'); ?>
                                        </div>
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="badge badge-info">
                                            <?php echo $jornada['numeroCacadores']; ?>
                                        </span>
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="badge badge-success">
                                            <?php echo $jornada['numeroAdultos']; ?>
                                        </span>
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="badge badge-warning">
                                            <?php echo $jornada['numeroJuvenis']; ?>
                                        </span>
                                    </td>
                                    <td class="text-center align-middle">
                                        <?php if (!empty($jornada['selosAtribuidos'])): ?>
                                            <div class="d-flex flex-wrap justify-content-center gap-1">
                                                <?php 
                                                $selosArray = explode(', ', $jornada['selosAtribuidos']);
                                                foreach ($selosArray as $selo): 
                                                    if (trim($selo) !== ''):
                                                ?>
                                                <span class="badge badge-info badge-small">
                                                    <?php echo htmlspecialchars(trim($selo)); ?>
                                                </span>
                                                <?php 
                                                    endif;
                                                endforeach; 
                                                ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center align-middle">
                                        <a href="edit.php?id=<?php echo $jornada['id']; ?>" class="btn btn-sm btn-primary me-2">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </a>
                                        <button class="btn btn-sm btn-danger" onclick="confirmDelete('<?php echo $jornada['id']; ?>')">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Profile Modal -->
    <?php include '../../includes/profile_modal.php'; ?>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Eliminação</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem a certeza que deseja eliminar esta jornada de caça?</p>
                    <p class="text-muted small">Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="jornada_id" id="deleteJornadaId">
                        <button type="submit" class="btn btn-danger">Eliminar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#jornadasTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/pt-PT.json'
                },
                order: [[0, 'desc']], // Sort by date descending
                pageLength: 25
            });
            
            // Show update success toast if jornada was updated
            <?php if ($show_update_toast): ?>
            Swal.fire({
                icon: 'success',
                title: 'Jornada Atualizada!',
                text: 'Jornada de caça atualizada com sucesso.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
            <?php endif; ?>
        });
        
        function confirmDelete(jornadaId) {
            document.getElementById('deleteJornadaId').value = jornadaId;
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
        
        function showJornadaHelp() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Jornadas de Caça</div>',
                html: `
                    <div class="help-modal-content">
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-calendar-alt text-primary me-2"></i>
                                <span>Funcionalidades disponíveis</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-plus text-success me-2"></i>
                                    <span><strong>Criar Jornada:</strong> Adicione uma nova jornada de caça para uma zona específica</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-edit text-primary me-2"></i>
                                    <span><strong>Editar Jornada:</strong> Modifique jornadas já existentes com todos os detalhes</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-trash text-danger me-2"></i>
                                    <span><strong>Eliminar Jornada:</strong> Remova uma jornada do sistema (ação irreversível)</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                <span>Estados das jornadas</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span><strong>Jornada Completa:</strong> Todos os dados obrigatórios foram preenchidos</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    <span><strong>Dados Incompletos:</strong> Alguns campos ainda precisam de ser preenchidos</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-dove text-primary me-2"></i>
                                <span>Informações exibidas</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Data e Zona:</strong> Quando e onde decorreu a jornada de caça</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Participantes:</strong> Número total de caçadores envolvidos</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Resultados:</strong> Classificação etária das rolas caçadas (adultos/juvenis)</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-circle text-primary me-2"></i>
                                    <span><strong>Documentação:</strong> Selos utilizados, amostras recolhidas e fotografias</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span>Precisa de Ajuda?</span>
                            </div>
                            <div class="help-section-body">
                                <div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">
                                    <div style="color: #0a7ea4; font-size: 0.9rem;">
                                        Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>
                                        <strong><EMAIL></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }
    </script>
</body>
</html> 