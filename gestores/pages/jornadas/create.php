<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Get user information
$user_email = $_SESSION['user']['email'] ?? '';
$user_nif = $_SESSION['user']['nif'] ?? '';
$user_id = $_SESSION['user']['id'] ?? '';

// Use the global database instance (already initialized in config.php)
global $database;

// Set the user's auth token for authenticated requests
if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Get gestor zones
$gestorZones = [];
$error_message = '';
$success_message = '';

// Handle AJAX request for cleaning up seal photos
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (isset($input['action']) && $input['action'] === 'cleanup_seal_photos') {
        // Clean any previous output
        ob_end_clean();
        
        // Set response type
        header('Content-Type: application/json');
        
        error_log("Cleanup seal photos handler activated");
        
        try {
            if (!isset($input['photoUrls']) || !is_array($input['photoUrls'])) {
                throw new Exception('No photo URLs provided');
            }
            
            $photoUrls = $input['photoUrls'];
            $deletedCount = 0;
            $errors = [];
            
            // Use admin token for Firebase Storage operations
            $adminToken = $database->getAdminAccessToken();
            if (!$adminToken) {
                throw new Exception('Unable to get admin token');
            }
            
            foreach ($photoUrls as $photoUrl) {
                try {
                    // Extract file path from URL
                    $parsedUrl = parse_url($photoUrl);
                    if (!$parsedUrl || !isset($parsedUrl['path'])) {
                        continue;
                    }
                    
                    // Remove /v0/b/{bucket}/o/ prefix and decode
                    $path = $parsedUrl['path'];
                    if (preg_match('/\/v0\/b\/[^\/]+\/o\/(.+)/', $path, $matches)) {
                        $filePath = urldecode($matches[1]);
                        
                        // Delete from Firebase Storage
                        $result = $database->deleteStorageFile($filePath, $adminToken);
                        if ($result) {
                            $deletedCount++;
                            error_log("Deleted seal photo: " . $filePath);
                        } else {
                            $errors[] = "Failed to delete: " . $filePath;
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error deleting photo " . $photoUrl . ": " . $e->getMessage());
                    $errors[] = $e->getMessage();
                }
            }
            
            echo json_encode([
                'success' => true,
                'deletedCount' => $deletedCount,
                'errors' => $errors
            ]);
            
        } catch (Exception $e) {
            error_log("Cleanup seal photos error: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
        exit();
    }
}

// Handle AJAX request for uploading seal photos
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload_seal_photo') {
    // Clean any previous output
    ob_end_clean();
    
    // Set response type
    header('Content-Type: application/json');
    
    error_log("Upload seal photo handler activated");
    
    try {
        // Validate required fields
        if (!isset($_POST['sealNumber']) || !isset($_FILES['photo'])) {
            throw new Exception('Missing required data');
        }
        
        $sealNumber = $_POST['sealNumber'];
        $file = $_FILES['photo'];
        
        // Validate file upload
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error: ' . $file['error']);
        }
        
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('Tipo de ficheiro não suportado. Use JPG, PNG ou GIF.');
        }
        
        // Validate file size (5MB limit)
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('Ficheiro muito grande. Máximo 5MB.');
        }
        
        // Use admin token for Firebase Storage upload
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception('Unable to get admin token');
        }
        
        // Sanitize filename to avoid URL encoding issues (same pattern as existing gestores system)
        $originalName = basename($file['name']);
        $sanitizedName = preg_replace('/[^a-zA-Z0-9._-]/', '_', $originalName);
        $fileName = 'selos/' . $sealNumber . '_' . uniqid() . '_' . $sanitizedName;
        error_log("Uploading seal photo to: " . $fileName);
        
        $uploadResult = $database->uploadFileToStorage(
            $file['tmp_name'],
            $fileName,
            $file['type'],
            $adminToken
        );
        
        if ($uploadResult && isset($uploadResult['downloadUrl'])) {
            error_log("Seal photo upload successful: " . $uploadResult['downloadUrl']);
            echo json_encode([
                'success' => true,
                'photoUrl' => $uploadResult['downloadUrl'],
                'fileName' => $fileName
            ]);
        } else {
            error_log("Seal photo upload failed: " . print_r($uploadResult, true));
            throw new Exception('Falha no carregamento para o Firebase Storage');
        }
        
    } catch (Exception $e) {
        error_log("Upload seal photo error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit();
}

// Handle AJAX request for loading seals
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['ajax']) && $_GET['ajax'] === 'get_seals') {
    // Clean any previous output
    ob_end_clean();
    
    // Set response type
    header('Content-Type: application/json');
    
    // Add debug logging for AJAX handler
    error_log("AJAX handler activated - get_seals");
    error_log("Session user email: " . ($_SESSION['user']['email'] ?? 'not set'));
    error_log("User NIF: " . ($user_nif ?? 'not set'));
    
    $zoneId = $_GET['zoneId'] ?? '';
    error_log("Zone ID requested: " . $zoneId);
    
    if (empty($zoneId)) {
        error_log("Zone ID is empty");
        echo json_encode(['error' => 'Zone ID is required', 'debug' => 'zoneId parameter is empty']);
        exit();
    }
    
    try {
        // Get admin token to read data
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        // Verify user has access to this zone
        $userZones = getGestorZones($database, $user_nif);
        $hasAccess = false;
        $zoneInfo = null;
        
        foreach ($userZones as $zone) {
            if ($zone['id'] === $zoneId) {
                $hasAccess = true;
                $zoneInfo = $zone;
                break;
            }
        }
        
        if (!$hasAccess) {
            echo json_encode(['error' => 'No access to this zone']);
            exit();
        }
        
        // Get seals for this zone using the same function as in selos page
        $seals = getSealsForZone($database, $zoneId, $zoneInfo);
        
        // Filter only available seals
        $availableSeals = array_filter($seals, function($seal) {
            return $seal['status'] === 'available';
        });
        
        echo json_encode([
            'success' => true,
            'seals' => array_values($availableSeals),
            'zoneName' => $zoneInfo['nomeZona']
        ]);
        exit();
        
    } catch (Exception $e) {
        error_log("Get zone seals error (inline): " . $e->getMessage());
        echo json_encode(['error' => 'Failed to load seals', 'debug' => $e->getMessage()]);
        exit();
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create') {
    // Clean any previous output
    ob_end_clean();
    
    // Set response type
    header('Content-Type: application/json');
    
    try {
        // Validate required fields
        $requiredFields = [
            'data' => 'Data da jornada',
            'zonaId' => 'Zona de caça',
            'numeroCacadores' => 'Número de caçadores'
        ];
        
        $errors = [];
        foreach ($requiredFields as $field => $label) {
            if (empty($_POST[$field])) {
                $errors[] = "$label é obrigatório.";
            }
        }
        
        if (!empty($errors)) {
            echo json_encode([
                'success' => false,
                'error' => implode('<br>', $errors)
            ]);
            exit();
        }
        
        // Find the zone name
        $zoneName = '';
        $gestorZones = getGestorZones($database, $user_nif);
        foreach ($gestorZones as $zone) {
            if ($zone['id'] === $_POST['zonaId']) {
                $zoneName = $zone['nomeZona'];
                break;
            }
        }
        
        // Process seal data (photos should already be uploaded)
        $selosData = json_decode($_POST['selosAtribuidos'] ?? '[]', true);
        error_log("Create jornada - selosData: " . print_r($selosData, true));
        
        if (is_array($selosData)) {
            foreach ($selosData as &$selo) {
                // Photos should already be uploaded and have photoUrl
                if (isset($selo['photoUrl'])) {
                    $selo['fotoAsaDireita'] = $selo['photoUrl'];
                    error_log("Create jornada - Using pre-uploaded photo for seal " . $selo['number'] . ": " . $selo['photoUrl']);
                } else {
                    error_log("Create jornada - No photo URL found for seal " . $selo['number']);
                }
            }
        }
        
        // Prepare jornada data
        $jornadaData = [
            'data' => $_POST['data'],
            'zonaId' => $_POST['zonaId'],
            'nomeZona' => $zoneName,
            'numeroCacadores' => (int)$_POST['numeroCacadores'],
            'selosAtribuidos' => json_encode($selosData),
            'numeroAsas' => (int)($_POST['numeroAsas'] ?? 0),
            'numeroEsfregacos' => (int)($_POST['numeroEsfregacos'] ?? 0),
            'numeroZaragatoas' => (int)($_POST['numeroZaragatoas'] ?? 0),
            'outros' => $_POST['outros'] ?? '',
            'createdBy' => $user_id,
            'createdAt' => date('Y-m-d\TH:i:s\Z'),
            'updatedAt' => date('Y-m-d\TH:i:s\Z')
        ];
        
        // Save to Firestore
        $result = createJornadaInFirestore($jornadaData, $database);
        if ($result) {
            error_log("Jornada created successfully - returning success response");
            echo json_encode([
                'success' => true,
                'message' => 'Jornada de caça criada com sucesso.',
                'redirect' => 'index.php'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Erro ao criar jornada de caça.'
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Create jornada error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Erro ao criar jornada: ' . $e->getMessage()
        ]);
    }
    exit();
}

try {
    // Get zones managed by this gestor using NIF
    $gestorZones = getGestorZones($database, $user_nif);
} catch (Exception $e) {
    error_log("Create jornada page data error: " . $e->getMessage());
    $error_message = "Erro ao carregar dados das zonas.";
}

/**
 * Get hunting zones for the current gestor (all zones with trajectory status)
 */
function getGestorZones($database, $userNif) {
    try {
        // Use admin token for reading zones
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userNif)) {
            return [];
        }
        
        // Get all zones for this gestor
        $zonesResult = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        if (!$zonesResult || !is_array($zonesResult)) {
            return [];
        }
        
        $allZones = [];
        
        foreach ($zonesResult as $docId => $data) {
            // Check if this zone has trajectory data in the 'zonas' collection
            $hasTrajectory = false;
            
            try {
                // Query trajectories for this specific zone
                $trajectoryResult = $database->queryDocuments('zonas', 'zoneId', 'EQUAL', $docId);
                
                if ($trajectoryResult && is_array($trajectoryResult) && !empty($trajectoryResult)) {
                    // Check if any trajectory has coordinates
                    foreach ($trajectoryResult as $trajDocId => $trajData) {
                        if (isset($trajData['coordinates']) && is_array($trajData['coordinates']) && !empty($trajData['coordinates'])) {
                            $hasTrajectory = true;
                            break;
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("Error checking trajectory for zone $docId: " . $e->getMessage());
                // Continue with hasTrajectory = false for this zone
            }
            
            // Add all zones with trajectory status
            $allZones[] = [
                'id' => $docId,
                'zona' => $data['zona'] ?? 'N/A',
                'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                'email' => $data['email'] ?? 'N/A',
                'quotaZona' => $data['quotaZona'] ?? 0,
                'minSelo' => $data['minSelo'] ?? 0,
                'maxSelo' => $data['maxSelo'] ?? 0,
                'localidade' => $data['localidade'] ?? '',
                'status' => $data['status'] ?? 'not registered',
                'registeredBy' => $data['registeredBy'] ?? null,
                'hasTrajectory' => $hasTrajectory
            ];
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getGestorZones: " . $e->getMessage());
        return [];
    }
}

/**
 * Get seals for a specific zone
 */
function getSealsForZone($database, $zoneId, $zoneInfo) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        $minSelo = intval($zoneInfo['minSelo']);
        $maxSelo = intval($zoneInfo['maxSelo']);
        
        if ($minSelo <= 0 || $maxSelo <= 0 || $minSelo > $maxSelo) {
            return [];
        }
        
        // Get used seals from Firestore for this specific zone with full pagination
        $usedSeals = [];
        try {
            $allSeals = [];
            $pageToken = null;
            
            do {
                $result = $database->listDocuments('selosUsados', $pageToken, 1000);
                
                if (!$result || !is_array($result)) {
                    break;
                }
                
                // Extract pagination token if present
                $pageToken = $result['_nextPageToken'] ?? null;
                unset($result['_nextPageToken']); // Remove pagination token from results
                
                // Merge results
                $allSeals = array_merge($allSeals, $result);
                
            } while ($pageToken);
            
            // Process all seals
            foreach ($allSeals as $docId => $data) {
                // Only include seals for this zone
                if (isset($data['zoneId']) && $data['zoneId'] === $zoneId && isset($data['seloNumber'])) {
                    $usedSeals[$data['seloNumber']] = [
                        'seloNumber' => $data['seloNumber'],
                        'zoneId' => $data['zoneId'],
                        'usedAt' => $data['usedAt'],
                        'usedBy' => $data['usedBy'],
                        'notes' => $data['notes'] ?? ''
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("Error getting used seals for zone $zoneId: " . $e->getMessage());
        }
        
        // Generate all seals in range for this zone
        $allSelos = [];
        for ($seloNum = $minSelo; $seloNum <= $maxSelo; $seloNum++) {
            $isUsed = isset($usedSeals[$seloNum]);
            
            $allSelos[] = [
                'id' => 'selo_' . $seloNum,
                'seloNumber' => $seloNum,
                'zoneId' => $zoneId,
                'status' => $isUsed ? 'used' : 'available',
                'year' => date('Y'),
                'usedAt' => $isUsed ? $usedSeals[$seloNum]['usedAt'] : null,
                'usedBy' => $isUsed ? $usedSeals[$seloNum]['usedBy'] : null,
                'notes' => $isUsed ? $usedSeals[$seloNum]['notes'] : '',
                'createdAt' => null
            ];
        }
        
        return $allSelos;
    } catch (Exception $e) {
        error_log("Exception in getSealsForZone: " . $e->getMessage());
        return [];
    }
}

/**
 * Create jornada in Firestore
 */
function createJornadaInFirestore($jornadaData, $database) {
    try {
        // Use admin token for creating
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        // Use addDocument method (not createDocument)
        $documentId = $database->addDocument('jornadasCaca', $jornadaData);
        
        // Log success
        error_log("Jornada created successfully with ID: " . $documentId);
        
        return $documentId;
    } catch (Exception $e) {
        error_log("Exception in createJornadaInFirestore: " . $e->getMessage());
        return false;
    }
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar Jornada de Caça - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/dark-mode.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        /* Modern Professional Design */
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .form-container {
            background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.08),
                0 8px 40px rgba(0, 0, 0, 0.04),
                0 16px 64px rgba(0, 0, 0, 0.02);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }
        
        .form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #0a7ea4 0%, #0891b2 25%, #06b6d4 50%, #0891b2 75%, #0a7ea4 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid rgba(226, 232, 240, 0.6);
            position: relative;
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .form-section h3 {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .form-section h3 i {
            color: #0a7ea4;
            font-size: 1.25rem;
            text-shadow: 0 2px 4px rgba(10, 126, 164, 0.2);
        }
        
        .form-label {
            font-weight: 600 !important;
            color: #1e293b !important;
            margin-bottom: 0.5rem !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            font-size: 0.95rem !important;
            letter-spacing: 0.025em !important;
        }
        
        .form-control, .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            font-weight: 500;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 1px 3px rgba(0, 0, 0, 0.05),
                inset 0 1px 2px rgba(255, 255, 255, 0.5);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #0a7ea4;
            background: #ffffff;
            box-shadow: 
                0 0 0 4px rgba(10, 126, 164, 0.15),
                0 4px 12px rgba(10, 126, 164, 0.1),
                inset 0 1px 2px rgba(255, 255, 255, 0.8);
            transform: translateY(-1px);
        }
        
        .form-control:hover:not(:focus), .form-select:hover:not(:focus) {
            border-color: #cbd5e1;
            transform: translateY(-0.5px);
            box-shadow: 
                0 2px 8px rgba(0, 0, 0, 0.08),
                inset 0 1px 2px rgba(255, 255, 255, 0.6);
        }
        
        /* Simple button styling to match index.php */
        .btn-submit {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: white !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 0.375rem !important;
            font-weight: 400 !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
            border: 1px solid transparent !important;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }
        
        .btn-submit:hover {
            background-color: #157347 !important;
            border-color: #146c43 !important;
            color: white !important;
        }
        
        .btn-cancel {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: white !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 0.375rem !important;
            font-weight: 400 !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
            border: 1px solid transparent !important;
            margin-right: 1rem !important;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }
        
        .btn-cancel:hover {
            background-color: #5c636a !important;
            border-color: #565e64 !important;
            color: white !important;
        }
        
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #15803d;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fecaca);
            color: #dc2626;
        }
        
        /* Dropzone Styles */
        .dropzone-container {
            width: 100%;
        }
        
        .dropzone-area {
            position: relative;
            min-height: 140px;
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            background: linear-gradient(145deg, #f9fafb 0%, #f3f4f6 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            overflow: hidden;
        }
        
        .dropzone-area:hover {
            border-color: #0a7ea4;
            background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.15);
        }
        
        .dropzone-area.dragover {
            border-color: #0a7ea4;
            background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(10, 126, 164, 0.25);
        }
        
        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem 1.5rem;
            text-align: center;
            min-height: 140px;
        }
        
        .dropzone-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3);
            transition: all 0.3s ease;
        }
        
        .dropzone-area:hover .dropzone-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(10, 126, 164, 0.4);
        }
        
        .dropzone-text {
            color: #374151;
        }
        
        .dropzone-main {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 0.25rem 0;
            color: #1f2937;
        }
        
        .dropzone-sub {
            font-size: 0.875rem;
            margin: 0;
            color: #6b7280;
        }
        
        .dropzone-preview {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #16a34a;
            border-radius: 12px;
            min-height: 140px;
            position: relative;
        }
        
        .preview-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 12px;
            border: 3px solid #16a34a;
            box-shadow: 0 4px 12px rgba(22, 163, 74, 0.25);
        }
        
        .preview-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding-right: 2rem;
        }
        
        .preview-name {
            font-weight: 600;
            color: #16a34a;
            font-size: 1rem;
            word-break: break-word;
        }
        
        .preview-size {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .preview-remove {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            width: 32px;
            height: 32px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }
        
        .preview-remove:hover {
            background: #dc2626;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
            align-items: end;
        }
        
        .form-row .col {
            flex: 1;
        }
        
        .info-text {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.5rem;
            font-style: italic;
        }
        
        .required {
            color: #ef4444;
            font-weight: 600;
            margin-left: 0.25rem;
            font-size: 0.9rem;
            text-shadow: 0 1px 2px rgba(239, 68, 68, 0.2);
        }
        
        /* Advanced Seal Selection Styles with 3D Animations */
        .seals-selection-container {
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid rgba(226, 232, 240, 0.8);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(5px);
        }
        

        
        .seals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
            gap: 0.75rem;
            padding: 0.75rem;
            position: relative;
            z-index: 1;
        }
        
        .seal-card-mini {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #10b981;
            border-radius: 16px;
            padding: 1rem 0.5rem; /* Increased top/bottom padding */
            text-align: center;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 65px; /* Increased height to give more space */
            font-weight: 700;
            font-size: 1rem;
            color: #10b981;
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 3px 6px rgba(16, 185, 129, 0.15),
                0 6px 12px rgba(0, 0, 0, 0.08),
                inset 0 1px 2px rgba(255, 255, 255, 0.8);
            animation: sealAppear 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            transform: scale(0.8) translateY(10px);
        }
        
        @keyframes sealAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(10px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0px);
            }
        }
        
        .seal-card-mini.appeared {
            opacity: 1;
            transform: scale(1) translateY(0px);
        }
        
        .seal-card-mini::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }
        
        .seal-card-mini:hover,
        .seal-card-mini.appeared:hover {
            transform: translateY(-6px) rotateX(3deg) rotateY(1deg) scale(1.08) !important;
            box-shadow: 
                0 10px 25px rgba(16, 185, 129, 0.25),
                0 15px 35px rgba(0, 0, 0, 0.12),
                inset 0 2px 4px rgba(255, 255, 255, 0.9);
            border-color: #059669;
            z-index: 10;
        }
        
        .seal-card-mini:hover::before {
            left: 100%;
        }
        
        .seal-card-mini.selected {
            border-color: #0a7ea4;
            background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
            color: #0a7ea4;
            transform: translateY(-4px) rotateX(5deg) scale(1.02);
            box-shadow: 
                0 8px 20px rgba(10, 126, 164, 0.3),
                0 16px 32px rgba(0, 0, 0, 0.12),
                inset 0 2px 4px rgba(255, 255, 255, 0.9);
            animation: selectedPulse 2s ease-in-out infinite;
        }
        
        @keyframes selectedPulse {
            0%, 100% {
                box-shadow: 
                    0 8px 20px rgba(10, 126, 164, 0.3),
                    0 16px 32px rgba(0, 0, 0, 0.12),
                    inset 0 2px 4px rgba(255, 255, 255, 0.9);
            }
            50% {
                box-shadow: 
                    0 12px 30px rgba(10, 126, 164, 0.4),
                    0 20px 40px rgba(0, 0, 0, 0.15),
                    inset 0 2px 4px rgba(255, 255, 255, 0.95);
            }
        }
        
        .seal-card-mini.selected:hover,
        .seal-card-mini.selected.appeared:hover {
            transform: translateY(-8px) rotateX(5deg) rotateY(-2deg) scale(1.12) !important;
            box-shadow: 
                0 12px 30px rgba(10, 126, 164, 0.3),
                0 20px 40px rgba(0, 0, 0, 0.15),
                inset 0 3px 6px rgba(255, 255, 255, 0.95);
            z-index: 15;
        }
        
        .seal-card-mini.selected::after {
            display: none; /* Hide original blue circle */
        }
        
        .seal-card-mini.selected::before {
            display: none; /* Hide original checkmark */
        }
        
        /* Alternative approach using FontAwesome icon as background */
.seal-card-mini.selected .seal-number::after {
content: '\f4ba';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: -19px;
    right: 16px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #0a7ea4 0%, #06b6d4 100%);
    color: #fff;
    border-radius: 50%;
    border: 1px solid white;
    box-shadow: 0 2px 8px rgba(10, 126, 164, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    z-index: 3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: checkmarkBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transform: none !important;
    transition: none !important;
}
        
        /* Ensure dove stays in place during all hover states */
        .seal-card-mini.selected:hover .seal-number::after,
        .seal-card-mini.selected.appeared:hover .seal-number::after {
            transform: none !important;
            transition: none !important;
        }
        
        @keyframes checkmarkBounce {
            0% {
                transform: scale(0) rotate(-180deg);
            }
            50% {
                transform: scale(1.3) rotate(-90deg);
            }
            100% {
                transform: scale(1) rotate(0deg);
            }
        }
        
        /* Clean seal card styling */
        .seal-card-mini {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .seal-number {
            flex: 1;
            text-align: center;
            position: relative; /* For positioning the dove icon */
        }
        

        
        /* Seal Selection Modal */
        .seal-modal {
            display: none;
            position: fixed;
            z-index: 1055;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }
        
        .seal-modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: none;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalFadeIn 0.3s ease-out;
        }
        
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .seal-modal-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 20px 20px 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .seal-modal-header h5 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .seal-modal-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .seal-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }
        
        .seal-modal-body {
            padding: 2rem;
        }
        
        .seal-modal-footer {
            padding: 0 2rem 1.5rem 2rem;
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            flex-wrap: nowrap;
        }
        
        .seal-modal-footer .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
            border-radius: 8px;
            font-weight: 600;
            white-space: nowrap;
            min-width: fit-content;
        }
        
        .age-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .age-option {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .age-option:hover {
            border-color: #0a7ea4;
            background: #f0f9ff;
        }
        
        .age-option.selected {
            border-color: #0a7ea4;
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
            color: #0a7ea4;
        }
        
        .age-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #0a7ea4;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .age-icon.juvenil {
            font-size: 1.5rem;
        }
        
        .age-label {
            font-weight: 600;
            font-size: 1.1rem;
        }
        

        
        /* Seals Placeholder Styling */
        .seals-placeholder {
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px dashed rgba(10, 126, 164, 0.3);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .seals-placeholder:hover {
            border-color: rgba(10, 126, 164, 0.5);
            background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        
        .placeholder-content {
            position: relative;
            z-index: 1;
        }
        
        .placeholder-icon {
            font-size: 2rem;
            color: #0a7ea4;
            margin-bottom: 0.5rem;
            opacity: 0.6;
            transition: all 0.3s ease;
        }
        
        .seals-placeholder:hover .placeholder-icon {
            opacity: 0.8;
            transform: scale(1.1);
        }
        
        .placeholder-title {
            color: #0a7ea4;
            font-weight: 600;
            margin-bottom: 0.4rem;
            font-size: 1.1rem;
        }
        
        .placeholder-text {
            color: #6b7280;
            margin: 0;
            font-size: 0.9rem;
            font-weight: 400;
        }
        
        .seals-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(10, 126, 164, 0.05), transparent);
            animation: placeholderShimmer 3s ease-in-out infinite;
        }
        
        @keyframes placeholderShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        /* Custom SweetAlert2 Styling to match app theme */
        .custom-swal-popup {
            border-radius: 12px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
            font-family: inherit !important;
        }

        .custom-swal-title {
            color: #374151 !important;
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
        }

        .custom-swal-content {
            color: #6b7280 !important;
            font-size: 0.95rem !important;
            line-height: 1.5 !important;
        }

        .custom-swal-confirm {
            background-color: #0a7ea4 !important;
            border: none !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 0.625rem 1.25rem !important;
            font-size: 0.875rem !important;
            color: white !important;
        }

        .custom-swal-confirm:hover {
            background-color: #0891b2 !important;
            color: white !important;
        }

        .custom-swal-cancel {
            background-color: #6b7280 !important;
            border: none !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 0.625rem 1.25rem !important;
            font-size: 0.875rem !important;
            color: white !important;
        }

        .custom-swal-cancel:hover {
            background-color: #4b5563 !important;
            color: white !important;
        }
        
        .custom-swal-actions {
            gap: 1rem !important;
            padding: 0 1rem !important;
        }
        
        /* Custom Select Styles */
        .custom-select {
            position: relative;
            width: 100%;
        }
        
        .custom-select-trigger {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 1rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
        }
        
        .custom-select-trigger:hover {
            border-color: #0a7ea4;
        }
        
        .custom-select-trigger.active {
            border-color: #0a7ea4;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
        }
        
        .custom-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .custom-select-option {
            padding: 0.75rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background-color 0.2s ease;
            font-size: 1rem;
        }
        
        .custom-select-option:hover:not(.disabled) {
            background-color: #f3f4f6;
        }
        
        .custom-select-option.disabled {
            color: #9ca3af;
            cursor: not-allowed;
            font-style: italic;
        }
        
        .custom-select-option.disabled i {
            color: #f59e0b !important;
        }
        
        /* Ensure SweetAlert appears above all modals */
        .swal2-container {
            z-index: 10000 !important;
        }
        
        .swal2-popup {
            z-index: 10001 !important;
        }
        


        /* Section Styling */
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            color: #0a7ea4;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label i {
            color: #0a7ea4;
            font-size: 0.875rem;
        }
        
        .required {
            color: #dc2626;
            font-weight: 600;
        }
        
        .form-control,
        .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 1rem;
            transition: all 0.2s ease;
        }
        
        .form-control:focus,
        .form-select:focus {
            border-color: #0a7ea4;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
            outline: none;
        }
        
        /* Placeholder styling */
        .seals-placeholder {
            background: #f9fafb;
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 1.5rem 2rem;
            text-align: center;
        }
        
        .placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }
        
        .placeholder-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0a7ea4, #0891b2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem auto;
        }
        
        .placeholder-title {
            color: #0a7ea4;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }
        
        .placeholder-text {
            color: #6b7280;
            margin: 0;
        }
    </style>
</head>
<body class="<?php echo isset($_COOKIE['darkMode']) && $_COOKIE['darkMode'] === 'true' ? 'dark-mode' : ''; ?>">
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-plus"></i>
                Criar Nova Jornada de Caça
            </h1>
        </div>
        <div class="header-actions">
            <button type="button" class="btn btn-help" onclick="showHelpModal()">
                <i class="fas fa-question-circle"></i>
                Ajuda
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div>
            <!-- Alert Messages -->
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Create Form -->
            <div class="zones-table-wrapper">
                <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2" style="color: #0a7ea4;"></i>
                        Informações
                    </h5>
                </div>
                
                <div class="p-4">
                    <form id="jornadaForm" method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" enctype="multipart/form-data" novalidate>
                        <input type="hidden" name="action" value="create">
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="zonaId" class="form-label">
                                        <i class="fas fa-binoculars me-1" style="color: #0a7ea4;"></i>
                                        Zona de Caça <span class="required">*</span>
                                    </label>
                                    <div class="position-relative">
                                        <div class="custom-select" id="zonaSelect">
                                            <div class="custom-select-trigger" onclick="toggleZoneDropdown()">
                                                <span id="zoneSelectedText">Seleccione uma zona</span>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="custom-select-options" id="zoneOptions" style="display: none;">
                                                <?php foreach ($gestorZones as $zone): ?>
                                                    <?php if ($zone['hasTrajectory']): ?>
                                                <div class="custom-select-option" data-value="<?php echo $zone['id']; ?>" onclick="selectZone('<?php echo $zone['id']; ?>', '<?php echo htmlspecialchars($zone['nomeZona'], ENT_QUOTES); ?>')">
                                                    <i class="fas fa-binoculars" style="color: #0a7ea4; margin-right: 8px;"></i>
                                                    <?php echo htmlspecialchars($zone['nomeZona']); ?>
                                                </div>
                                                    <?php else: ?>
                                                <div class="custom-select-option disabled">
                                                    <i class="fas fa-exclamation-triangle" style="color: #f59e0b; margin-right: 8px;"></i>
                                                    <?php echo htmlspecialchars($zone['nomeZona']); ?> - Necessita Trajeto
                                                </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                        <input type="hidden" id="zonaId" name="zonaId" value="<?php echo $_POST['zonaId'] ?? ''; ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="data" class="form-label">
                                        <i class="fas fa-calendar-alt me-1" style="color: #0a7ea4;"></i>
                                        Data da Jornada <span class="required">*</span>
                                    </label>
                                    <div class="position-relative">
                                        <div class="custom-select" id="dataSelect">
                                            <div class="custom-select-trigger" onclick="toggleDateDropdown()">
                                                <span id="dateSelectedText">Seleccione a data</span>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="custom-select-options" id="dateOptions" style="display: none;">
                                                <div class="custom-select-option" data-value="2025-08-24" onclick="selectDate('2025-08-24', '24 de agosto de 2025')">
                                                    <i class="fas fa-calendar-alt" style="color: #0a7ea4; margin-right: 8px;"></i>
                                                    24 de agosto de 2025
                                                </div>
                                                <div class="custom-select-option" data-value="2025-08-31" onclick="selectDate('2025-08-31', '31 de agosto de 2025')">
                                                    <i class="fas fa-calendar-alt" style="color: #0a7ea4; margin-right: 8px;"></i>
                                                    31 de agosto de 2025
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="data" name="data" value="<?php echo $_POST['data'] ?? ''; ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="numeroCacadores" class="form-label">
                                        <i class="fas fa-users me-1" style="color: #0a7ea4;"></i>
                                        Número de Caçadores <span class="required">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="numeroCacadores" name="numeroCacadores" min="1" max="999" value="<?php echo $_POST['numeroCacadores'] ?? ''; ?>" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                </div>
                            </div>
                        </div>

                        <!-- Seals Section -->
                        <div class="mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-tag me-2" style="color: #0a7ea4;"></i>
                                Selos Disponíveis
                            </h6>
                            <p class="text-muted mb-3" style="font-size: 0.875rem;">Clique nos selos para seleccioná-los. Selos seleccionados aparecerão marcados a azul.</p>
                            
                            <div id="sealsLoadingMessage" class="seals-placeholder">
                                <div class="placeholder-content">
                                    <div class="placeholder-icon">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    <h5 class="placeholder-title">Selos Disponíveis</h5>
                                    <p class="placeholder-text">Seleccione uma zona de caça para ver os selos disponíveis</p>
                                </div>
                            </div>
                            <div id="sealsContainer" class="seals-selection-container" style="display: none;"></div>
                            <input type="hidden" id="selectedSealsData" name="selosAtribuidos" value="<?php echo htmlspecialchars($_POST['selosAtribuidos'] ?? ''); ?>">
                        </div>
                        
                        <!-- Scientific Studies Section -->
                        <div class="mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-microscope me-2" style="color: #0a7ea4;"></i>
                                Contribuição para Estudos Complementares
                                <span class="badge bg-secondary" style="margin-left: 10px;">
                                    <i class="fas fa-info-circle" style="font-size: 0.75rem;color:#fff"></i>Opcional
                                </span>
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="numeroAsas" class="form-label">
                                            <i class="fas fa-feather-alt me-1" style="color: #0a7ea4;"></i>
                                            Número de Asas
                                        </label>
                                        <input type="number" class="form-control" id="numeroAsas" name="numeroAsas" min="0" max="9999" value="<?php echo $_POST['numeroAsas'] ?? '0'; ?>" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="numeroEsfregacos" class="form-label">
                                            <i class="fas fa-tint me-1" style="color: #0a7ea4;"></i>
                                            Número de Esfregaços de Sangue
                                        </label>
                                        <input type="number" class="form-control" id="numeroEsfregacos" name="numeroEsfregacos" min="0" max="9999" value="<?php echo $_POST['numeroEsfregacos'] ?? '0'; ?>" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="numeroZaragatoas" class="form-label">
                                            <i class="fas fa-vial me-1" style="color: #0a7ea4;"></i>
                                            Número de Zaragatoas
                                        </label>
                                        <input type="number" class="form-control" id="numeroZaragatoas" name="numeroZaragatoas" min="0" max="9999" value="<?php echo $_POST['numeroZaragatoas'] ?? '0'; ?>" oninput="this.value = this.value.replace(/[^0-9]/g, '')" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="outros" class="form-label">
                                            <i class="fas fa-plus-circle me-1" style="color: #0a7ea4;"></i>
                                            Outros
                                        </label>
                                        <input type="text" class="form-control" id="outros" name="outros" placeholder="Outros materiais..." value="<?php echo $_POST['outros'] ?? ''; ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 pt-3 border-top">
                            <button type="button" class="btn btn-secondary" onclick="handleCancel()">
                                <i class="fas fa-times me-2"></i>
                                Cancelar
                            </button>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="fas fa-save me-2"></i>
                                Criar Jornada
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Seal Selection Modal -->
    <div class="seal-modal" id="sealModal">
        <div class="seal-modal-content">
            <div class="seal-modal-header">
                <h5>
                    <i class="fas fa-tag"></i>
                                            Selo <span id="modalSealNumber"></span>
                </h5>
                <button type="button" class="seal-modal-close" onclick="closeSealModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="seal-modal-body">
                <div class="mb-3">
                    <label class="form-label">Classificação Etária <span class="required">*</span></label>
                    <div class="age-selection">
                        <div class="age-option" data-age="adulto" onclick="selectAge('adulto')">
                            <div class="age-icon">
                                <i class="fas fa-dove"></i>
                            </div>
                            <div class="age-label">Adulto</div>
                        </div>
                        <div class="age-option" data-age="juvenil" onclick="selectAge('juvenil')">
                            <div class="age-icon juvenil">
                                <i class="fas fa-dove"></i>
                            </div>
                            <div class="age-label">Juvenil</div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="modalFotoAsaDireita" class="form-label">Foto da Asa Direita <span class="required">*</span></label>
                    <div class="dropzone-container" id="photoDropzone">
                        <input type="file" class="file-input" id="modalFotoAsaDireita" accept="image/*" style="display: none;">
                        <div class="dropzone-area" id="dropzoneArea">
                            <div class="dropzone-content" id="dropzoneContent">
                                <div class="dropzone-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="dropzone-text">
                                    <p class="dropzone-main">Arraste a foto aqui ou clique para seleccionar</p>
                                    <p class="dropzone-sub">Formatos aceites: JPG, PNG, GIF (máx. 5MB)</p>
                                </div>
                            </div>
                            <div class="dropzone-preview" id="dropzonePreview" style="display: none;">
                                <img class="preview-image" id="previewImage" src="" alt="Preview">
                                <div class="preview-info">
                                    <div class="preview-name" id="previewName"></div>
                                    <div class="preview-size" id="previewSize"></div>
                                    <button type="button" class="preview-remove" id="removePhoto">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="info-text">Anexe foto da asa direita para documentação</div>
                </div>
            </div>
            <div class="seal-modal-footer">
                <button type="button" class="btn btn-cancel" onclick="closeSealModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-danger" onclick="unselectCurrentSeal()" id="unselectSealBtn" style="display: none;">
                    <i class="fas fa-times"></i>
                    Desmarcar
                </button>
                <button type="button" class="btn btn-submit" onclick="confirmSealSelection()" id="confirmSealBtn">
                    <i class="fas fa-check"></i>
                    Confirmar Selo
                </button>
            </div>
        </div>
    </div>
    

    

    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Removed main.js as it requires Firebase and causes conflicts -->
    
    <script>
        // Check if page loads correctly
        console.log('Jornadas create page loaded successfully');
        console.log('Current URL:', window.location.href);
        
        // Test if fetch works
        window.testFetch = function() {
            console.log('Testing fetch...');
            fetch('get_zone_seals.php?zoneId=test')
                .then(response => {
                    console.log('Test fetch response:', response);
                    return response.text();
                })
                .then(text => {
                    console.log('Test fetch text:', text);
                })
                .catch(error => {
                    console.error('Test fetch error:', error);
                });
        };
        
        // Test simple GET request to the same directory
        window.testSimple = function() {
            console.log('Testing simple request...');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_zone_seals.php?zoneId=test', true);
            xhr.onreadystatechange = function() {
                console.log('XHR state:', xhr.readyState, 'status:', xhr.status);
                if (xhr.readyState === 4) {
                    console.log('XHR response:', xhr.responseText);
                }
            };
            xhr.onerror = function(e) {
                console.error('XHR error:', e);
            };
            xhr.send();
        };
        
        // Test new inline AJAX approach
        window.testInline = function() {
            console.log('Testing inline AJAX...');
            fetch('?ajax=get_seals&zoneId=test')
                .then(response => {
                    console.log('Inline test response:', response.status, response.ok);
                    return response.text();
                })
                .then(text => {
                    console.log('Inline test result:', text);
                    console.log('Response length:', text.length);
                    console.log('First 500 chars:', text.substring(0, 500));
                    
                    // Try to parse as JSON
                    try {
                        const parsed = JSON.parse(text);
                        console.log('Successfully parsed JSON:', parsed);
                    } catch (e) {
                        console.error('JSON parse failed:', e);
                        console.log('Raw response for debugging:', text);
                    }
                })
                .catch(error => {
                    console.error('Inline test error:', error);
                });
        };
    </script>
    
    <script>
        // Global variables for seal selection
        let availableSeals = [];
        let selectedSeals = [];
        let currentSealNumber = null;
        let selectedAge = null;
        let isEditMode = false;
        let isValidating = false; // Flag to track validation state
        let isSubmitting = false; // Flag to track legitimate form submission
        
        // Preserve selected seals data from previous form submission
        <?php if (!empty($_POST['selosAtribuidos'])): ?>
        try {
            const preservedSeals = <?php echo $_POST['selosAtribuidos']; ?>;
            if (Array.isArray(preservedSeals)) {
                selectedSeals = preservedSeals;
                console.log('Restored selected seals from previous submission:', selectedSeals);
            }
        } catch (e) {
            console.error('Error restoring selected seals:', e);
        }
        <?php endif; ?>
        
        // Custom dropdown functions
        function toggleZoneDropdown() {
            const trigger = document.querySelector('#zonaSelect .custom-select-trigger');
            const options = document.getElementById('zoneOptions');
            const dateOptions = document.getElementById('dateOptions');
            
            // Close date dropdown if open
            if (dateOptions.style.display === 'block') {
                dateOptions.style.display = 'none';
                document.querySelector('#dataSelect .custom-select-trigger').classList.remove('active');
            }
            
            if (options.style.display === 'none') {
                options.style.display = 'block';
                trigger.classList.add('active');
            } else {
                options.style.display = 'none';
                trigger.classList.remove('active');
            }
        }
        
        function toggleDateDropdown() {
            const trigger = document.querySelector('#dataSelect .custom-select-trigger');
            const options = document.getElementById('dateOptions');
            const zoneOptions = document.getElementById('zoneOptions');
            
            // Close zone dropdown if open
            if (zoneOptions.style.display === 'block') {
                zoneOptions.style.display = 'none';
                document.querySelector('#zonaSelect .custom-select-trigger').classList.remove('active');
            }
            
            if (options.style.display === 'none') {
                options.style.display = 'block';
                trigger.classList.add('active');
            } else {
                options.style.display = 'none';
                trigger.classList.remove('active');
            }
        }
        
        function selectZone(zoneId, zoneName) {
            document.getElementById('zonaId').value = zoneId;
            document.getElementById('zoneSelectedText').textContent = zoneName;
            document.getElementById('zoneOptions').style.display = 'none';
            document.querySelector('#zonaSelect .custom-select-trigger').classList.remove('active');
            
            // Load seals for selected zone
            loadZoneSeals(zoneId);
        }
        
        function selectDate(dateValue, dateText) {
            document.getElementById('data').value = dateValue;
            document.getElementById('dateSelectedText').textContent = dateText;
            document.getElementById('dateOptions').style.display = 'none';
            document.querySelector('#dataSelect .custom-select-trigger').classList.remove('active');
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const zoneSelect = document.getElementById('zonaSelect');
            const dateSelect = document.getElementById('dataSelect');
            
            if (!zoneSelect.contains(event.target)) {
                document.getElementById('zoneOptions').style.display = 'none';
                document.querySelector('#zonaSelect .custom-select-trigger').classList.remove('active');
            }
            
            if (!dateSelect.contains(event.target)) {
                document.getElementById('dateOptions').style.display = 'none';
                document.querySelector('#dataSelect .custom-select-trigger').classList.remove('active');
            }
        });
        
        // Initialize dropdowns with pre-selected values if any
        document.addEventListener('DOMContentLoaded', function() {
            const preSelectedZone = document.getElementById('zonaId').value;
            const preSelectedDate = document.getElementById('data').value;
            
            if (preSelectedZone) {
                // Find zone name and update display
                const zoneOption = document.querySelector(`[data-value="${preSelectedZone}"]`);
                if (zoneOption) {
                    const zoneName = zoneOption.textContent.trim();
                    document.getElementById('zoneSelectedText').textContent = zoneName;
                    loadZoneSeals(preSelectedZone);
                }
            }
            
            if (preSelectedDate) {
                // Find date text and update display
                const dateOption = document.querySelector(`[data-value="${preSelectedDate}"]`);
                if (dateOption) {
                    const dateText = dateOption.textContent.trim();
                    document.getElementById('dateSelectedText').textContent = dateText;
                }
            }
        });
        
        // Load seals for selected zone
        async function loadZoneSeals(zoneId) {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            // Maintain the same visual structure during loading
            loadingMessage.innerHTML = `
                <div class="placeholder-content">
                    <div class="placeholder-icon">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <h5 class="placeholder-title">Carregando selos...</h5>
                </div>
            `;
            loadingMessage.style.display = 'block';
            sealsContainer.style.display = 'none';
            
            try {
                console.log('Loading seals for zone:', zoneId);
                const response = await fetch(`?ajax=get_seals&zoneId=${encodeURIComponent(zoneId)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin'
                });
                
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const responseText = await response.text();
                console.log('Raw response text:', responseText);
                console.log('Response text length:', responseText.length);
                console.log('First 200 chars:', responseText.substring(0, 200));
                
                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('Parsed JSON data:', data);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Failed to parse response:', responseText);
                    throw new Error('Invalid JSON response: ' + parseError.message);
                }
                
                if (data.success) {
                    availableSeals = data.seals;
                    displaySeals(data.seals, data.zoneName);
                    
                    // Update form data if we have preserved seals
                    if (selectedSeals.length > 0) {
                        updateFormData();
                        console.log('Updated form data with preserved seals');
                        
                        // Check if any seals are missing photos
                        const sealsWithoutPhotos = selectedSeals.filter(seal => !seal.photoUrl);
                        if (sealsWithoutPhotos.length > 0) {
                            setTimeout(() => {
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Fotos dos Selos',
                                    html: `<p>Alguns selos precisam de fotos:</p>
                                           <p><strong>Selos sem foto: ${sealsWithoutPhotos.map(s => s.number).join(', ')}</strong></p>
                                           <p>Clique em cada selo para adicionar a foto da asa direita.</p>`,
                                    customClass: {
                                        popup: 'custom-swal-popup',
                                        title: 'custom-swal-title',
                                        content: 'custom-swal-content',
                                        confirmButton: 'custom-swal-confirm'
                                    },
                                    buttonsStyling: false,
                                    confirmButtonText: 'Entendi'
                                });
                            }, 500);
                        }
                    }
                } else {
                    showError('Erro ao carregar selos: ' + (data.error || 'Erro desconhecido'));
                }
            } catch (error) {
                console.error('Error loading seals:', error);
                console.error('Error details:', error.message);
                showError('Erro ao carregar selos: ' + error.message);
            }
        }
        
        // Display available seals with staggered animation
        function displaySeals(seals, zoneName) {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            if (seals.length === 0) {
                loadingMessage.innerHTML = `
                    <div class="placeholder-content">
                        <div class="placeholder-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h5 class="placeholder-title">Nenhum selo disponível</h5>
                        <p class="placeholder-text">Esta zona não tem selos disponíveis</p>
                    </div>
                `;
                loadingMessage.style.display = 'block';
                sealsContainer.style.display = 'none';
                return;
            }
            
            loadingMessage.style.display = 'none';
            sealsContainer.style.display = 'block';
            
            let html = `<div class="mb-2" style="text-align: center;">
                <h4 style="color: #1e293b; font-weight: 700; display: flex; align-items: center; justify-content: center; gap: 0.5rem; font-size: 1.2rem; margin-bottom: 0.75rem;">
                    <i class="fas fa-binoculars" style="color: #0a7ea4;"></i>
                    ${zoneName}
                </h4>
            </div>`;
            html += '<div class="seals-grid">';
            
            seals.forEach((seal, index) => {
                const isSelected = selectedSeals.some(s => s.number == seal.seloNumber);
                const selectedClass = isSelected ? 'selected' : '';
                const clickAction = isSelected ? `openSealModalForEdit(${seal.seloNumber})` : `openSealModal(${seal.seloNumber})`;
                
                html += `<div class="seal-card-mini ${selectedClass}" 
                              onclick="${clickAction}" 
                              style="animation-delay: ${index * 0.02}s;"
                              data-seal-number="${seal.seloNumber}">
                    <span class="seal-number">${seal.seloNumber}</span>
                </div>`;
            });
            
            html += '</div>';
            sealsContainer.innerHTML = html;
            
            // Add entrance sound effect simulation (visual feedback) and ensure hover works
            setTimeout(() => {
                const sealCards = document.querySelectorAll('.seal-card-mini');
                sealCards.forEach((card, index) => {
                    setTimeout(() => {
                        // Add appeared class to enable proper hover effects
                        card.classList.add('appeared');
                        
                        // Brief scale feedback
                        card.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            card.style.transform = '';
                        }, 150);
                    }, index * 20);
                });
            }, 50);
        }
        
        // Clear seals display
        function clearSeals() {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            loadingMessage.innerHTML = `
                <div class="placeholder-content">
                    <div class="placeholder-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <h5 class="placeholder-title">Selos Disponíveis</h5>
                    <p class="placeholder-text">Seleccione uma zona de caça para ver os selos disponíveis</p>
                </div>
            `;
            loadingMessage.style.display = 'block';
            sealsContainer.style.display = 'none';
            availableSeals = [];
        }
        
        // Show error message
        function showError(message) {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            loadingMessage.innerHTML = `
                <div class="placeholder-content">
                    <div class="placeholder-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h5 class="placeholder-title">Erro</h5>
                    <p class="placeholder-text">${message}</p>
                </div>
            `;
            loadingMessage.style.display = 'block';
            sealsContainer.style.display = 'none';
        }
        
        // Open seal selection modal
        function openSealModal(sealNumber) {
            currentSealNumber = sealNumber;
            selectedAge = null;
            
            document.getElementById('modalSealNumber').textContent = sealNumber;
            document.getElementById('sealModal').style.display = 'block';
            
            // Reset form
            resetSealModal();
        }
        
        // Close seal modal
        function closeSealModal() {
            document.getElementById('sealModal').style.display = 'none';
            currentSealNumber = null;
            selectedAge = null;
            resetSealModal();
        }
        
        // Reset seal modal form
        function resetSealModal() {
            // Clear age selection
            document.querySelectorAll('.age-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Reset dropzone
            const fileInput = document.getElementById('modalFotoAsaDireita');
            const dropzoneContent = document.getElementById('dropzoneContent');
            const dropzonePreview = document.getElementById('dropzonePreview');
            const previewImage = document.getElementById('previewImage');
            const previewName = document.getElementById('previewName');
            const previewSize = document.getElementById('previewSize');
            
            if (fileInput) fileInput.value = '';
            if (dropzoneContent) dropzoneContent.style.display = 'flex';
            if (dropzonePreview) dropzonePreview.style.display = 'none';
            if (previewImage) previewImage.src = '';
            if (previewName) previewName.textContent = '';
            if (previewSize) previewSize.textContent = '';
        }
        
        // Select age option
        function selectAge(age) {
            selectedAge = age;
            
            // Update UI
            document.querySelectorAll('.age-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-age="${age}"]`).classList.add('selected');
            
            // Button is always enabled now
        }
        
        // Removed checkFormValid function - button is always enabled now
        
        // Confirm seal selection with validation and immediate upload
        async function confirmSealSelection() {
            const fileInput = document.getElementById('modalFotoAsaDireita');
            const file = fileInput.files[0];
            
            // Validate required fields
            const errors = [];
            
            if (!selectedAge) {
                errors.push('Classificação Etária');
            }
            
            // Check photo requirement - in edit mode, existing photo is acceptable
            const hasExistingPhoto = isEditMode && selectedSeals.find(s => s.number === currentSealNumber)?.photoUrl;
            if (!file && !hasExistingPhoto) {
                errors.push('Foto da Asa Direita');
            }
            
            if (errors.length > 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Campos Obrigatórios',
                    html: `Por favor, preencha os seguintes campos:<br><br><strong>${errors.join('<br>')}</strong>`,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
                return;
            }
            
            // Show progress
            const confirmBtn = document.getElementById('confirmSealBtn');
            const originalBtnText = confirmBtn.innerHTML;
            confirmBtn.disabled = true;
            
            if (file) {
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>A carregar foto...';
            } else {
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>A actualizar selo...';
            }
            
            try {
                let photoUrl, photoName, uploadedAt;
                
                if (file) {
                    // Upload new photo to Firebase Storage
                    photoUrl = await uploadSealPhoto(file, currentSealNumber);
                    
                    if (!photoUrl) {
                        throw new Error('Falha no carregamento da foto');
                    }
                    
                    photoName = file.name;
                    uploadedAt = new Date().toISOString();
                } else if (isEditMode) {
                    // Use existing photo data
                    const existingSeal = selectedSeals.find(s => s.number === currentSealNumber);
                    photoUrl = existingSeal.photoUrl;
                    photoName = existingSeal.photoName;
                    uploadedAt = existingSeal.uploadedAt;
                }
                
                // Create seal object with photo data
                const sealData = {
                    number: currentSealNumber,
                    age: selectedAge,
                    photoName: photoName,
                    photoUrl: photoUrl,
                    uploadedAt: uploadedAt
                };
                
                if (isEditMode) {
                    // Update existing seal
                    const index = selectedSeals.findIndex(s => s.number === currentSealNumber);
                    if (index > -1) {
                        selectedSeals[index] = sealData;
                    }
                } else {
                    // Add new seal
                    selectedSeals.push(sealData);
                }
                
                // Update form data
                updateFormData();
                
                // Refresh seals display to show the selected state
                refreshSealsDisplay();
                
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Selo Confirmado',
                    text: `Selo ${currentSealNumber} foi confirmado e a foto foi carregada com sucesso.`,
                    timer: 2000,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content'
                    },
                    buttonsStyling: false
                });
                
                // Close modal
                closeSealModal();
                
            } catch (error) {
                console.error('Error uploading seal photo:', error);
                
                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Erro no Carregamento',
                    text: 'Erro ao carregar a foto: ' + error.message,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
                
                // Restore button
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalBtnText;
            }
        }
        
        // Handle cancel button click
        function handleCancel() {
            if (selectedSeals.length === 0) {
                // No seals selected, just redirect
                window.location.href = 'index.php';
                return;
            }
            
            // Show confirmation dialog
            Swal.fire({
                icon: 'question',
                title: 'Cancelar Criação',
                html: `<p>Tem a certeza que pretende cancelar?</p>
                       <p><strong>${selectedSeals.length} selo(s) seleccionado(s) com fotos carregadas serão removidos.</strong></p>`,
                showCancelButton: true,
                confirmButtonText: 'Sim, Cancelar',
                cancelButtonText: 'Continuar a Editar',
                reverseButtons: true,
                customClass: {
                    popup: 'custom-swal-popup',
                    title: 'custom-swal-title',
                    content: 'custom-swal-content',
                    confirmButton: 'custom-swal-confirm',
                    cancelButton: 'custom-swal-cancel',
                    actions: 'custom-swal-actions'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    cleanupUploadedPhotos();
                }
            });
        }
        
        // Clean up uploaded photos when canceling
        async function cleanupUploadedPhotos() {
            if (selectedSeals.length === 0) {
                window.location.href = 'index.php';
                return;
            }
            
            // Show cleanup progress
            Swal.fire({
                title: 'A limpar fotos...',
                html: 'Por favor aguarde enquanto removemos as fotos carregadas.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            try {
                // Get photos to clean up
                const photosToCleanup = selectedSeals.filter(seal => seal.photoUrl).map(seal => seal.photoUrl);
                
                if (photosToCleanup.length > 0) {
                    const response = await fetch('', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'cleanup_seal_photos',
                            photoUrls: photosToCleanup
                        }),
                        credentials: 'same-origin'
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        console.log('Successfully cleaned up photos:', data.deletedCount);
                    } else {
                        console.error('Error cleaning up photos:', data.error);
                    }
                }
                
                // Redirect regardless of cleanup success/failure
                window.location.href = 'index.php';
                
            } catch (error) {
                console.error('Error during cleanup:', error);
                // Still redirect even if cleanup fails
                window.location.href = 'index.php';
            }
        }
        
        // Upload seal photo to Firebase Storage
        async function uploadSealPhoto(file, sealNumber) {
            try {
                const formData = new FormData();
                formData.append('photo', file);
                formData.append('sealNumber', sealNumber);
                formData.append('action', 'upload_seal_photo');
                
                const response = await fetch('', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const responseText = await response.text();
                console.log('Upload response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', responseText);
                    throw new Error('Invalid response from server');
                }
                
                if (data.success) {
                    return data.photoUrl;
                } else {
                    throw new Error(data.error || 'Upload failed');
                }
                
            } catch (error) {
                console.error('Upload error:', error);
                throw error;
            }
        }
        
        // Unselect a seal (when clicking on selected seal)
        function unselectSeal(sealNumber) {
            const index = selectedSeals.findIndex(s => s.number === sealNumber);
            if (index > -1) {
                selectedSeals.splice(index, 1);
                updateFormData();
                refreshSealsDisplay();
            }
        }
        
        // Unselect the current seal from modal
        function unselectCurrentSeal() {
            if (currentSealNumber && isEditMode) {
                // Show confirmation dialog
                Swal.fire({
                    icon: 'question',
                    title: 'Desmarcar Selo',
                    text: `Tem a certeza que pretende desmarcar o selo ${currentSealNumber}?`,
                    showCancelButton: true,
                    confirmButtonText: 'Sim, Desmarcar',
                    cancelButtonText: 'Cancelar',
                    reverseButtons: true,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm',
                        cancelButton: 'custom-swal-cancel',
                        actions: 'custom-swal-actions'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Store the seal number before any operations
                        const sealNumber = currentSealNumber;
                        
                        // Remove the seal
                        unselectSeal(currentSealNumber);
                        
                        // Close modal
                        closeSealModal();
                        
                        // Show success message with stored seal number
                        Swal.fire({
                            icon: 'success',
                            title: 'Selo Desmarcado',
                            text: `O selo ${sealNumber} foi desmarcado com sucesso.`,
                            timer: 2000,
                            showConfirmButton: false,
                            customClass: {
                                popup: 'custom-swal-popup',
                                title: 'custom-swal-title',
                                content: 'custom-swal-content'
                            },
                            buttonsStyling: false
                        });
                    }
                });
            }
        }
        
        // Refresh the seals display to update selected states
        function refreshSealsDisplay() {
            if (availableSeals.length > 0) {
                // Get the zone name from the display text
                const zoneNameElement = document.getElementById('zoneSelectedText');
                const zoneName = zoneNameElement ? zoneNameElement.textContent : 'Zona';
                displaySeals(availableSeals, zoneName);
            }
        }
        
        // Update hidden form data
        function updateFormData() {
            const formData = selectedSeals.map(seal => ({
                number: seal.number,
                age: seal.age,
                photoName: seal.photoName
            }));
            
            document.getElementById('selectedSealsData').value = JSON.stringify(formData);
        }
        
        // Dropzone functionality for photo upload
        function initializeDropzone() {
            const dropzoneArea = document.getElementById('dropzoneArea');
            const fileInput = document.getElementById('modalFotoAsaDireita');
            const dropzoneContent = document.getElementById('dropzoneContent');
            const dropzonePreview = document.getElementById('dropzonePreview');
            const previewImage = document.getElementById('previewImage');
            const previewName = document.getElementById('previewName');
            const previewSize = document.getElementById('previewSize');
            const removeBtn = document.getElementById('removePhoto');
            
            // Click to select file
            dropzoneArea.addEventListener('click', () => {
                fileInput.click();
            });
            
            // Drag and drop handlers
            dropzoneArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropzoneArea.classList.add('dragover');
            });
            
            dropzoneArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                if (!dropzoneArea.contains(e.relatedTarget)) {
                    dropzoneArea.classList.remove('dragover');
                }
            });
            
            dropzoneArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dropzoneArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelection(files[0]);
                }
            });
            
            // File input change handler
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    handleFileSelection(file);
                }
            });
            
            // Remove photo handler
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                clearPhoto();
            });
            
            // Handle file selection
            function handleFileSelection(file) {
                // Validate file type
                if (!file.type.startsWith('image/')) {
                    showFileError('Por favor, seleccione apenas ficheiros de imagem.');
                    return;
                }
                
                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    showFileError('O ficheiro deve ter no máximo 5MB.');
                    return;
                }
                
                // Set the file to the input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                
                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewName.textContent = file.name;
                    previewSize.textContent = formatFileSize(file.size);
                    
                    // Show preview, hide dropzone content
                    dropzoneContent.style.display = 'none';
                    dropzonePreview.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            }
            
            // Clear photo
            function clearPhoto() {
                fileInput.value = '';
                dropzoneContent.style.display = 'flex';
                dropzonePreview.style.display = 'none';
                previewImage.src = '';
                previewName.textContent = '';
                previewSize.textContent = '';
            }
            
            // Show file error
            function showFileError(message) {
                Swal.fire({
                    icon: 'error',
                    title: 'Ficheiro Inválido',
                    text: message,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
            }
        }
        
        // Initialize dropzone when modal opens
        function openSealModal(sealNumber) {
            currentSealNumber = sealNumber;
            selectedAge = null;
            isEditMode = false;
            
            document.getElementById('modalSealNumber').textContent = sealNumber;
            document.getElementById('sealModal').style.display = 'block';
            
            // Reset form
            resetSealModal();
            
            // Show confirm button, hide unselect button
            document.getElementById('confirmSealBtn').style.display = 'inline-block';
            document.getElementById('confirmSealBtn').innerHTML = '<i class="fas fa-check"></i> Confirmar Selo';
            document.getElementById('confirmSealBtn').disabled = false; // Ensure button is enabled for new seals
            document.getElementById('unselectSealBtn').style.display = 'none';
            
            // Initialize dropzone
            setTimeout(() => {
                initializeDropzone();
            }, 100);
        }
        
        // Open modal for editing a selected seal
        function openSealModalForEdit(sealNumber) {
            currentSealNumber = sealNumber;
            isEditMode = true;
            
            // Find the selected seal data
            const sealData = selectedSeals.find(s => s.number === sealNumber);
            if (!sealData) return;
            
            document.getElementById('modalSealNumber').textContent = sealNumber;
            document.getElementById('sealModal').style.display = 'block';
            
            // Reset form first
            resetSealModal();
            
            // Pre-populate the form with existing data
            selectedAge = sealData.age;
            
            // Set age selection
            document.querySelector(`[data-age="${sealData.age}"]`).classList.add('selected');
            
            // Check if seal has uploaded photo
            if (sealData.photoUrl) {
                const dropzoneContent = document.getElementById('dropzoneContent');
                const dropzonePreview = document.getElementById('dropzonePreview');
                const previewImage = document.getElementById('previewImage');
                const previewName = document.getElementById('previewName');
                const previewSize = document.getElementById('previewSize');
                
                // Show the uploaded photo
                previewImage.src = sealData.photoUrl;
                previewName.textContent = sealData.photoName || 'Foto carregada';
                previewSize.textContent = sealData.uploadedAt ? 'Carregada em ' + new Date(sealData.uploadedAt).toLocaleString() : 'Foto carregada';
                
                dropzoneContent.style.display = 'none';
                dropzonePreview.style.display = 'flex';
            } else {
                // No photo uploaded yet - show a notice in the dropzone
                const dropzoneContent = document.getElementById('dropzoneContent');
                const dropzoneIcon = dropzoneContent.querySelector('.dropzone-icon');
                const dropzoneMain = dropzoneContent.querySelector('.dropzone-main');
                const dropzoneSub = dropzoneContent.querySelector('.dropzone-sub');
                
                dropzoneIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                dropzoneIcon.style.background = 'linear-gradient(135deg, #f59e0b, #d97706)';
                dropzoneMain.textContent = 'Foto da asa direita necessária';
                dropzoneSub.textContent = 'Clique ou arraste para seleccionar a foto';
            }
            
            // Show both buttons - allow editing or unselecting
            document.getElementById('confirmSealBtn').style.display = 'inline-block';
            document.getElementById('confirmSealBtn').innerHTML = '<i class="fas fa-save"></i> Actualizar Selo';
            document.getElementById('confirmSealBtn').disabled = false; // Ensure button is enabled in edit mode
            document.getElementById('unselectSealBtn').style.display = 'inline-block';
            
            // Initialize dropzone
            setTimeout(() => {
                initializeDropzone();
            }, 100);
        }
        
        // Helper function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
        
        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('sealModal');
            if (event.target === modal) {
                closeSealModal();
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('sealModal');
                if (modal.style.display === 'block') {
                    closeSealModal();
                }
            }
        });
        

        

        
        // Prevent negative values in number inputs
        function preventNegative(event) {
            if (event.key === '-' || event.key === '+' || event.key === 'e' || event.key === 'E') {
                event.preventDefault();
            }
        }
        
        // Apply to all number inputs
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('keydown', preventNegative);
            
            // Also prevent pasting non-numeric content
            input.addEventListener('paste', function(e) {
                const paste = (e.clipboardData || window.clipboardData).getData('text');
                if (!/^\d+$/.test(paste)) {
                    e.preventDefault();
                }
            });
        });
        
        // Show help modal
        function showHelpModal() {
            Swal.fire({
                title: '<div class="help-modal-title"><i class="fas fa-question-circle me-2"></i>Ajuda - Criar Nova Jornada de Caça</div>',
                html: `
                    <div class="help-modal-content">
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                <span>Informações Básicas</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <span><strong>Data da Jornada:</strong> Seleccione a data em que a caça foi realizada</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-binoculars text-primary me-2"></i>
                                    <span><strong>Zona de Caça:</strong> Escolha uma das suas zonas registadas. Apenas zonas com trajeto definido podem ser seleccionadas</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    <span><strong>Número de Caçadores:</strong> Indique quantos caçadores participaram na jornada</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-tag text-primary me-2"></i>
                                <span>Selos e Documentação</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-item">
                                    <i class="fas fa-mouse-pointer text-success me-2"></i>
                                    <span><strong>Selecção de Selos:</strong> Clique nos selos para os seleccionar. Cada selo requer classificação etária e foto da asa direita</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-dove text-primary me-2"></i>
                                    <span><strong>Classificação Etária:</strong> Para cada selo, deve especificar se é Adulto ou Juvenil</span>
                                </div>
                                <div class="help-item">
                                    <i class="fas fa-camera text-info me-2"></i>
                                    <span><strong>Foto da Asa Direita:</strong> Obrigatório anexar foto da asa direita para documentação de cada selo</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-microscope text-primary me-2"></i>
                                <span>Estudos Complementares</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-flask text-secondary me-2"></i>
                                    <span><strong>Contribuição Opcional:</strong> Campos opcionais para contribuir com material científico</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-feather-alt text-secondary me-2"></i>
                                    <span><strong>Asas, Esfregaços, Zaragatoas:</strong> Indique as quantidades recolhidas para estudos</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                <span>Notas Importantes</span>
                            </div>
                            <div class="help-section-body">
                                <div class="help-tip">
                                    <i class="fas fa-route text-warning me-2"></i>
                                    <span><strong>Zonas sem Trajeto:</strong> Aparecem como "Necessita Trajeto" e não podem ser seleccionadas</span>
                                </div>
                                <div class="help-tip">
                                    <i class="fas fa-asterisk text-danger me-2"></i>
                                    <span><strong>Campos Obrigatórios:</strong> Todos os campos marcados com * são obrigatórios</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <div class="help-section-header">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <span>Precisa de Ajuda?</span>
                            </div>
                            <div class="help-section-body">
                                <div style="background: #eff6ff; border: 1px solid #0a7ea4; border-radius: 6px; padding: 0.75rem;">
                                    <div style="color: #0a7ea4; font-size: 0.9rem;">
                                        Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:<br>
                                        <strong><EMAIL></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: '<i class="fas fa-check me-2"></i>Entendi',
                confirmButtonColor: '#0a7ea4',
                width: 'auto',
                customClass: {
                    popup: 'help-modal-popup',
                    title: 'help-modal-title-container',
                    htmlContainer: 'help-modal-html-container',
                    confirmButton: 'help-modal-button'
                }
            });
        }
        

        
        // Show button loading state
        function showButtonLoading() {
            const submitBtn = document.getElementById('submitBtn');
            const originalContent = submitBtn.innerHTML;
            
            // Store original content for potential restoration
            submitBtn.setAttribute('data-original-content', originalContent);
            
            // Update button to loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>A guardar...';
            submitBtn.style.opacity = '0.8';
            submitBtn.style.cursor = 'not-allowed';
        }
        
        // Hide button loading state (in case of validation errors)
        function hideButtonLoading() {
            const submitBtn = document.getElementById('submitBtn');
            const originalContent = submitBtn.getAttribute('data-original-content');
            
            if (originalContent) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalContent;
                submitBtn.style.opacity = '1';
                submitBtn.style.cursor = 'pointer';
            }
        }
        
        // Form submission with SweetAlert validation
        document.getElementById('jornadaForm').addEventListener('submit', function(e) {
            // Always prevent default first to handle validation
            e.preventDefault();
            e.stopPropagation();
            
            // Debug: Check file inputs before submission
            console.log('=== FORM SUBMISSION DEBUG ===');
            const fileInputs = document.querySelectorAll('input[type="file"]');
            console.log('Total file inputs found:', fileInputs.length);
            
            fileInputs.forEach((input, index) => {
                console.log(`File input ${index + 1}:`, {
                    name: input.name,
                    id: input.id,
                    filesCount: input.files ? input.files.length : 0,
                    files: input.files ? Array.from(input.files).map(f => f.name) : []
                });
            });
            
            console.log('Selected seals data:', selectedSeals);
            console.log('=== END DEBUG ===');
            
            // Validate required fields
            const requiredFields = [
                { field: 'zonaId', name: 'Zona de Caça' },
                { field: 'data', name: 'Data da Jornada' },
                { field: 'numeroCacadores', name: 'Número de Caçadores' }
            ];
            
            const errors = [];
            
            requiredFields.forEach(({ field, name }) => {
                const input = document.getElementById(field);
                if (!input.value || input.value.trim() === '') {
                    errors.push(name);
                }
            });
            
            // Check if we have selected seals but missing photos
            if (selectedSeals.length > 0) {
                const missingPhotos = [];
                selectedSeals.forEach(seal => {
                    if (!seal.photoUrl) {
                        missingPhotos.push(seal.number);
                    }
                });
                
                if (missingPhotos.length > 0) {
                    errors.push(`Fotos em falta para os selos: ${missingPhotos.join(', ')}`);
                }
            }
            
            if (errors.length > 0) {
                // Set validation flag to prevent beforeunload warning
                isValidating = true;
                
                Swal.fire({
                    icon: 'error',
                    title: 'Campos Obrigatórios',
                    html: `Por favor, corrija os seguintes problemas:<br><br><strong>${errors.join('<br>')}</strong>`,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                }).then(() => {
                    // Reset validation flag when modal is closed
                    isValidating = false;
                });
                
                // Hide loading state if validation fails
                hideButtonLoading();
                return false;
            }
            
            // If validation passes, show loading state and submit the form
            showButtonLoading();
            
            // Set flag to allow navigation without warning
            isSubmitting = true;
            
            // Submit the form programmatically
            const form = this;
            
            // Make sure we have all the form data including the selected seals
            const formData = new FormData(form);
            
            // Explicitly add the selected seals data
            formData.set('selosAtribuidos', JSON.stringify(selectedSeals));
            
            // Make sure the action field is set
            formData.set('action', 'create');
            
            // Debug what we're sending
            console.log('Form action:', form.action);
            console.log('Submitting to:', window.location.href);
            console.log('FormData contents:');
            for (let [key, value] of formData.entries()) {
                if (value instanceof File) {
                    console.log(key, 'FILE:', value.name, value.size + ' bytes');
                } else {
                    console.log(key, value);
                }
            }
            
            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                // Don't set Content-Type header - let browser set it with boundary for multipart
            }).then(response => {
                console.log('Response status:', response.status);
                return response.json();
            }).then(data => {
                console.log('Response received:', data);
                
                hideButtonLoading();
                
                if (data.success) {
                    // Keep isSubmitting = true during success modal and redirect
                    // Show success message and redirect
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                        text: data.message,
                        customClass: {
                            popup: 'custom-swal-popup',
                            title: 'custom-swal-title',
                            content: 'custom-swal-content',
                            confirmButton: 'custom-swal-confirm'
                        },
                        buttonsStyling: false
                    }).then(() => {
                        // Keep isSubmitting true to prevent beforeunload warning during redirect
                        window.location.href = data.redirect || 'index.php';
                    });
                } else {
                    // Only reset isSubmitting on error
                    isSubmitting = false;
                    
                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro',
                        html: data.error,
                        customClass: {
                            popup: 'custom-swal-popup',
                            title: 'custom-swal-title',
                            content: 'custom-swal-content',
                            confirmButton: 'custom-swal-confirm'
                        },
                        buttonsStyling: false
                    });
                }
            }).catch(error => {
                console.error('Fetch error:', error);
                hideButtonLoading();
                isSubmitting = false;
                
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    text: 'Ocorreu um erro ao guardar a jornada. Tente novamente.',
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
            });
        });
        
        // Also add loading state when button is clicked (before form submission)
        document.getElementById('submitBtn').addEventListener('click', function(e) {
            // Prevent default to avoid any browser validation
            e.preventDefault();
            e.stopPropagation();
            
            // Manually trigger the form submit event which has our custom validation
            const form = document.getElementById('jornadaForm');
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(submitEvent);
        });
        
        // Handle page unload (browser close, tab close, navigation away)
        window.addEventListener('beforeunload', function(e) {
            // Only show warning if there are selected seals with uploaded photos
            // Don't show warning during form validation or successful form submission
            const sealsWithPhotos = selectedSeals.filter(seal => seal.photoUrl && seal.photoUrl.trim() !== '');
            
            // Only show warning for actual page navigation (not during validation or submission)
            if (sealsWithPhotos.length > 0 && !isValidating && !isSubmitting) {
                const message = `Tem ${sealsWithPhotos.length} foto(s) de selos carregadas que serão perdidas.`;
                e.preventDefault();
                e.returnValue = message;
                return message;
            }
        });
        
        // Clean up photos when page is unloaded (if user ignores warning)
        // BUT NOT when form was successfully submitted
        window.addEventListener('unload', function() {
            // Don't cleanup photos if form was successfully submitted
            if (isSubmitting) {
                console.log('Form was successfully submitted - skipping photo cleanup');
                return;
            }
            
            const sealsWithPhotos = selectedSeals.filter(seal => seal.photoUrl);
            if (sealsWithPhotos.length > 0) {
                console.log('Page unloaded without successful submission - cleaning up photos');
                // Use sendBeacon for reliable cleanup on page unload
                const photosToCleanup = sealsWithPhotos.map(seal => seal.photoUrl);
                const data = JSON.stringify({
                    action: 'cleanup_seal_photos',
                    photoUrls: photosToCleanup
                });
                
                navigator.sendBeacon('', data);
            }
        });
        
        // SweetAlert for success/error messages on page load
        document.addEventListener('DOMContentLoaded', function() {
            <?php if (!empty($success_message)): ?>
                Swal.fire({
                    icon: 'success',
                    title: 'Sucesso!',
                    text: '<?php echo addslashes($success_message); ?>',
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                }).then(() => {
                    window.location.href = 'index.php';
                });
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    html: '<?php echo addslashes($error_message); ?>',
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
            <?php endif; ?>
        });
    </script>
</body>
</html> 