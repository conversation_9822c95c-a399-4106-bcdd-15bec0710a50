<?php
ob_start();
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

if (!isGestorUser()) {
    ob_end_clean();
    header('Location: ../auth/login.php');
    exit();
}

$user_email = $_SESSION['user']['email'] ?? '';
$user_nif = $_SESSION['user']['nif'] ?? '';
$user_id = $_SESSION['user']['id'] ?? '';

global $database;

if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Handle AJAX request for refreshing photo URL
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['ajax']) && $_GET['ajax'] === 'refresh_photo_url') {
    // Clean any previous output
    ob_end_clean();
    
    // Set response type
    header('Content-Type: application/json');
    
    $photoPath = $_GET['photoPath'] ?? '';
    
    if (empty($photoPath)) {
        echo json_encode(['success' => false, 'error' => 'Photo path is required']);
        exit();
    }
    
    try {
        // Use admin token to get fresh download URL
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            throw new Exception('Unable to get admin token');
        }
        
        // Get fresh download URL
        $downloadUrl = $database->getStorageDownloadUrl($photoPath, $adminToken);
        
        if ($downloadUrl) {
            echo json_encode([
                'success' => true,
                'downloadUrl' => $downloadUrl
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Unable to get download URL'
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Refresh photo URL error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit();
}

// Handle AJAX request for loading seals
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['ajax']) && $_GET['ajax'] === 'get_seals') {
    // Clean any previous output
    ob_end_clean();
    
    // Set response type
    header('Content-Type: application/json');
    
    $zoneId = $_GET['zoneId'] ?? '';
    
    if (empty($zoneId)) {
        echo json_encode(['error' => 'Zone ID is required']);
        exit();
    }
    
    try {
        // Get admin token to read data
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        // Verify user has access to this zone
        $userZones = getGestorZones($database, $user_nif);
        $hasAccess = false;
        $zoneInfo = null;
        
        foreach ($userZones as $zone) {
            if ($zone['id'] === $zoneId) {
                $hasAccess = true;
                $zoneInfo = $zone;
                break;
            }
        }
        
        if (!$hasAccess) {
            echo json_encode(['error' => 'No access to this zone']);
            exit();
        }
        
        // Get seals for this zone
        $seals = getSealsForZone($database, $zoneId, $zoneInfo);
        
        echo json_encode([
            'success' => true,
            'seals' => $seals,
            'zoneName' => $zoneInfo['nomeZona']
        ]);
        exit();
        
    } catch (Exception $e) {
        error_log("Get zone seals error (edit): " . $e->getMessage());
        echo json_encode(['error' => 'Failed to load seals', 'debug' => $e->getMessage()]);
        exit();
    }
}

$gestorZones = [];
$jornada = null;
$error_message = '';
$success_message = '';

// Get jornada ID from URL
$jornadaId = $_GET['id'] ?? '';
if (empty($jornadaId)) {
    header('Location: index.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit') {
    try {
        $requiredFields = [
            'data' => 'Data da jornada',
            'zonaId' => 'Zona de caça',
            'numeroCacadores' => 'Número de caçadores'
        ];
        
        $errors = [];
        foreach ($requiredFields as $field => $label) {
            if (empty($_POST[$field])) {
                $errors[] = "$label é obrigatório.";
            }
        }
        
        if (empty($errors)) {
            // Find zone name - first try from current jornada, then from gestorZones
            $zoneName = $jornada['nomeZona'] ?? '';
            if (empty($zoneName)) {
            foreach ($gestorZones as $zone) {
                if ($zone['id'] === $_POST['zonaId']) {
                    $zoneName = $zone['nomeZona'];
                    break;
                    }
                }
            }
            
            // Debug logging
            error_log("Edit jornada - Original jornada data: " . print_r($jornada, true));
            error_log("Edit jornada - Zone name found: " . $zoneName);
            error_log("Edit jornada - Original createdBy: " . ($jornada['createdBy'] ?? 'NULL'));
            error_log("Edit jornada - Original createdAt: " . ($jornada['createdAt'] ?? 'NULL'));
            

            // Process seal photos if any were uploaded
            $selosData = json_decode($_POST['selosAtribuidos'] ?? '[]', true);
            if (is_array($selosData)) {
                foreach ($selosData as &$selo) {
                    // Check if there's a photo file for this seal
                    $photoFieldName = 'seal_photo_' . $selo['number'];
                    if (isset($_FILES[$photoFieldName]) && $_FILES[$photoFieldName]['error'] === UPLOAD_ERR_OK) {
                try {
                    // Use admin token for Firebase Storage upload
                    $adminToken = $database->getAdminAccessToken();
                    if ($adminToken) {
                                $fileName = 'selos/' . $selo['number'] . '_' . uniqid() . '_' . basename($_FILES[$photoFieldName]['name']);
                        $uploadResult = $database->uploadFileToStorage(
                                    $_FILES[$photoFieldName]['tmp_name'],
                            $fileName,
                                    $_FILES[$photoFieldName]['type'],
                            $adminToken
                        );
                        
                        if ($uploadResult && isset($uploadResult['downloadUrl'])) {
                                    $selo['fotoAsaDireita'] = $uploadResult['downloadUrl'];
                                    $selo['photoName'] = basename($_FILES[$photoFieldName]['name']);
                        }
                    }
                } catch (Exception $e) {
                            error_log("Error uploading seal photo to Firebase Storage: " . $e->getMessage());
                        }
                    }
                }
            }
            
            // Ensure we have critical fields - use fallbacks if needed
            $createdBy = $jornada['createdBy'] ?? $user_id; // Fallback to current user if missing
            $createdAt = $jornada['createdAt'] ?? date('Y-m-d\TH:i:s\Z'); // Fallback to current time if missing
            
            $jornadaData = [
                'data' => $_POST['data'],
                'zonaId' => $_POST['zonaId'],
                'nomeZona' => $zoneName,
                'numeroCacadores' => (int)$_POST['numeroCacadores'],
                'selosAtribuidos' => json_encode($selosData),
                'numeroAsas' => (int)($_POST['numeroAsas'] ?? 0),
                'numeroEsfregacos' => (int)($_POST['numeroEsfregacos'] ?? 0),
                'numeroZaragatoas' => (int)($_POST['numeroZaragatoas'] ?? 0),
                'outros' => $_POST['outros'] ?? '',
                'createdBy' => $createdBy, // Preserve original creator
                'createdAt' => $createdAt, // Preserve original creation time
                'updatedAt' => date('Y-m-d\TH:i:s\Z')
            ];
            
            // Debug the data being sent to Firestore
            error_log("Edit jornada - Final jornadaData: " . print_r($jornadaData, true));
            
            $result = updateJornadaInFirestore($jornadaId, $jornadaData, $database);
            if ($result) {
                // Set session flag for success toast
                $_SESSION['jornada_updated'] = true;
                header('Location: index.php');
                exit();
            } else {
                $error_message = 'Erro ao atualizar jornada de caça.';
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    } catch (Exception $e) {
        $error_message = 'Erro ao atualizar jornada: ' . $e->getMessage();
    }
}

try {
    $gestorZones = getGestorZones($database, $user_nif);
    $jornada = getJornadaById($database, $jornadaId);
    
    if (!$jornada) {
        header('Location: index.php');
        exit();
    }
    
    // Check if this jornada belongs to current user
    if ($jornada['createdBy'] !== $user_id) {
        header('Location: index.php');
        exit();
    }
    
} catch (Exception $e) {
    error_log("Edit jornada page data error: " . $e->getMessage());
    $error_message = "Erro ao carregar dados da jornada.";
}

function getGestorZones($database, $userNif) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userNif)) {
            return [];
        }
        
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        $allZones = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allZones[] = [
                    'id' => $docId,
                    'zona' => $data['zona'] ?? 'N/A',
                    'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                    'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                    'email' => $data['email'] ?? 'N/A',
                    'quotaZona' => $data['quotaZona'] ?? 0,
                    'minSelo' => $data['minSelo'] ?? 0,
                    'maxSelo' => $data['maxSelo'] ?? 0,
                    'localidade' => $data['localidade'] ?? '',
                    'status' => $data['status'] ?? 'not registered',
                    'registeredBy' => $data['registeredBy'] ?? null
                ];
            }
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getGestorZones: " . $e->getMessage());
        return [];
    }
}

function getJornadaById($database, $jornadaId) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        $result = $database->getDocument('jornadasCaca', $jornadaId);
        
        if ($result) {
            return [
                'id' => $jornadaId,
                'data' => $result['data'] ?? '',
                'zonaId' => $result['zonaId'] ?? '',
                'nomeZona' => $result['nomeZona'] ?? '',
                'numeroCacadores' => $result['numeroCacadores'] ?? 0,
                'numeroAdultos' => $result['numeroAdultos'] ?? 0,
                'numeroJuvenis' => $result['numeroJuvenis'] ?? 0,
                'selosAtribuidos' => $result['selosAtribuidos'] ?? '',
                'numeroAsas' => $result['numeroAsas'] ?? 0,
                'numeroEsfregacos' => $result['numeroEsfregacos'] ?? 0,
                'numeroZaragatoas' => $result['numeroZaragatoas'] ?? 0,
                'outros' => $result['outros'] ?? '',
                'fotoAsaDireita' => $result['fotoAsaDireita'] ?? '',
                'createdBy' => $result['createdBy'] ?? '',
                'createdAt' => $result['createdAt'] ?? null,
                'updatedAt' => $result['updatedAt'] ?? null
            ];
        }
        
        return null;
    } catch (Exception $e) {
        error_log("Exception in getJornadaById: " . $e->getMessage());
        return null;
    }
}

function updateJornadaInFirestore($jornadaId, $jornadaData, $database) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        error_log("Updating jornada $jornadaId with data: " . print_r($jornadaData, true));
        
        $result = $database->updateDocument('jornadasCaca', $jornadaId, $jornadaData);
        
        error_log("Update result: " . ($result ? 'SUCCESS' : 'FAILED'));
        
        // Verify the update by reading the document back
        $verifyResult = $database->getDocument('jornadasCaca', $jornadaId);
        error_log("Document after update: " . print_r($verifyResult, true));
        
        return $result;
    } catch (Exception $e) {
        error_log("Exception in updateJornadaInFirestore: " . $e->getMessage());
        return false;
    }
}

function getSealsForZone($database, $zoneId, $zoneInfo) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        $minSelo = intval($zoneInfo['minSelo']);
        $maxSelo = intval($zoneInfo['maxSelo']);
        
        if ($minSelo <= 0 || $maxSelo <= 0 || $minSelo > $maxSelo) {
            return [];
        }
        
        // Get used seals from Firestore for this specific zone with full pagination
        $usedSeals = [];
        try {
            $allSeals = [];
            $pageToken = null;
            
            do {
                $result = $database->listDocuments('selosUsados', $pageToken, 1000);
                
                if (!$result || !is_array($result)) {
                    break;
                }
                
                // Extract pagination token if present
                $pageToken = $result['_nextPageToken'] ?? null;
                unset($result['_nextPageToken']); // Remove pagination token from results
                
                // Merge results
                $allSeals = array_merge($allSeals, $result);
                
            } while ($pageToken);
            
            // Process all seals
            foreach ($allSeals as $docId => $data) {
                // Only include seals for this zone
                if (isset($data['zoneId']) && $data['zoneId'] === $zoneId && isset($data['seloNumber'])) {
                    $usedSeals[$data['seloNumber']] = [
                        'seloNumber' => $data['seloNumber'],
                        'zoneId' => $data['zoneId'],
                        'usedAt' => $data['usedAt'],
                        'usedBy' => $data['usedBy'],
                        'notes' => $data['notes'] ?? ''
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("Error getting used seals for zone $zoneId: " . $e->getMessage());
        }
        
        // Generate all seals in range for this zone
        $allSelos = [];
        for ($seloNum = $minSelo; $seloNum <= $maxSelo; $seloNum++) {
            $isUsed = isset($usedSeals[$seloNum]);
            
            $allSelos[] = [
                'id' => 'selo_' . $seloNum,
                'seloNumber' => $seloNum,
                'zoneId' => $zoneId,
                'status' => $isUsed ? 'used' : 'available',
                'year' => date('Y'),
                'usedAt' => $isUsed ? $usedSeals[$seloNum]['usedAt'] : null,
                'usedBy' => $isUsed ? $usedSeals[$seloNum]['usedBy'] : null,
                'notes' => $isUsed ? $usedSeals[$seloNum]['notes'] : '',
                'createdAt' => null
            ];
        }
        
        return $allSelos;
    } catch (Exception $e) {
        error_log("Exception in getSealsForZone: " . $e->getMessage());
        return [];
    }
}

ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Jornada de Caça - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/dark-mode.css" rel="stylesheet">
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        /* Modern Professional Design */
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        /* Zones table wrapper styling */
        .zones-table-wrapper {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        /* Header section styling to match create page exactly */
        .zones-table-wrapper .d-flex.justify-content-between {
            padding: 0.75rem 1rem !important;
            background-color: white !important;
            border-bottom: 1px solid #dee2e6 !important;
        }

        .zones-table-wrapper .d-flex.justify-content-between h5 {
            font-size: 1.25rem !important;
            font-weight: 500 !important;
            color: #212529 !important;
            margin-bottom: 0 !important;
        }

        /* Form padding and spacing */
        .zones-table-wrapper > div {
            padding: 2rem !important;
        }

        /* Section spacing */
        .mb-4 {
            margin-bottom: 2rem !important;
        }

        /* Form group spacing */
        .form-group {
            margin-bottom: 1.5rem !important;
        }

        /* Row spacing */
        .row {
            margin-bottom: 1rem !important;
        }

        /* Content wrapper padding */
        .content {
            padding: 1.5rem;
        }

        /* Consistent h6 title styling */
        h6 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #212529 !important;
            margin-bottom: 1rem !important;
        }
        
        .form-label {
            font-weight: 500;
            color: #212529;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.875rem;
        }

        .form-label i {
            color: #0a7ea4;
            font-size: 1rem;
        }
        
        .form-control, .form-select {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0.5rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            background: white;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            height: calc(1.5em + 1rem + 2px);
        }
        
        .form-control[type="number"], .form-control[type="date"], .form-control[type="text"] {
            height: calc(1.5em + 1rem + 2px);
        }
        
        textarea.form-control {
            height: auto;
            min-height: calc(1.5em + 1rem + 2px);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            outline: none;
        }
        
        /* Simple button styling to match index.php */
        .btn-submit {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: white !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 0.375rem !important;
            font-weight: 400 !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
            border: 1px solid transparent !important;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }
        
        .btn-submit:hover {
            background-color: #157347 !important;
            border-color: #146c43 !important;
            color: white !important;
        }
        
        .btn-cancel {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: white !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 0.375rem !important;
            font-weight: 400 !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
            border: 1px solid transparent !important;
            margin-right: 1rem !important;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }
        
        .btn-cancel:hover {
            background-color: #5c636a !important;
            border-color: #565e64 !important;
            color: white !important;
        }
        
        .file-input-container {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }
        
        .file-input-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 2rem;
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            background: #f9fafb;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-input-label:hover {
            border-color: #0a7ea4;
            background: #f0f9ff;
            color: #0a7ea4;
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .current-photo {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-text {
            color: #64748b !important;
            font-size: 0.875rem;
            font-style: italic;
            margin-top: 0.5rem;
        }
        
        .required {
            color: #dc3545;
            font-weight: 700;
        }
        
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #15803d;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #dc2626;
        }

        /* Section titles styling */
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        /* Form group styling */
        .form-group {
            margin-bottom: 1.5rem;
        }

        /* Journey Information Cards */
        .journey-info-section {
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .info-card {
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 12px;
            padding: 1rem;
            height: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.1);
            border-color: rgba(10, 126, 164, 0.2);
        }

        .info-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        }

        .info-card-header i {
            color: #0a7ea4;
            font-size: 1rem;
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .info-card-content {
            font-size: 1.125rem;
            font-weight: 700;
            color: #374151;
            line-height: 1.4;
        }

        /* Bootstrap button overrides */
        .btn-secondary {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: white !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 0.375rem !important;
            font-weight: 400 !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
            border: 1px solid transparent !important;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }

        .btn-secondary:hover {
            background-color: #5c636a !important;
            border-color: #565e64 !important;
            color: white !important;
        }

        .btn-success {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: white !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 0.375rem !important;
            font-weight: 400 !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
            border: 1px solid transparent !important;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }

        .btn-success:hover {
            background-color: #157347 !important;
            border-color: #146c43 !important;
            color: white !important;
        }

        /* Advanced Seal Selection Styles with 3D Animations */
        .seals-selection-container {
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid rgba(226, 232, 240, 0.8);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(5px);
        }

        .seals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
            gap: 0.75rem;
            padding: 0.75rem;
            position: relative;
            z-index: 1;
        }
        
        .seal-card-mini {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #10b981;
            border-radius: 16px;
            padding: 1rem 0.5rem; /* Increased top/bottom padding */
            text-align: center;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 65px; /* Increased height to give more space */
            font-weight: 700;
            font-size: 1rem;
            color: #10b981;
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 3px 6px rgba(16, 185, 129, 0.15),
                0 6px 12px rgba(0, 0, 0, 0.08),
                inset 0 1px 2px rgba(255, 255, 255, 0.8);
            animation: sealAppear 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            transform: scale(0.8) translateY(10px);
        }
        
        @keyframes sealAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(10px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0px);
            }
        }
        
        .seal-card-mini.appeared {
            opacity: 1;
            transform: scale(1) translateY(0px);
        }
        
        .seal-card-mini::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }
        
        .seal-card-mini:hover,
        .seal-card-mini.appeared:hover {
            transform: translateY(-6px) rotateX(3deg) rotateY(1deg) scale(1.08) !important;
            box-shadow: 
                0 10px 25px rgba(16, 185, 129, 0.25),
                0 15px 35px rgba(0, 0, 0, 0.12),
                inset 0 2px 4px rgba(255, 255, 255, 0.9);
            border-color: #059669;
            z-index: 10;
        }
        
        .seal-card-mini:hover::before {
            left: 100%;
        }
        
        .seal-card-mini.selected {
            border-color: #0a7ea4;
            background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
            color: #0a7ea4;
            transform: translateY(-4px) rotateX(5deg) scale(1.02);
            box-shadow: 
                0 8px 20px rgba(10, 126, 164, 0.3),
                0 16px 32px rgba(0, 0, 0, 0.12),
                inset 0 2px 4px rgba(255, 255, 255, 0.9);
            animation: selectedPulse 2s ease-in-out infinite;
        }
        
        @keyframes selectedPulse {
            0%, 100% {
                box-shadow: 
                    0 8px 20px rgba(10, 126, 164, 0.3),
                    0 16px 32px rgba(0, 0, 0, 0.12),
                    inset 0 2px 4px rgba(255, 255, 255, 0.9);
            }
            50% {
                box-shadow: 
                    0 12px 30px rgba(10, 126, 164, 0.4),
                    0 20px 40px rgba(0, 0, 0, 0.15),
                    inset 0 2px 4px rgba(255, 255, 255, 0.95);
            }
        }
        
        .seal-card-mini.selected:hover,
        .seal-card-mini.selected.appeared:hover {
            transform: translateY(-8px) rotateX(5deg) rotateY(-2deg) scale(1.12) !important;
            box-shadow: 
                0 12px 30px rgba(10, 126, 164, 0.3),
                0 20px 40px rgba(0, 0, 0, 0.15),
                inset 0 3px 6px rgba(255, 255, 255, 0.95);
            z-index: 15;
        }
        
        .seal-card-mini.selected::after {
            display: none; /* Hide original blue circle */
        }
        
        .seal-card-mini.selected::before {
            display: none; /* Hide original checkmark */
        }

        /* Alternative approach using FontAwesome icon as background */
.seal-card-mini.selected .seal-number::after {
content: '\f4ba';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: -19px;
    right: 16px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #0a7ea4 0%, #06b6d4 100%);
    color: #fff;
    border-radius: 50%;
    border: 1px solid white;
    box-shadow: 0 2px 8px rgba(10, 126, 164, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    z-index: 3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: checkmarkBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transform: none !important;
    transition: none !important;
}
        
        /* Ensure dove stays in place during all hover states */
        .seal-card-mini.selected:hover .seal-number::after,
        .seal-card-mini.selected.appeared:hover .seal-number::after {
            transform: none !important;
            transition: none !important;
        }
        
        @keyframes checkmarkBounce {
            0% {
                transform: scale(0) rotate(-180deg);
            }
            50% {
                transform: scale(1.3) rotate(-90deg);
            }
            100% {
                transform: scale(1) rotate(0deg);
            }
        }
        
        .seal-number {
            flex: 1;
            text-align: center;
            position: relative; /* For positioning the dove icon */
        }

        .seals-summary {
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #0a7ea4;
            margin-top: 1rem;
        }

        .seals-summary h6 {
            color: #0a7ea4;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        /* Seal Modal Styles */
        .seal-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease-out;
        }

        .seal-modal-content {
            background-color: #ffffff;
            margin: 2% auto;
            padding: 0;
            border: none;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            max-height: 95vh;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .seal-modal-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #0a7ea4 0%, #0ea5e9 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: none;
        }

        .seal-modal-header h5 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .seal-modal-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .seal-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .seal-modal-body {
            padding: 2rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .seal-modal-footer {
            padding: 1.5rem 2rem;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        .seal-modal-footer .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-cancel {
            background: #6b7280;
            color: white;
            border: none;
        }

        .btn-cancel:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
            border: none;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-submit {
            background: #0a7ea4;
            color: white;
            border: none;
        }

        .btn-submit:hover {
            background: #0369a1;
        }

        /* Age Selection */
        .age-selection {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .age-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            background: #f9fafb;
        }

        .age-option:hover {
            border-color: #0a7ea4;
            background: #f0f9ff;
        }

        .age-option.selected {
            border-color: #0a7ea4;
            background: #dbeafe;
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
        }

        .age-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #0a7ea4;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .age-icon.juvenil {
            font-size: 1.5rem;
        }

        .age-label {
            font-weight: 600;
            color: #374151;
        }

        /* Dropzone Styles */
        .dropzone-container {
            margin-top: 0.5rem;
        }

        .dropzone-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f9fafb;
        }

        .dropzone-area:hover,
        .dropzone-area.dragover {
            border-color: #0a7ea4;
            background: #f0f9ff;
        }

        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .dropzone-icon {
            font-size: 2rem;
            color: #6b7280;
        }

        .dropzone-text {
            text-align: center;
        }

        .dropzone-main {
            font-weight: 600;
            color: #374151;
            margin: 0 0 0.5rem 0;
        }

        .dropzone-sub {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .dropzone-preview {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
        }

        .preview-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }

        .preview-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .preview-name {
            font-weight: 600;
            color: #16a34a;
        }

        .preview-size {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .preview-remove {
            background: #dc2626;
            color: white;
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .preview-remove:hover {
            background: #b91c1c;
            transform: scale(1.1);
        }

        .info-text {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .required {
            color: #dc2626;
        }

        /* SweetAlert z-index fix */
        .swal2-container {
            z-index: 20000 !important;
        }

        /* Custom SweetAlert styling */
        .custom-swal-popup {
            border-radius: 16px !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        }

        .custom-swal-title {
            color: #374151 !important;
            font-weight: 600 !important;
        }

        .custom-swal-content {
            color: #6b7280 !important;
        }

        .custom-swal-confirm {
            background: #0a7ea4 !important;
            color: white !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 500 !important;
        }

        .custom-swal-confirm:hover {
            background: #0369a1 !important;
            color: white !important;
        }

        .custom-swal-cancel {
            background: #6b7280 !important;
            color: white !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 500 !important;
        }

        .custom-swal-cancel:hover {
            background: #4b5563 !important;
            color: white !important;
        }

        .custom-swal-actions {
            gap: 1rem !important;
        }

        /* Loading animation for submit button */
        .btn-success.loading .btn-text {
            display: none;
        }

        .btn-success.loading .btn-loading-text {
            display: inline;
        }

        .btn-loading-text {
            display: none;
        }

        .btn-success.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-success.loading .btn-icon {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    <?php include '../../includes/profile_modal.php'; ?>
    
    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-edit"></i>
                Editar Jornada de Caça
            </h1>
            </div>
        </div>
        
    <!-- Main Content -->
    <div class="content" id="content">
        <div>
            <!-- Alert Messages -->
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($jornada): ?>
            <!-- Edit Form -->
            <div class="zones-table-wrapper">
                <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2" style="color: #0a7ea4;"></i>
                        Informações
                    </h5>
                </div>
                
                <div class="p-4">
                    <form method="POST" enctype="multipart/form-data" novalidate>
                    <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="zonaId" value="<?php echo htmlspecialchars($jornada['zonaId']); ?>">
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            
                            <!-- Journey Information Display -->
                            <div class="journey-info-section">
                                <div class="row g-3" style="margin-bottom: 0rem !important;">
                            <div class="col-md-6">
                                        <div class="info-card">
                                            <div class="info-card-header">
                                                <i class="fas fa-binoculars me-2"></i>
                                                <span class="info-label">Zona de Caça</span>
                                </div>
                                            <div class="info-card-content">
                                                <?php 
                                                // Find zone name from available zones
                                                $zoneName = $jornada['nomeZona'] ?? '';
                                                if (empty($zoneName)) {
                                                    foreach ($gestorZones as $zone) {
                                                        if ($zone['id'] === $jornada['zonaId']) {
                                                            $zoneName = $zone['nomeZona'];
                                                            break;
                                                        }
                                                    }
                                                }
                                                echo htmlspecialchars($zoneName ?: 'Zona não encontrada');
                                                ?>
                            </div>
                                </div>
                            </div>
                                    <div class="col-md-6">
                                        <div class="info-card">
                                            <div class="info-card-header">
                                                <i class="fas fa-calendar-alt me-2"></i>
                                                <span class="info-label">Data da Jornada</span>
                        </div>
                                            <div class="info-card-content">
                                                <?php 
                                                $dataFormatada = date('d/m/Y', strtotime($jornada['data']));
                                                echo htmlspecialchars($dataFormatada);
                                                ?>
                    </div>
                                </div>
                                        <input type="hidden" name="data" value="<?php echo htmlspecialchars($jornada['data']); ?>">
                            </div>
                        </div>
                    </div>
                        
                        <div class="row">
                                <div class="col-md-4 col-lg-3">
                                    <div class="form-group mb-3">
                                        <label for="numeroCacadores" class="form-label">
                                            <i class="fas fa-users me-1"></i>
                                            Número de Caçadores <span class="required">*</span>
                                        </label>
                                        <input type="number" class="form-control" id="numeroCacadores" name="numeroCacadores" min="1" max="999" required value="<?php echo $_POST['numeroCacadores'] ?? $jornada['numeroCacadores']; ?>" style="max-width: 120px;">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                        <!-- Seals Section -->
                                <div class="mb-3">
                            <h6 class="mb-2">
                                <i class="fas fa-tag me-2" style="color: #0a7ea4;" ></i>Selos Atribuídos
                            </h6>
                            <p class="text-muted mb-2" style="font-size: 0.875rem;">Edite os selos atribuídos nesta jornada. Selos selecionados aparecerão marcados a azul.</p>
                            
                            <div id="sealsLoadingMessage" class="seals-placeholder" style="display: none;">
                                <div class="placeholder-content">
                                    <div class="placeholder-icon">
                                        <i class="fas fa-tag"></i>
                                        </div>
                                    <h5 class="placeholder-title">Selos Disponíveis</h5>
                                    <p class="placeholder-text">Carregando selos para esta zona...</p>
                                    </div>
                                </div>
                            <div id="sealsContainer" class="seals-selection-container"></div>
                            <input type="hidden" id="selectedSealsData" name="selosAtribuidos" value="<?php echo htmlspecialchars($_POST['selosAtribuidos'] ?? $jornada['selosAtribuidos']); ?>">
                            </div>



                    </div>
                    
                        <!-- Scientific Studies Section -->
                        <div>
                            <h6 class="mb-2">
                                <i class="fas fa-microscope me-2" style="color: #0a7ea4;" ></i>Contribuição para Estudos Complementares
                                <span class="badge bg-secondary" style="margin-left: 10px;">
                                    <i class="fas fa-info-circle" style="font-size: 0.75rem;color:#fff"></i>Opcional
                                </span>
                            </h6>
                        
                        <div class="row">
                            <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="numeroAsas" class="form-label">
                                            <i class="fas fa-feather-alt me-1"></i>
                                            Número de Asas
                                        </label>
                                        <input type="number" class="form-control" id="numeroAsas" name="numeroAsas" min="0" max="9999" value="<?php echo $_POST['numeroAsas'] ?? $jornada['numeroAsas']; ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="numeroEsfregacos" class="form-label">
                                            <i class="fas fa-tint me-1"></i>
                                            Número de Esfregaços de Sangue
                                        </label>
                                        <input type="number" class="form-control" id="numeroEsfregacos" name="numeroEsfregacos" min="0" max="9999" value="<?php echo $_POST['numeroEsfregacos'] ?? $jornada['numeroEsfregacos']; ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="numeroZaragatoas" class="form-label">
                                            <i class="fas fa-vial me-1"></i>
                                            Número de Zaragatoas
                                        </label>
                                        <input type="number" class="form-control" id="numeroZaragatoas" name="numeroZaragatoas" min="0" max="9999" value="<?php echo $_POST['numeroZaragatoas'] ?? $jornada['numeroZaragatoas']; ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="outros" class="form-label">
                                            <i class="fas fa-plus-circle me-1"></i>
                                            Outros
                                        </label>
                                    <input type="text" class="form-control" id="outros" name="outros" placeholder="Outros materiais..." value="<?php echo $_POST['outros'] ?? $jornada['outros']; ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 pt-3 border-top">
                            <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </a>
                            <button type="submit" class="btn btn-success" id="updateJornadaBtn">
                            <i class="fas fa-save me-2 btn-icon"></i>
                            <span class="btn-text">Atualizar Jornada</span>
                            <span class="btn-loading-text">A atualizar...</span>
                        </button>
                    </div>
                </form>
                </div>
            </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Jornada não encontrada.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Seal Selection Modal -->
    <div class="seal-modal" id="sealModal">
        <div class="seal-modal-content">
            <div class="seal-modal-header">
                <h5>
                    <i class="fas fa-tag"></i>
                    Selo <span id="modalSealNumber"></span>
                </h5>
                <button type="button" class="seal-modal-close" onclick="closeSealModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="seal-modal-body">
                <div class="mb-3">
                    <label class="form-label">Classificação Etária <span class="required">*</span></label>
                    <div class="age-selection">
                        <div class="age-option" data-age="adulto" onclick="selectAge('adulto')">
                            <div class="age-icon">
                                <i class="fas fa-dove"></i>
                            </div>
                            <div class="age-label">Adulto</div>
                        </div>
                        <div class="age-option" data-age="juvenil" onclick="selectAge('juvenil')">
                            <div class="age-icon juvenil">
                                <i class="fas fa-dove"></i>
                            </div>
                            <div class="age-label">Juvenil</div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="modalFotoAsaDireita" class="form-label">Foto da Asa Direita <span class="required">*</span></label>
                    <div class="dropzone-container" id="photoDropzone">
                        <input type="file" class="file-input" id="modalFotoAsaDireita" accept="image/*" style="display: none;">
                        <div class="dropzone-area" id="dropzoneArea">
                            <div class="dropzone-content" id="dropzoneContent">
                                <div class="dropzone-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="dropzone-text">
                                    <p class="dropzone-main">Arraste a foto aqui ou clique para seleccionar</p>
                                    <p class="dropzone-sub">Formatos aceites: JPG, PNG, GIF (máx. 5MB)</p>
                                </div>
                            </div>
                            <div class="dropzone-preview" id="dropzonePreview" style="display: none;">
                                <img class="preview-image" id="previewImage" src="" alt="Preview">
                                <div class="preview-info">
                                    <div class="preview-name" id="previewName"></div>
                                    <div class="preview-size" id="previewSize"></div>
                                    <button type="button" class="preview-remove" id="removePhoto">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="info-text">Anexe foto da asa direita para documentação</div>
                </div>
            </div>
            <div class="seal-modal-footer">
                <button type="button" class="btn btn-cancel" onclick="closeSealModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-danger" onclick="unselectCurrentSeal()" id="unselectSealBtn" style="display: none;">
                    <i class="fas fa-times"></i>
                    Desmarcar
                </button>
                <button type="button" class="btn btn-submit" onclick="confirmSealSelection()" id="confirmSealBtn">
                    <i class="fas fa-check"></i>
                    Confirmar Selo
                </button>
            </div>
        </div>
    </div>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables for seal selection
        let availableSeals = [];
        let selectedSeals = [];
        let currentSealNumber = null;
        let selectedAge = null;
        let isEditMode = false;
        
        // Load seals when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const zoneId = '<?php echo $jornada['zonaId']; ?>';
            if (zoneId) {
                loadSealsForZone(zoneId);
            }
            
            // Parse existing selected seals
            const existingSeals = '<?php echo addslashes($jornada['selosAtribuidos']); ?>';
            if (existingSeals) {
                try {
                    const parsed = JSON.parse(existingSeals);
                    if (Array.isArray(parsed)) {
                        selectedSeals = parsed.map(seal => ({
                            number: seal.number,
                            age: seal.age,
                            photoName: seal.photoName || '',
                            photoUrl: seal.photoUrl || seal.fotoAsaDireita || '', // Include photo URL
                            uploadedAt: seal.uploadedAt || null,
                            photo: null // Will be set if we need to edit
                        }));
                        console.log('Loaded existing seals:', selectedSeals);
                        // Debug: Log each seal's photo data
                        selectedSeals.forEach(seal => {
                            console.log(`Seal ${seal.number}: photoUrl="${seal.photoUrl}", photoName="${seal.photoName}"`);
                        });
                    }
                } catch (e) {
                    console.log('Could not parse existing seals:', e);
                }
            }
            
            // Form submission with loading animation
            const form = document.querySelector('form[method="POST"]');
            console.log('Form found:', form); // Debug log
            if (form) {
                form.addEventListener('submit', function(e) {
                    console.log('Form submit event triggered'); // Debug log
                    const submitBtn = document.getElementById('updateJornadaBtn');
                    console.log('Submit button found:', submitBtn); // Debug log
                    if (submitBtn) {
                        const icon = submitBtn.querySelector('.btn-icon');
                        console.log('Icon found:', icon); // Debug log
                        
                        // Show loading state
                        submitBtn.classList.add('loading');
                        if (icon) {
                            icon.className = 'fas fa-spinner me-2 btn-icon';
                        }
                        
                        // Disable the button to prevent double submission
                        submitBtn.disabled = true;
                        
                        console.log('Loading animation applied'); // Debug log
                    }
                });
                console.log('Form submit listener added'); // Debug log
            } else {
                console.error('Form not found!'); // Debug log
            }
        });
        
        function loadSealsForZone(zoneId) {
            if (!zoneId) return;
            
            // Show loading
            document.getElementById('sealsLoadingMessage').style.display = 'block';
            document.getElementById('sealsContainer').style.display = 'none';
            
            fetch(`?ajax=get_seals&zoneId=${encodeURIComponent(zoneId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        availableSeals = data.seals;
                        displaySeals(availableSeals, data.zoneName);
            } else {
                        console.error('Error loading seals:', data.error);
                        showError('Erro ao carregar selos: ' + (data.error || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Network error loading seals:', error);
                    showError('Erro de rede ao carregar selos');
                })
                .finally(() => {
                    document.getElementById('sealsLoadingMessage').style.display = 'none';
                });
        }
        
        // Display available seals with staggered animation
        function displaySeals(seals, zoneName) {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            if (seals.length === 0) {
                loadingMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Nenhum selo disponível para esta zona';
                sealsContainer.style.display = 'none';
                return;
            }
            
            loadingMessage.style.display = 'none';
            sealsContainer.style.display = 'block';
            
            let html = `<div class="mb-2" style="text-align: center;">
                <h4 style="color: #1e293b; font-weight: 700; display: flex; align-items: center; justify-content: center; gap: 0.5rem; font-size: 1.2rem; margin-bottom: 0.75rem;">
                    <i class="fas fa-binoculars" style="color: #0a7ea4;"></i>
                    ${zoneName}
                </h4>
            </div>`;
            html += '<div class="seals-grid">';
            
            seals.forEach((seal, index) => {
                const isSelected = selectedSeals.some(s => s.number === seal.seloNumber);
                const selectedClass = isSelected ? 'selected' : '';
                const clickAction = isSelected ? `openSealModalForEdit(${seal.seloNumber})` : `openSealModal(${seal.seloNumber})`;
                
                html += `<div class="seal-card-mini ${selectedClass}" 
                              onclick="${clickAction}" 
                              style="animation-delay: ${index * 0.02}s;"
                              data-seal-number="${seal.seloNumber}">
                    <span class="seal-number">${seal.seloNumber}</span>
                </div>`;
            });
            
            html += '</div>';
            sealsContainer.innerHTML = html;
            
            // Add entrance sound effect simulation (visual feedback) and ensure hover works
            setTimeout(() => {
                const sealCards = document.querySelectorAll('.seal-card-mini');
                sealCards.forEach((card, index) => {
                    setTimeout(() => {
                        // Add appeared class to enable proper hover effects
                        card.classList.add('appeared');
                        
                        // Brief scale feedback
                        card.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            card.style.transform = '';
                        }, 150);
                    }, index * 20);
                });
            }, 50);
        }
        
        // Clear seals display
        function clearSeals() {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            loadingMessage.innerHTML = '<i class="fas fa-info-circle me-2"></i>Seleccione uma zona de caça para ver os selos disponíveis';
            loadingMessage.style.display = 'block';
            sealsContainer.style.display = 'none';
            availableSeals = [];
        }
        
        // Show error message
        function showError(message) {
            const loadingMessage = document.getElementById('sealsLoadingMessage');
            const sealsContainer = document.getElementById('sealsContainer');
            
            loadingMessage.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
            loadingMessage.style.display = 'block';
            sealsContainer.style.display = 'none';
        }
        
        // Open seal selection modal
        function openSealModal(sealNumber) {
            currentSealNumber = sealNumber;
            selectedAge = null;
            isEditMode = false;
            
            document.getElementById('modalSealNumber').textContent = sealNumber;
            document.getElementById('sealModal').style.display = 'block';
            
            // Reset form
            resetSealModal();
            
            // Show confirm button, hide unselect button
            document.getElementById('confirmSealBtn').style.display = 'inline-block';
            document.getElementById('unselectSealBtn').style.display = 'none';
            
            // Initialize dropzone
            setTimeout(() => {
                initializeDropzone();
            }, 100);
        }
        
        // Open modal for editing a selected seal
        function openSealModalForEdit(sealNumber) {
            currentSealNumber = sealNumber;
            isEditMode = true;
            
            // Find the selected seal data
            const sealData = selectedSeals.find(s => s.number === sealNumber);
            if (!sealData) return;
            
            document.getElementById('modalSealNumber').textContent = sealNumber;
            document.getElementById('sealModal').style.display = 'block';
            
            // Reset form first
            resetSealModal();
            
            // Pre-populate the form with existing data
            selectedAge = sealData.age;
            
            // Set age selection
            document.querySelector(`[data-age="${sealData.age}"]`).classList.add('selected');
            
            // Check if seal has uploaded photo and display it
            if (sealData.photoUrl && sealData.photoUrl.trim() !== '') {
                console.log('Attempting to load image for seal', sealNumber, ':', sealData.photoUrl);
                
                const dropzoneContent = document.getElementById('dropzoneContent');
                const dropzonePreview = document.getElementById('dropzonePreview');
                const previewImage = document.getElementById('previewImage');
                const previewName = document.getElementById('previewName');
                const previewSize = document.getElementById('previewSize');
                
                // Show the uploaded photo with error handling
                previewImage.onload = function() {
                    // Image loaded successfully
                    console.log('Image loaded successfully for seal', sealNumber);
                    dropzoneContent.style.display = 'none';
                    dropzonePreview.style.display = 'flex';
                };
                
                let imageErrorHandled = false; // Prevent recursive error handling
                
                previewImage.onerror = function() {
                    // Prevent recursive error handling
                    if (imageErrorHandled) {
                        return;
                    }
                    imageErrorHandled = true;
                    
                    // Image failed to load - try to refresh the URL
                    console.error('Failed to load image for seal', sealNumber, ':', sealData.photoUrl);
                    
                    // Check if URL is empty or invalid
                    if (!sealData.photoUrl || sealData.photoUrl.trim() === '') {
                        console.log('Empty photo URL for seal', sealNumber);
                        showImageError('Nenhuma foto carregada');
                        return;
                    }
                    
                    // Extract the file path from the URL for refresh attempt
                    let photoPath = '';
                    try {
                        const url = new URL(sealData.photoUrl);
                        const pathMatch = url.pathname.match(/\/v0\/b\/[^\/]+\/o\/(.+)/);
                        if (pathMatch) {
                            photoPath = decodeURIComponent(pathMatch[1]);
                            console.log('Extracted photo path:', photoPath);
                            console.log('Original URL:', sealData.photoUrl);
                            console.log('URL pathname:', url.pathname);
                            
                            // Try to refresh the download URL
                            fetch(`?ajax=refresh_photo_url&photoPath=${encodeURIComponent(photoPath)}`)
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success && data.downloadUrl) {
                                        console.log('Got fresh download URL, retrying image load');
                                        // Update the seal data with new URL
                                        sealData.photoUrl = data.downloadUrl;
                                        // Reset error flag for retry
                                        imageErrorHandled = false;
                                        // Try loading the image again
                                        previewImage.src = data.downloadUrl;
                                        return;
                                    } else {
                                        throw new Error(data.error || 'Failed to refresh URL');
                                    }
                                })
                                .catch(error => {
                                    console.error('Failed to refresh photo URL:', error);
                                    // Check if it's a 404 error (file doesn't exist)
                                    if (error.message && error.message.includes('404')) {
                                        console.log('File not found - clearing photo data for seal', sealNumber);
                                        // Clear the photo data from the seal since file doesn't exist
                                        sealData.photoUrl = '';
                                        sealData.photoName = '';
                                        sealData.uploadedAt = null;
                                        showImageError('Foto não encontrada no servidor');
                                    } else {
                                        showImageError('Erro ao carregar foto existente');
                                    }
                                });
                        } else {
                            console.error('Could not extract photo path from URL');
                            showImageError('URL da foto inválida');
                        }
                    } catch (error) {
                        console.error('Error processing photo URL:', error);
                        showImageError('Erro ao processar foto');
                    }
                    
                    function showImageError(customMessage) {
                        const dropzoneIcon = dropzoneContent.querySelector('.dropzone-icon');
                        const dropzoneMain = dropzoneContent.querySelector('.dropzone-main');
                        const dropzoneSub = dropzoneContent.querySelector('.dropzone-sub');
                        
                        if (dropzoneIcon && dropzoneMain && dropzoneSub) {
                            dropzoneIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                            dropzoneIcon.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
                            dropzoneMain.textContent = customMessage || 'Erro ao carregar foto existente';
                            dropzoneSub.textContent = 'Clique para seleccionar nova foto';
                        }
                        
                        dropzoneContent.style.display = 'flex';
                        dropzonePreview.style.display = 'none';
                    }
                };
                
                previewImage.src = sealData.photoUrl;
                previewName.textContent = sealData.photoName || 'Foto carregada';
                previewSize.textContent = sealData.uploadedAt ? 'Carregada em ' + new Date(sealData.uploadedAt).toLocaleString() : 'Foto carregada';
                
                // Initially show preview (will be hidden if image fails to load)
                dropzoneContent.style.display = 'none';
                dropzonePreview.style.display = 'flex';
            } else {
                console.log('No photo URL found for seal', sealNumber, '- showing upload dropzone');
            }
            
            // Show both buttons - allow editing or unselecting
            document.getElementById('confirmSealBtn').style.display = 'inline-block';
            document.getElementById('confirmSealBtn').innerHTML = '<i class="fas fa-save"></i> Actualizar Selo';
            document.getElementById('unselectSealBtn').style.display = 'inline-block';
            
            // Initialize dropzone
            setTimeout(() => {
                initializeDropzone();
            }, 100);
        }
        
        // Close seal modal
        function closeSealModal() {
            document.getElementById('sealModal').style.display = 'none';
            currentSealNumber = null;
            selectedAge = null;
            resetSealModal();
        }
        
        // Reset seal modal form
        function resetSealModal() {
            // Clear age selection
            document.querySelectorAll('.age-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Reset dropzone
            const fileInput = document.getElementById('modalFotoAsaDireita');
            const dropzoneContent = document.getElementById('dropzoneContent');
            const dropzonePreview = document.getElementById('dropzonePreview');
            const previewImage = document.getElementById('previewImage');
            const previewName = document.getElementById('previewName');
            const previewSize = document.getElementById('previewSize');
            
            if (fileInput) fileInput.value = '';
            if (dropzoneContent) dropzoneContent.style.display = 'flex';
            if (dropzonePreview) dropzonePreview.style.display = 'none';
            if (previewImage) previewImage.src = '';
            if (previewName) previewName.textContent = '';
            if (previewSize) previewSize.textContent = '';
        }
        
        // Select age option
        function selectAge(age) {
            selectedAge = age;
            
            // Update UI
            document.querySelectorAll('.age-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-age="${age}"]`).classList.add('selected');
        }
        
        // Confirm seal selection with validation
        async function confirmSealSelection() {
            const fileInput = document.getElementById('modalFotoAsaDireita');
            const file = fileInput.files[0];
            
            // Validate required fields
            const errors = [];
            
            if (!selectedAge) {
                errors.push('Classificação Etária');
            }
            
            if (!file) {
                errors.push('Foto da Asa Direita');
            }
            
            if (errors.length > 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Campos Obrigatórios',
                    html: `Por favor, preencha os seguintes campos:<br><br><strong>${errors.join('<br>')}</strong>`,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
                return;
            }
            
            // Create or update the file input for this seal
            const existingInput = document.getElementById('seal_photo_' + currentSealNumber);
            if (existingInput) {
                existingInput.remove();
            }
            
            // Create a new file input for this seal
            const hiddenFileInput = document.createElement('input');
            hiddenFileInput.type = 'file';
            hiddenFileInput.name = 'seal_photo_' + currentSealNumber;
            hiddenFileInput.id = 'seal_photo_' + currentSealNumber;
            hiddenFileInput.style.display = 'none';
            
            // Transfer the file from modal to the form
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            hiddenFileInput.files = dataTransfer.files;
            
            // Add the input to the form
            document.querySelector('form').appendChild(hiddenFileInput);
            
            // Create seal object
            const sealData = {
                number: currentSealNumber,
                age: selectedAge,
                photoName: file.name
            };
            
            if (isEditMode) {
                // Update existing seal
                const index = selectedSeals.findIndex(s => s.number === currentSealNumber);
                if (index > -1) {
                    selectedSeals[index] = sealData;
                }
            } else {
                // Add new seal
                selectedSeals.push(sealData);
            }
            
            // Update form data
            updateFormData();
            
            // Refresh seals display to show the selected state
            refreshSealsDisplay();
            
            // Close modal
            closeSealModal();
        }
        
        // Unselect a seal (when clicking on selected seal)
        function unselectSeal(sealNumber) {
            const index = selectedSeals.findIndex(s => s.number === sealNumber);
            if (index > -1) {
                selectedSeals.splice(index, 1);
                updateFormData();
                refreshSealsDisplay();
            }
        }
        
        // Unselect the current seal from modal
        function unselectCurrentSeal() {
            if (currentSealNumber && isEditMode) {
                // Show confirmation dialog
                Swal.fire({
                    icon: 'question',
                    title: 'Desmarcar Selo',
                    text: `Tem a certeza que pretende desmarcar o selo ${currentSealNumber}?`,
                    showCancelButton: true,
                    confirmButtonText: 'Sim, Desmarcar',
                    cancelButtonText: 'Cancelar',
                    reverseButtons: true,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm',
                        cancelButton: 'custom-swal-cancel',
                        actions: 'custom-swal-actions'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Store the seal number before any operations
                        const sealNumber = currentSealNumber;
                        
                        // Remove the seal
                        unselectSeal(currentSealNumber);
                        
                        // Close modal
                        closeSealModal();
                        
                        // Show success message with stored seal number
                        Swal.fire({
                            icon: 'success',
                            title: 'Selo Desmarcado',
                            text: `O selo ${sealNumber} foi desmarcado com sucesso.`,
                            timer: 2000,
                            showConfirmButton: false,
                            customClass: {
                                popup: 'custom-swal-popup',
                                title: 'custom-swal-title',
                                content: 'custom-swal-content'
                            },
                            buttonsStyling: false
                        });
                    }
                });
            }
        }
        
        // Refresh the seals display to update selected states
        function refreshSealsDisplay() {
            if (availableSeals.length > 0) {
                const zoneName = '<?php echo addslashes($jornada['nomeZona']); ?>';
                displaySeals(availableSeals, zoneName);
            }
        }
        
        // Update hidden form data
        function updateFormData() {
            const formData = selectedSeals.map(seal => ({
                number: seal.number,
                age: seal.age,
                photoName: seal.photoName
            }));
            
            document.getElementById('selectedSealsData').value = JSON.stringify(formData);
        }
        
        // Dropzone functionality for photo upload
        function initializeDropzone() {
            const dropzoneArea = document.getElementById('dropzoneArea');
            const fileInput = document.getElementById('modalFotoAsaDireita');
            const dropzoneContent = document.getElementById('dropzoneContent');
            const dropzonePreview = document.getElementById('dropzonePreview');
            const previewImage = document.getElementById('previewImage');
            const previewName = document.getElementById('previewName');
            const previewSize = document.getElementById('previewSize');
            const removeBtn = document.getElementById('removePhoto');
            
            // Click to select file
            dropzoneArea.addEventListener('click', () => {
                fileInput.click();
            });
            
            // Drag and drop handlers
            dropzoneArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropzoneArea.classList.add('dragover');
            });
            
            dropzoneArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                if (!dropzoneArea.contains(e.relatedTarget)) {
                    dropzoneArea.classList.remove('dragover');
                }
            });
            
            dropzoneArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dropzoneArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelection(files[0]);
                }
            });
            
            // File input change handler
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    handleFileSelection(file);
                }
            });
            
            // Remove photo handler
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                clearPhoto();
            });
            
            // Handle file selection
            function handleFileSelection(file) {
                // Validate file type
                if (!file.type.startsWith('image/')) {
                    showFileError('Por favor, seleccione apenas ficheiros de imagem.');
                    return;
                }
                
                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    showFileError('O ficheiro deve ter no máximo 5MB.');
                    return;
                }
                
                // Set the file to the input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                
                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewName.textContent = file.name;
                    previewSize.textContent = formatFileSize(file.size);
                    
                    // Show preview, hide dropzone content
                    dropzoneContent.style.display = 'none';
                    dropzonePreview.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            }
            
            // Clear photo
            function clearPhoto() {
                fileInput.value = '';
                dropzoneContent.style.display = 'flex';
                dropzonePreview.style.display = 'none';
                previewImage.src = '';
                previewName.textContent = '';
                previewSize.textContent = '';
            }
            
            // Show file error
            function showFileError(message) {
                Swal.fire({
                    icon: 'error',
                    title: 'Ficheiro Inválido',
                    text: message,
                    customClass: {
                        popup: 'custom-swal-popup',
                        title: 'custom-swal-title',
                        content: 'custom-swal-content',
                        confirmButton: 'custom-swal-confirm'
                    },
                    buttonsStyling: false
                });
            }
        }
        
        // Helper function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
        
        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('sealModal');
            if (event.target === modal) {
                closeSealModal();
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('sealModal');
                if (modal.style.display === 'block') {
                    closeSealModal();
                }
            }
        });
        
        // Note: Photo upload is handled through the seal selection modal in edit mode
    </script>
</body>
</html> 