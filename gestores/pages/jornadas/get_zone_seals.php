<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized', 'debug' => 'User not logged in as gestor']);
    exit();
}

// Clean any previous output
ob_end_clean();

// Set response type
header('Content-Type: application/json');

// Add debug logging
error_log("Get zone seals - User: " . ($_SESSION['user']['email'] ?? 'unknown'));
error_log("Get zone seals - Zone ID: " . ($_GET['zoneId'] ?? 'missing'));

// Use the global database instance
global $database;

// Set the user's auth token for authenticated requests
if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Get user data
$user_nif = $_SESSION['user']['nif'] ?? '';
$user_email = $_SESSION['user']['email'];

// Get zone ID from request
$zoneId = $_GET['zoneId'] ?? '';

if (empty($zoneId)) {
    error_log("Get zone seals - Missing zone ID");
    echo json_encode(['error' => 'Zone ID is required', 'debug' => 'zoneId parameter is empty']);
    exit();
}

try {
    // Get admin token to read data
    $adminToken = $database->getAdminAccessToken();
    if ($adminToken) {
        $database->setAccessToken($adminToken);
    }

    // Verify user has access to this zone
    $userZones = getGestorZones($database, $user_nif);
    $hasAccess = false;
    $zoneInfo = null;
    
    foreach ($userZones as $zone) {
        if ($zone['id'] === $zoneId) {
            $hasAccess = true;
            $zoneInfo = $zone;
            break;
        }
    }
    
    if (!$hasAccess) {
        error_log("Get zone seals - User has no access to zone: $zoneId");
        echo json_encode(['error' => 'No access to this zone', 'debug' => 'User not authorized for this zone']);
        exit();
    }
    
    // Get seals for this zone
    $seals = getSealsForZone($database, $zoneId, $zoneInfo);
    
    // Filter only available seals
    $availableSeals = array_filter($seals, function($seal) {
        return $seal['status'] === 'available';
    });
    
    error_log("Get zone seals - Success. Found " . count($availableSeals) . " available seals for zone: " . $zoneInfo['nomeZona']);
    
    echo json_encode([
        'success' => true,
        'seals' => array_values($availableSeals),
        'zoneName' => $zoneInfo['nomeZona']
    ]);
    
} catch (Exception $e) {
    error_log("Get zone seals error: " . $e->getMessage());
    error_log("Get zone seals error trace: " . $e->getTraceAsString());
    echo json_encode(['error' => 'Failed to load seals', 'debug' => $e->getMessage()]);
}

/**
 * Get hunting zones for the current gestor
 */
function getGestorZones($database, $userNif) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        if (empty($userNif)) {
            return [];
        }
        
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        $allZones = [];
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allZones[] = [
                    'id' => $docId,
                    'zona' => $data['zona'] ?? 'N/A',
                    'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                    'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                    'email' => $data['email'] ?? 'N/A',
                    'quotaZona' => $data['quotaZona'] ?? 0,
                    'minSelo' => $data['minSelo'] ?? 0,
                    'maxSelo' => $data['maxSelo'] ?? 0,
                    'status' => $data['status'] ?? 'not registered',
                    'registeredBy' => $data['registeredBy'] ?? null
                ];
            }
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getGestorZones: " . $e->getMessage());
        return [];
    }
}

/**
 * Get seals for a specific zone
 */
function getSealsForZone($database, $zoneId, $zoneInfo) {
    try {
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        $minSelo = $zoneInfo['minSelo'] ?? 0;
        $maxSelo = $zoneInfo['maxSelo'] ?? 0;
        
        if ($minSelo <= 0 || $maxSelo <= 0 || $minSelo > $maxSelo) {
            return [];
        }
        
        // Get used seals for this zone
        $usedSeals = [];
        try {
            $usedResult = $database->queryDocuments('selosUtilizados', 'zonaId', 'EQUAL', $zoneId);
            
            if ($usedResult && is_array($usedResult)) {
                foreach ($usedResult as $docId => $data) {
                    if (isset($data['seloNumber'])) {
                        $usedSeals[$data['seloNumber']] = [
                            'usedBy' => $data['usedBy'] ?? '',
                            'usedAt' => $data['usedAt'] ?? '',
                            'notes' => $data['notes'] ?? ''
                        ];
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Error getting used seals: " . $e->getMessage());
        }
        
        // Generate all seals in range
        $allSeals = [];
        for ($i = $minSelo; $i <= $maxSelo; $i++) {
            $isUsed = isset($usedSeals[$i]);
            
            $allSeals[] = [
                'seloNumber' => $i,
                'status' => $isUsed ? 'used' : 'available',
                'usedBy' => $isUsed ? $usedSeals[$i]['usedBy'] : null,
                'usedAt' => $isUsed ? $usedSeals[$i]['usedAt'] : null,
                'notes' => $isUsed ? $usedSeals[$i]['notes'] : null
            ];
        }
        
        return $allSeals;
    } catch (Exception $e) {
        error_log("Exception in getSealsForZone: " . $e->getMessage());
        return [];
    }
}
?> 