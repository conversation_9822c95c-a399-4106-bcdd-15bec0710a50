<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Clean any previous output
ob_end_clean();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/dark-mode.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    
    <style>
        .coming-soon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 70vh;
            text-align: center;
            padding: 3rem 2rem;
            position: relative;
        }
        
        .coming-soon-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(10, 126, 164, 0.03) 0%, 
                rgba(6, 182, 212, 0.02) 50%, 
                rgba(14, 165, 233, 0.03) 100%);
            border-radius: 24px;
            z-index: -1;
        }
        
        .coming-soon-icon-wrapper {
            position: relative;
            margin-bottom: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .coming-soon-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 50%, #06b6d4 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            box-shadow: 
                0 8px 32px rgba(10, 126, 164, 0.3),
                0 16px 64px rgba(10, 126, 164, 0.15);
            position: relative;
            animation: pulse 3s ease-in-out infinite;
        }
        
        .coming-soon-icon i {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            line-height: 1;
            transform: translateX(1px);
        }
        
        .coming-soon-icon::before {
            content: '';
            position: absolute;
            inset: -8px;
            background: linear-gradient(135deg, #0a7ea4, #06b6d4);
            border-radius: 50%;
            z-index: -1;
            opacity: 0.2;
            animation: rotate 8s linear infinite;
        }
        
        .coming-soon-icon::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: linear-gradient(135deg, 
                rgba(255,255,255,0.3) 0%, 
                rgba(255,255,255,0) 50%, 
                rgba(255,255,255,0.1) 100%);
            pointer-events: none;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .coming-soon-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 50%, #06b6d4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            letter-spacing: -0.025em;
            line-height: 1.2;
        }
        
        .coming-soon-text {
            font-size: 1.2rem;
            color: #64748b;
            margin-bottom: 2.5rem;
            max-width: 600px;
            line-height: 1.6;
            font-weight: 400;
        }
        
        .coming-soon-card {
            background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
            border: none;
            border-radius: 20px;
            padding: 2.5rem;
            max-width: 700px;
            width: 100%;
            box-shadow: 
                0 4px 16px rgba(0, 0, 0, 0.05),
                0 8px 32px rgba(0, 0, 0, 0.03),
                0 16px 64px rgba(0, 0, 0, 0.02);
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .coming-soon-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0a7ea4 0%, #0891b2 50%, #06b6d4 100%);
        }
        
        .development-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: #0369a1;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-size: 0.95rem;
            font-weight: 500;
            border: 1px solid #bae6fd;
            margin-top: 1rem;
        }
        
        .development-badge i {
            font-size: 0.875rem;
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .coming-soon-container {
                padding: 2rem 1rem;
                min-height: 60vh;
            }
            
            .coming-soon-icon {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
            
            .coming-soon-title {
                font-size: 2rem;
            }
            
            .coming-soon-text {
                font-size: 1.1rem;
            }
            
            .coming-soon-card {
                padding: 2rem;
                margin: 0 1rem;
            }
        }

    </style>
</head>
<body class="<?php echo isset($_COOKIE['darkMode']) && $_COOKIE['darkMode'] === 'true' ? 'dark-mode' : ''; ?>">
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-file-alt"></i>
                Relatórios
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div class="container-fluid">
            
            <div class="coming-soon-container">
                <div class="coming-soon-card">
                    <div class="coming-soon-icon-wrapper">
                        <div class="coming-soon-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                    </div>
                    
                    <h1 class="coming-soon-title">Relatórios da App Móvel</h1>
                    
                    <p class="coming-soon-text">
                        Aqui serão exibidos todos os relatórios criados na aplicação móvel ProROLA.
                    </p>
                    
                    <div class="development-badge">
                        <i class="fas fa-cog"></i>
                        <span>Funcionalidade em preparação</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <!-- Custom JS -->
    <script src="../../../webadmin/assets/js/main.js"></script>
    <script src="../../../webadmin/assets/js/dark-mode.js"></script>
</body>
</html> 