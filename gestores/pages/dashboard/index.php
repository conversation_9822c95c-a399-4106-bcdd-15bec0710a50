<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/system_messages.php';

// Check if user is logged in as gestor
if (!isGestorUser()) {
    ob_end_clean(); // Clear output buffer
    header('Location: ../auth/login.php');
    exit();
}

// Clean any previous output
ob_end_clean();

// Use the global database instance (already initialized in config.php)
global $database;

// Set the user's auth token for authenticated requests
if (isset($_SESSION['user']['auth_token'])) {
    $database->setAccessToken($_SESSION['user']['auth_token']);
}

// Get gestor data and statistics
$user_id = $_SESSION['user']['id'];
$user_email = $_SESSION['user']['email'];
$user_nif = $_SESSION['user']['nif'] ?? '';
$error_message = '';
$stats = [
    'total_zones' => 0,
    'total_trajetos' => 0,
    'zones_with_trajetos' => 0,
    'zones_without_trajetos' => 0,
    'total_quota' => 0
];

$gestorZones = [];
$gestorTrajetos = [];
$recentActivity = [];
$systemMessages = [];

try {
    // Get admin token once and reuse it for all operations - OPTIMIZED
    $adminToken = $database->getAdminAccessToken();
    if ($adminToken) {
        $database->setAccessToken($adminToken);
    }
    
    // Debug: Check what's in the user's Firestore document - OPTIMIZED
    $debugUserData = [];
    try {
        $userDoc = $database->getDocument('gestoresZonaCaca', $user_id);
        $debugUserData = $userDoc ? $userDoc : ['error' => 'User document not found'];
        
        // If user NIF is empty in session but exists in Firestore, update the session
        if (empty($user_nif) && !empty($userDoc['nif'])) {
            $_SESSION['user']['nif'] = $userDoc['nif'];
            $user_nif = $userDoc['nif'];
            $debugUserData['session_updated'] = 'NIF added to session: ' . $user_nif;
        }
    } catch (Exception $e) {
        $debugUserData = ['error' => $e->getMessage()];
    }
    
    // Get zones managed by this gestor using NIF (more secure)
    $gestorZones = getGestorZones($database, $user_nif);
    
    // Get trajetos created by this gestor
    $gestorTrajetos = getGestorTrajetos($database, $user_id);
    
    // Get GPS trajetos created by this gestor
    $gestorMobileTrajetos = getGestorMobileTrajetos($database, $user_id);
    
    // Merge both regular and mobile trajetos for statistics
    $allTrajetos = array_merge($gestorTrajetos, $gestorMobileTrajetos);
    
    // Calculate statistics
    $stats['total_zones'] = count($gestorZones);
    $stats['total_trajetos'] = count($allTrajetos);
    
    // Count zones with and without trajetos (check both regular and mobile trajetos)
    $zonesWithTrajetos = 0;
    foreach ($gestorZones as $zone) {
        $hasTrajetoAssociated = false;
        foreach ($allTrajetos as $trajeto) {
            if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                $hasTrajetoAssociated = true;
                break;
            }
        }
        if ($hasTrajetoAssociated) {
            $zonesWithTrajetos++;
        }
    }
    
    $stats['zones_with_trajetos'] = $zonesWithTrajetos;
    $stats['zones_without_trajetos'] = $stats['total_zones'] - $zonesWithTrajetos;
    $stats['total_quota'] = array_sum(array_column($gestorZones, 'quotaZona'));
    
    // Generate recent activity based on zones and trajetos (including mobile trajetos)
    $recentActivity = generateRecentActivity($gestorZones, $allTrajetos);
    
    // Get active system messages
    $systemMessages = getActiveSystemMessages($database);
    
} catch (Exception $e) {
    error_log("Dashboard data error: " . $e->getMessage());
    $error_message = "Erro ao carregar dados do dashboard.";
}

/**
 * Parse Firestore document format
 */
function parseFirestoreDocument($doc) {
    if (!isset($doc['fields'])) {
        return [];
    }
    
    $result = [];
    foreach ($doc['fields'] as $key => $field) {
        $result[$key] = convertFromFirestoreValue($field);
    }
    
    return $result;
}

/**
 * Convert Firestore field value to PHP value
 */
function convertFromFirestoreValue($field) {
    if (isset($field['stringValue'])) {
        return $field['stringValue'];
    } elseif (isset($field['integerValue'])) {
        return (int)$field['integerValue'];
    } elseif (isset($field['doubleValue'])) {
        return (float)$field['doubleValue'];
    } elseif (isset($field['booleanValue'])) {
        return $field['booleanValue'];
    } elseif (isset($field['timestampValue'])) {
        return $field['timestampValue'];
    } elseif (isset($field['nullValue'])) {
        return null;
    } elseif (isset($field['arrayValue'])) {
        $result = [];
        if (isset($field['arrayValue']['values'])) {
            foreach ($field['arrayValue']['values'] as $value) {
                $result[] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    } elseif (isset($field['mapValue'])) {
        $result = [];
        if (isset($field['mapValue']['fields'])) {
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $result[$key] = convertFromFirestoreValue($value);
            }
        }
        return $result;
    }
    
    return null;
}

/**
 * Get count of used seals for ALL zones at once - OPTIMIZED VERSION
 */
function getAllUsedSealsCounts($database, $zoneIds) {
    $sealCounts = [];
    
    // Initialize all zones with 0 seals
    foreach ($zoneIds as $zoneId) {
        $sealCounts[$zoneId] = 0;
    }
    
    try {
        // Get admin token (will be cached from previous call)
        $adminToken = $database->getAdminAccessToken();
        
        // Fetch ALL jornadas at once with pagination
        $allJornadas = [];
        $nextPageToken = null;
        
        do {
            $url = "https://firestore.googleapis.com/v1/projects/" . FIREBASE_PROJECT_ID . "/databases/(default)/documents/jornadasCaca";
            if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
            }
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status === 200) {
                $pageResult = json_decode($response, true);
                
                if (isset($pageResult['documents'])) {
                    $allJornadas = array_merge($allJornadas, $pageResult['documents']);
                }
                
                $nextPageToken = $pageResult['nextPageToken'] ?? null;
            } else {
                break; // Stop on error
            }
            
        } while ($nextPageToken);
        
        // Process all jornadas and count seals by zone
        foreach ($allJornadas as $doc) {
            $jornada = parseFirestoreDocument($doc);
            $zonaId = $jornada['zonaId'] ?? '';
            
            // Only process if this zone belongs to our user
            if (in_array($zonaId, $zoneIds)) {
                $selosAtribuidos = $jornada['selosAtribuidos'] ?? '';
                
                if (!empty($selosAtribuidos)) {
                    if (is_string($selosAtribuidos)) {
                        // Try to decode JSON
                        $selosJson = json_decode($selosAtribuidos, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($selosJson)) {
                            $sealCounts[$zonaId] += count($selosJson);
                        } else {
                            // If not JSON, count comma-separated values
                            $selosArray = array_filter(explode(',', $selosAtribuidos));
                            $sealCounts[$zonaId] += count($selosArray);
                        }
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Error getting all used seals counts: " . $e->getMessage());
    }
    
    return $sealCounts;
}

/**
 * Get hunting zones for the current gestor - OPTIMIZED VERSION
 */
function getGestorZones($database, $userNif) {
    try {
        if (empty($userNif)) {
            return [];
        }
        
        // Use Firestore query to filter by NIF directly (more secure than email)
        $result = $database->queryDocuments('zonasCaca', 'nifEntidade', 'EQUAL', $userNif);
        
        $allZones = [];
        
        if ($result && is_array($result)) {
            // First, collect all zone IDs
            $zoneIds = array_keys($result);
            
            // Get used seals count for ALL zones at once (single batch query)
            $usedSealsCounts = getAllUsedSealsCounts($database, $zoneIds);
            
            // Now build the zones array with the pre-calculated seal counts
            foreach ($result as $docId => $data) {
                $usedSealsCount = $usedSealsCounts[$docId] ?? 0;
                $totalQuota = $data['quotaZona'] ?? 0;
                $availableQuota = max(0, $totalQuota - $usedSealsCount);
                
                $allZones[] = [
                    'id' => $docId,
                    'zona' => $data['zona'] ?? 'N/A',
                    'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                    'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                    'email' => $data['email'] ?? 'N/A',
                    'quotaZona' => $totalQuota,
                    'availableQuota' => $availableQuota,
                    'usedSeals' => $usedSealsCount,
                    'minSelo' => $data['minSelo'] ?? 0,
                    'maxSelo' => $data['maxSelo'] ?? 0,
                    'status' => $data['status'] ?? 'not registered',
                    'registeredBy' => $data['registeredBy'] ?? null
                ];
            }
        }
        
        return $allZones;
    } catch (Exception $e) {
        error_log("Exception in getGestorZones: " . $e->getMessage());
        return [];
    }
}

/**
 * Get trajetos created by the current gestor - OPTIMIZED VERSION
 */
function getGestorTrajetos($database, $userId) {
    try {
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId
        $result = $database->queryDocuments('zonas', 'createdBy', 'EQUAL', $userId);
        
        $allTrajetos = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                $allTrajetos[] = [
                    'id' => $docId,
                    'name' => $data['name'] ?? '',
                    'description' => $data['description'] ?? '',
                    'zoneId' => $data['zoneId'] ?? '',
                    'status' => $data['status'] ?? 'draft',
                    'difficulty' => $data['difficulty'] ?? 'medio',
                    'distance' => $data['distance'] ?? '0 km',
                    'createdBy' => $data['createdBy'] ?? '',
                    'createdAt' => $data['createdAt'] ?? null,
                    'updatedAt' => $data['updatedAt'] ?? null
                ];
            }
        }
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getGestorTrajetos: " . $e->getMessage());
        return [];
    }
}

/**
 * Get GPS trajetos created by the current gestor
 */
function getGestorMobileTrajetos($database, $userId) {
    try {
        if (empty($userId)) {
            return [];
        }
        
        // Use Firestore query to filter by userId (GPS trajectories use 'userId' field)
        $result = $database->queryDocuments('gestorMobile_trajetos', 'userId', 'EQUAL', $userId);
        
        $allTrajetos = [];
        
        if ($result && is_array($result)) {
            foreach ($result as $docId => $data) {
                // Calculate distance from totalDistance (stored in meters)
                $totalDistance = $data['totalDistance'] ?? 0;
                $distanceKm = $totalDistance > 0 ? number_format($totalDistance / 1000, 2) . ' km' : '0 km';
                
                $allTrajetos[] = [
                    'id' => $docId,
                    'name' => $data['name'] ?? 'Trajeto GPS',
                    'description' => $data['description'] ?? '',
                    'zoneId' => $data['zoneId'] ?? '',
                    'status' => $data['status'] ?? 'active',
                    'difficulty' => $data['difficulty'] ?? 'medio',
                    'distance' => $distanceKm,
                    'createdBy' => $data['createdBy'] ?? '',
                    'createdAt' => $data['createdAt'] ?? null,
                    'updatedAt' => $data['updatedAt'] ?? null,
                    'source' => 'gps' // Mark as GPS trajectory
                ];
            }
        }
        
        return $allTrajetos;
    } catch (Exception $e) {
        error_log("Exception in getGestorMobileTrajetos: " . $e->getMessage());
        return [];
    }
}

/**
 * Generate recent activity based on zones and trajetos
 */
function generateRecentActivity($zones, $trajetos) {
    $activities = [];
    
    // Create a lookup array for zone names
    $zoneNames = [];
    foreach ($zones as $zone) {
        $zoneNames[$zone['id']] = $zone['nomeZona'];
    }
    
    // Add trajeto-related activities
    foreach ($trajetos as $trajeto) {
        $status = strtolower($trajeto['status'] ?? '');
        $zoneName = $zoneNames[$trajeto['zoneId']] ?? 'Zona Desconhecida';
        $isGpsTrajeto = ($trajeto['source'] ?? '') === 'gps';
        
        if ($status === 'completed' || $status === 'concluído') {
            $activities[] = [
                'icon' => 'success',
                'icon_class' => $isGpsTrajeto ? 'fas fa-mobile-alt' : 'fas fa-route',
                'title' => $isGpsTrajeto ? 'Trajeto GPS criado' : 'Trajeto criado',
                'description' => $trajeto['name'] . ' - ' . $zoneName,
                'time' => 'há ' . rand(1, 7) . ' dias'
            ];
        } elseif ($status === 'draft' || $status === 'rascunho') {
            $activities[] = [
                'icon' => 'warning',
                'icon_class' => $isGpsTrajeto ? 'fas fa-mobile-alt' : 'fas fa-edit',
                'title' => $isGpsTrajeto ? 'Trajeto GPS em edição' : 'Trajeto em edição',
                'description' => $trajeto['name'] . ' - ' . $zoneName,
                'time' => 'há ' . rand(1, 3) . ' horas'
            ];
        } elseif ($status === 'active') {
            // GPS trajetos typically have 'active' status
            $activities[] = [
                'icon' => 'success',
                'icon_class' => 'fas fa-mobile-alt',
                'title' => 'Trajeto GPS ativo',
                'description' => $trajeto['name'] . ' - ' . $zoneName,
                'time' => 'há ' . rand(1, 7) . ' dias'
            ];
        }
    }
    
    // Add zone-related activities
    foreach ($zones as $zone) {
        $status = strtolower($zone['status'] ?? '');
        if ($status === 'registered' || $status === 'registada') {
            $activities[] = [
                'icon' => 'success',
                'icon_class' => 'fas fa-check',
                'title' => 'Zona registada',
                'description' => $zone['nomeZona'],
                'time' => 'há ' . rand(1, 7) . ' dias'
            ];
        }
    }
    
    // Add some sample activities if no real ones
    if (empty($activities)) {
        $activities[] = [
            'icon' => 'info',
            'icon_class' => 'fas fa-info-circle',
            'title' => 'Sistema iniciado',
            'description' => 'Bem-vindo ao sistema de gestão de zonas de caça',
            'time' => 'há 1 hora'
        ];
    }
    
    // Sort by most recent and limit to 5
    usort($activities, function($a, $b) {
        return rand(-1, 1); // Random sort for demo
    });
    
    return array_slice($activities, 0, 5);
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel de Controlo - Gestores de Zonas de Caça - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../../webadmin/assets/css/style.css" rel="stylesheet">
    <link href="../../../webadmin/assets/css/dark-mode.css" rel="stylesheet">
    <!-- Gestores Common CSS -->
    <link href="../../assets/css/gestores-common.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Critical CSS to prevent FOUC -->
    <style>
        /* Prevent FOUC for main layout */
        .content {
            margin-left: 220px !important;
            padding: 1.5rem !important;
            min-height: 100vh !important;
            background-color: #f5f6fa !important;
            transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }
        
        .content.sidebar-collapsed {
            margin-left: 60px !important;
        }
        
        .header {
            position: fixed !important;
            top: 0 !important;
            left: 220px !important;
            right: 0 !important;
            height: 60px !important;
            background: white !important;
            border-bottom: 1px solid #e5e7eb !important;
            display: flex !important;
            align-items: center !important;
            padding: 0 1.5rem !important;
            z-index: 1000 !important;
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }
        
        .header.sidebar-collapsed {
            left: 60px !important;
        }
        
        .content {
            padding-top: 80px !important;
        }
        
        /* Notifications Card Styling - Match News Section */
        .notifications-card {
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #e5e7eb !important;
            overflow: hidden !important;
        }
        
        .notifications-card .card-header {
            padding: 0.7rem 1rem !important;
            border-bottom: 1px solid #e5e7eb !important;
            background: #f9fafb !important;
        }
        
        .notifications-card .card-header h5 {
            margin: 0 !important;
            font-size: 1.125rem !important;
            font-weight: 600 !important;
            color: #374151 !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }
        
        .notifications-close-btn {
            background: none !important;
            border: none !important;
            font-size: 1.1rem !important;
            color: #64748b !important;
            opacity: 0.8 !important;
            transition: all 0.2s ease !important;
            padding: 0.25rem !important;
            width: 28px !important;
            height: 28px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 6px !important;
            cursor: pointer !important;
        }
        
        .notifications-close-btn:hover {
            opacity: 1 !important;
            color: #dc2626 !important;
            background: rgba(239, 68, 68, 0.1) !important;
            transform: scale(1.05) !important;
        }
        
        .system-message-alert {
            border: none !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            margin-bottom: 1rem !important;
            padding: 1rem !important;
            position: relative !important;
            transition: all 0.3s ease !important;
        }
        
        /* Subtle background colors based on message type */
        .system-message-alert.info {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.05) 0%, rgba(10, 126, 164, 0.02) 100%) !important;
            border: 1px solid rgba(10, 126, 164, 0.1) !important;
        }
        
        .system-message-alert.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%) !important;
            border: 1px solid rgba(16, 185, 129, 0.1) !important;
        }
        
        .system-message-alert.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%) !important;
            border: 1px solid rgba(245, 158, 11, 0.1) !important;
        }
        
        .system-message-alert.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%) !important;
            border: 1px solid rgba(239, 68, 68, 0.1) !important;
        }
        
        .system-message-alert:last-child {
            margin-bottom: 0 !important;
        }
        
        .message-content-wrapper {
            display: flex !important;
            align-items: flex-start !important;
            gap: 1rem !important;
            position: relative !important;
        }
        
        .message-icon {
            width: 44px !important;
            height: 44px !important;
            border-radius: 12px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 1.25rem !important;
            flex-shrink: 0 !important;
        }
        
        .system-message-alert.info .message-icon {
            background: linear-gradient(135deg, rgba(10, 126, 164, 0.1) 0%, rgba(10, 126, 164, 0.05) 100%) !important;
            color: #0a7ea4 !important;
        }
        
        .system-message-alert.success .message-icon {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%) !important;
            color: #10b981 !important;
        }
        
        .system-message-alert.warning .message-icon {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%) !important;
            color: #f59e0b !important;
        }
        
        .system-message-alert.error .message-icon {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%) !important;
            color: #ef4444 !important;
        }
        
        .message-body {
            flex: 1 !important;
            min-width: 0 !important;
        }
        
        .message-header-inline {
            display: flex !important;
            align-items: center !important;
            gap: 0.75rem !important;
            margin-bottom: 0.5rem !important;
            flex-wrap: wrap !important;
        }
        
        .message-title {
            font-weight: 600 !important;
            color: #1f2937 !important;
            font-size: 1.1rem !important;
            margin: 0 !important;
            line-height: 1.4 !important;
        }
        
        .message-text {
            color: #4b5563 !important;
            line-height: 1.6 !important;
            font-size: 0.95rem !important;
            margin-bottom: 0.5rem !important;
        }
        
        .message-dates {
            margin-top: 0.75rem !important;
        }
        
        .message-dates small {
            color: #6b7280 !important;
            font-size: 0.85rem !important;
        }
        
        /* Carousel Styles */
        .notifications-carousel-container {
            position: relative !important;
            overflow: hidden !important;
            padding-bottom: 2rem !important; /* Space for pagination dots */
        }
        
        .notifications-carousel {
            position: relative !important;
            width: 100% !important;
            height: auto !important;
        }
        
        .carousel-slide {
            width: 100% !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            opacity: 0 !important;
            transform: translateX(100%) !important;
            transition: all 0.5s ease-in-out !important;
            z-index: 1 !important;
        }
        
        .carousel-slide.active {
            opacity: 1 !important;
            transform: translateX(0) !important;
            position: relative !important;
            z-index: 2 !important;
        }
        
        .carousel-slide.slide-out-left {
            opacity: 0 !important;
            transform: translateX(-100%) !important;
            z-index: 1 !important;
        }
        
        .carousel-slide.slide-in-right {
            opacity: 1 !important;
            transform: translateX(0) !important;
            z-index: 2 !important;
        }
        
        .carousel-pagination {
            position: absolute !important;
            bottom: 0 !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            display: flex !important;
            justify-content: center !important;
            gap: 0.5rem !important;
            padding: 0.5rem 0 !important;
            z-index: 10 !important;
        }
        
        .pagination-dot {
            width: 10px !important;
            height: 10px !important;
            border-radius: 50% !important;
            border: none !important;
            background: #d1d5db !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            opacity: 0.6 !important;
        }
        
        .pagination-dot:hover {
            background: #9ca3af !important;
            opacity: 0.8 !important;
            transform: scale(1.2) !important;
        }
        
        .pagination-dot.active {
            background: #0a7ea4 !important;
            opacity: 1 !important;
            transform: scale(1.3) !important;
        }
        
        /* Pause animation on hover */
        .notifications-carousel-container:hover .carousel-slide {
            animation-play-state: paused !important;
        }

        
        /* Priority badges styling */
        .badge {
            font-size: 0.75rem !important;
            padding: 0.25rem 0.5rem !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .message-content-wrapper {
                padding: 1rem !important;
                gap: 0.75rem !important;
            }
            
            .message-icon {
                width: 36px !important;
                height: 36px !important;
                font-size: 1.1rem !important;
            }
            
            .message-title {
                font-size: 1rem !important;
            }
            
            .message-text {
                font-size: 0.9rem !important;
            }
            
            .message-close-btn {
                top: 0.75rem !important;
                right: 0.75rem !important;
                width: 28px !important;
                height: 28px !important;
            }
        }
        
        /* Main grid critical styles */
        .main-grid {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 1.5rem !important;
            margin-bottom: 2rem !important;
        }
        
        .card {
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #e5e7eb !important;
            overflow: hidden !important;
        }
        
        .card-header {
            padding: 0.7rem 1rem !important;
            border-bottom: 1px solid #e5e7eb !important;
            background: #f9fafb !important;
        }
        
        .card-header h3 {
            margin: 0 !important;
            font-size: 1.125rem !important;
            font-weight: 600 !important;
            color: #374151 !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }
        
        .card-body {
            padding: 1.5rem !important;
        }
        
        /* Zone Cards Styling - Simple design with zonas page visual elements */
        .zone-item-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }
        
        .zone-item-link:hover {
            text-decoration: none;
            color: inherit;
        }
        
        .zone-item {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            box-shadow: 
                0 1px 3px rgba(0, 0, 0, 0.1),
                0 4px 8px rgba(0, 0, 0, 0.05),
                0 8px 16px rgba(0, 0, 0, 0.03);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 0.5rem;
            overflow: hidden;
            position: relative;
        }
        
        .zone-item:hover {
            transform: translateY(-4px);
            box-shadow: 
                0 8px 25px rgba(10, 126, 164, 0.15),
                0 16px 40px rgba(0, 0, 0, 0.1),
                0 32px 64px rgba(0, 0, 0, 0.05);
            border-color: #0a7ea4;
        }
        
        .zone-item-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
            min-height: 100px;
            width: 100%;
        }
        
        .zone-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex: 1;
        }
        
        .zone-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
            flex-shrink: 0;
        }
        
        .zone-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .zone-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .zone-number {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #0a7ea4;
            padding: 0rem 0.3rem;
            border-radius: 20px;
            font-size: 0.6rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid #e2e8f0;
            display: inline-block;
            width: fit-content;
            flex-shrink: 0;
        }
        
        .zone-name {
            color: #1e293b;
            font-size: 1.375rem;
            font-weight: 700;
            margin: 0;
            letter-spacing: -0.025em;
        }
        
        .zone-status {
            display: flex;
            align-items: center;
        }
        
        .status-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
        }
        
        .status-badge.status-active {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status-badge.status-needed {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .zone-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-shrink: 0;
            margin-left: auto;
        }
        
        .quota-box {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.3);
            min-width: 120px;
        }
        
        .quota-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .quota-header i {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        
        .quota-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.9;
            margin-left: 0.25rem;
        }
        
        .quota-number {
            font-size: 0.75rem;
            font-weight: 800;
            line-height: 1;
            letter-spacing: 0.5px;
        }
        
        .quota-progress-container {
            position: relative;
            margin-bottom: 0.4rem;
        }
        
        .quota-progress-bar {
            width: 100%;
            height: 16px;
            background-color: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            display: flex;
        }
        
        .quota-progress-available {
            height: 100%;
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            transition: width 0.3s ease;
            position: relative;
        }
        
        .quota-progress-used {
            height: 100%;
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
            transition: width 0.3s ease;
            position: relative;
        }
        
        .quota-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
            border-radius: 8px;
            transition: width 0.3s ease;
            position: relative;
        }
        
        .quota-percentage-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.65rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            letter-spacing: 0.3px;
            z-index: 2;
        }
        
        .quota-details {
            font-size: 0.7rem;
            font-weight: 500;
            opacity: 0.8;
            line-height: 1.2;
            text-align: center;
        }
        
        .quota-unavailable {
            background: #f1f5f9;
            color: #64748b;
            padding: 1.25rem 1.5rem;
            border-radius: 16px;
            text-align: center;
            border: 2px solid #e2e8f0;
            min-width: 120px;
        }
        
        .quota-progress-disabled {
            background-color: rgba(100, 116, 139, 0.2) !important;
        }
        
        .quota-unavailable .quota-progress-fill {
            background: rgba(100, 116, 139, 0.3) !important;
        }
        
        .quota-unavailable .quota-percentage-text {
            color: #64748b !important;
        }
        
        .quota-unavailable .quota-details {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .quota-unavailable .quota-header {
            justify-content: center;
        }
        
        /* Dark mode enhancements */
        .dark-mode .zone-item {
            background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
            box-shadow: 
                0 1px 3px rgba(0, 0, 0, 0.3),
                0 4px 8px rgba(0, 0, 0, 0.2),
                0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .dark-mode .zone-item:hover {
            box-shadow: 
                0 8px 25px rgba(10, 126, 164, 0.3),
                0 16px 40px rgba(0, 0, 0, 0.2),
                0 32px 64px rgba(0, 0, 0, 0.15);
        }
        
        .dark-mode .zone-number {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            color: #60a5fa;
            border-color: #4b5563;
        }
        
        .dark-mode .zone-name {
            color: #f8fafc;
        }
        
        .dark-mode .status-badge.status-needed {
            background: linear-gradient(135deg, #451a03 0%, #78350f 30%);
            border-color: #d97706;
            color: #fbbf24;
        }
        
        .dark-mode .status-badge.status-active {
            background: linear-gradient(135deg, #064e3b 0%, #065f46 30%);
            border-color: #059669;
            color: #34d399;
        }
        
        .dark-mode .quota-unavailable {
            background: #374151;
            border-color: #4b5563;
            color: #9ca3af;
        }

        /* Instructions Accordion Styling */
        .instructions-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .instructions-card:hover {
            box-shadow: 0 4px 20px rgba(10, 126, 164, 0.12);
            transform: translateY(-2px);
        }

        .instructions-header {
            background: #f9fafb;
            color: #374151;
            padding: 0.7rem 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }

        .instructions-header:hover {
            background: #f3f4f6;
        }

        .instructions-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
        }

        .instructions-title {
            font-weight: 600;
            font-size: 1.125rem;
            margin-bottom: 0;
            color: #374151;
        }

        .instructions-header small {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .accordion-toggle {
            width: 32px;
            height: 32px;
            background: #e5e7eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: #6b7280;
        }

        .accordion-toggle:hover {
            background: #0a7ea4;
            color: white;
        }

        .accordion-toggle i {
            transition: transform 0.3s ease;
            font-size: 0.875rem;
        }

        .instructions-card.expanded .accordion-toggle i {
            transform: rotate(180deg);
        }

        .instructions-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .instructions-card.expanded .instructions-content {
            max-height: 2000px;
        }

        .instructions-steps {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .instruction-step {
            background: #f8fafc;
            border-radius: 16px;
            padding: 0;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .step-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .step-number {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            font-weight: bold;
            backdrop-filter: blur(10px);
        }

        .step-header h4 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .step-content {
            padding: 2rem;
        }

        .subsection {
            margin-bottom: 2rem;
        }

        .subsection:last-child {
            margin-bottom: 0;
        }

        .subsection h5 {
            color: #0a7ea4;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
        }

        .subsection h5 i {
            color: #0a7ea4;
        }

        .subsection p {
            color: #374151;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .instruction-list {
            color: #374151;
            line-height: 1.6;
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }

        .instruction-list li {
            margin-bottom: 0.5rem;
        }

        .example-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .example-box h6 {
            color: #1e40af;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .example-box p {
            color: #3b82f6;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .example-list {
            color: #374151;
            line-height: 1.5;
            margin: 0;
            padding-left: 1.5rem;
        }

        .example-list li {
            margin-bottom: 0.25rem;
        }

        .image-placeholder {
            background: #ffffff;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            color: #64748b;
            transition: all 0.3s ease;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .image-placeholder:hover {
            border-color: #0a7ea4;
            background: #f0f9ff;
            color: #0a7ea4;
        }

        .image-placeholder i {
            font-size: 2rem;
            opacity: 0.6;
        }

        .image-placeholder span {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .instruction-image {
            margin: 1rem 0;
            text-align: center;
        }

        .instruction-image img {
            max-width: 100%;
            height: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .instruction-image img:hover {
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.15);
            transform: translateY(-2px);
        }

        .instructions-footer {
            border-top: 1px solid #e5e7eb;
            padding-top: 1.5rem;
        }

        .instructions-footer .alert {
            background: linear-gradient(135deg, #dbeafe 0%, #e0f2fe 100%);
            border: 1px solid #bfdbfe;
            color: #1e40af;
            border-radius: 8px;
            padding: 1rem;
            margin: 0;
        }

        .instructions-footer .alert i {
            color: #3b82f6;
        }

        /* Dark mode styles for instructions */
        .dark-mode .instructions-card {
            background: #1f2937;
            border-color: #374151;
        }

        .dark-mode .instructions-header {
            background: #111827;
            border-color: #374151;
        }

        .dark-mode .instructions-header:hover {
            background: #1f2937;
        }

        .dark-mode .instructions-title {
            color: #f9fafb;
        }

        .dark-mode .instructions-header small {
            color: #9ca3af;
        }

        .dark-mode .accordion-toggle {
            background: #374151;
            color: #9ca3af;
        }

        .dark-mode .accordion-toggle:hover {
            background: #0a7ea4;
            color: white;
        }

        .dark-mode .instruction-step {
            background: #111827;
            border-color: #374151;
        }

        .dark-mode .step-header h4 {
            color: #f9fafb;
        }

        .dark-mode .step-content {
            background: #111827;
        }

        .dark-mode .subsection h5 {
            color: #60a5fa;
        }

        .dark-mode .subsection h5 i {
            color: #60a5fa;
        }

        .dark-mode .subsection p {
            color: #d1d5db;
        }

        .dark-mode .instruction-list {
            color: #d1d5db;
        }

        .dark-mode .example-box {
            background: #1e3a8a;
            border-color: #3b82f6;
        }

        .dark-mode .example-box h6 {
            color: #dbeafe;
        }

        .dark-mode .example-box p {
            color: #93c5fd;
        }

        .dark-mode .example-list {
            color: #d1d5db;
        }

        .dark-mode .image-placeholder {
            background: #1f2937;
            border-color: #4b5563;
            color: #9ca3af;
        }

        .dark-mode .image-placeholder:hover {
            border-color: #0a7ea4;
            background: #0f172a;
            color: #0a7ea4;
        }

        .dark-mode .instruction-image img {
            border-color: #374151;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .instruction-image img:hover {
            box-shadow: 0 4px 16px rgba(10, 126, 164, 0.25);
        }

        .dark-mode .instructions-footer {
            border-color: #374151;
        }

        .dark-mode .instructions-footer .alert {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            border-color: #3b82f6;
            color: #dbeafe;
        }

        .dark-mode .instructions-footer .alert i {
            color: #60a5fa;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .instructions-steps {
                gap: 1.5rem;
            }
            
            .instructions-title {
                font-size: 1rem;
            }
            
            .instructions-icon {
                width: 36px;
                height: 36px;
                font-size: 0.875rem;
            }

            .accordion-toggle {
                width: 28px;
                height: 28px;
            }

            .step-header {
                padding: 0.75rem 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .step-number {
                width: 32px;
                height: 32px;
                font-size: 1rem;
            }

            .step-header h4 {
                font-size: 1.125rem;
            }

            .step-content {
                padding: 1.5rem;
            }

            .subsection h5 {
                font-size: 1rem;
            }
        }

        /* News Section Styling */
        .news-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.05),
                0 4px 8px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }
        
        .news-card:hover {
            box-shadow: 
                0 4px 12px rgba(10, 126, 164, 0.1),
                0 8px 24px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        
        .news-card .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: 16px 16px 0 0 !important;
            padding: 0.25rem 1.5rem;
        }
        
        .news-card .card-header h5 {
            color: #1e293b;
            font-weight: 600;
            font-size: 1.125rem;
        }
        
        .news-card .card-header i {
            color: #0a7ea4;
        }
        
        .news-close-btn {
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #64748b;
            opacity: 0.8;
            transition: all 0.2s ease;
            padding: 0.25rem;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .news-close-btn:hover {
            opacity: 1;
            color: #dc2626;
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.05);
        }
        
        .news-logo {
            flex-shrink: 0;
        }
        
        .icnf-logo {
            width: 100px;
            height: 100px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .news-title {
            color: #1e293b;
            font-size: 1.25rem;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 0.5rem;
        }
        
        .news-date {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            background: #f1f5f9;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            white-space: nowrap;
        }
        
        .news-text {
            color: #475569;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .news-footer {
            border-top: 1px solid #f1f5f9;
            padding-top: 0.75rem;
        }
        
        .news-source {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .news-link {
            color: #0a7ea4;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            transition: all 0.2s ease;
        }
        
        .news-link:hover {
            color: #0891b2;
            text-decoration: none;
            transform: translateX(2px);
        }
        
        .news-link i {
            font-size: 0.75rem;
        }
        
        /* Dark mode news styling */
        .dark-mode .news-card {
            background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
            border-color: #475569;
        }
        
        .dark-mode .news-card .card-header {
            background: linear-gradient(135deg, #334155 0%, #475569 100%);
            border-bottom-color: #475569;
        }
        
        .dark-mode .news-card .card-header h5 {
            color: #f8fafc;
        }
        
        .dark-mode .news-card .card-header i {
            color: #60a5fa;
        }
        
        .dark-mode .news-close-btn {
            color: #cbd5e1;
        }
        
        .dark-mode .news-close-btn:hover {
            color: #f87171;
            background: rgba(239, 68, 68, 0.2);
        }
        
        .dark-mode .news-title {
            color: #f8fafc;
        }
        
        .dark-mode .news-date {
            background: #475569;
            color: #cbd5e1;
        }
        
        .dark-mode .news-text {
            color: #cbd5e1;
        }
        
        .dark-mode .news-footer {
            border-top-color: #475569;
        }
        
        .dark-mode .news-source {
            color: #94a3b8;
        }
        
        .dark-mode .news-link {
            color: #60a5fa;
        }
        
        .dark-mode .news-link:hover {
            color: #93c5fd;
        }
        
        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr !important;
            }
        }
        
        @media (max-width: 768px) {
            .content {
                margin-left: 0 !important;
                padding: 1rem !important;
            }
            .header {
                left: 0 !important;
            }
            
            .zone-item-content {
                flex-direction: column;
                align-items: stretch;
                gap: 1.5rem;
                padding: 1.5rem;
            }
            
            .zone-left {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 1rem;
            }
            
            .zone-icon {
                width: 56px;
                height: 56px;
                font-size: 1.25rem;
            }
            
            .zone-header {
                flex-direction: column;
                gap: 0.5rem;
                align-items: center;
            }
            
            .zone-name {
                font-size: 1.25rem;
            }
            
            .zone-right {
                order: -1;
                align-self: center;
            }
            
            .quota-box, .quota-unavailable {
                min-width: 100px;
            }
            
            /* News responsive */
            .news-card .d-flex {
                flex-direction: column;
                gap: 1rem;
            }
            
            .news-logo {
                align-self: center;
            }
            
            .icnf-logo {
                width: 80px;
                height: 80px;
            }
            
            .news-title {
                font-size: 1.125rem;
                text-align: center;
            }
            
            .news-footer {
                flex-direction: column;
                gap: 0.75rem;
                text-align: center;
            }
        }
    </style>
</head>
<body class="<?php echo isset($_COOKIE['darkMode']) && $_COOKIE['darkMode'] === 'true' ? 'dark-mode' : ''; ?>">
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- Header -->
    <div class="header" id="header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-chart-line"></i>
                Painel de Controlo
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content" id="content">
        <div>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Debug info for development -->
            <?php if (empty($gestorZones) && !$error_message): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Informação:</strong> A carregar dados das zonas de caça para o email: <?php echo htmlspecialchars($user_email); ?>
                    <br><small>Se não vir dados, verifique se o email está correto e se tem zonas associadas.</small>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            


                        <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                <div class="stat-card">
                        <div class="stat-icon" style="background-color: #0a7ea4;">
                        <i class="fas fa-binoculars"></i>
                    </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($stats['total_zones']); ?></div>
                            <div class="stat-label">Total de Zonas</div>
                </div>
                    </div>
                </div>
                <div class="col-md-3">
                <div class="stat-card">
                        <div class="stat-icon" style="background-color: #16a34a;">
                        <i class="fas fa-route"></i>
                    </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($stats['total_trajetos']); ?></div>
                            <div class="stat-label">Trajetos Criados</div>
                </div>
                    </div>
                </div>
                <div class="col-md-3">
                <div class="stat-card">
                        <div class="stat-icon" style="background-color: #9333ea;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($stats['zones_with_trajetos']); ?></div>
                            <div class="stat-label">Zonas com Trajetos</div>
                </div>
                    </div>
                </div>
                <div class="col-md-3">
                <div class="stat-card">
                        <div class="stat-icon" style="background-color: #ea580c;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($stats['zones_without_trajetos']); ?></div>
                            <div class="stat-label">Zonas sem Trajetos</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Messages Section -->
            <?php if (!empty($systemMessages)): ?>
            <div class="row mb-2" id="systemMessagesSection">
                <div class="col-12">
                    <div class="card notifications-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2" style="color: #0a7ea4;"></i>
                                Notificações
                            </h5>
                            <button type="button" class="notifications-close-btn" onclick="closeNotifications()" aria-label="Fechar notificações">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="card-body" style="padding: 1.5rem !important; padding-bottom: 0 !important;">
                            <div class="notifications-carousel-container">
                                <div class="notifications-carousel" id="notificationsCarousel">
                                    <?php foreach ($systemMessages as $index => $message): ?>
                                    <div class="carousel-slide <?= $index === 0 ? 'active' : '' ?>" data-slide="<?= $index ?>">
                                        <div class="system-message-alert <?= $message['type'] ?>" data-type="<?= $message['type'] ?>">
                                            <div class="message-content-wrapper">
                                                <div class="message-icon">
                                                    <i class="<?= getMessageIcon($message['type']) ?>"></i>
                                                </div>
                                                <div class="message-body">
                                                    <div class="message-header-inline">
                                                        <h6 class="message-title"><?= htmlspecialchars($message['title']) ?></h6>
                                                        <?php if ($message['priority'] !== 'normal'): ?>
                                                        <span class="<?= getPriorityBadgeClass($message['priority']) ?>">
                                                            <?= getPriorityText($message['priority']) ?>
                                                        </span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="message-text">
                                                        <?= nl2br(htmlspecialchars($message['content'])) ?>
                                                    </div>
                                                    <?php if ($message['startDate'] || $message['endDate']): ?>
                                                    <div class="message-dates">
                                                        <small class="text-muted">
                                                            <?php if ($message['startDate'] && $message['endDate']): ?>
                                                                <i class="fas fa-calendar-alt me-1"></i>
                                                                Válida de <?= date('d/m/Y', strtotime($message['startDate'])) ?> a <?= date('d/m/Y', strtotime($message['endDate'])) ?>
                                                            <?php elseif ($message['startDate']): ?>
                                                                <i class="fas fa-calendar-plus me-1"></i>
                                                                Válida a partir de <?= date('d/m/Y', strtotime($message['startDate'])) ?>
                                                            <?php elseif ($message['endDate']): ?>
                                                                <i class="fas fa-calendar-times me-1"></i>
                                                                Válida até <?= date('d/m/Y', strtotime($message['endDate'])) ?>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                

                                
                                <!-- Pagination dots (only show if more than 1 message) -->
                                <?php if (count($systemMessages) > 1): ?>
                                <div class="carousel-pagination">
                                    <?php foreach ($systemMessages as $index => $message): ?>
                                    <button class="pagination-dot <?= $index === 0 ? 'active' : '' ?>" 
                                            onclick="goToSlide(<?= $index ?>)" 
                                            data-slide="<?= $index ?>"
                                            aria-label="Ir para mensagem <?= $index + 1 ?>"></button>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- News Section -->
            <div class="row mb-3" id="newsSection">
                <div class="col-12">
                    <div class="card news-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-newspaper me-2"></i>
                                Notícias
                            </h5>
                            <button type="button" class="news-close-btn" onclick="closeNews()" aria-label="Fechar notícias">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <div class="news-logo me-3">
                                    <img src="../../assets/images/logo_icnf.svg" alt="ICNF" class="icnf-logo">
                                </div>
                                <div class="news-content flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="news-title mb-0">Caça à Rola-Comum Autorizada para 2025/26 e 2026/27</h6>
                                        <span class="news-date">21 maio 2025</span>
                                    </div>
                                    <p class="news-text mb-2">
                                        A caça à rola-comum volta a ser possível durante as épocas venatórias de 2025/26 e 2026/27, 
                                        graças à publicação da Portaria n.º 222-A/2025/1. O ICNF definirá os períodos de caça, 
                                        limites de abate e critérios para seleção dos locais onde a caça será permitida.
                                    </p>
                                    <div class="news-footer d-flex justify-content-between align-items-center">
                                        <span class="news-source">Instituto da Conservação da Natureza e das Florestas</span>
                                        <a href="https://www.icnf.pt/imprensa/cacaarolacomum" target="_blank" class="news-link">
                                            <i class="fas fa-external-link-alt"></i> Ler mais
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions Accordion Section -->
            <div class="row mb-2">
                <div class="col-12">
                    <div class="card instructions-card">
                        <div class="card-header instructions-header" onclick="toggleInstructions()" role="button" tabindex="0" onkeypress="handleInstructionsKeyPress(event)">
                            <div class="d-flex align-items-center justify-content-between w-100">
                                <div class="d-flex align-items-center flex-grow-1">
                                    <div class="instructions-icon me-3">
                                        <i class="fas fa-route"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h5 class="mb-0 instructions-title">
                                            <i class="fas fa-info-circle" style="color: #6b7280;"></i>
                                            Instruções para a realização dos trajetos
                                        </h5>
                                        <small class="text-muted">Clique para expandir as instruções detalhadas</small>
                                    </div>
                                </div>
                                <div class="accordion-toggle d-flex align-items-center justify-content-center">
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                            </div>
                        </div>
                        <div class="instructions-content" id="instructionsContent">
                            <div class="card-body">
                                <div class="instructions-intro mb-4">
                                    <div class="alert alert-primary d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <span><strong>Vai fazer os trajetos para monitorização da população de rola-comum?</strong> Então há 3 fases para seguir com sucesso:</span>
                                    </div>
                                </div>

                                <div class="instructions-steps">
                                    <!-- Step 1: Preparar o trabalho -->
                                    <div class="instruction-step">
                                        <div class="step-header">
                                            <div class="step-number">1</div>
                                            <h4>Preparar o trabalho</h4>
                                        </div>

                                        <div class="step-content">
                                            <div class="subsection">
                                                <h5><i class="fas fa-map-marked-alt me-2"></i>Onde fazer o trajeto?</h5>
                                                <p>O trajeto de 2,5km deve ser feito num habitat que é reconhecido na Zona de Caça, como sendo bom para a criação de rola-comum. Dependendo do tipo de paisagem da zona, poderá passar por uma parcela florestal, pela bordadura de uma linha de água, por um pomar, enfim, compete a cada zona de caça avaliar a situação para selecionar o percurso mais adequado á avaliação da população de rolas que pode estar ali a criar.</p>
                                                <p>Procure identificar num mapa o seu percurso: pode procurar a zona, numa carta militar, ou mapa digital (como o Google maps), identifique o local de inicio e o fim do percurso. E se possível delineie o caminho a fazer, até percorrer pelo menos 2,5km.</p>
                                            </div>

                                            <div class="subsection">
                                                <h5><i class="fas fa-route me-2"></i>Tipo de percurso</h5>
                                                <p>Escolha o trajeto com um percurso preferencialmente linear, fora de caminhos muito usados ou que preveja terem alguma perturbação (preferencialmente num caminho de pé posto ou dentro das parcelas). Este trajeto não deverá ser circular. Isto para evitar duplas contagens, ou seja, para garantir que as rolas que assinalou na parte inicial do percurso não possam de novo ser contadas no fim.</p>
                                            </div>

                                            <div class="subsection">
                                                <h5><i class="fas fa-backpack me-2"></i>Material a levar para fazer o percurso</h5>
                                                <p>Se optar por realizar o percurso e só depois passar a informação para a aplicação ProROLA, é preciso preparar a base de anotação, num mapa com uma escala suficientemente boa para que consiga em qualquer altura anotar a sua localização e a das rolas que encontra (ouve ou observa). Assim sugere-se:</p>
                                                
                                                <ul class="instruction-list">
                                                    <li><strong>a)</strong> Identificar, delinear o trajeto no mapa disponível de base (na carta militar ou imagem de satélite (Google maps, por exemplo).</li>
                                                    <li><strong>b)</strong> Estudar o percurso de modo a arranjar alguma escala de distância que auxilie na localização dos contactos com as rolas. Por exemplo, pode desenhar no mapa que vai levar para o campo, uma banda de largura, centrada no percurso: 50 m largura para cada lado e assim já fica com uma indicação para a localização dos contactos.</li>
                                                    <li><strong>c)</strong> Por conveniência para usar no campo, pode dividir o percurso em 3-4 seções longitudinais; e imprimir cada uma dessas seções numa folha A4 com o trajeto delineado.</li>
                                                </ul>

                                                <p>Poderá registar no mapa a localização dos contactos com as rolas ou levar uma folha extra para fazer todas as anotações necessárias:</p>
                                                
                                                <ul class="instruction-list">
                                                    <li><strong>a)</strong> Data</li>
                                                    <li><strong>b)</strong> Hora do início do percurso (hora e minutos, ex. 6h:45mn), hora a que chegou a meio do percurso e a hora a que o finalizou;</li>
                                                    <li><strong>c)</strong> Condições do estado do tempo: nublado, céu limpo, vento forte, chuviscos, etc..</li>
                                                    <li><strong>d)</strong> Identificação de qualquer constrangimento ou perturbação enquanto está a fazer o percurso (por exemplo, cão a ladrar, presença de gado, rapina no ar, passar junto a um cevadouro)</li>
                                                    <li><strong>e)</strong> Registo dos contactos com a rola-comum</li>
                                                </ul>

                                                <p>Assim, para além dos mapas, não se esqueça da caneta ou outro utensilio que escreva bem nos mapas e nas folhas de campo</p>
                                                <p>Se puder utilizar uns binóculos, fica mais fácil procurar as rolas e identificar os locais onde elas estão</p>
                                            </div>

                                            <div class="subsection">
                                                <h5><i class="fas fa-dove me-2"></i>Relembrar aspetos da identificação da rola-comum</h5>
                                                <p>Estes trajetos são uma contribuição importante para a monitorização da população de rola-comum em Portugal. Assim, e dado que na maioria das zonas de caça para além da rola-comum, há também rola-turca e pombos, é importante estar confiante na maneira de distinguir quer, pela plumagem, quer pelo canto e arrulhos, a rola-comum das restantes espécies. Por isso convém:</p>
                                                
                                                <ul class="instruction-list">
                                                    <li>recordar os sons/canto/arrulhos da rola-comum e como os pode distinguir da rola-turca.</li>
                                                    <li>recordar como distingue a rola-comum da rola turca, com base na cor da plumagem, no padrão das asas ou ao gravata no pescoço.</li>
                                                </ul>

                                                <p>Se tiver a possibilidade, pode procurar na internet, por exemplo, <em>Turtle-dove Call Rola-brava Canto Streptopelia turtur Sound</em>, ou este link <em>European Turtle Dove (Streptopelia turtur) :: xeno-canto</em></p>
                                            </div>

                                            <div class="subsection">
                                                <h5><i class="fas fa-clock me-2"></i>Quando fazer o trajeto</h5>
                                                <p>Escolha um dia de boa visibilidade, de preferência pouco nublado e sem ou com pouco vento, para facilitar a audição das rolas.</p>
                                                <p>O trajeto deverá ser feito durante as primeiras horas, após o nascer do sol: entre 6h e 9h:30mn, para coincidir com o período de maior atividade canora das espécies.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 2: No dia da realização -->
                                    <div class="instruction-step">
                                        <div class="step-header">
                                            <div class="step-number">2</div>
                                            <h4>No dia da realização do trajeto</h4>
                                        </div>

                                        <div class="step-content">
                                            <div class="subsection">
                                                <h5><i class="fas fa-walking me-2"></i>Método durante o percurso</h5>
                                                <p>Quando chegar ao local do início do trajeto, aguarde uns 5 minutos até começar a andar e a registar. Aproveite esse tempo para registar as condições do tempo, a hora do início e outras observações que julgar necessárias.</p>
                                                
                                                <p>Se fizer o trajeto usando a aplicação App ProROLA, basta-lhe chegar ao ponto de início, ligar a App ProROLA, e seguir as indicações. Com a sua permissão para aceder à sua localização, a App regista o seu percurso, as horas e condições meteorológicas em que o fez e facilita na descrição dos contactos com as rolas. É só seguir as indicações.</p>
                                                
                                                <p>O trajeto é feito a pé, a passo lento, de modo a permitir ir detetando as rolas que estão ao longo do trajeto e a identificar a sua localização aproximada no mapa. Sempre que necessário, pare para confirmar algum contacto (ver de onde vem o som, por exemplo) e fazer o respetivo registo.</p>
                                            </div>

                                            <div class="subsection">
                                                <h5><i class="fas fa-clipboard-check me-2"></i>Registo dos contactos com a rola-comum</h5>
                                                <p>Se vai registar a informação em papel, é necessário anotar todo tipo de contacto com rolas:</p>
                                                <p>Os registos devem fazer-se sempre da mesma maneira para facilitar a sua interpretação; No mapa localize e numere os contactos; depois, a cada número deve corresponder uma anotação da situação do contacto.</p>
                                                
                                                <div class="example-box">
                                                    <h6><i class="fas fa-lightbulb me-2"></i>Exemplo do que pode escrever (nº, hora do contacto, tipo de contacto)</h6>
                                                    <p><em>(outras informações que achar pertinentes poder colocar nas "observações")</em></p>
                                                    <ul class="example-list">
                                                        <li><strong>1</strong> – 7h:15mn – rola a cantar num pinheiro</li>
                                                        <li><strong>2</strong> – 7h-20 - rola a voar para a frente do observador</li>
                                                        <li><strong>3</strong> – 7h:25mn – bando de 5 rolas a comer no pousio</li>
                                                        <li><strong>4</strong> – 8h - ninho de rola num pinheiro</li>
                                                        <li><strong>5</strong> – 8:15h – rola no ninho, num sobreiro</li>
                                                        <li><strong>6</strong> – 8:15h – 2 juvenis poisadas</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 3: Após fazer o trajeto -->
                                    <div class="instruction-step">
                                        <div class="step-header">
                                            <div class="step-number">3</div>
                                            <h4>Após fazer o trajeto</h4>
                                        </div>

                                        <div class="step-content">
                                            <div class="subsection">
                                                <h5><i class="fas fa-upload me-2"></i>Transferir os dados recolhidos para a App ProROLA</h5>
                                                <p>Após finalizar o trajeto e anotar a hora (h:mn) em que terminou, chegou a fase de passar todos os dados para a AppProROLA. Tire foto do mapa e das anotações pois vai precisar.</p>
                                                
                                                <p>No computador, aceda à aplicação ProROLA com as credenciais que lhe foram dadas (com o email e a palavra-passe) e siga as instruções de reporte.</p>
                                                
                                                <p>Inicia essa tarefa pelo desenho do percurso que fez.</p>
                                                
                                                <div class="instruction-image">
                                                    <img src="../../assets/images/instructions1.jpg" alt="Interface da aplicação ProROLA" class="img-fluid rounded">
                                                </div>
                                                
                                                <p>Depois, vai transpor para esse desenho no mapa, os locais onde registou, durante a visita cada contacto com a rola-comum. Para isso precisa de saber a hora, o tipo de contacto (exemplo: rola a voar, a cantar, ninho etc..) e onde ocorreu (na arvore, no chão, etc.).</p>
                                                
                                                <p>Siga as instruções e em caso de dúvida, há um botão de "Ajuda" em todas as páginas.</p>
                                                
                                                <p>No final tem que anexar imagem do trajeto, o mapa com as anotações que fez durante e o percurso. Só depois é que poderá encerrar o processo. Guarde toda a informação e está pronto!!</p>
                                                
                                                <div class="instruction-image">
                                                    <img src="../../assets/images/instructions2.jpg" alt="Desenho do percurso na aplicação" class="img-fluid rounded">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="instructions-footer">
                                    <div class="alert alert-info d-flex align-items-center">
                                        <i class="fas fa-headset me-2"></i>
                                        <div>
                                            <span>Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:</span>
                                            <br>
                                            <strong><a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Zones List -->
            <?php if (!empty($gestorZones)): ?>
            <div class="zones-list">
                                <?php foreach ($gestorZones as $zone): ?>
                                <?php 
                                // Find trajeto associated with this zone (check both manual and GPS trajetos)
                                $associatedTrajeto = null;
                                foreach ($allTrajetos as $trajeto) {
                                    if (isset($trajeto['zoneId']) && $trajeto['zoneId'] === $zone['id']) {
                                        $associatedTrajeto = $trajeto;
                                        break;
                                    }
                                }
                                ?>
                <a href="../zonas/" class="zone-item-link">
                    <div class="zone-item">
                        <div class="zone-item-content">
                            <div class="zone-left">
                                <div class="zone-icon">
                                    <i class="fas fa-binoculars"></i>
                                            </div>
                                <div class="zone-details">
                                    <div class="zone-header" style="display: flex; align-items: center; gap: 1rem;">
                                        <h3 class="zone-name"><?php echo htmlspecialchars($zone['nomeZona']); ?></h3>
                                        <div class="zone-number">Zona <?php echo htmlspecialchars($zone['zona']); ?></div>
                                            </div>
                                    <div class="zone-status">
                                        <span class="status-badge <?php echo $associatedTrajeto ? 'status-active' : 'status-needed'; ?>">
                                            <i class="<?php echo $associatedTrajeto ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'; ?>"></i>
                                            <?php echo $associatedTrajeto ? 'Trajeto Ativo' : 'Necessita Trajeto'; ?>
                                                </span>
                    </div>
                </div>
            </div>
                            <div class="zone-right">
                                <?php if ($associatedTrajeto): ?>
                                    <div class="quota-box">
                                        <?php 
                                        // Calculate quota details - CORRECTED LOGIC
                                        $totalQuota = $zone['quotaZona'] ?? 0;
                                        $usedSeals = $zone['usedSeals'] ?? 0;
                                        $availableQuota = $totalQuota - $usedSeals;
                                        $percentageUsed = $totalQuota > 0 ? round(($usedSeals / $totalQuota) * 100) : 0;
                                        $percentageAvailable = 100 - $percentageUsed;
                                        ?>
                                        <div class="quota-header">
                                            <i class="fas fa-dove"></i>
                                            <span class="quota-title">QUOTA</span>
                                            <div class="quota-number"><?php echo number_format($availableQuota); ?></div>
                                        </div>
                                        <div class="quota-progress-container">
                                            <div class="quota-progress-bar">
                                                <!-- Green portion (available) -->
                                                <div class="quota-progress-available" style="width: <?php echo $percentageAvailable; ?>%"></div>
                                                <!-- Red portion (used) -->
                                                <div class="quota-progress-used" style="width: <?php echo $percentageUsed; ?>%"></div>
                                            </div>
                                            <div class="quota-percentage-text"><?php echo $percentageAvailable; ?>%</div>
                                        </div>
                                        <div class="quota-details">
                                            <?php echo number_format($usedSeals); ?> de <?php echo number_format($totalQuota); ?>
                </div>
                                    </div>
                        <?php else: ?>
                                    <div class="quota-unavailable">
                                        <div class="quota-header">
                                            <i class="fas fa-dove"></i>
                                            <span class="quota-title">QUOTA</span>
                                        </div>
                                        <div class="quota-progress-container">
                                            <div class="quota-progress-bar quota-progress-disabled">
                                                <div class="quota-progress-fill" style="width: 0%"></div>
                                            </div>
                                            <div class="quota-percentage-text">0%</div>
                                        </div>
                                        <div class="quota-details">
                                            INDISPONÍVEL
                                        </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
                </a>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

        </div>
    </div>

    <!-- Include Profile Modal -->
    <?php include '../../includes/profile_modal.php'; ?>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Firebase v8 Compatibility -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <!-- Custom JS -->
    <script src="../../../webadmin/assets/js/main.js"></script>
    <script src="../../../webadmin/assets/js/dark-mode.js"></script>

    <style>
        /* Sidebar Header Styles */
        .sidebar-header {
            padding: 0.7rem 1rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(10px);
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            position: relative;
        }

        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
        }

        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1.5rem;
            background: white;
            border-radius: 50%;
            width: 100px;
            height: 100px;
            margin: 0 auto 1.5rem;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .logo-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .sidebar-logo {
            width: 120px;
            height: auto;
            image-rendering: auto;
            image-rendering: -webkit-optimize-contrast;
            shape-rendering: geometricPrecision;
            transform: translateZ(0);
            will-change: transform;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            filter: none;
            backface-visibility: hidden;
        }

        .user-info {
            text-align: center;
            padding: 0;
            margin: 0;
        }

        .user-name-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
            padding: 0.25rem 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            height: 32px;
            box-sizing: border-box;
        }

        .user-name {
            color: #fff;
            font-size: 0.875rem;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            line-height: 1;
            display: flex;
            align-items: center;
            margin: 0;
            padding: 0;
            transform: translateY(-1px);
        }

        .settings-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 0;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.75rem;
            border-radius: 6px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
            flex-shrink: 0;
            line-height: 1;
            box-sizing: border-box;
        }

        .settings-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: rotate(45deg) scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .user-role {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.75rem;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Header and Content Layout - Chrome Compatible */
        .header {
            background-color: #fff;
            padding: 0.5rem 1.25rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            left: 220px !important;
            z-index: 999 !important;
            height: 40px;
            display: flex;
            align-items: center;
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -webkit-transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -moz-transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -o-transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header.sidebar-collapsed {
            left: 60px !important;
        }



        /* Ensure sidebar has correct width and positioning */
        .sidebar {
            width: 220px !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            bottom: 0 !important;
            z-index: 1000 !important;
            overflow-y: auto !important;
        }

        .sidebar.collapsed {
            width: 60px !important;
        }

        /* Override any conflicting main-content rules */
        .main-content {
            margin-left: 0 !important;
            padding-top: 0 !important;
        }

        /* Ensure body has proper layout */
        body {
            margin: 0 !important;
            padding: 0 !important;
            overflow-x: hidden !important;
        }

        /* Ensure container-fluid has proper margins */
        .container-fluid {
            margin: 0 !important;
            max-width: none !important;
        }

        /* Sidebar Navigation */
        .sidebar-nav {
            padding-bottom: 80px; /* Add space for footer */
            overflow-y: auto;
            flex: 1;
        }

        /* Sidebar Footer */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem;
            background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        .version-info {
            text-align: center;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .version-info:hover {
            opacity: 1;
        }

        .app-name {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.75rem;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
            margin-bottom: 0.125rem;
            letter-spacing: 0.25px;
        }

        .version-text {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.625rem;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.15px;
        }

        /* Collapsed Sidebar Styles */
        .sidebar-toggle {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            backdrop-filter: blur(10px);
        }

        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        .sidebar-toggle i {
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .sidebar-toggle {
            position: static;
            margin: 0 auto 0.75rem auto;
            display: block;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 0.75rem 0.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sidebar.collapsed .sidebar-toggle i {
            transform: rotate(180deg);
        }

        /* Sidebar Layout */
        .sidebar {
            display: flex;
            flex-direction: column;
            height: 100vh;
            position: relative;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -webkit-transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -moz-transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -o-transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Collapsed sidebar styles */
        .sidebar.collapsed {
            width: 60px !important;
            overflow: visible;
        }

        .sidebar.collapsed .logo-container {
            width: 40px;
            height: 40px;
            margin: 0;
        }

        .sidebar.collapsed .sidebar-logo {
            width: 40px;
            height: 40px;
        }

        .sidebar.collapsed .user-info {
            display: none;
        }

        .sidebar.collapsed .sidebar-nav a {
            padding: 0.875rem 0.5rem;
            justify-content: center;
            position: relative;
        }

        .sidebar.collapsed .sidebar-nav a .menu-text {
            display: none;
        }

        .sidebar.collapsed .sidebar-nav a i:not(.submenu-icon) {
            margin-right: 0;
            font-size: 1.25rem;
        }

        .sidebar.collapsed .submenu-icon {
            display: none;
        }

        .sidebar.collapsed .has-submenu .submenu {
            display: none;
        }

        /* Tooltip for collapsed sidebar */
        .sidebar.collapsed .sidebar-nav a::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1002;
            pointer-events: none;
        }

        .sidebar.collapsed .sidebar-nav a:hover::after {
            opacity: 1;
            visibility: visible;
            left: 65px;
        }

        /* Collapsed sidebar navigation adjustments */
        .sidebar.collapsed .sidebar-nav {
            padding-bottom: 60px; /* Reduced space for smaller footer */
        }

        /* Collapsed sidebar footer adjustments */
        .sidebar.collapsed .sidebar-footer {
            padding: 0.5rem 0.25rem;
        }

        .sidebar.collapsed .app-name {
            font-size: 0.625rem;
            margin-bottom: 0.125rem;
            line-height: 1.2;
        }

        .sidebar.collapsed .version-text {
            font-size: 0.5rem;
            line-height: 1.2;
        }

        /* Force hardware acceleration for Chrome */
        .sidebar, .header, .content {
            -webkit-transform: translateZ(0);
            -moz-transform: translateZ(0);
            -ms-transform: translateZ(0);
            -o-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            -moz-backface-visibility: hidden;
            -ms-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-perspective: 1000;
            -moz-perspective: 1000;
            -ms-perspective: 1000;
            perspective: 1000;
        }

        /* Mobile adjustments for toggle */
        @media (max-width: 768px) {
            .sidebar-toggle {
                width: 28px;
                height: 28px;
                font-size: 0.875rem;
                top: 0.5rem;
                right: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .sidebar-toggle {
                display: none; /* Hide toggle on very small screens */
            }
        }



        /* Enhanced Zone List */
        .zones-list {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .zone-item-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .zone-item-link:hover {
            text-decoration: none;
            color: inherit;
        }

        .zone-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.75rem;
            background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
            border: none;
            border-radius: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 1px 3px rgba(0, 0, 0, 0.05),
                0 4px 8px rgba(0, 0, 0, 0.02),
                0 8px 16px rgba(0, 0, 0, 0.02);
        }

        .zone-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0a7ea4 0%, #0891b2 50%, #06b6d4 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .zone-item-link:hover .zone-item {
            transform: translateY(-4px) scale(1.01);
            box-shadow: 
                0 8px 25px rgba(10, 126, 164, 0.15),
                0 16px 40px rgba(0, 0, 0, 0.08),
                0 32px 64px rgba(0, 0, 0, 0.05);
        }

        .zone-item-link:hover .zone-item::before {
            opacity: 1;
        }

        .zone-left {
            flex: 1;
        }

        .zone-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .zone-icon-box {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(10, 126, 164, 0.2);
        }

        .zone-icon-box i {
            font-size: 1rem;
        }

        .zone-header h4 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            margin-right: 0.75rem;
        }

        .zone-status {
            flex-shrink: 0;
        }



        .zone-right {
            flex-shrink: 0;
        }

        .quota-box {
            background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
            color: white;
            padding: 1rem 1.25rem;
            border-radius: 12px;
            text-align: center;
            min-width: 80px;
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.2);
        }

        .quota-number {
            font-size: 1.5rem;
            font-weight: 800;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .quota-label {
            font-size: 0.75rem;
            font-weight: 600;
            opacity: 0.9;
            letter-spacing: 0.05em;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
        }

        .quota-label i {
            font-size: 0.75rem;
            opacity: 0.8;
        }



        /* Responsive */
        @media (max-width: 768px) {
            .zone-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1.25rem;
                padding: 1.5rem;
            }

            .zone-right {
                align-self: flex-end;
            }

            .zone-status {
                margin-left: 0;
            }
        }

        /* Horizontal activity styles */
        .activity-list-horizontal {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .activity-item-horizontal {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            transition: background-color 0.2s;
            border-left: 3px solid transparent;
        }

        .activity-item-horizontal:hover {
            background-color: #F9FAFB;
            border-left-color: #0a7ea4;
        }

        .activity-content-horizontal {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }

        .activity-separator {
            color: #D1D5DB;
            font-weight: 500;
            user-select: none;
        }

        .activity-title {
            font-weight: 600;
            color: #374151;
        }

        .activity-desc {
            color: #6B7280;
        }

        .activity-time {
            color: #9CA3AF;
            font-size: 0.875rem;
        }

        /* Custom text colors - no harsh black */
        .text-dark-gray {
            color: #374151 !important;
        }

        /* Zones table styling */
        .zones-table {
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .zones-table thead th {
            background-color: #F9FAFB;
            border-bottom: 1px solid #E5E7EB;
            color: #6B7280;
            font-weight: 500;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            padding: 1rem 1.25rem;
        }

        .zones-table tbody td {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #F3F4F6;
            vertical-align: middle;
        }

        .zones-table tbody tr:last-child td {
            border-bottom: none;
        }

        .zones-table tbody tr:hover {
            background-color: #F8FAFC;
        }

        /* Badge improvements */
        .badge {
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 6px;
        }

        .badge.bg-success {
            background-color: #10B981 !important;
        }

        .badge.bg-warning {
            background-color: #F59E0B !important;
        }

        .badge.bg-danger {
            background-color: #EF4444 !important;
        }

        /* WebAdmin Button Styling - Exact Match */
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.625rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
            gap: 0.5rem;
            border: none;
            margin-right: 0.5rem;
            margin-bottom: 0;
            white-space: nowrap;
        }

        .btn i {
            font-size: 0.875rem;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.813rem;
            border-radius: 6px;
        }

        .btn-primary {
            background-color: #0a7ea4;
            color: white;
        }

        .btn-primary:hover {
            background-color: #096d8c;
            color: white;
        }

        .btn-secondary {
            color: #374151;
            background-color: #e5e7eb;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background-color: #d1d5db;
            border-color: #9ca3af;
            color: #374151;
        }

        .btn-success {
            background-color: #16a34a;
            color: white;
        }

        .btn-success:hover {
            background-color: #15803d;
            color: white;
        }

        .btn-danger {
            background-color: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background-color: #b91c1c;
            color: white;
        }

        /* Table button layout - keep them inline like webadmin */
        .zones-table td:last-child {
            white-space: nowrap;
            min-width: 180px;
        }

        .zones-table td .btn:last-child {
            margin-right: 0;
        }

        /* Card header improvements - soft gray instead of black */
        .card-header h3 {
            color: #374151 !important;
            font-weight: 600;
        }

        /* Stats card text improvements */
        .stat-card .value {
            color: #374151 !important;
        }

        .stat-card .title {
            color: #6B7280;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1rem;
            color: #374151;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }

        .activity-icon.success {
            background-color: #10B981;
        }

        .activity-icon.info {
            background-color: #0a7ea4;
        }

        .activity-icon.warning {
            background-color: #F59E0B;
        }

        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .activity-content-horizontal {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }
            
            .activity-separator {
                display: none;
            }
        }
        

    </style>

    <script>
        function exportData() {
            Swal.fire({
                title: 'Exportar Dados',
                text: 'Esta funcionalidade estará disponível em breve.',
                icon: 'info',
                confirmButtonText: 'OK',
                confirmButtonColor: '#0a7ea4'
            });
        }

        function closeNews() {
            const newsSection = document.getElementById('newsSection');
            if (newsSection) {
                newsSection.style.transition = 'all 0.3s ease';
                newsSection.style.opacity = '0';
                newsSection.style.transform = 'translateY(-20px)';
                
                setTimeout(() => {
                    newsSection.style.display = 'none';
                }, 300);
            }
        }

        function toggleInstructions() {
            const instructionsCard = document.querySelector('.instructions-card');
            const instructionsContent = document.getElementById('instructionsContent');
            
            if (instructionsCard && instructionsContent) {
                instructionsCard.classList.toggle('expanded');
                
                if (instructionsCard.classList.contains('expanded')) {
                    // Expand
                    instructionsContent.style.maxHeight = instructionsContent.scrollHeight + 'px';
                } else {
                    // Collapse
                    instructionsContent.style.maxHeight = '0px';
                }
            }
        }

        function handleInstructionsKeyPress(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                toggleInstructions();
            }
        }

        // System Messages Functions
        function closeNotifications() {
            const systemMessagesSection = document.getElementById('systemMessagesSection');
            if (systemMessagesSection) {
                systemMessagesSection.style.transition = 'all 0.3s ease';
                systemMessagesSection.style.opacity = '0';
                systemMessagesSection.style.transform = 'translateY(-20px)';
                
                setTimeout(() => {
                    systemMessagesSection.style.display = 'none';
                }, 300);
            }
        }

        // Carousel functionality
        let currentSlide = 0;
        let totalSlides = 0;
        let carouselInterval;
        let isHovered = false;

        function initializeCarousel() {
            const slides = document.querySelectorAll('.carousel-slide');
            const container = document.querySelector('.notifications-carousel-container');
            totalSlides = slides.length;
            
            if (totalSlides <= 1) return;
            
            // Set up hover pause functionality
            if (container) {
                container.addEventListener('mouseenter', function() {
                    isHovered = true;
                    if (carouselInterval) {
                        clearInterval(carouselInterval);
                    }
                });
                
                container.addEventListener('mouseleave', function() {
                    isHovered = false;
                    startCarouselTimer();
                });
            }
            
            // Start auto-advance
            startCarouselTimer();
        }

        function startCarouselTimer() {
            if (carouselInterval) {
                clearInterval(carouselInterval);
            }
            
            carouselInterval = setInterval(function() {
                if (!isHovered && totalSlides > 1) {
                    changeSlide(1);
                }
            }, 4000); // Change slide every 4 seconds
        }

        function changeSlide(direction) {
            const slides = document.querySelectorAll('.carousel-slide');
            const dots = document.querySelectorAll('.pagination-dot');
            
            if (slides.length <= 1) return;
            
            const oldSlide = currentSlide;
            
            // Calculate new slide index
            currentSlide += direction;
            
            // Handle wrap-around
            if (currentSlide >= slides.length) {
                currentSlide = 0;
            } else if (currentSlide < 0) {
                currentSlide = slides.length - 1;
            }
            
            // Animate transition
            animateSlideTransition(slides, dots, oldSlide, currentSlide, direction);
        }

        function goToSlide(slideIndex) {
            const slides = document.querySelectorAll('.carousel-slide');
            const dots = document.querySelectorAll('.pagination-dot');
            
            if (slideIndex < 0 || slideIndex >= slides.length || slideIndex === currentSlide) return;
            
            const oldSlide = currentSlide;
            const direction = slideIndex > currentSlide ? 1 : -1;
            currentSlide = slideIndex;
            
            // Animate transition
            animateSlideTransition(slides, dots, oldSlide, currentSlide, direction);
        }

        function animateSlideTransition(slides, dots, oldSlide, newSlide, direction) {
            // Remove all transition classes first
            slides.forEach(slide => {
                slide.classList.remove('active', 'slide-out-left', 'slide-in-right');
            });
            
            // Update dots
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === newSlide);
            });
            
            // Set initial positions
            if (direction > 0) {
                // Moving forward: old slide goes left, new slide comes from right
                slides[oldSlide].classList.add('active');
                slides[newSlide].style.transform = 'translateX(100%)';
                slides[newSlide].style.opacity = '0';
                
                // Force reflow
                slides[newSlide].offsetHeight;
                
                // Start animation
                setTimeout(() => {
                    slides[oldSlide].classList.add('slide-out-left');
                    slides[oldSlide].classList.remove('active');
                    slides[newSlide].classList.add('slide-in-right');
                    slides[newSlide].classList.add('active');
                }, 10);
            } else {
                // Moving backward: old slide goes right, new slide comes from left
                slides[oldSlide].classList.add('active');
                slides[newSlide].style.transform = 'translateX(-100%)';
                slides[newSlide].style.opacity = '0';
                
                // Force reflow
                slides[newSlide].offsetHeight;
                
                // Start animation
                setTimeout(() => {
                    slides[oldSlide].style.transform = 'translateX(100%)';
                    slides[oldSlide].style.opacity = '0';
                    slides[oldSlide].classList.remove('active');
                    slides[newSlide].style.transform = 'translateX(0)';
                    slides[newSlide].style.opacity = '1';
                    slides[newSlide].classList.add('active');
                }, 10);
            }
            
            // Clean up after animation
            setTimeout(() => {
                slides.forEach((slide, index) => {
                    slide.classList.remove('slide-out-left', 'slide-in-right');
                    if (index !== newSlide) {
                        slide.classList.remove('active');
                        slide.style.transform = '';
                        slide.style.opacity = '';
                    }
                });
            }, 500);
        }



        // Initialize instructions accordion and carousel on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state as collapsed
            const instructionsCard = document.querySelector('.instructions-card');
            const instructionsContent = document.getElementById('instructionsContent');
            
            if (instructionsCard && instructionsContent) {
                instructionsContent.style.maxHeight = '0px';
            }
            
            // Initialize carousel
            initializeCarousel();
        });
    </script>
</body>
</html> 