RewriteEngine On
RewriteBase /gestores/

# Firebase Environment Variables for Admin SDK
SetEnv FIREBASE_PROJECT_ID "prorola-a2f66"
SetEnv FIREBASE_PRIVATE_KEY_ID "634f31a42fe43dab65cecd5b9199da60612c76b7"
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
SetEnv FIREBASE_CLIENT_EMAIL "<EMAIL>"
SetEnv FIREBASE_CLIENT_ID "115274394222206322285"
SetEnv FIREBASE_AUTH_URI "https://accounts.google.com/o/oauth2/auth"
SetEnv FIREBASE_TOKEN_URI "https://oauth2.googleapis.com/token"
SetEnv FIREBASE_AUTH_PROVIDER_CERT_URL "https://www.googleapis.com/oauth2/v1/certs"
SetEnv FIREBASE_CLIENT_CERT_URL "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40prorola-a2f66.iam.gserviceaccount.com"
SetEnv FIREBASE_UNIVERSE_DOMAIN "googleapis.com"

# Redirect requests to PHP files if they exist
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [L]

# Redirect root access to login page
RewriteRule ^/?$ pages/auth/login.php [L]

# Handle error pages
ErrorDocument 404 /gestores/pages/error/404.php
ErrorDocument 403 /gestores/pages/error/unauthorized.php
ErrorDocument 500 /gestores/pages/error/500.php

# Prevent directory listing
Options -Indexes

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Prevent access to sensitive files
<Files "*.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

<Files ".htaccess">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# Set proper MIME types
AddType application/javascript .js
AddType text/css .css

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule> 