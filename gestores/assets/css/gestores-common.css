/* Gestores Common Styles - Shared between <PERSON><PERSON><PERSON> and Trajetos pages */

/* Header Styling - Consistent across both pages */
.header {
    background-color: #fff !important;
    padding: 0 1.5rem !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: 220px !important;
    z-index: 999 !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.header.sidebar-collapsed {
    left: 60px !important;
}

.header-content {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.header-actions {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.page-title {
    color: #374151 !important;
    font-size: 1.25rem !important;
    font-weight: 500 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.content {
    margin-left: 220px !important;
    padding: 80px 20px 20px 20px !important;
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    min-height: 100vh !important;
    background-color: #f5f6fa !important;
    width: calc(100% - 220px) !important;
    position: relative !important;
    overflow-x: hidden !important;
}

.content.sidebar-collapsed {
    margin-left: 60px !important;
    width: calc(100% - 60px) !important;
}

/* Help Button Styling - Original Design */
.btn-help {
    background: rgba(10, 126, 164, 0.08) !important;
    color: #0a7ea4 !important;
    border: 1px solid rgba(10, 126, 164, 0.15) !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.813rem !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    height: 32px !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.375rem !important;
}

.btn-help:hover {
    background: #0a7ea4 !important;
    color: white !important;
    border-color: #0a7ea4 !important;
}

.btn-help:active {
    transform: translateY(0);
}

/* Help Modal Styling */
.help-modal-popup {
    border-radius: 1rem !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
    width: auto !important;
}

.help-modal-popup .swal2-actions {
    padding-bottom: 1rem !important;
    margin-bottom: 0 !important;
}

.help-modal-title {
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
    color: white;
    padding: 1.5rem 2rem;
    margin: -1.5rem -2rem 1.5rem -2rem;
    border-radius: 1rem 1rem 0 0;
    font-size: 1.25rem;
    font-weight: 600;
    text-align: center;
}

.help-modal-content {
    text-align: left;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.5rem;
}

.help-section {
    margin-bottom: 2rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.help-section-header {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #0a7ea4;
}

.help-section-body {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.help-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.help-item:hover {
    background: #f1f5f9;
    border-color: #0a7ea4;
    transform: translateX(4px);
}

.help-item span {
    color: #374151;
    font-size: 0.95rem;
    line-height: 1.5;
}

.help-status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.help-status-item:hover {
    background: #f1f5f9;
    border-color: #0a7ea4;
}

.help-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
}

.help-status-desc {
    color: #6b7280;
    font-size: 0.9rem;
}

.help-tip {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.help-tip:hover {
    background: #f1f5f9;
    border-color: #0a7ea4;
}

.help-tip span {
    color: #374151;
    font-size: 0.95rem;
    line-height: 1.5;
}

.help-modal-button {
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%) !important;
    border: none !important;
    padding: 0.75rem 2rem !important;
    font-weight: 500 !important;
    border-radius: 0.5rem !important;
    transition: all 0.3s ease !important;
    margin-bottom: 0.5rem !important;
}

.help-modal-button:hover {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%) !important;
    transform: translateY(-1px) !important;
}

.text-blue {
    color: #0a7ea4 !important;
}

/* Statistics Cards Styling */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

/* Specific icon background colors */
.stat-icon.bg-blue {
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
}

.stat-icon.bg-green {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
}

.stat-icon.bg-purple {
    background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
}

.stat-icon.bg-orange {
    background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
}

/* Trajetos Table Styling */
.trajetos-container {
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.trajetos-table-wrapper {
    overflow-x: auto;
    overflow-y: hidden;
}

.trajetos-table {
    margin: 0;
    border: none;
    width: 100%;
}

.trajetos-table thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 2px solid #e2e8f0;
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    padding: 1.25rem 1rem;
    border-top: none;
    position: sticky;
    top: 0;
    z-index: 10;
    text-align: center;
}

.trajetos-table tbody td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
    border-top: none;
    text-align: center;
}

.trajeto-row {
    transition: all 0.2s ease;
}

.trajeto-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.trajeto-row:last-child td {
    border-bottom: none;
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.empty-state-title {
    color: #374151;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state-text {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.empty-state-info {
    color: #6b7280;
    font-size: 0.875rem;
    margin-top: 1rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #374151;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    margin-top: 0.25rem;
}

/* Table Styling */
.zones-table-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

.zones-table-wrapper .border-bottom {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.zones-table {
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.zones-table thead th {
    background-color: #f8fafc;
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem;
    border: none;
    text-align: center !important;
}

.zones-table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid #e5e7eb;
    text-align: center !important;
}

.zones-table tbody tr:hover {
    background-color: #f9fafb;
}

/* Zone table cell styling - all centered */
.zone-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;
    text-align: center;
}

.zone-number {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #0a7ea4;
    font-size: 0.95rem;
}

.zone-name {
    color: #6b7280;
    font-size: 0.9rem;
    text-align: center;
}

.quota-display,
.selos-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.quota-display i,
.selos-display i {
    color: #0a7ea4;
    font-size: 1rem;
}

.quota-display span,
.selos-display span {
    font-weight: 500;
    color: #374151;
}

.trajeto-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;
    text-align: center;
}

.trajeto-name {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: #059669;
    font-size: 0.9rem;
}

.trajeto-meta {
    color: #6b7280;
    font-size: 0.8rem;
    text-align: center;
}

.no-trajeto {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #dc2626;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Status badges - EXACT copy from help-badge styling */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
}

/* Help modal matching colors - EXACT Bootstrap colors */
.status-badge.bg-success,
.status-complete {
    background-color: #198754;
    color: white;
}

.status-badge.bg-warning,
.status-needs-trajeto {
    background-color: #ffc107;
    color: #000;
}

.status-badge.bg-danger {
    background-color: #dc3545;
    color: white;
}

.status-badge.bg-secondary {
    background-color: #6c757d;
    color: white;
}

.status-in-progress {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-review {
    background-color: #e0e7ff;
    color: #3730a3;
}

/* Actions column styling */
.actions-col {
    width: 200px;
    text-align: center !important;
}

.actions-cell {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.actions-cell .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    min-width: 120px;
    justify-content: center;
}

.actions-cell .btn-primary {
    background-color: #0a7ea4;
    border-color: #0a7ea4;
    color: white;
}

.actions-cell .btn-primary:hover {
    background-color: #0369a1;
    border-color: #0369a1;
}

.actions-cell .btn-success {
    background-color: #059669;
    border-color: #059669;
    color: white;
}

.actions-cell .btn-success:hover {
    background-color: #047857;
    border-color: #047857;
}

.actions-cell .btn-secondary {
    background-color: #6b7280;
    border-color: #6b7280;
    color: white;
}

.actions-cell .btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

/* Button styling */
.btn-primary {
    background-color: #0a7ea4;
    border-color: #0a7ea4;
}

.btn-primary:hover {
    background-color: #0891b2;
    border-color: #0891b2;
}

.btn-success {
    background-color: #059669;
    border-color: #059669;
}

.btn-success:hover {
    background-color: #047857;
    border-color: #047857;
}

.btn-secondary {
    background-color: #6b7280;
    border-color: #6b7280;
}

.btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

/* Status cell styling */
.status-cell {
    text-align: center;
}

.status-cell .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Responsive adjustments */
/* Laptop/Desktop responsive adjustments */
@media (max-width: 1366px) and (min-width: 769px) {
    .help-modal-popup {
        max-width: 85vw !important;
        max-height: 85vh !important;
    }
    
    .help-modal-content {
        max-height: 55vh;
    }
}

@media (max-width: 992px) {
    .header-actions {
        position: static;
        margin-top: 1rem;
        right: auto;
        top: auto;
        transform: none;
    }
    
    .header {
        flex-direction: column;
        align-items: flex-start;
        padding-bottom: 1rem;
    }
    
    .header-content {
        width: 100%;
    }
    
    .header-actions {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .content {
        margin-top: 0;
        padding-top: 1rem;
    }
    
    .sidebar.collapsed ~ .content {
        margin-left: 0 !important;
    }
    
    .sidebar:not(.collapsed) ~ .content {
        margin-left: 0 !important;
    }
    
    .sidebar.collapsed ~ .header {
        margin-left: 0 !important;
    }
    
    .sidebar:not(.collapsed) ~ .header {
        margin-left: 0 !important;
    }
}

@media (max-width: 768px) {
    .header {
        position: static;
        margin-left: 0 !important;
        left: 0 !important;
    }
    
    .content {
        margin-left: 0 !important;
        left: 0 !important;
    }
    
    .help-modal-popup {
        max-width: 95vw !important;
        max-height: 90vh !important;
    }

    .help-modal-content {
        max-height: 65vh;
    }
    
    .help-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .help-section-header {
        font-size: 1rem;
    }
    
    .help-item,
    .help-status-item,
    .help-tip {
        padding: 0.5rem;
    }
    
    .help-item span,
    .help-tip span {
        font-size: 0.9rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .zones-table-wrapper {
        border-radius: 8px;
    }
    
    .zones-table thead th,
    .zones-table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
    }
    
    .zone-number {
        font-size: 0.9rem;
    }
    
    .zone-name {
        font-size: 0.8rem;
    }
    
    .quota-display,
    .selos-display {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .quota-display i,
    .selos-display i {
        font-size: 0.8rem;
    }
    
    .trajeto-name {
        font-size: 0.85rem;
    }
    
    .trajeto-meta {
        font-size: 0.7rem;
    }
    
    .status-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    .actions-cell .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .status-cell .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .zones-table thead th,
    .zones-table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .quota-col,
    .selos-col {
        display: none;
    }
    
    .zone-info-col {
        width: 40%;
    }
    
    .trajeto-col {
        width: 35%;
    }
    
    .status-col {
        width: 25%;
    }
}

/* Muted text for quota/selos when no trajeto */
.text-muted {
    color: #9ca3af !important;
    font-size: 1.2rem;
    font-weight: 300;
}

/* Email Verification Blinking Animation */
.blinking-text {
    animation: blink 2s ease-in-out infinite;
    font-weight: 500 !important;
    color: inherit !important;
}

@keyframes blink {
    0%, 85% {
        opacity: 1;
    }
    90%, 95% {
        opacity: 0.4;
    }
    100% {
        opacity: 1;
    }
}

/* Email Verification Card Styling */
.email-verification-sent {
    border-left: 4px solid #0a7ea4 !important;
}

.email-verification-sent .info-icon {
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 90% {
        transform: scale(1);
    }
    95% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.info-text-secondary {
    margin-top: 0.5rem !important;
    padding: 0.5rem 0.75rem !important;
    background: rgba(10, 126, 164, 0.05) !important;
    border-radius: 6px !important;
    font-size: 0.85rem !important;
    color: #6b7280 !important;
    line-height: 1.4 !important;
    border: 1px solid rgba(10, 126, 164, 0.1) !important;
} 