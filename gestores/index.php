<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'includes/config.php';
require_once 'includes/auth.php';

// Clean any previous output
ob_end_clean();

// Check if user is logged in as gestor
if (isGestorUser()) {
    // Redirect to dashboard
    header('Location: pages/dashboard/');
    exit();
} else {
    // Redirect to login page
    header('Location: pages/auth/login.php');
    exit();
}
?> 