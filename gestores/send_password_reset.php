<?php
// Proxy file to handle password reset requests from the root gestores directory
// This file changes the working directory context and includes the actual implementation

// Change to the auth directory context so relative paths work correctly
$originalDir = getcwd();
chdir(__DIR__ . '/pages/auth');

// Include the actual implementation
require_once 'send_password_reset.php';

// Restore original directory
chdir($originalDir);
?> 