<?php
/**
 * Authentication functions for Gestores system
 */

/**
 * Check if user is logged in as a gestor
 */
function isGestorUser() {
    // Check if session exists and has user data
    if (!isset($_SESSION['user']) || !is_array($_SESSION['user'])) {
        return false;
    }
    
    // Check if user has the correct role for gestores
    $userRole = $_SESSION['user']['role'] ?? '';
    return $userRole === 'gestor_caca';
}

/**
 * Get current user data
 */
function getCurrentUser() {
    return $_SESSION['user'] ?? null;
}

/**
 * Clear gestores session data safely
 * This function preserves temporary registration data while clearing auth data
 */
function clearGestoresAuthSession() {
    // Store temporary registration data to preserve it
    $tempData = [];
    $preserveKeys = [
        'temp_zones', 'temp_zone', 'temp_zone_id', 'temp_nif', 'temp_email',
        'email_zones', 'email_selected', 'email_verification_sent', 
        'temp_user_id', 'temp_password', 'using_password_reset'
    ];
    
    foreach ($preserveKeys as $key) {
        if (isset($_SESSION[$key])) {
            $tempData[$key] = $_SESSION[$key];
        }
    }
    
    // Clear user authentication data
    if (isset($_SESSION['user'])) {
        unset($_SESSION['user']);
    }
    
    // Restore temporary registration data
    foreach ($tempData as $key => $value) {
        $_SESSION[$key] = $value;
    }
    
    error_log("DEBUG: Cleared gestores auth session while preserving registration data");
}

/**
 * Login user with email and password
 */
function loginUser($email, $password) {
    global $database;
    
    try {
        error_log("DEBUG: loginUser called for email: $email");
        
        // Clear any existing token from database instance to ensure fresh authentication
        $database->setAccessToken(null);
        
        // Authenticate with Firebase
        try {
            error_log("DEBUG: Calling Firebase signInWithEmailAndPassword for: $email");
            $authResult = $database->signInWithEmailAndPassword($email, $password);
            error_log("DEBUG: Firebase auth call completed successfully");
        } catch (Exception $authException) {
            error_log("DEBUG: Firebase authentication exception: " . $authException->getMessage());
            return false;
        }
        
        error_log("DEBUG: Firebase auth result: " . json_encode($authResult));
        
        if (!$authResult || !isset($authResult['localId'])) {
            error_log("DEBUG: Firebase authentication failed - no localId in response");
            return false;
        }
        
        $userId = $authResult['localId'];
        error_log("DEBUG: Firebase auth successful, userId: $userId");
        
        // Set the new token for Firestore operations
        $database->setAccessToken($authResult['idToken']);
        
        // Get user data from Firestore
        try {
            error_log("DEBUG: Calling Firestore getDocument for userId: $userId");
            $userData = $database->getDocument('gestoresZonaCaca', $userId);
            error_log("DEBUG: Firestore getDocument call completed");
        } catch (Exception $firestoreException) {
            error_log("DEBUG: Firestore getDocument exception: " . $firestoreException->getMessage());
            return false;
        }
        
        error_log("DEBUG: Firestore user data: " . json_encode($userData));
        
        if (!$userData) {
            error_log("DEBUG: No user data found in gestoresZonaCaca collection for userId: $userId");
            return false;
        }
        
        // Check if user has the correct role
        if (($userData['role'] ?? '') !== 'gestor_caca') {
            error_log("DEBUG: User role mismatch. Expected: gestor_caca, Got: " . ($userData['role'] ?? 'NOT SET'));
            return false;
        }
        
        error_log("DEBUG: User validation successful, creating session");
        
        // IMPORTANT: Completely clear any existing session data before creating new session
        // This ensures no conflicts with previous login attempts
        if (isset($_SESSION['user'])) {
            error_log("DEBUG: Clearing existing session data");
            unset($_SESSION['user']);
        }
        
        // Store user data in session
        try {
            $_SESSION['user'] = [
                'id' => $userId,
                'email' => $userData['email'] ?? $email,
                'name' => $userData['name'] ?? '',
                'role' => $userData['role'] ?? '',
                'verified' => $userData['verified'] ?? false,
                'nif' => $userData['nif'] ?? '',
                'auth_token' => $authResult['idToken'] ?? '',
                'refresh_token' => $authResult['refreshToken'] ?? '',
                'token_expires' => time() + (int)($authResult['expiresIn'] ?? 3600),
                'last_activity' => time()
            ];
            
            error_log("DEBUG: Session data stored, verifying...");
            
            // Verify session was created properly
            if (!isset($_SESSION['user']) || !is_array($_SESSION['user'])) {
                error_log("DEBUG: CRITICAL - Session creation failed! Session user not set or not array");
                return false;
            }
            
            if (($_SESSION['user']['role'] ?? '') !== 'gestor_caca') {
                error_log("DEBUG: CRITICAL - Session role verification failed! Role in session: " . ($_SESSION['user']['role'] ?? 'NOT SET'));
                return false;
            }
            
            error_log("DEBUG: Session created and verified successfully. User role: " . $_SESSION['user']['role']);
            return true;
            
        } catch (Exception $sessionError) {
            error_log("DEBUG: CRITICAL - Session creation exception: " . $sessionError->getMessage());
            return false;
        }
        
    } catch (Exception $e) {
        error_log("Gestores login error: " . $e->getMessage());
        // Clear any partial session data on error
        if (isset($_SESSION['user'])) {
            unset($_SESSION['user']);
        }
        return false;
    }
}

/**
 * Logout user
 */
function logoutUser() {
    // Clear session data
    $_SESSION = array();
    
    // Destroy session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy session
    session_destroy();
}
?> 