<?php
// Ensure this file is included, not accessed directly
if (!defined('SITE_NAME')) {
    exit('No direct script access allowed');
}
?>

<!-- Profile Edit Modal -->
<div id="profileEditModal" class="profile-modal-overlay" style="display: none;">
    <div class="profile-modal-dialog">
        <div class="profile-modal-content">
            <div class="profile-modal-header">
                <h5 class="profile-modal-title">
                    <i class="fas fa-user-edit"></i>
                    Editar Perfil
                </h5>
                <button type="button" class="profile-modal-close" onclick="closeProfileEditModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="profile-modal-body">
                <form id="profileEditForm" method="POST" action="pages/auth/save_profile.php">
                    <input type="hidden" id="profileEditUserId" name="id" value="<?php echo $_SESSION['user']['id']; ?>">
                    
                    <div class="profile-form-group">
                        <label for="profileEditName">Nome:</label>
                        <input type="text" id="profileEditName" name="name" class="profile-form-control" required>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditEmail">Email:</label>
                        <input type="email" id="profileEditEmail" name="email" class="profile-form-control" value="<?php echo htmlspecialchars($_SESSION['user']['email']); ?>" readonly>
                        <small class="profile-help-text">
                            O email não pode ser alterado
                        </small>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditCurrentPassword">Palavra-passe Atual:</label>
                        <input type="password" id="profileEditCurrentPassword" name="current_password" class="profile-form-control">
                        <small class="profile-help-text">
                            Obrigatório para alterar a palavra-passe
                        </small>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditPassword">Nova Palavra-passe:</label>
                        <input type="password" id="profileEditPassword" name="password" class="profile-form-control">
                        <small class="profile-help-text">
                            Deixe em branco para manter a palavra-passe atual
                        </small>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditConfirmPassword">Confirmar Nova Palavra-passe:</label>
                        <input type="password" id="profileEditConfirmPassword" name="confirm_password" class="profile-form-control">
                        <div class="profile-password-requirements">
                            A palavra-passe deve ter pelo menos 6 caracteres e ser igual à confirmação
                        </div>
                    </div>
                </form>
            </div>
            <div class="profile-modal-footer">
                <button type="button" class="profile-btn profile-btn-secondary" onclick="closeProfileEditModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="profile-btn profile-btn-primary" onclick="saveProfileEdit()">
                    <i class="fas fa-save"></i> Guardar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Logout Confirmation Modal -->
<div class="modal" id="logoutModal" style="display: none !important; visibility: hidden !important; opacity: 0 !important;">
    <div class="modal-dialog">
        <div class="modal-content logout-modal">
            <div class="modal-header">
                <div class="modal-title-container">
                    <div class="modal-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h5 class="modal-title">Terminar Sessão</h5>
                </div>
                <button type="button" class="close" onclick="closeLogoutModal()">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="logout-message">
                    <p>Tem a certeza de que pretende terminar a sessão?</p>
                    <p class="logout-subtitle">Será redirecionado para a página de login.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeLogoutModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    Terminar Sessão
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Isolated modal styling with specific selectors */
.profile-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
}

.profile-modal-overlay.show {
    display: flex !important;
}

.profile-modal-dialog {
    background: white !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5) !important;
    width: 90% !important;
    max-width: 500px !important;
    max-height: 90vh !important;
    margin: 0 auto !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
}

.profile-modal-content {
    border-radius: 8px !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
}

.profile-modal-header {
    padding: 0.7rem 1.5rem !important;
    border-bottom: 1px solid #e5e7eb !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%) !important;
    flex-shrink: 0 !important;
}

.profile-modal-title {
    margin: 0 !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.profile-modal-title i {
    font-size: 1.125rem !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

.profile-modal-body {
    padding: 1.5rem !important;
    flex: 1 !important;
    overflow-y: auto !important;
    max-height: calc(90vh - 140px) !important;
}

.profile-modal-body::-webkit-scrollbar {
    width: 6px !important;
}

.profile-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.profile-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
}

.profile-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

.profile-form-group {
    margin-bottom: 0.75rem !important;
}

.profile-form-group label {
    display: block !important;
    margin-bottom: 0.5rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
}

.profile-form-control {
    width: 100% !important;
    padding: 0.625rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 0.875rem !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    box-sizing: border-box !important;
}

.profile-form-control:focus {
    border-color: #0a7ea4 !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25) !important;
}

.profile-form-control[readonly] {
    background-color: #f9fafb !important;
    opacity: 1 !important;
}

.profile-form-control[disabled] {
    background-color: #f3f4f6 !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
    opacity: 0.8 !important;
}

.profile-help-text {
    display: block !important;
    margin-top: 0.25rem !important;
    font-size: 0.75rem !important;
    color: #6b7280 !important;
}

.profile-password-requirements {
    margin-top: 0.25rem !important;
    font-size: 0.75rem !important;
    color: #6b7280 !important;
}

.profile-modal-footer {
    padding: 1rem 1.5rem !important;
    border-top: 1px solid #e5e7eb !important;
    background-color: #f8f9fa !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 0.75rem !important;
    flex-shrink: 0 !important;
}

.profile-modal-close {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: white !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.profile-modal-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.05) !important;
}

.profile-btn {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.625rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    text-align: center !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    transition: all 0.2s !important;
    cursor: pointer !important;
    gap: 0.5rem !important;
    border: none !important;
}

.profile-btn-primary {
    background-color: #0a7ea4 !important;
    color: white !important;
}

.profile-btn-primary:hover {
    background-color: #096d8c !important;
    color: white !important;
}

.profile-btn-secondary {
    background-color: #6b7280 !important;
    color: white !important;
}

.profile-btn-secondary:hover {
    background-color: #374151 !important;
    color: white !important;
}

.profile-btn-danger {
    background-color: #dc2626 !important;
    color: white !important;
}

.profile-btn-danger:hover {
    background-color: #b91c1c !important;
    color: white !important;
}

.profile-alert {
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.profile-alert-success {
    background-color: #DEF7EC !important;
    border: 1px solid #31C48D !important;
    color: #03543F !important;
}

.profile-alert-danger {
    background-color: #FDE8E8 !important;
    border: 1px solid #F98080 !important;
    color: #9B1C1C !important;
}

/* Logout Modal Styling */
.logout-modal {
    border-radius: 16px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.logout-modal .modal-header {
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem 2rem;
    position: relative;
    display: flex;
    align-items: center;
}

.logout-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.modal-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
    width: 100%;
}

.logout-modal .modal-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.logout-modal .modal-icon i {
    font-size: 1.5rem;
    color: white;
}

.logout-modal .modal-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    text-align: center;
    margin-right: 48px; /* Compensate for close button width */
}

.logout-modal .close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 2;
}

.logout-modal .close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.logout-modal .modal-body {
    padding: 2rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.logout-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
}

.logout-message p {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #374151;
    font-weight: 500;
    white-space: nowrap;
    text-align: center;
}

.logout-subtitle {
    font-size: 0.95rem !important;
    color: #6b7280 !important;
    font-weight: 400 !important;
    white-space: nowrap;
    text-align: center;
}

.logout-modal .modal-footer {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.logout-modal .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.logout-modal .btn-secondary {
    background: #6b7280;
    color: white;
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.logout-modal .btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
}

.logout-modal .btn-danger {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.logout-modal .btn-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

/* Modal backdrop - Override any existing modal styles */
#logoutModal.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px);
    z-index: -1 !important; /* Hidden by default */
    display: none !important; /* Completely hidden by default */
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    margin: 0 !important;
    padding: 0 !important;
    pointer-events: none !important; /* Don't block clicks when hidden */
}

#logoutModal.modal[style*="flex"],
#logoutModal.modal[style*="block"],
#logoutModal.modal.show {
    display: flex !important; /* Show as flex when active */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 9999 !important; /* Bring to front when shown */
    pointer-events: auto !important; /* Enable clicks when shown */
}

#logoutModal .modal-dialog {
    position: relative !important;
    margin: 0 !important;
    max-width: 520px !important;
    width: 95% !important;
    min-width: 400px !important;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

#logoutModal.modal[style*="block"] .modal-dialog {
    transform: scale(1) !important;
}

/* Override Bootstrap modal styles completely */
#logoutModal.modal.show,
#logoutModal.modal[style*="block"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#logoutModal .modal-dialog {
    position: static !important;
    display: block !important;
    margin: 0 auto !important;
    vertical-align: middle !important;
}

/* Ensure modal is always centered */
#logoutModal {
    text-align: center;
}

#logoutModal .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}

#logoutModal:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -0.25em;
}
</style>

<script>
// Isolated profile editing functions to prevent conflicts
(function() {
    'use strict';
    
    const siteUrl = '<?php echo GESTORES_SITE_URL ?? ''; ?>';
    
    // Cache for updated profile data
    window.cachedProfileData = window.cachedProfileData || {
        name: '<?php echo htmlspecialchars($_SESSION['user']['name'] ?? ''); ?>'
    };

    // Make editProfile globally available immediately (not waiting for DOM)
    window.editProfile = function() {
        try {
            // Get current user's data (use cached data if available)
            document.getElementById('profileEditUserId').value = '<?php echo $_SESSION['user']['id'] ?? ''; ?>';
            document.getElementById('profileEditName').value = window.cachedProfileData.name;
            
            // Reset password fields
            document.getElementById('profileEditCurrentPassword').value = '';
            document.getElementById('profileEditPassword').value = '';
            document.getElementById('profileEditConfirmPassword').value = '';
            
            // Show the modal
            const modal = document.getElementById('profileEditModal');
            if (modal) {
                modal.classList.add('show');
            }
        } catch (error) {
            console.error('Error opening profile modal:', error);
        }
    };
    
    window.closeProfileEditModal = function() {
        try {
            const modal = document.getElementById('profileEditModal');
            if (modal) {
                modal.classList.remove('show');
            }
            
            // Clear any alert messages
            const alerts = document.querySelectorAll('#profileEditForm .profile-alert');
            alerts.forEach(alert => alert.remove());
        } catch (error) {
            console.error('Error closing profile modal:', error);
        }
    };

    window.showLogoutModal = function() {
        try {
            const modal = document.getElementById('logoutModal');
            modal.classList.add('show');
            modal.style.display = 'flex';
            modal.style.visibility = 'visible';
            modal.style.opacity = '1';
            modal.style.zIndex = '9999';
            modal.style.pointerEvents = 'auto';
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
            
            // Focus trap
            const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            const firstElement = focusableElements[0];
            
            if (firstElement) {
                firstElement.focus();
            }
        } catch (error) {
            console.error('Error opening logout modal:', error);
        }
    };

    window.closeLogoutModal = function() {
        try {
            const modal = document.getElementById('logoutModal');
            modal.classList.remove('show');
            modal.style.display = 'none';
            modal.style.visibility = 'hidden';
            modal.style.opacity = '0';
            modal.style.zIndex = '-1';
            modal.style.pointerEvents = 'none';
            
            // Restore body scroll
            document.body.style.overflow = '';
        } catch (error) {
            console.error('Error closing logout modal:', error);
        }
    };

    window.confirmLogout = function() {
        // Add loading state to button
        const logoutBtn = document.querySelector('.logout-modal .btn-danger');
        if (logoutBtn) {
            const originalContent = logoutBtn.innerHTML;
            logoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A terminar...';
            logoutBtn.disabled = true;
        }
        
        // Redirect to logout
        setTimeout(() => {
            window.location.href = '../auth/logout.php';
        }, 500);
    };
    
    window.saveProfileEdit = function() {
        try {
            const form = document.getElementById('profileEditForm');
            const formData = new FormData(form);
            
            // Validate password fields if changing password
            const currentPassword = document.getElementById('profileEditCurrentPassword').value;
            const newPassword = document.getElementById('profileEditPassword').value;
            const confirmPassword = document.getElementById('profileEditConfirmPassword').value;
            
            // If any password field is filled, all must be validated
            if (currentPassword || newPassword || confirmPassword) {
                if (!currentPassword) {
                    showProfileAlert('A palavra-passe atual é obrigatória para alterar a palavra-passe', 'danger');
                    return;
                }
                if (!newPassword) {
                    showProfileAlert('A nova palavra-passe é obrigatória', 'danger');
                    return;
                }
                if (!confirmPassword) {
                    showProfileAlert('A confirmação da palavra-passe é obrigatória', 'danger');
                    return;
                }
                if (newPassword.length < 6) {
                    showProfileAlert('A nova palavra-passe deve ter pelo menos 6 caracteres', 'danger');
                    return;
                }
                if (newPassword !== confirmPassword) {
                    showProfileAlert('As palavras-passe não coincidem', 'danger');
                    return;
                }
            }
            
            // Show loading state
            const submitButton = document.querySelector('#profileEditModal .profile-btn-primary');
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A guardar...';
            
            // For now, simulate successful update
            setTimeout(() => {
                const newName = document.getElementById('profileEditName').value;
                
                showProfileAlert('Perfil atualizado com sucesso', 'success');
                
                // Update the sidebar with new name
                const sidebarName = document.querySelector('.user-name');
                if (sidebarName) {
                    sidebarName.textContent = newName;
                }
                
                // Update cached profile data for next time modal opens
                window.cachedProfileData.name = newName;
                
                // Clear password fields
                document.getElementById('profileEditCurrentPassword').value = '';
                document.getElementById('profileEditPassword').value = '';
                document.getElementById('profileEditConfirmPassword').value = '';
                
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
                
                setTimeout(() => {
                    window.closeProfileEditModal();
                }, 1500);
            }, 1000);
        } catch (error) {
            console.error('Error saving profile:', error);
        }
    };
    
    function showProfileAlert(message, type) {
        try {
            // Remove any existing alerts
            const existingAlerts = document.querySelectorAll('#profileEditForm .profile-alert');
            existingAlerts.forEach(alert => alert.remove());
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `profile-alert profile-alert-${type}`;
            
            const iconClass = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
            alertDiv.innerHTML = `<i class="${iconClass}"></i>${message}`;
            
            const form = document.getElementById('profileEditForm');
            form.insertBefore(alertDiv, form.firstChild);
        } catch (error) {
            console.error('Error showing alert:', error);
        }
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        // Profile modal
        if (e.target.classList.contains('profile-modal-overlay') && e.target.id === 'profileEditModal') {
            window.closeProfileEditModal();
        }
        // Logout modal
        if (e.target.id === 'logoutModal') {
            window.closeLogoutModal();
        }
    });

    // Handle escape key for logout modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const logoutModal = document.getElementById('logoutModal');
            if (logoutModal && logoutModal.style.display === 'flex') {
                window.closeLogoutModal();
            }
        }
    });
})();
</script>