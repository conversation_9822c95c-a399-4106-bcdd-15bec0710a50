<?php

/**
 * Get active system messages from Firestore
 */
function getActiveSystemMessages($database) {
    try {
        // Use admin token for reading system messages
        $adminToken = $database->getAdminAccessToken();
        if ($adminToken) {
            $database->setAccessToken($adminToken);
        }
        
        // Get all system messages with pagination
        $allMessages = [];
        $pageToken = null;
        
        do {
            $result = $database->listDocuments('systemMessages', $pageToken, 1000);
            
            if (!$result || !is_array($result)) {
                break;
            }
            
            // Extract pagination token if present
            $pageToken = $result['_nextPageToken'] ?? null;
            unset($result['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $allMessages = array_merge($allMessages, $result);
            
        } while ($pageToken);
        
        if (empty($allMessages)) {
            return [];
        }
        
        // Use the combined results
        $result = $allMessages;
        
        $activeMessages = [];
        $currentDate = date('Y-m-d');
        
        foreach ($result as $docId => $message) {
            // Check if message is active
            if (!isset($message['active']) || !$message['active']) {
                continue;
            }
            
            // Check date range
            $startDate = $message['startDate'] ?? null;
            $endDate = $message['endDate'] ?? null;
            
            // If start date is set and current date is before start date, skip
            if ($startDate && $currentDate < $startDate) {
                continue;
            }
            
            // If end date is set and current date is after end date, skip
            if ($endDate && $currentDate > $endDate) {
                continue;
            }
            
            $activeMessages[] = [
                'id' => $docId,
                'title' => $message['title'] ?? '',
                'content' => $message['content'] ?? '',
                'type' => $message['type'] ?? 'info',
                'priority' => $message['priority'] ?? 'normal',
                'startDate' => $startDate,
                'endDate' => $endDate,
                'createdAt' => $message['createdAt'] ?? null
            ];
        }
        
        // Sort by priority (high first) and then by creation date (newest first)
        usort($activeMessages, function($a, $b) {
            $priorityOrder = ['high' => 3, 'normal' => 2, 'low' => 1];
            $aPriority = $priorityOrder[$a['priority']] ?? 2;
            $bPriority = $priorityOrder[$b['priority']] ?? 2;
            
            if ($aPriority !== $bPriority) {
                return $bPriority - $aPriority; // High priority first
            }
            
            // If same priority, sort by creation date (newest first)
            return strcmp($b['createdAt'] ?? '', $a['createdAt'] ?? '');
        });
        
        return $activeMessages;
        
    } catch (Exception $e) {
        error_log("Error fetching system messages: " . $e->getMessage());
        return [];
    }
}

/**
 * Get alert class for message type
 */
function getMessageAlertClass($type) {
    switch ($type) {
        case 'success':
            return 'alert-success';
        case 'warning':
            return 'alert-warning';
        case 'error':
            return 'alert-danger';
        case 'info':
        default:
            return 'alert-info';
    }
}

/**
 * Get icon for message type
 */
function getMessageIcon($type) {
    switch ($type) {
        case 'success':
            return 'fas fa-check-circle';
        case 'warning':
            return 'fas fa-exclamation-triangle';
        case 'error':
            return 'fas fa-times-circle';
        case 'info':
        default:
            return 'fas fa-info-circle';
    }
}

/**
 * Get priority badge class
 */
function getPriorityBadgeClass($priority) {
    switch ($priority) {
        case 'high':
            return 'badge bg-danger';
        case 'low':
            return 'badge bg-secondary';
        case 'normal':
        default:
            return 'badge bg-primary';
    }
}

/**
 * Get priority text in Portuguese
 */
function getPriorityText($priority) {
    switch ($priority) {
        case 'high':
            return 'Alta';
        case 'low':
            return 'Baixa';
        case 'normal':
        default:
            return 'Normal';
    }
} 