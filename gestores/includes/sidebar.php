<?php
// Get the current page from the URL path instead of query parameter
$request_uri = $_SERVER['REQUEST_URI'];
$current_page = 'dashboard'; // default

// Detect current page from URL path
if (strpos($request_uri, '/dashboard/') !== false) {
    $current_page = 'dashboard';
} elseif (strpos($request_uri, '/zonas/') !== false) {
    $current_page = 'zonas';
} elseif (strpos($request_uri, '/trajetos/') !== false) {
    $current_page = 'trajetos';
} elseif (strpos($request_uri, '/jornadas/') !== false) {
    $current_page = 'jornadas';
} elseif (strpos($request_uri, '/selos/') !== false) {
    $current_page = 'selos';
} elseif (strpos($request_uri, '/relatorios/') !== false) {
    $current_page = 'relatorios';
}

// Determine base path based on current location
$base_path = '';
if (strpos($_SERVER['REQUEST_URI'], '/pages/') !== false) {
    $base_path = '../'; // We're in a page folder, so go up one level to pages directory
}
?>
<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <!-- Sidebar Toggle Button -->
        <button class="sidebar-toggle" id="sidebarToggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <div class="logo-container">
            <img src="../../../webadmin/assets/img/prorola-logo.svg" alt="ProRola Logo" class="sidebar-logo">
        </div>
        <div class="user-info">
            <div class="user-name-container">
                <span class="user-name"><?php echo htmlspecialchars($_SESSION['user']['name'] ?? 'Gestor'); ?></span>
                <button onclick="editProfile()" class="settings-button" title="Editar Perfil">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
            <div class="user-role">Gestor de Zonas de Caça</div>
        </div>
    </div>
    <nav class="sidebar-nav">
        <ul>
            <li><a href="<?php echo $base_path; ?>dashboard/" <?php echo $current_page == 'dashboard' ? 'class="active"' : ''; ?> data-tooltip="Painel de Controlo"><i class="fas fa-chart-line"></i><span class="menu-text">Painel de Controlo</span></a></li>
            <li><a href="<?php echo $base_path; ?>zonas/" <?php echo $current_page == 'zonas' || $current_page == 'trajetos' ? 'class="active"' : ''; ?> data-tooltip="Zonas de Caça"><i class="fas fa-binoculars"></i><span class="menu-text">Zonas de Caça</span></a></li>
            <li><a href="<?php echo $base_path; ?>jornadas/" <?php echo $current_page == 'jornadas' ? 'class="active"' : ''; ?> data-tooltip="Jornadas de Caça"><i class="fas fa-calendar-alt"></i><span class="menu-text">Jornadas de Caça</span></a></li>
            <li><a href="<?php echo $base_path; ?>selos/" <?php echo $current_page == 'selos' ? 'class="active"' : ''; ?> data-tooltip="Listagem de Selos"><i class="fas fa-tag"></i><span class="menu-text">Listagem de Selos</span></a></li>
            <li><a href="<?php echo $base_path; ?>relatorios/" <?php echo $current_page == 'relatorios' ? 'class="active"' : ''; ?> data-tooltip="Relatórios"><i class="fas fa-file-alt"></i><span class="menu-text">Relatórios</span></a></li>
            <li><a href="#" onclick="showLogoutModal(); return false;" data-tooltip="Terminar Sessão"><i class="fas fa-power-off"></i><span class="menu-text">Terminar Sessão</span></a></li>
        </ul>
    </nav>
    
    <!-- Version Footer -->
    <div class="sidebar-footer">
        <div class="version-info">
            <div class="app-name">ProROLA Gestores</div>
            <div class="version-text">v1.0 © 2025</div>
        </div>
    </div>
</aside>

<style>
/* Prevent FOUC (Flash of Unstyled Content) - Critical inline styles */

/* Sidebar Header */
.sidebar-header {
    padding: 0.7rem 1rem !important;
    text-align: center !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
    backdrop-filter: blur(10px) !important;
    position: relative !important;
}

/* Logo Container */
.logo-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 0.75rem !important;
    background: white !important;
    border-radius: 50% !important;
    width: 100px !important;
    height: 100px !important;
    margin: 0 auto 0.75rem !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.logo-container:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;
}

.sidebar-logo {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block !important;
    /* SVG optimization */
    image-rendering: auto !important;
    image-rendering: -webkit-optimize-contrast !important;
    shape-rendering: geometricPrecision !important;
    /* Hardware acceleration */
    transform: translateZ(0) !important;
    will-change: transform !important;
    /* Anti-aliasing */
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    /* Remove any filters that might blur */
    filter: none !important;
    /* Ensure crisp edges for SVG */
    backface-visibility: hidden !important;
}

.user-info {
    padding: 0;
    margin: 0;
    text-align: center;
}

.user-name-container {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin-bottom: 0.25rem !important;
    padding: 0.25rem 0.5rem !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 16px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    height: 32px !important;
    box-sizing: border-box !important;
}

.user-name {
    color: #fff !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    line-height: 1 !important;
    display: flex !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: translateY(-1px) !important;
}

.settings-button {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    padding: 0 !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    font-size: 0.75rem !important;
    border-radius: 6px !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(5px) !important;
    flex-shrink: 0 !important;
    line-height: 1 !important;
    box-sizing: border-box !important;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: rotate(45deg) scale(1.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.user-role {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.75rem !important;
    font-weight: 400 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding-bottom: 80px !important; /* Add space for footer */
    overflow-y: auto !important;
    flex: 1 !important;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding: 1rem !important;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    z-index: 10 !important;
}

.version-info {
    text-align: center !important;
    opacity: 0.8 !important;
    transition: opacity 0.3s ease !important;
    margin: 0 !important;
    padding: 0 !important;
}

.version-info:hover {
    opacity: 1 !important;
}

.app-name {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15) !important;
    margin-bottom: 0.125rem !important;
    letter-spacing: 0.25px !important;
}

.version-text {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 0.625rem !important;
    font-weight: 400 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    letter-spacing: 0.15px !important;
    margin: 0 !important;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: absolute !important;
    top: 0.75rem !important;
    right: 0.75rem !important;
    width: 32px !important;
    height: 32px !important;
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    color: white !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1rem !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 1001 !important;
    backdrop-filter: blur(10px) !important;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: scale(1.05) !important;
}

.sidebar-toggle i {
    transition: transform 0.3s ease !important;
}

/* Fix icon alignment for all menu items */
.sidebar-nav a {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.sidebar-nav i {
    width: 1.2em !important;
    text-align: center !important;
}

/* Collapsed sidebar styles */
.sidebar.collapsed .logo-container {
    width: 40px !important;
    height: 40px !important;
    margin: 0 !important;
}

.sidebar.collapsed .sidebar-logo {
    width: 40px !important;
    height: 40px !important;
}

.sidebar.collapsed .user-info {
    display: none !important;
}

.sidebar.collapsed .sidebar-nav a {
    padding: 0.875rem 0.5rem !important;
    justify-content: center !important;
    position: relative !important;
}

.sidebar.collapsed .sidebar-nav a .menu-text {
    display: none !important;
}

.sidebar.collapsed .sidebar-nav a i:not(.submenu-icon) {
    margin-right: 0 !important;
    font-size: 1.25rem !important;
}

.sidebar.collapsed .sidebar-toggle {
    position: static !important;
    margin: 0 auto 0.75rem auto !important;
    display: block !important;
}

.sidebar.collapsed .sidebar-header {
    padding: 0.75rem 0.5rem !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg) !important;
}

/* Collapsed sidebar navigation adjustments */
.sidebar.collapsed .sidebar-nav {
    padding-bottom: 60px !important; /* Reduced space for smaller footer */
}

/* Collapsed sidebar footer adjustments */
.sidebar.collapsed .sidebar-footer {
    padding: 0.5rem 0.25rem !important;
}

.sidebar.collapsed .app-name {
    font-size: 0.625rem !important;
    margin-bottom: 0.125rem !important;
    line-height: 1.2 !important;
}

.sidebar.collapsed .version-text {
    font-size: 0.5rem !important;
    line-height: 1.2 !important;
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .sidebar-nav a::after {
    content: attr(data-tooltip) !important;
    position: absolute !important;
    left: 70px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    font-size: 0.75rem !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1002 !important;
    pointer-events: none !important;
}

.sidebar.collapsed .sidebar-nav a:hover::after {
    opacity: 1 !important;
    visibility: visible !important;
    left: 65px !important;
}
</style>

<?php
// Include the new profile modal system
include_once 'profile_modal.php';
?>

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const header = document.getElementById('header');
    const content = document.getElementById('content');
    
    sidebar.classList.toggle('collapsed');
    if (header) header.classList.toggle('sidebar-collapsed');
    if (content) content.classList.toggle('sidebar-collapsed');
    
    // Save state to localStorage (like webadmin)
    const isCollapsed = sidebar.classList.contains('collapsed');
    localStorage.setItem('sidebarCollapsed', isCollapsed);
}

// Restore sidebar state on page load
document.addEventListener('DOMContentLoaded', function() {
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        const sidebar = document.getElementById('sidebar');
        const header = document.getElementById('header');
        const content = document.getElementById('content');
        
        sidebar.classList.add('collapsed');
        if (header) header.classList.add('sidebar-collapsed');
        if (content) content.classList.add('sidebar-collapsed');
    }
});
</script> 