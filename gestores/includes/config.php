<?php
// Set separate session name for gestores - CRITICAL: This must be first!
ini_set('session.name', 'GESTORES_SESSION');
session_name('GESTORES_SESSION');

// Only start session if one hasn't been started yet
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base paths
$scriptPath = dirname(__FILE__);
define('GESTORES_BASE_PATH', dirname($scriptPath));
define('GESTORES_INCLUDES_PATH', $scriptPath);

// Site configuration
define('SITE_NAME', 'ProROLA');
define('GESTORES_SITE_URL', 'https://prorola.app/gestores');

// Firebase configuration
define('FIREBASE_PROJECT_ID', 'prorola-a2f66');
define('FIREBASE_API_KEY', 'AIzaSyAIOTTQiP22chvCkD8q4EwTBnXVNAIa5is');
define('FIREBASE_AUTH_DOMAIN', 'prorola-a2f66.firebaseapp.com');
define('FIREBASE_STORAGE_BUCKET', 'prorola-a2f66.firebasestorage.app');
define('FIREBASE_MESSAGING_SENDER_ID', '1055297588088');
define('FIREBASE_APP_ID', '1:1055297588088:web:8b8b8b8b8b8b8b8b');

// Google Maps API configuration
define('GOOGLE_MAPS_API_KEY', 'AIzaSyAIOTTQiP22chvCkD8q4EwTBnXVNAIa5is');

/**
 * Firebase REST API Helper Class for Gestores
 * This is a simplified version that includes only what gestores needs
 */
class GestoresFirebase {
    private $projectId;
    private $apiKey;
    private $accessToken;
    
    public function __construct($projectId, $apiKey) {
        $this->projectId = $projectId;
        $this->apiKey = $apiKey;
        
        // Check if we have a session with a token
        if (isset($_SESSION['user']) && isset($_SESSION['user']['auth_token'])) {
            $this->setAccessToken($_SESSION['user']['auth_token']);
        }
    }
    
    public function setAccessToken($token) {
        $this->accessToken = $token;
    }
    
    public function signInWithEmailAndPassword($email, $password) {
        $url = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=" . $this->apiKey;
        
        $data = [
            'email' => $email,
            'password' => $password,
            'returnSecureToken' => true
        ];
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30 second timeout
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10 second connection timeout
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        // Check for cURL errors first
        if ($response === false || !empty($curlError)) {
            throw new Exception("Network error during authentication: " . $curlError);
        }
        
        if ($status >= 400) {
            $errorDetails = json_decode($response, true);
            $errorMessage = "Authentication failed (HTTP $status)";
            
            if (isset($errorDetails['error']['message'])) {
                $errorMessage .= ": " . $errorDetails['error']['message'];
            }
            
            // Add specific handling for common errors
            if ($status === 429) {
                $errorMessage .= " (Rate limit exceeded - please wait and try again)";
            } elseif ($status === 400 && isset($errorDetails['error']['message'])) {
                $fbError = $errorDetails['error']['message'];
                if (strpos($fbError, 'INVALID_PASSWORD') !== false) {
                    $errorMessage = "Invalid password";
                } elseif (strpos($fbError, 'EMAIL_NOT_FOUND') !== false) {
                    $errorMessage = "Email not found";
                } elseif (strpos($fbError, 'USER_DISABLED') !== false) {
                    $errorMessage = "User account disabled";
                } elseif (strpos($fbError, 'TOO_MANY_ATTEMPTS_TRY_LATER') !== false) {
                    $errorMessage = "Too many failed attempts - please try again later";
                }
            }
            
            error_log("Firebase Auth Error - Status: $status, Response: $response");
            throw new Exception($errorMessage);
        }
        
        $result = json_decode($response, true);
        
        if (!$result || !is_array($result)) {
            throw new Exception("Invalid response from Firebase Auth");
        }
        
        if (isset($result['idToken'])) {
            $this->setAccessToken($result['idToken']);
        }
        
        return $result;
    }
    
    public function createUser($email, $password) {
        $url = "https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=" . $this->apiKey;
        
        $data = [
            'email' => $email,
            'password' => $password,
            'returnSecureToken' => true
        ];
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            $result = json_decode($response, true);
            $errorMessage = isset($result['error']['message']) ? $result['error']['message'] : 'User creation failed';
            throw new Exception($errorMessage);
        }
        
        $result = json_decode($response, true);
        if (isset($result['idToken'])) {
            $this->setAccessToken($result['idToken']);
        }
        
        return $result;
    }
    
    public function getDocument($collection, $document) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}/{$document}";
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            return null;
        }
        
        $result = json_decode($response, true);
        return $this->parseDocument($result);
    }
    
    private function parseDocument($doc) {
        if (!isset($doc['fields'])) {
            return null;
        }
        
        $result = [];
        foreach ($doc['fields'] as $key => $field) {
            $result[$key] = $this->convertFromFirestoreValue($field);
        }
        
        return $result;
    }
    
    private function convertFromFirestoreValue($field) {
        if (isset($field['stringValue'])) {
            return $field['stringValue'];
        } elseif (isset($field['integerValue'])) {
            return (int)$field['integerValue'];
        } elseif (isset($field['doubleValue'])) {
            return (float)$field['doubleValue'];
        } elseif (isset($field['booleanValue'])) {
            return $field['booleanValue'];
        } elseif (isset($field['timestampValue'])) {
            return $field['timestampValue'];
        } elseif (isset($field['nullValue'])) {
            return null;
        } elseif (isset($field['arrayValue'])) {
            $result = [];
            if (isset($field['arrayValue']['values'])) {
                foreach ($field['arrayValue']['values'] as $value) {
                    $result[] = $this->convertFromFirestoreValue($value);
                }
            }
            return $result;
        } elseif (isset($field['mapValue'])) {
            $result = [];
            if (isset($field['mapValue']['fields'])) {
                foreach ($field['mapValue']['fields'] as $key => $value) {
                    $result[$key] = $this->convertFromFirestoreValue($value);
                }
            }
            return $result;
        }
        
        return null;
    }
    
    public function getAdminAccessToken() {
        try {
            // Try to load service account credentials from environment variables first
            $serviceAccount = null;

            // Check if environment variables are set
            $envProjectId = $_ENV['FIREBASE_PROJECT_ID'] ?? getenv('FIREBASE_PROJECT_ID');
            $envPrivateKey = $_ENV['FIREBASE_PRIVATE_KEY'] ?? getenv('FIREBASE_PRIVATE_KEY');
            $envClientEmail = $_ENV['FIREBASE_CLIENT_EMAIL'] ?? getenv('FIREBASE_CLIENT_EMAIL');

            if ($envProjectId && $envPrivateKey && $envClientEmail) {
                // Use environment variables (most secure)
                $serviceAccount = [
                    'type' => 'service_account',
                    'project_id' => $envProjectId,
                    'private_key_id' => $_ENV['FIREBASE_PRIVATE_KEY_ID'] ?? getenv('FIREBASE_PRIVATE_KEY_ID'),
                    'private_key' => str_replace('\\n', "\n", $envPrivateKey), // Handle escaped newlines
                    'client_email' => $envClientEmail,
                    'client_id' => $_ENV['FIREBASE_CLIENT_ID'] ?? getenv('FIREBASE_CLIENT_ID'),
                    'auth_uri' => $_ENV['FIREBASE_AUTH_URI'] ?? getenv('FIREBASE_AUTH_URI') ?? 'https://accounts.google.com/o/oauth2/auth',
                    'token_uri' => $_ENV['FIREBASE_TOKEN_URI'] ?? getenv('FIREBASE_TOKEN_URI') ?? 'https://oauth2.googleapis.com/token',
                    'auth_provider_x509_cert_url' => $_ENV['FIREBASE_AUTH_PROVIDER_CERT_URL'] ?? getenv('FIREBASE_AUTH_PROVIDER_CERT_URL') ?? 'https://www.googleapis.com/oauth2/v1/certs',
                    'client_x509_cert_url' => $_ENV['FIREBASE_CLIENT_CERT_URL'] ?? getenv('FIREBASE_CLIENT_CERT_URL'),
                    'universe_domain' => $_ENV['FIREBASE_UNIVERSE_DOMAIN'] ?? getenv('FIREBASE_UNIVERSE_DOMAIN') ?? 'googleapis.com'
                ];

                error_log("Gestores: Using Firebase credentials from environment variables");
            } else {
                // No environment variables available
                throw new Exception("Firebase service account environment variables not found. Please set FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, and FIREBASE_CLIENT_EMAIL environment variables for security.");
            }

            // Create JWT token for service account authentication
            $now = time();
            $header = [
                'alg' => 'RS256',
                'typ' => 'JWT'
            ];

            $payload = [
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/firebase https://www.googleapis.com/auth/identitytoolkit https://www.googleapis.com/auth/cloud-platform',
                'aud' => 'https://oauth2.googleapis.com/token',
                'iat' => $now,
                'exp' => $now + 3600 // 1 hour
            ];

            // Simple JWT creation (for production, consider using a proper JWT library)
            $headerEncoded = base64url_encode(json_encode($header));
            $payloadEncoded = base64url_encode(json_encode($payload));
            $signature = '';

            // Create signature using RS256
            $data = $headerEncoded . '.' . $payloadEncoded;
            openssl_sign($data, $signature, $serviceAccount['private_key'], OPENSSL_ALGO_SHA256);
            $signatureEncoded = base64url_encode($signature);

            $jwt = $data . '.' . $signatureEncoded;

            // Exchange JWT for access token
            $tokenUrl = 'https://oauth2.googleapis.com/token';
            $postData = [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ];

            $ch = curl_init($tokenUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($status !== 200) {
                throw new Exception("Failed to get admin access token: HTTP $status - $response");
            }

            $tokenData = json_decode($response, true);
            if (!isset($tokenData['access_token'])) {
                throw new Exception("No access token in response: $response");
            }

            return $tokenData['access_token'];

        } catch (Exception $e) {
            error_log("Gestores error getting admin access token: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function listDocuments($collection, $pageToken = null, $pageSize = 100) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
        
        // Add pagination parameters
        $params = [];
        if ($pageSize) {
            $params['pageSize'] = $pageSize;
        }
        if ($pageToken) {
            $params['pageToken'] = $pageToken;
        }
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to list documents");
        }
        
        $result = json_decode($response, true);
        $documents = [];
        
        if (isset($result['documents'])) {
            foreach ($result['documents'] as $doc) {
                $docId = basename($doc['name']);
                $documents[$docId] = $this->parseDocument($doc);
            }
        }
        
        // Add pagination token to results if present
        if (isset($result['nextPageToken'])) {
            $documents['_nextPageToken'] = $result['nextPageToken'];
        }
        
        return $documents;
    }
    
    public function queryDocuments($collection, $field, $operator, $value, $limit = 1000) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        // Use reliable "fetch all then filter" approach with pagination
        $allDocuments = [];
        $nextPageToken = null;
        $pageCount = 0;
        
        do {
            $pageCount++;
            error_log("queryDocuments: Fetching page {$pageCount} from collection '{$collection}'");
            
            $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
            if ($nextPageToken) {
                $url .= "?pageToken=" . urlencode($nextPageToken);
            }
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status >= 400) {
                throw new Exception("Failed to query documents: HTTP $status - $response");
            }
            
            $pageResult = json_decode($response, true);
            
            if (isset($pageResult['documents'])) {
                $allDocuments = array_merge($allDocuments, $pageResult['documents']);
            }
            
            $nextPageToken = $pageResult['nextPageToken'] ?? null;
            
            // Safety check to prevent infinite loops
            if ($pageCount > 10) {
                error_log("queryDocuments: Too many pages, stopping fetch for collection '{$collection}'");
                break;
            }
            
        } while ($nextPageToken && count($allDocuments) < 10000);
        
        error_log("queryDocuments: Found " . count($allDocuments) . " total documents in collection '{$collection}'");
        
        // Now filter the documents by the specified criteria
        $filteredDocuments = [];
        
        foreach ($allDocuments as $doc) {
            $docData = $this->parseDocument($doc);
            $docId = basename($doc['name']);
            
            // Apply the filter
            $fieldValue = $docData[$field] ?? null;
            $matches = false;
            
            switch (strtoupper($operator)) {
                case 'EQUAL':
                case '==':
                    $matches = ($fieldValue === $value);
                    break;
                case 'NOT_EQUAL':
                case '!=':
                    $matches = ($fieldValue !== $value);
                    break;
                case 'GREATER_THAN':
                case '>':
                    $matches = ($fieldValue > $value);
                    break;
                case 'GREATER_THAN_OR_EQUAL':
                case '>=':
                    $matches = ($fieldValue >= $value);
                    break;
                case 'LESS_THAN':
                case '<':
                    $matches = ($fieldValue < $value);
                    break;
                case 'LESS_THAN_OR_EQUAL':
                case '<=':
                    $matches = ($fieldValue <= $value);
                    break;
                case 'ARRAY_CONTAINS':
                    $matches = (is_array($fieldValue) && in_array($value, $fieldValue));
                    break;
                default:
                    error_log("queryDocuments: Unsupported operator '{$operator}', defaulting to EQUAL");
                    $matches = ($fieldValue === $value);
                    break;
            }
            
            if ($matches) {
                $filteredDocuments[$docId] = $docData;
            }
        }
        
        error_log("queryDocuments: Filtered to " . count($filteredDocuments) . " documents where {$field} {$operator} '{$value}'");
        
        return $filteredDocuments;
    }
    
    public function setDocument($collection, $document, $data) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}/{$document}";
        
        $firestoreData = [
            'fields' => $this->convertToFirestoreFields($data)
        ];
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($firestoreData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to set document");
        }
        
        return json_decode($response, true);
    }
    
    public function addDocument($collection, $data) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
        
        $firestoreData = [
            'fields' => $this->convertToFirestoreFields($data)
        ];
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($firestoreData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to add document: HTTP $status - $response");
        }
        
        $result = json_decode($response, true);
        
        // Extract document ID from the response
        if (isset($result['name'])) {
            $documentId = basename($result['name']);
            return $documentId;
        }
        
        throw new Exception("Failed to get document ID from response");
    }
    
    public function updateDocument($collection, $document, $data) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}/{$document}";
        
        $firestoreData = [
            'fields' => $this->convertToFirestoreFields($data)
        ];
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($firestoreData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to update document: HTTP $status - $response");
        }
        
        return true;
    }
    
    public function deleteDocument($collection, $document) {
        if (!$this->accessToken) {
            throw new Exception("No access token available");
        }
        
        $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}/{$document}";
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to delete document: HTTP $status - $response");
        }
        
        return true;
    }
    
    private function convertToFirestoreFields($data) {
        $fields = [];
        foreach ($data as $key => $value) {
            $fields[$key] = $this->convertToFirestoreValue($value);
        }
        return $fields;
    }
    
    private function convertToFirestoreValue($value) {
        if (is_string($value)) {
            return ['stringValue' => $value];
        } elseif (is_int($value)) {
            return ['integerValue' => (string)$value];
        } elseif (is_bool($value)) {
            return ['booleanValue' => $value];
        } elseif (is_null($value)) {
            return ['nullValue' => null];
        } elseif (is_array($value)) {
            // Check if this is an indexed array or associative array
            if (array_keys($value) !== range(0, count($value) - 1)) {
                // Associative array - convert to map
                $mapValues = [];
                foreach ($value as $key => $item) {
                    $mapValues[$key] = $this->convertToFirestoreValue($item);
                }
                return ['mapValue' => ['fields' => $mapValues]];
            } else {
                // Indexed array - convert to array
                $arrayValues = [];
                foreach ($value as $item) {
                    $arrayValues[] = $this->convertToFirestoreValue($item);
                }
                return ['arrayValue' => ['values' => $arrayValues]];
            }
        } elseif (is_float($value) || is_double($value)) {
            return ['doubleValue' => $value];
        }
        
        return ['stringValue' => (string)$value];
    }
    
    /**
     * Upload file to Firebase Storage
     */
    public function uploadFileToStorage($filePath, $fileName, $contentType, $accessToken = null) {
        $token = $accessToken ?: $this->accessToken;
        if (!$token) {
            throw new Exception("No access token available for storage upload");
        }
        
        // Read file content
        if (!file_exists($filePath)) {
            throw new Exception("File not found: " . $filePath);
        }
        
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            throw new Exception("Failed to read file: " . $filePath);
        }
        
        // Upload to Firebase Storage
        $url = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o?uploadType=media&name=" . urlencode($fileName);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContent);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: ' . $contentType,
            'Authorization: Bearer ' . $token
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to upload file: HTTP $status - $response");
        }
        
        $result = json_decode($response, true);
        
        if (!$result || !isset($result['name'])) {
            throw new Exception("Invalid upload response");
        }
        
        // Generate download URL with token for public access (same pattern as existing gestores system)
        $downloadUrl = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/" . urlencode($result['name']) . "?alt=media";
        
        // Add download token if available for public access
        if (isset($result['downloadTokens'])) {
            $token = explode(',', $result['downloadTokens'])[0]; // Get first token
            $downloadUrl .= "&token=" . $token;
        }
        
        return [
            'downloadUrl' => $downloadUrl,
            'name' => $result['name'],
            'bucket' => $result['bucket'],
            'generation' => $result['generation']
        ];
    }
    
    /**
     * Delete file from Firebase Storage
     */
    public function deleteStorageFile($fileName, $accessToken = null) {
        $token = $accessToken ?: $this->accessToken;
        if (!$token) {
            throw new Exception("No access token available for storage deletion");
        }
        
        $url = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/" . urlencode($fileName);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400 && $status !== 404) {
            throw new Exception("Failed to delete file: HTTP $status - $response");
        }
        
        return true;
    }
    
    /**
     * Get fresh download URL for a file in Firebase Storage
     */
    public function getStorageDownloadUrl($fileName, $accessToken = null) {
        $token = $accessToken ?: $this->accessToken;
        if (!$token) {
            throw new Exception("No access token available for storage access");
        }
        
        // Get file metadata to get download tokens
        $url = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/" . urlencode($fileName);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token
        ]);
        
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($status >= 400) {
            throw new Exception("Failed to get file metadata: HTTP $status - $response");
        }
        
        $result = json_decode($response, true);
        
        if (!$result || !isset($result['name'])) {
            throw new Exception("Invalid file metadata response");
        }
        
        // Generate download URL with token for public access
        $downloadUrl = "https://firebasestorage.googleapis.com/v0/b/" . FIREBASE_STORAGE_BUCKET . "/o/" . urlencode($result['name']) . "?alt=media";
        
        // Add download token if available for public access
        if (isset($result['downloadTokens'])) {
            $token = explode(',', $result['downloadTokens'])[0]; // Get first token
            $downloadUrl .= "&token=" . $token;
        }
        
        return $downloadUrl;
    }
}

/**
 * Base64 URL encode function for JWT
 */
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

/**
 * Check if system is in maintenance mode for gestores
 * Returns maintenance message array if in maintenance mode, false otherwise
 */
function checkMaintenanceMode() {
    global $database;
    
    try {
        // Get admin token for accessing system messages
        $adminToken = $database->getAdminAccessToken();
        if (!$adminToken) {
            return false; // If can't get admin token, allow access
        }
        $database->setAccessToken($adminToken);
        
        // Get all system messages with pagination
        $allMessages = [];
        $pageToken = null;
        
        do {
            $messages = $database->listDocuments('systemMessages', $pageToken, 1000);
            
            if (!$messages || !is_array($messages)) {
                break;
            }
            
            // Extract pagination token if present
            $pageToken = $messages['_nextPageToken'] ?? null;
            unset($messages['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $allMessages = array_merge($allMessages, $messages);
            
        } while ($pageToken);
        
        if (empty($allMessages)) {
            return false;
        }
        
        // Use the combined results
        $messages = $allMessages;
        
        $now = new DateTime();
        
        // Check for active maintenance messages
        foreach ($messages as $docId => $messageData) {
            // Check if message is active and has maintenance mode enabled
            if (!isset($messageData['active']) || !$messageData['active']) {
                continue;
            }
            
            if (!isset($messageData['maintenanceMode']) || !$messageData['maintenanceMode']) {
                continue;
            }
            
            // Check if message is within valid date range
            $startDate = isset($messageData['startDate']) && $messageData['startDate'] ? new DateTime($messageData['startDate']) : null;
            $endDate = isset($messageData['endDate']) && $messageData['endDate'] ? new DateTime($messageData['endDate']) : null;
            
            // Check if current time is within the message's active period
            if ($startDate && $now < $startDate) {
                continue; // Message not started yet
            }
            
            if ($endDate && $now > $endDate) {
                continue; // Message expired
            }
            
            // Found an active maintenance message
            return [
                'title' => $messageData['title'] ?? 'Sistema em Manutenção',
                'content' => $messageData['content'] ?? 'O sistema está temporariamente indisponível para manutenção.',
                'type' => $messageData['type'] ?? 'maintenance'
            ];
        }
        
        return false; // No active maintenance messages found
        
    } catch (Exception $e) {
        error_log("Error checking maintenance mode: " . $e->getMessage());
        return false; // If error occurs, allow access
    }
}

// Initialize Firebase database instance for gestores
global $database;
$database = new GestoresFirebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
?> 