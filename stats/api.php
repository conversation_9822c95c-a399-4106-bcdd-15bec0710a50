<?php
// Start output buffering to prevent any stray output
ob_start();

// Set timezone to match local time (UTC+1 for summer time)
date_default_timezone_set('Europe/Lisbon');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Error reporting for debugging
ini_set('display_errors', 0); // Disable display_errors to prevent HTML output mixing with JSON
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Simple test endpoint
if (isset($_GET['test'])) {
    echo json_encode(['success' => true, 'message' => 'API is working', 'timestamp' => date('Y-m-d H:i:s')]);
    exit;
}

// Debug endpoint to check collections
if (isset($_GET['debug'])) {
    // Clear any previous output
    ob_clean();
    
    try {
        $firebase = new PublicFirebase($projectId);
        $collections = ['users', 'gestoresZonaCaca', 'administradores', 'zonas', 'gestorMobile_trajetos', 'contacts', 'gestorMobile_contacts', 'reports', 'contactEvents', 'monitoringSessions', 'zonasCaca'];
        $debug = [];
        
        foreach ($collections as $collection) {
            try {
                $data = $firebase->getCollectionData($collection, 5); // Get first 5 documents
                $debug[$collection] = [
                    'count' => count($data),
                    'sample_count' => count($data),
                    'has_data' => count($data) > 0
                ];
                
                // Include sample data for debugging
                if (count($data) > 0 && count($data) <= 2) {
                    $debug[$collection]['sample'] = array_slice($data, 0, 2);
                }
                
                // Add specific info for key collections
                if ($collection === 'gestoresZonaCaca' && count($data) > 0) {
                    $debug[$collection]['first_keys'] = array_keys($data[0]);
                }
                
            } catch (Exception $e) {
                $debug[$collection] = [
                    'error' => $e->getMessage(),
                    'count' => 0
                ];
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'debug' => $debug], JSON_PRETTY_PRINT);
        exit;
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        exit;
    }
}

// Firebase configuration
$projectId = getEnvVar('FIREBASE_PROJECT_ID') ?: 'prorola-a2f66'; // Use environment variable or fallback
$cacheFile = 'stats_cache.json';
$cacheExpiry = 3600; // 1 hour

// Check if we should use cache
$useCache = true;
$forceRefresh = false;

// Load environment variables from .htaccess
function getEnvVar($name) {
    return $_SERVER[$name] ?? getenv($name) ?? '';
}

class PublicFirebase {
    private $projectId;
    private $accessToken;
    
    public function __construct($projectId) {
        $this->projectId = $projectId;
        $this->accessToken = $this->getAdminAccessToken();
    }
    
    private function getAdminAccessToken() {
        try {
            // Get service account credentials from environment variables
            $serviceAccount = [
                'type' => getEnvVar('FIREBASE_TYPE'),
                'project_id' => getEnvVar('FIREBASE_PROJECT_ID'),
                'private_key_id' => getEnvVar('FIREBASE_PRIVATE_KEY_ID'),
                'private_key' => str_replace('\\n', "\n", getEnvVar('FIREBASE_PRIVATE_KEY')),
                'client_email' => getEnvVar('FIREBASE_CLIENT_EMAIL'),
                'client_id' => getEnvVar('FIREBASE_CLIENT_ID'),
                'auth_uri' => getEnvVar('FIREBASE_AUTH_URI'),
                'token_uri' => getEnvVar('FIREBASE_TOKEN_URI'),
                'auth_provider_x509_cert_url' => getEnvVar('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
                'client_x509_cert_url' => getEnvVar('FIREBASE_CLIENT_X509_CERT_URL')
            ];
            
            if (empty($serviceAccount['private_key']) || empty($serviceAccount['client_email'])) {
                throw new Exception('Firebase service account credentials not found in environment variables');
            }
            
            // Create JWT
            $now = time();
            $payload = [
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/datastore https://www.googleapis.com/auth/firebase.database',
                'aud' => 'https://oauth2.googleapis.com/token',
                'exp' => $now + 3600,
                'iat' => $now
            ];
            
            $jwt = $this->createJWT($payload, $serviceAccount['private_key']);
            
            // Exchange JWT for access token
            $tokenData = [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($tokenData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode !== 200) {
                throw new Exception("Failed to get access token. HTTP Code: $httpCode, Response: $response");
            }
            
            $tokenResponse = json_decode($response, true);
            if (!isset($tokenResponse['access_token'])) {
                throw new Exception('Access token not found in response: ' . $response);
            }
            
            return $tokenResponse['access_token'];
            
        } catch (Exception $e) {
            error_log("Firebase auth error: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function createJWT($payload, $privateKey) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
        $payload = json_encode($payload);
        
        $base64Header = $this->base64UrlEncode($header);
        $base64Payload = $this->base64UrlEncode($payload);
        
        $signature = '';
        openssl_sign($base64Header . '.' . $base64Payload, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        $base64Signature = $this->base64UrlEncode($signature);
        
        return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
    }
    
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    public function getCollectionData($collection, $limit = null) {
        try {
            $documents = [];
            $pageToken = null;
            $pageSize = 1000; // Process in chunks
            
            do {
                // Build URL with pagination
                $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
                $queryParams = [];
                
                if ($pageSize) {
                    $queryParams['pageSize'] = $pageSize;
                }
                if ($pageToken) {
                    $queryParams['pageToken'] = $pageToken;
                }
                if (!empty($queryParams)) {
                    $url .= '?' . http_build_query($queryParams);
                }
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->accessToken,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode !== 200) {
                    error_log("HTTP $httpCode error for collection $collection: $response");
                    break;
                }
                
                $data = json_decode($response, true);
                
                if (isset($data['documents'])) {
                    foreach ($data['documents'] as $doc) {
                        $documents[] = $this->parseDocument($doc);
                    }
                }
                
                // Get next page token for pagination
                $pageToken = $data['nextPageToken'] ?? null;
                
                // Add small delay between pagination requests to prevent rate limiting
                if ($pageToken) {
                    usleep(100000); // 100ms delay between pages
                }
                
                // Apply limit if specified
                if ($limit && count($documents) >= $limit) {
                    $documents = array_slice($documents, 0, $limit);
                    break;
                }
                
            } while ($pageToken);
            
            return $documents;
            
        } catch (Exception $e) {
            error_log("Error getting collection data for $collection: " . $e->getMessage());
            return [];
        }
    }
    
    private function parseDocument($doc) {
        $result = [];
        if (isset($doc['fields'])) {
            foreach ($doc['fields'] as $key => $field) {
                $result[$key] = $this->convertFromFirestoreValue($field);
            }
        }
        return $result;
    }
    
    private function convertFromFirestoreValue($field) {
        if (isset($field['stringValue'])) {
            return $field['stringValue'];
        } elseif (isset($field['integerValue'])) {
            return (int)$field['integerValue'];
        } elseif (isset($field['doubleValue'])) {
            return (float)$field['doubleValue'];
        } elseif (isset($field['booleanValue'])) {
            return $field['booleanValue'];
        } elseif (isset($field['timestampValue'])) {
            return $field['timestampValue'];
        } elseif (isset($field['arrayValue']['values'])) {
            $array = [];
            foreach ($field['arrayValue']['values'] as $value) {
                $array[] = $this->convertFromFirestoreValue($value);
            }
            return $array;
        } elseif (isset($field['mapValue']['fields'])) {
            $map = [];
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $map[$key] = $this->convertFromFirestoreValue($value);
            }
            return $map;
        } elseif (isset($field['nullValue'])) {
            return null;
        }
        return null;
    }
}

// Cache functions
function isCacheValid($cacheFile, $cacheExpiry) {
    if (!file_exists($cacheFile)) {
        return false;
    }
    
    $cacheTime = filemtime($cacheFile);
    
    // Check if we're still within the same hour as the cache was created
    $cacheHour = date('H', $cacheTime);
    $currentHour = date('H');
    $cacheDate = date('Y-m-d', $cacheTime);
    $currentDate = date('Y-m-d');
    
    // Cache is valid if it's the same hour of the same day
    return ($cacheDate === $currentDate && $cacheHour === $currentHour);
}

function loadCache($cacheFile) {
    if (file_exists($cacheFile)) {
        $content = file_get_contents($cacheFile);
        return json_decode($content, true);
    }
    return null;
}

function saveCache($cacheFile, $data) {
    $cacheData = [
        'timestamp' => time(),
        'data' => $data
    ];
    file_put_contents($cacheFile, json_encode($cacheData, JSON_PRETTY_PRINT));
}

try {
    // Initialize Firebase
    $firebase = new PublicFirebase($projectId);
    
    // Check cache first
    $stats = null;
    $cacheUsed = false;
    
    if ($useCache && !$forceRefresh && isCacheValid($cacheFile, $cacheExpiry)) {
        $cacheContent = loadCache($cacheFile);
        if ($cacheContent && isset($cacheContent['data'])) {
            $stats = $cacheContent['data'];
            $cacheUsed = true;
            error_log("Using cached statistics data");
        }
    }
    
    // If no valid cache, gather fresh statistics
    if (!$stats) {
        error_log("Gathering fresh statistics data");
        
        // Define test emails to exclude from statistics
        $testEmails = ['<EMAIL>'];
        
        // Helper function to check if a zone is a test zone (same as webadmin)
        function isTestZone($nif) {
            // Check if NIF starts with "12345678" followed by a single digit (0-9)
            return preg_match('/^12345678[0-9]$/', $nif);
        }
        
        // Initialize stats structure
        $stats = [
            'users' => [
                'total' => 0,
                'technicians' => 0,
                'colaboradores' => 0,
                'hunt_managers' => 0,
                'administrators' => 0
            ],
            'trajectories' => [
                'total' => 0,
                'manual' => 0,
                'gps' => 0,
                'total_distance' => 0,
                'distance_distribution' => []
            ],
            'contacts' => [
                'total' => 0,
                'manual' => 0,
                'mobile' => 0,
                'by_circumstance' => [],
                'by_location' => []
            ],
            'hunting_zones' => [
                'total' => 0,
                'registered' => 0,
                'with_trajectories' => 0,
                'total_quota' => 0
            ],
            'reports' => [
                'total' => 0,
                'with_contacts' => 0,
                'total_report_contacts' => 0,
                'avg_contacts_per_report' => 0,
                'by_role' => [],
                'by_location' => []
            ]
        ];
        
        // Get user statistics
        try {
            $users = $firebase->getCollectionData('users');
            error_log("Users collection count: " . count($users));
            
            $technicianCount = 0;
            $colaboradorCount = 0;
            
            foreach ($users as $user) {
                $userRole = $user['role'] ?? $user['userRole'] ?? '';
                if ($userRole === 'tecnico_prorola' || $userRole === 'tecnico') {
                    $technicianCount++;
                } elseif ($userRole === 'colaborador') {
                    $colaboradorCount++;
                }
            }
            
            $stats['users']['technicians'] = $technicianCount;
            $stats['users']['colaboradores'] = $colaboradorCount;
            
            $huntManagers = $firebase->getCollectionData('gestoresZonaCaca');
            error_log("Hunt managers collection count: " . count($huntManagers));
            $stats['users']['hunt_managers'] = count($huntManagers);
            
            $admins = $firebase->getCollectionData('administradores');
            error_log("Admins collection count: " . count($admins));
            $stats['users']['administrators'] = count($admins);
            
            $stats['users']['total'] = $stats['users']['technicians'] + $stats['users']['colaboradores'] + $stats['users']['hunt_managers'] + $stats['users']['administrators'];
            
        } catch (Exception $e) {
            error_log("Error gathering user statistics: " . $e->getMessage());
        }
        
        // Get hunting zone statistics
        try {
            // Reuse huntManagers from user statistics to avoid duplicate query
            $huntingZones = $firebase->getCollectionData('zonasCaca');
            
            // Filter out test zones (same logic as webadmin)
            $filteredHuntingZones = [];
            
            foreach ($huntingZones as $zona) {
                $nif = $zona['nifEntidade'] ?? '';
                $createdByEmail = $zona['createdByEmail'] ?? $zona['email'] ?? '';
                
                // Filter out both test NIFs and test emails
                if (!isTestZone($nif) && !in_array($createdByEmail, $testEmails)) {
                    $filteredHuntingZones[] = $zona;
                }
            }
            
            // Count total zones and registered zones from the filtered hunting zones
            $totalZones = count($filteredHuntingZones) > 0 ? count($filteredHuntingZones) : $stats['users']['hunt_managers'];
            
            // Count registered zones - same logic as webadmin: zones with status='registered'
            $registeredZones = 0;
            foreach ($filteredHuntingZones as $zona) {
                if (isset($zona['status']) && $zona['status'] === 'registered') {
                    $registeredZones++;
                }
            }
            
            $totalQuota = 0;
            
            // Calculate quota from hunting zones if available (excluding test zones)
            foreach ($filteredHuntingZones as $zona) {
                if (isset($zona['quotaZona'])) {
                    $totalQuota += (int)$zona['quotaZona'];
                } elseif (isset($zona['quota'])) {
                    $totalQuota += (int)$zona['quota'];
                }
            }
            
            // If no quota from zones, we'll skip individual manager quota calculation for now
            // to avoid additional Firebase queries that might get rate limited
            
            // Count zones with trajectories
            $zonesWithTrajectories = 0;
            $manualTrajectories = $firebase->getCollectionData('zonas');
            $gpsTrajectories = $firebase->getCollectionData('gestorMobile_trajetos');
            
            // Filter out test user trajectories
            $filteredManualTrajectories = [];
            foreach ($manualTrajectories as $traj) {
                $createdByEmail = $traj['createdByEmail'] ?? $traj['email'] ?? '';
                if (!in_array($createdByEmail, $testEmails)) {
                    $filteredManualTrajectories[] = $traj;
                }
            }
            
            $filteredGpsTrajectories = [];
            foreach ($gpsTrajectories as $traj) {
                $createdByEmail = $traj['createdByEmail'] ?? $traj['email'] ?? '';
                if (!in_array($createdByEmail, $testEmails)) {
                    $filteredGpsTrajectories[] = $traj;
                }
            }
            
            // Get unique zone IDs from trajectories - FIXED LOGIC (excluding test user data)
            $zoneIds = [];
            $manualTrajetosWithCoordinates = 0;
            $manualTrajetosWithoutCoordinates = 0;
            
            // Count unique zones for each trajectory type to avoid counting duplicates
            $manualZoneIds = [];
            $gpsZoneIds = [];
            
            // From manual trajectories (zonas collection) - only count zones with actual coordinates
            foreach ($filteredManualTrajectories as $traj) {
                if (isset($traj['zoneId'])) {
                    if (isset($traj['coordinates']) && is_array($traj['coordinates']) && !empty($traj['coordinates'])) {
                        $zoneIds[$traj['zoneId']] = true;
                        $manualZoneIds[$traj['zoneId']] = true;
                        $manualTrajetosWithCoordinates++;
                    } else {
                        $manualTrajetosWithoutCoordinates++;
                        error_log("Manual trajectory without coordinates: zoneId=" . ($traj['zoneId'] ?? 'null') . ", name=" . ($traj['name'] ?? 'null'));
                    }
                }
            }
            
            error_log("Manual trajectories WITH coordinates (excluding test data): " . $manualTrajetosWithCoordinates);
            error_log("Manual trajectories WITHOUT coordinates (excluding test data): " . $manualTrajetosWithoutCoordinates);
            
            // From GPS trajectories (gestorMobile_trajetos collection) - count zones that have GPS trajetos
            foreach ($filteredGpsTrajectories as $traj) {
                if (isset($traj['zoneId']) && !empty($traj['zoneId'])) {
                    $zoneIds[$traj['zoneId']] = true;
                    $gpsZoneIds[$traj['zoneId']] = true;
                }
            }
            
            $zonesWithTrajectories = count($zoneIds);
            

            
            $stats['hunting_zones']['total'] = $totalZones;
            $stats['hunting_zones']['registered'] = $registeredZones;
            $stats['hunting_zones']['with_trajectories'] = $zonesWithTrajectories;
            $stats['hunting_zones']['total_quota'] = $totalQuota;
            
            // Create detailed zone lists for display
            $registeredZonesWithoutTrajectories = [];
            $unregisteredZones = [];
            $registeredZonesWithTrajectories = [];
            
            foreach ($filteredHuntingZones as $zona) {
                $zoneInfo = [
                    'zona' => $zona['zona'] ?? 'N/A',
                    'nomeZona' => $zona['nomeZona'] ?? 'N/A',
                    'email' => $zona['email'] ?? 'N/A'
                ];
                
                $isRegistered = isset($zona['status']) && $zona['status'] === 'registered';
                $zoneNumber = $zona['zona'];
                // Try multiple formats to match zone IDs from trajectories
                $hasTrajectories = isset($zoneIds[$zoneNumber]) || 
                                 isset($zoneIds[(string)$zoneNumber]) || 
                                 isset($zoneIds[intval($zoneNumber)]) ||
                                 isset($zoneIds['zone_' . $zoneNumber]);
                
                if ($isRegistered && !$hasTrajectories) {
                    $registeredZonesWithoutTrajectories[] = $zoneInfo;
                } elseif (!$isRegistered) {
                    $unregisteredZones[] = $zoneInfo;
                } elseif ($isRegistered && $hasTrajectories) {
                    // Determine trajectory type for this zone
                    $trajectoryType = 'N/A';
                    
                    // More robust zone ID matching
                    $zoneVariations = [
                        $zoneNumber,
                        (string)$zoneNumber,
                        intval($zoneNumber),
                        'zone_' . $zoneNumber,
                        str_pad($zoneNumber, 3, '0', STR_PAD_LEFT), // 004, 007, etc.
                        'Zona ' . $zoneNumber,
                        'zona_' . $zoneNumber
                    ];
                    
                    $hasManual = false;
                    $hasGps = false;
                    
                    foreach ($zoneVariations as $variation) {
                        if (isset($manualZoneIds[$variation])) {
                            $hasManual = true;
                        }
                        if (isset($gpsZoneIds[$variation])) {
                            $hasGps = true;
                        }
                    }
                    

                    if ($hasManual && $hasGps) {
                        $trajectoryType = 'Manual + GPS';
                    } elseif ($hasManual) {
                        $trajectoryType = 'Manual';
                    } elseif ($hasGps) {
                        $trajectoryType = 'GPS';
                    }
                    
                    $zoneInfo['trajectoryType'] = $trajectoryType;
                    $registeredZonesWithTrajectories[] = $zoneInfo;
                }
            }
            

            
            // Sort zones by zone number
            usort($registeredZonesWithoutTrajectories, function($a, $b) {
                return intval($a['zona']) - intval($b['zona']);
            });
            usort($unregisteredZones, function($a, $b) {
                return intval($a['zona']) - intval($b['zona']);
            });
            usort($registeredZonesWithTrajectories, function($a, $b) {
                return intval($a['zona']) - intval($b['zona']);
            });
            
            $stats['hunting_zones']['registered_without_trajectories'] = $registeredZonesWithoutTrajectories;
            $stats['hunting_zones']['unregistered'] = $unregisteredZones;
            $stats['hunting_zones']['registered_with_trajectories'] = $registeredZonesWithTrajectories;
            
        } catch (Exception $e) {
            error_log("Error gathering hunting zone statistics: " . $e->getMessage());
        }
        
        // Get trajectory statistics
        try {
            // Reuse trajectory data from hunting zone statistics to avoid duplicate queries
            error_log("Manual trajectories (zonas) count (excluding test data): " . count($filteredManualTrajectories));
            error_log("GPS trajectories count (excluding test data): " . count($filteredGpsTrajectories));
            error_log("Unique zones with trajectories (excluding test data): " . $zonesWithTrajectories);
            
            // Zone IDs are already populated above during the hunting zone statistics processing
            
            $uniqueManualZones = count($manualZoneIds);
            $uniqueGpsZones = count($gpsZoneIds);
            
            error_log("Unique zones with manual trajectories (excluding test data): " . $uniqueManualZones);
            error_log("Unique zones with GPS trajectories (excluding test data): " . $uniqueGpsZones);
            
            // Use unique zone counts instead of document counts to avoid duplicates
            $stats['trajectories']['manual'] = $uniqueManualZones;
            $stats['trajectories']['gps'] = $uniqueGpsZones;
            $stats['trajectories']['total'] = $zonesWithTrajectories; // Total unique zones with any trajectory type
            
            $totalDistance = 0;
            $distances = [];
            
            foreach ($filteredManualTrajectories as $trajectory) {
                $distance = 0;
                
                // Try multiple distance field names and formats
                $distanceFields = ['distance', 'totalDistance', 'distancia', 'distanceKm'];
                foreach ($distanceFields as $field) {
                    if (isset($trajectory[$field])) {
                        if (is_numeric($trajectory[$field])) {
                            $distance = (float)$trajectory[$field];
                            // If it's in meters, convert to km
                            if ($distance > 100) {
                                $distance = $distance / 1000;
                            }
                        } elseif (is_string($trajectory[$field])) {
                            // Remove 'km' and other text, extract number
                            $cleanDistance = preg_replace('/[^0-9.,]/', '', $trajectory[$field]);
                            $cleanDistance = str_replace(',', '.', $cleanDistance);
                            if (is_numeric($cleanDistance)) {
                                $distance = (float)$cleanDistance;
                            }
                        }
                        if ($distance > 0) {
                            error_log("Manual trajectory distance: $distance km from field '$field'");
                            break;
                        }
                    }
                }
                
                if ($distance > 0) {
                    $totalDistance += $distance;
                    $distances[] = $distance;
                }
            }
            
            foreach ($filteredGpsTrajectories as $trajectory) {
                $distance = 0;
                
                // Try multiple distance field names
                $distanceFields = ['totalDistance', 'distance', 'distancia', 'distanceKm'];
                foreach ($distanceFields as $field) {
                    if (isset($trajectory[$field])) {
                        if (is_numeric($trajectory[$field])) {
                            $distance = (float)$trajectory[$field];
                            // GPS trajectories usually store in meters, convert to km
                            if ($distance > 100) {
                                $distance = $distance / 1000;
                            }
                            error_log("GPS trajectory distance: $distance km from field '$field'");
                            break;
                        }
                    }
                }
                
                if ($distance > 0) {
                    $totalDistance += $distance;
                    $distances[] = $distance;
                }
            }
            
            $stats['trajectories']['total_distance'] = round($totalDistance, 2);
            
            $stats['trajectories']['distance_distribution'] = [
                '0-2km' => 0,
                '2-5km' => 0,
                '5-10km' => 0,
                '10km+' => 0
            ];
            
            foreach ($distances as $distance) {
                if ($distance <= 2) {
                    $stats['trajectories']['distance_distribution']['0-2km']++;
                } elseif ($distance <= 5) {
                    $stats['trajectories']['distance_distribution']['2-5km']++;
                } elseif ($distance <= 10) {
                    $stats['trajectories']['distance_distribution']['5-10km']++;
                } else {
                    $stats['trajectories']['distance_distribution']['10km+']++;
                }
            }
            
        } catch (Exception $e) {
            error_log("Error gathering trajectory statistics: " . $e->getMessage());
        }
        
        // Get contact statistics
        try {
            $contacts = $firebase->getCollectionData('contacts');
            $contactEvents = $firebase->getCollectionData('contactEvents');
            $mobileContacts = $firebase->getCollectionData('gestorMobile_contacts');
            
            // Filter out test user contacts
            $filteredContacts = [];
            foreach ($contacts as $contact) {
                $createdByEmail = $contact['createdByEmail'] ?? $contact['email'] ?? '';
                if (!in_array($createdByEmail, $testEmails)) {
                    $filteredContacts[] = $contact;
                }
            }
            
            $filteredContactEvents = [];
            foreach ($contactEvents as $contactEvent) {
                $createdByEmail = $contactEvent['createdByEmail'] ?? $contactEvent['email'] ?? '';
                if (!in_array($createdByEmail, $testEmails)) {
                    $filteredContactEvents[] = $contactEvent;
                }
            }
            
            $filteredMobileContacts = [];
            foreach ($mobileContacts as $mobileContact) {
                $createdByEmail = $mobileContact['createdByEmail'] ?? $mobileContact['email'] ?? '';
                if (!in_array($createdByEmail, $testEmails)) {
                    $filteredMobileContacts[] = $mobileContact;
                }
            }
            
            $stats['contacts']['manual'] = count($filteredContacts) + count($filteredContactEvents);
            $stats['contacts']['mobile'] = count($filteredMobileContacts);
            $stats['contacts']['total'] = $stats['contacts']['manual'] + $stats['contacts']['mobile'];
            
            $circumstances = [];
            $locations = [];
            
            // Safely merge filtered contact arrays
            $allContacts = array_merge(
                is_array($filteredContacts) ? $filteredContacts : [],
                is_array($filteredContactEvents) ? $filteredContactEvents : [],
                is_array($filteredMobileContacts) ? $filteredMobileContacts : []
            );
            
            foreach ($allContacts as $contact) {
                if (isset($contact['circumstance'])) {
                    $circumstance = trim($contact['circumstance']);
                    if (!empty($circumstance)) {
                        $circumstances[$circumstance] = ($circumstances[$circumstance] ?? 0) + 1;
                    }
                }
                
                if (isset($contact['location'])) {
                    $location = $contact['location'];
                    if (is_array($location) && count($location) >= 2 && isset($location[0]) && isset($location[1])) {
                        $locationStr = "Coordenadas (" . round($location[0], 2) . ", " . round($location[1], 2) . ")";
                    } else {
                        $locationStr = is_string($location) ? trim($location) : '';
                    }
                    
                    if (!empty($locationStr)) {
                        $locations[$locationStr] = ($locations[$locationStr] ?? 0) + 1;
                    }
                }
            }
            
            $stats['contacts']['by_circumstance'] = $circumstances;
            $stats['contacts']['by_location'] = $locations;
            
        } catch (Exception $e) {
            error_log("Error gathering contact statistics: " . $e->getMessage());
        }
        
        // Get report statistics
        try {
            $reports = $firebase->getCollectionData('reports');
            
            // Filter out test user reports
            $filteredReports = [];
            foreach ($reports as $report) {
                $createdByEmail = $report['createdByEmail'] ?? $report['email'] ?? '';
                if (!in_array($createdByEmail, $testEmails)) {
                    $filteredReports[] = $report;
                }
            }
            
            $stats['reports']['total'] = count($filteredReports);
            
            $reportsWithContacts = 0;
            $totalReportContacts = 0;
            $roleStats = [];
            $locationStats = [];
            
            error_log("Processing " . count($filteredReports) . " reports for contact statistics (excluding test data)");
            
            foreach ($filteredReports as $report) {
                $contactCount = 0;
                
                // Try multiple field names and methods to count contacts
                $contactFields = [
                    'contactCount', 'contacts', 'contactEvents', 'totalContacts', 
                    'numContacts', 'contact_count', 'contactsCount'
                ];
                
                foreach ($contactFields as $field) {
                    if (isset($report[$field])) {
                        if (is_numeric($report[$field])) {
                            $contactCount = (int)$report[$field];
                            error_log("Found contact count $contactCount in field '$field' for report");
                            break;
                        } elseif (is_array($report[$field])) {
                            $contactCount = count($report[$field]);
                            error_log("Found contact array with count $contactCount in field '$field' for report");
                            break;
                        }
                    }
                }
                
                // Alternative: Check if this report has a sessionId and count contactEvents
                // Note: This is done separately to avoid querying contactEvents for every report
                if ($contactCount === 0 && isset($report['sessionId'])) {
                    $report['_needsContactEventCount'] = $report['sessionId'];
                }
                
                if ($contactCount > 0) {
                    $reportsWithContacts++;
                    $totalReportContacts += $contactCount;
                }
                
                $role = $report['userRole'] ?? $report['role'] ?? 'Desconhecido';
                $roleStats[$role] = ($roleStats[$role] ?? 0) + 1;
                
                if (isset($report['location'])) {
                    if (is_array($report['location']) && isset($report['location'][0]) && isset($report['location'][1])) {
                        $location = "Coordenadas (" . round($report['location'][0], 2) . ", " . round($report['location'][1], 2) . ")";
                    } else {
                        $location = is_string($report['location']) ? $report['location'] : 'Localização Desconhecida';
                    }
                    $locationStats[$location] = ($locationStats[$location] ?? 0) + 1;
                }
            }
            
            // Now handle reports that need contact event counting (more efficient to do this once)
            $reportsNeedingContactEvents = array_filter($filteredReports, function($report) {
                return isset($report['_needsContactEventCount']);
            });
            
            if (!empty($reportsNeedingContactEvents)) {
                try {
                    error_log("Fetching contact events for " . count($reportsNeedingContactEvents) . " reports");
                    $allContactEvents = $firebase->getCollectionData('contactEvents');
                    
                    // Filter out test user contact events
                    $filteredAllContactEvents = [];
                    foreach ($allContactEvents as $event) {
                        $createdByEmail = $event['createdByEmail'] ?? $event['email'] ?? '';
                        if (!in_array($createdByEmail, $testEmails)) {
                            $filteredAllContactEvents[] = $event;
                        }
                    }
                    
                    foreach ($reportsNeedingContactEvents as $report) {
                        $sessionId = $report['_needsContactEventCount'];
                        $contactCount = 0;
                        
                        foreach ($filteredAllContactEvents as $event) {
                            if (isset($event['sessionId']) && $event['sessionId'] === $sessionId) {
                                $contactCount++;
                            }
                        }
                        
                        if ($contactCount > 0) {
                            $reportsWithContacts++;
                            $totalReportContacts += $contactCount;
                            error_log("Found $contactCount contact events for sessionId $sessionId");
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error counting contact events: " . $e->getMessage());
                }
            }
            
            $stats['reports']['with_contacts'] = $reportsWithContacts;
            $stats['reports']['total_report_contacts'] = $totalReportContacts;
            $stats['reports']['avg_contacts_per_report'] = $stats['reports']['total'] > 0 ? 
                round($totalReportContacts / $stats['reports']['total'], 1) : 0;
            $stats['reports']['by_role'] = $roleStats;
            $stats['reports']['by_location'] = $locationStats;
            
            // Calculate monthly reports distribution
            $monthlyReports = [];
            foreach ($filteredReports as $report) {
                $createdAt = null;
                
                // Try different date field names
                $dateFields = ['createdAt', 'created_at', 'timestamp', 'date', 'submissionDate'];
                foreach ($dateFields as $field) {
                    if (isset($report[$field])) {
                        $createdAt = $report[$field];
                        break;
                    }
                }
                
                if ($createdAt) {
                    // Handle different date formats
                    if (is_array($createdAt) && isset($createdAt['seconds'])) {
                        // Firestore timestamp format
                        $timestamp = $createdAt['seconds'];
                    } elseif (is_numeric($createdAt)) {
                        // Unix timestamp
                        $timestamp = $createdAt;
                    } else {
                        // String date
                        $timestamp = strtotime($createdAt);
                    }
                    
                    if ($timestamp) {
                        $monthYear = date('Y-m', $timestamp);
                        $monthlyReports[$monthYear] = ($monthlyReports[$monthYear] ?? 0) + 1;
                    }
                }
            }
            
            // Sort monthly reports by date
            ksort($monthlyReports);
            $stats['reports']['monthly_reports'] = $monthlyReports;
            
            error_log("Report statistics: total={$stats['reports']['total']}, with_contacts={$reportsWithContacts}, total_contacts={$totalReportContacts}, monthly_reports=" . count($monthlyReports));
            
        } catch (Exception $e) {
            error_log("Error gathering report statistics: " . $e->getMessage());
        }
        
        // Save to cache
        if ($useCache) {
            saveCache($cacheFile, $stats);
        }
    }
    
    // Return JSON response
    echo json_encode([
        'success' => true,
        'data' => $stats,
        'cache_used' => $cacheUsed,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    http_response_code(500);
    
    // If this is an AJAX request, return JSON
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to load statistics data',
            'message' => $e->getMessage()
        ]);
    } else {
        // Redirect to 500 error page
        header('Location: error/500.php');
        exit;
    }
}
?> 