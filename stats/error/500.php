<?php
// Start output buffering to prevent header issues
ob_start();

// Set timezone
date_default_timezone_set('Europe/Lisbon');

// Clean any previous output
ob_end_clean();

http_response_code(500);
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erro Interno do Servidor - Estatísticas ProROLA</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f59e0b;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .error-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 1rem;
        }

        .error-icon {
            font-size: 4rem;
            color: #f59e0b;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        .error-code {
            font-size: 4rem;
            font-weight: 800;
            color: #f59e0b;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .error-message {
            font-size: 1rem;
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .error-details {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .error-details h3 {
            color: #f59e0b;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .error-details ul {
            list-style: none;
            padding: 0;
        }

        .error-details li {
            padding: 0.25rem 0;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .error-details li i {
            color: #f59e0b;
            width: 16px;
        }

        .error-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: #f59e0b;
            color: white;
        }

        .btn-primary:hover {
            background: #d97706;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
        }

        .btn-secondary {
            background: rgba(107, 114, 128, 0.1);
            color: #374151;
            border: 1px solid rgba(107, 114, 128, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.2);
            transform: translateY(-2px);
        }

        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        }

        @media (max-width: 768px) {
            .error-code {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-card {
                padding: 2rem 1.5rem;
            }
            
            .error-details {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="background-pattern"></div>
    
    <div class="error-container">
        <div class="error-card">
            <img src="../prorola-logo.svg" alt="ProROLA Logo" class="logo">
            
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <div class="error-code">500</div>
            
            <h1 class="error-title">Erro Interno do Servidor</h1>
            
            <p class="error-message">
                Ocorreu um erro interno no servidor. 
                Por favor, tente novamente mais tarde.
            </p>
            
            <div class="error-details">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    O que pode fazer:
                </h3>
                <ul>
                    <li>
                        <i class="fas fa-refresh"></i>
                        Recarregar a página
                    </li>
                    <li>
                        <i class="fas fa-clock"></i>
                        Aguardar alguns minutos e tentar novamente
                    </li>
                    <li>
                        <i class="fas fa-home"></i>
                        Voltar à página inicial
                    </li>
                    <li>
                        <i class="fas fa-envelope"></i>
                        Contactar o administrador se o problema persistir
                    </li>
                </ul>
            </div>
            
            <div class="error-actions">
                <a href="../index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Voltar às Estatísticas
                </a>
                
                <a href="javascript:window.location.reload()" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i>
                    Recarregar Página
                </a>
            </div>
        </div>
    </div>
</body>
</html> 