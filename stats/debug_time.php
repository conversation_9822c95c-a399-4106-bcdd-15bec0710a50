<?php
$cacheFile = __DIR__ . '/stats_cache.json';

echo "Debug Time Calculation:<br>";
echo "Cache file: " . $cacheFile . "<br>";
echo "Cache file exists: " . (file_exists($cacheFile) ? 'Yes' : 'No') . "<br>";

if (file_exists($cacheFile)) {
    $lastUpdate = filemtime($cacheFile);
    echo "Last update timestamp: " . $lastUpdate . "<br>";
    echo "Last update formatted: " . date('d/m/Y H:i', $lastUpdate) . "<br>";
    
    // Calculate next update at the top of the next hour
    $currentHour = date('H');
    $nextHour = ($currentHour + 1) % 24;
    $nextUpdate = mktime($nextHour, 0, 0, date('n'), date('j'), date('Y'));
    
    echo "Current hour: " . $currentHour . "<br>";
    echo "Next hour: " . $nextHour . "<br>";
    echo "Next update timestamp: " . $nextUpdate . "<br>";
    echo "Next update formatted: " . date('H:i', $nextUpdate) . "<br>";
    
    echo "<hr>";
    echo "Display output:<br>";
    echo 'Atualizado: ' . date('d/m/Y H:i', $lastUpdate) . "<br>";
    echo 'Próxima atualização: ' . date('H:i', $nextUpdate) . "<br>";
} else {
    echo "Cache file does not exist<br>";
}
?> 