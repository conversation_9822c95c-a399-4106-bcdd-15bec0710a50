<?php
// Force cache regeneration script
// This script deletes the cache and regenerates it

// Delete existing cache
if (file_exists('stats_cache.json')) {
    unlink('stats_cache.json');
    echo "Cache deleted.<br>";
}

// Include the API to regenerate cache
$_GET['force_refresh'] = true;
include 'api.php';

echo "Cache regenerated.<br>";

// Test the new cache
$cache = json_decode(file_get_contents('stats_cache.json'), true);

if (isset($cache['data']['hunting_zones']['registered_with_trajectories'])) {
    echo 'Zones with trajectories found: ' . count($cache['data']['hunting_zones']['registered_with_trajectories']) . '<br>';
} else {
    echo 'Zones with trajectories not found in cache<br>';
}

if (isset($cache['data']['hunting_zones']['registered_without_trajectories'])) {
    echo 'Zones without trajectories found: ' . count($cache['data']['hunting_zones']['registered_without_trajectories']) . '<br>';
} else {
    echo 'Zones without trajectories not found in cache<br>';
}

if (isset($cache['data']['hunting_zones']['unregistered'])) {
    echo 'Unregistered zones found: ' . count($cache['data']['hunting_zones']['unregistered']) . '<br>';
} else {
    echo 'Unregistered zones not found in cache<br>';
}
?> 