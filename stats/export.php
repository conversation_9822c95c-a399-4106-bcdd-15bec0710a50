<?php
// Export handler for statistics
// This file handles PDF and Excel export generation with actual data

// Load environment variables from .htaccess
function getEnvVar($name) {
    return $_SERVER[$name] ?? getenv($name) ?? '';
}

// Firebase configuration
$projectId = getEnvVar('FIREBASE_PROJECT_ID') ?: 'prorola-a2f66';
$cacheFile = 'stats_cache.json';
$cacheExpiry = 3600; // 1 hour

if (!isset($_GET['type'])) {
    http_response_code(400);
    header('Location: error/404.php');
    exit;
}

$exportType = $_GET['type'];

try {
    // Try to load from cache first
    $statsData = null;
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheExpiry) {
        $cacheContent = file_get_contents($cacheFile);
        $cacheData = json_decode($cacheContent, true);
        if ($cacheData && isset($cacheData['data'])) {
            $statsData = $cacheData['data'];
        }
    }
    
    // If no cache data, create basic structure with zeros
    if (!$statsData) {
        $statsData = [
            'users' => [
                'total' => 0,
                'technicians' => 0,
                'colaboradores' => 0,
                'hunt_managers' => 0,
                'administrators' => 0
            ],
            'hunting_zones' => [
                'total' => 0,
                'registered' => 0,
                'with_trajectories' => 0,
                'total_quota' => 0
            ],
            'trajectories' => [
                'total' => 0,
                'manual' => 0,
                'gps' => 0,
                'total_distance' => 0.0
            ],
            'contacts' => [
                'total' => 0,
                'manual' => 0,
                'mobile' => 0
            ],
            'reports' => [
                'total' => 0,
                'with_contacts' => 0,
                'total_report_contacts' => 0,
                'avg_contacts_per_report' => 0.0
            ]
        ];
    }
    
    if ($exportType === 'pdf') {
        generatePDF($statsData);
    } elseif ($exportType === 'excel') {
        generateExcel($statsData);
    } else {
        http_response_code(400);
        header('Location: error/404.php');
        exit;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    header('Location: error/500.php');
    exit;
}

function generatePDF($stats) {
    // Check if we can use wkhtmltopdf or fall back to HTML-to-PDF conversion
    $useWkhtmltopdf = false; // Set to true if wkhtmltopdf is available on server
    
    if ($useWkhtmltopdf && shell_exec('which wkhtmltopdf')) {
        generatePDFWithWkhtmltopdf($stats);
    } else {
        // Fallback: Generate HTML optimized for PDF conversion
        generateHTMLForPDF($stats);
    }
}

function generatePDFWithWkhtmltopdf($stats) {
    // Generate HTML content
    $htmlContent = generatePDFHTML($stats);
    
    // Create temporary HTML file
    $tempHtml = tempnam(sys_get_temp_dir(), 'prorola_stats_') . '.html';
    file_put_contents($tempHtml, $htmlContent);
    
    // Generate PDF using wkhtmltopdf
    $pdfFile = tempnam(sys_get_temp_dir(), 'prorola_stats_') . '.pdf';
    $command = "wkhtmltopdf --page-size A4 --margin-top 0.75in --margin-right 0.75in --margin-bottom 0.75in --margin-left 0.75in --encoding UTF-8 '$tempHtml' '$pdfFile'";
    
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($pdfFile)) {
        // Send PDF to browser
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="ProROLA_Estatisticas_' . date('Y-m-d_H-i') . '.pdf"');
        header('Content-Length: ' . filesize($pdfFile));
        readfile($pdfFile);
        
        // Clean up temporary files
        unlink($tempHtml);
        unlink($pdfFile);
    } else {
        // Fallback to HTML
        generateHTMLForPDF($stats);
    }
}

function generateHTMLForPDF($stats) {
    header('Content-Type: text/html; charset=UTF-8');
    header('Content-Disposition: inline; filename="ProROLA_Estatisticas_' . date('Y-m-d_H-i') . '.html"');
    
    echo generatePDFHTML($stats);
}

function generatePDFHTML($stats) {
    return '<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProROLA - Estatísticas</title>
    <style>
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            box-sizing: border-box;
        }
        
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body { 
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, Arial, sans-serif; 
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
            background: white;
            font-size: 12px;
        }
        
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 3px solid #0a7ea4;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 8px;
            page-break-inside: avoid;
        }
        
        h1 { 
            color: #0a7ea4; 
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .subtitle {
            color: #374151;
            font-size: 14px;
            margin: 6px 0;
        }
        
        .stats-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 2px solid #0a7ea4; 
            border-radius: 8px;
            background: #f8f9fa;
            page-break-inside: avoid;
        }
        
        .section-title {
            color: #0a7ea4;
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #0a7ea4;
            padding-bottom: 6px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .stats-grid { 
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item { 
            text-align: center; 
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .stat-value { 
            font-size: 24px; 
            font-weight: bold; 
            color: #0a7ea4; 
            display: block;
            margin-bottom: 6px;
        }
        
        .stat-label { 
            font-size: 11px; 
            color: #374151; 
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .chart-data {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .chart-title {
            color: #0a7ea4;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .chart-title i {
            margin-right: 6px;
        }
        
        .chart-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }
        
        .chart-stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
        }
        
        .chart-stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #0a7ea4;
            display: block;
            margin-bottom: 3px;
        }
        
        .chart-stat-label {
            font-size: 10px;
            color: #666;
            font-weight: 500;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 2px solid #0a7ea4;
            padding-top: 20px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            page-break-inside: avoid;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        /* Print optimizations */
        @media print {
            body { 
                font-size: 11px; 
            }
            .stats-section { 
                margin: 15px 0;
                padding: 15px;
            }
            .header { 
                margin-bottom: 20px; 
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="header">
        <h1>ProROLA</h1>
        <div class="subtitle">Estatísticas Públicas</div>
        <div class="subtitle">Sistema de Monitorização de Rola Brava</div>
        <div class="subtitle">Gerado em: ' . date('d/m/Y H:i') . '</div>
         </div>
    
    <div class="page-break"></div>
    
    <div class="stats-section">
        <h2 class="section-title"><i class="fas fa-binoculars"></i> Estatísticas de Zonas de Caça</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['hunting_zones']['total']) . '</span>
                <div class="stat-label">Total de Zonas</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['hunting_zones']['registered']) . '</span>
                <div class="stat-label">Zonas Registadas</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['hunting_zones']['with_trajectories']) . '</span>
                <div class="stat-label">Com Trajetos</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['hunting_zones']['total_quota']) . '</span>
                <div class="stat-label">Quota Total</div>
            </div>
        </div>
        
        <div class="chart-data">
            <div class="chart-title"><i class="fas fa-chart-pie"></i> Distribuição de Zonas</div>
            <div class="chart-stats-grid">
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['hunting_zones']['registered'] / max($stats['hunting_zones']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Taxa de Registo</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['hunting_zones']['with_trajectories'] / max($stats['hunting_zones']['registered'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Com Atividade</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round($stats['hunting_zones']['total_quota'] / max($stats['hunting_zones']['total'], 1), 1) . '</span>
                    <div class="chart-stat-label">Quota Média</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="stats-section">
        <h2 class="section-title"><i class="fas fa-route"></i> Estatísticas de Trajetos</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['trajectories']['total']) . '</span>
                <div class="stat-label">Total de Trajetos</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['trajectories']['manual']) . '</span>
                <div class="stat-label">Trajetos Manuais</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['trajectories']['gps']) . '</span>
                <div class="stat-label">Trajetos GPS</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['trajectories']['total_distance'], 1) . ' km</span>
                <div class="stat-label">Distância Total</div>
            </div>
        </div>
        
        <div class="chart-data">
            <div class="chart-title"><i class="fas fa-chart-bar"></i> Análise de Trajetos</div>
            <div class="chart-stats-grid">
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['trajectories']['manual'] / max($stats['trajectories']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Manuais</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['trajectories']['gps'] / max($stats['trajectories']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">GPS</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round($stats['trajectories']['total_distance'] / max($stats['trajectories']['total'], 1), 1) . ' km</span>
                    <div class="chart-stat-label">Distância Média</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="stats-section">
        <h2 class="section-title"><i class="fas fa-dove"></i> Estatísticas de Contactos</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['contacts']['total']) . '</span>
                <div class="stat-label">Total de Contactos</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['contacts']['manual']) . '</span>
                <div class="stat-label">Contactos Manuais</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['contacts']['mobile']) . '</span>
                <div class="stat-label">Contactos Móveis</div>
            </div>
        </div>
        
        <div class="chart-data">
            <div class="chart-title"><i class="fas fa-chart-line"></i> Distribuição de Contactos</div>
            <div class="chart-stats-grid">
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['contacts']['manual'] / max($stats['contacts']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Manuais</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['contacts']['mobile'] / max($stats['contacts']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Móveis</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round($stats['contacts']['total'] / max($stats['trajectories']['total'], 1), 1) . '</span>
                    <div class="chart-stat-label">Por Trajeto</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="stats-section">
        <h2 class="section-title"><i class="fas fa-file-alt"></i> Estatísticas de Relatórios</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['reports']['total']) . '</span>
                <div class="stat-label">Total de Relatórios</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['reports']['with_contacts']) . '</span>
                <div class="stat-label">Com Contactos</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['reports']['total_report_contacts']) . '</span>
                <div class="stat-label">Total de Contactos</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['reports']['avg_contacts_per_report'], 1) . '</span>
                <div class="stat-label">Média Contactos/Relatório</div>
            </div>
        </div>
        
        <div class="chart-data">
            <div class="chart-title"><i class="fas fa-chart-area"></i> Eficiência dos Relatórios</div>
            <div class="chart-stats-grid">
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['reports']['with_contacts'] / max($stats['reports']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Taxa de Conclusão</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . number_format($stats['reports']['total'] - $stats['reports']['with_contacts']) . '</span>
                    <div class="chart-stat-label">Sem Contactos</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round($stats['contacts']['total'] / max($stats['reports']['total'], 1), 1) . '</span>
                    <div class="chart-stat-label">Contactos/Relatório</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="stats-section">
        <h2 class="section-title"><i class="fas fa-users"></i> Estatísticas de Utilizadores</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['users']['total']) . '</span>
                <div class="stat-label">Total de Utilizadores</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['users']['technicians']) . '</span>
                <div class="stat-label">Técnicos</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['users']['colaboradores']) . '</span>
                <div class="stat-label">Colaboradores</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['users']['hunt_managers']) . '</span>
                <div class="stat-label">Gestores de Zona</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">' . number_format($stats['users']['administrators']) . '</span>
                <div class="stat-label">Administradores</div>
            </div>
        </div>
        
        <div class="chart-data">
            <div class="chart-title"><i class="fas fa-user-chart"></i> Distribuição de Utilizadores</div>
            <div class="chart-stats-grid">
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['users']['technicians'] / max($stats['users']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Técnicos</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['users']['colaboradores'] / max($stats['users']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Colaboradores</div>
                </div>
                <div class="chart-stat-item">
                    <span class="chart-stat-value">' . round(($stats['users']['hunt_managers'] / max($stats['users']['total'], 1)) * 100, 1) . '%</span>
                    <div class="chart-stat-label">Gestores</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><strong>ProROLA © ' . date('Y') . '</strong></p>
        <p>Sistema de Monitorização de Rola Brava</p>
        <p>Dados exportados em: ' . date('d/m/Y H:i:s') . '</p>
    </div>
</body>
</html>';
}

function generateExcel($stats) {
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="ProROLA_Estatisticas_' . date('Y-m-d_H-i') . '.xls"');
    
    echo '<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <style>
        table { 
            border-collapse: collapse; 
            width: 100%; 
            font-family: Arial, sans-serif;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left; 
        }
        th { 
            background-color: #0a7ea4; 
            color: white; 
            font-weight: bold; 
        }
        .section-header { 
            background-color: #e6f3f7; 
            font-weight: bold; 
            text-align: center;
        }
        .numeric { 
            text-align: right; 
        }
        h1 {
            color: #0a7ea4;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>ProROLA - Estatísticas Públicas</h1>
    <p style="text-align: center;">Gerado em: ' . date('d/m/Y H:i') . '</p>
    
    <table>
        <tr><th colspan="2" class="section-header">Estatísticas de Zonas de Caça</th></tr>
        <tr><td>Total de Zonas</td><td class="numeric">' . number_format($stats['hunting_zones']['total']) . '</td></tr>
        <tr><td>Zonas Registadas</td><td class="numeric">' . number_format($stats['hunting_zones']['registered']) . '</td></tr>
        <tr><td>Com Trajetos</td><td class="numeric">' . number_format($stats['hunting_zones']['with_trajectories']) . '</td></tr>
        <tr><td>Quota Total</td><td class="numeric">' . number_format($stats['hunting_zones']['total_quota']) . '</td></tr>
        
        <tr><th colspan="2" class="section-header">Estatísticas de Trajetos</th></tr>
        <tr><td>Total de Trajetos</td><td class="numeric">' . number_format($stats['trajectories']['total']) . '</td></tr>
        <tr><td>Trajetos Manuais</td><td class="numeric">' . number_format($stats['trajectories']['manual']) . '</td></tr>
        <tr><td>Trajetos GPS</td><td class="numeric">' . number_format($stats['trajectories']['gps']) . '</td></tr>
        <tr><td>Distância Total (km)</td><td class="numeric">' . number_format($stats['trajectories']['total_distance'], 1) . '</td></tr>
        
        <tr><th colspan="2" class="section-header">Estatísticas de Contactos</th></tr>
        <tr><td>Total de Contactos</td><td class="numeric">' . number_format($stats['contacts']['total']) . '</td></tr>
        <tr><td>Contactos Manuais</td><td class="numeric">' . number_format($stats['contacts']['manual']) . '</td></tr>
        <tr><td>Contactos Móveis</td><td class="numeric">' . number_format($stats['contacts']['mobile']) . '</td></tr>
        
        <tr><th colspan="2" class="section-header">Estatísticas de Relatórios</th></tr>
        <tr><td>Total de Relatórios</td><td class="numeric">' . number_format($stats['reports']['total']) . '</td></tr>
        <tr><td>Com Contactos</td><td class="numeric">' . number_format($stats['reports']['with_contacts']) . '</td></tr>
        <tr><td>Total de Contactos</td><td class="numeric">' . number_format($stats['reports']['total_report_contacts']) . '</td></tr>
        <tr><td>Média Contactos/Relatório</td><td class="numeric">' . number_format($stats['reports']['avg_contacts_per_report'], 1) . '</td></tr>
        
        <tr><th colspan="2" class="section-header">Estatísticas de Utilizadores</th></tr>
        <tr><td>Total de Utilizadores</td><td class="numeric">' . number_format($stats['users']['total']) . '</td></tr>
        <tr><td>Técnicos</td><td class="numeric">' . number_format($stats['users']['technicians']) . '</td></tr>
        <tr><td>Colaboradores</td><td class="numeric">' . number_format($stats['users']['colaboradores']) . '</td></tr>
        <tr><td>Gestores de Zona</td><td class="numeric">' . number_format($stats['users']['hunt_managers']) . '</td></tr>
        <tr><td>Administradores</td><td class="numeric">' . number_format($stats['users']['administrators']) . '</td></tr>
    </table>
    
    <p style="text-align: center; margin-top: 20px; font-size: 12px;">
        <strong>ProROLA © ' . date('Y') . '</strong><br>
        Sistema de Monitorização de Rola Brava
    </p>
</body>
</html>';
}
?> 