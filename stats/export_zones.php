<?php
// Zone Export handler for statistics
// This file handles Excel export generation for zone data

// Set timezone to match local time
date_default_timezone_set('Europe/Lisbon');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables from .htaccess
function getEnvVar($name) {
    return $_SERVER[$name] ?? getenv($name) ?? '';
}

// Firebase configuration
$projectId = getEnvVar('FIREBASE_PROJECT_ID') ?: 'prorola-a2f66';
$cacheFile = 'stats_cache.json';
$cacheExpiry = 3600; // 1 hour

if (!isset($_GET['type'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Export type not specified']);
    exit;
}

$exportType = $_GET['type'];
$validTypes = ['zones_with_trajectories', 'zones_without_trajectories', 'zones_unregistered'];

if (!in_array($exportType, $validTypes)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid export type']);
    exit;
}

try {
    // Try to load from cache first
    $statsData = null;
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheExpiry) {
        $cacheContent = file_get_contents($cacheFile);
        $cacheData = json_decode($cacheContent, true);
        if ($cacheData && isset($cacheData['data'])) {
            $statsData = $cacheData['data'];
        }
    }
    
    // If no cache data, redirect to main page to generate cache
    if (!$statsData) {
        header('Location: index.php');
        exit;
    }
    
    // Generate the Excel file
    generateZoneExcel($statsData, $exportType);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Export failed: ' . $e->getMessage()]);
}

function generateZoneExcel($stats, $exportType) {
    // Set headers for CSV download
    header('Content-Type: text/csv; charset=UTF-8');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    $filename = '';
    $title = '';
    $zones = [];
    
    switch ($exportType) {
        case 'zones_with_trajectories':
            $filename = 'Zonas_Com_Trajetos_' . date('Y-m-d_H-i') . '.csv';
            $title = 'Zonas Registadas com Trajetos';
            $zones = getZonesWithTrajectories($stats);
            break;
            
        case 'zones_without_trajectories':
            $filename = 'Zonas_Sem_Trajetos_' . date('Y-m-d_H-i') . '.csv';
            $title = 'Zonas Registadas sem Trajetos';
            $zones = $stats['hunting_zones']['registered_without_trajectories'] ?? [];
            break;
            
        case 'zones_unregistered':
            $filename = 'Zonas_Nao_Registadas_' . date('Y-m-d_H-i') . '.csv';
            $title = 'Zonas Não Registadas';
            $zones = $stats['hunting_zones']['unregistered'] ?? [];
            break;
    }
    
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    // Output BOM for proper UTF-8 encoding in Excel
    echo "\xEF\xBB\xBF";
    
    // Create CSV content that Excel can read properly
    $output = fopen('php://output', 'w');
    
    // Write title and summary information
    fputcsv($output, ['ProROLA - ' . $title], ';');
    fputcsv($output, ['Gerado em: ' . date('d/m/Y H:i')], ';');
    fputcsv($output, [''], ';'); // Empty line
    
    // Write summary
    fputcsv($output, ['RESUMO'], ';');
    fputcsv($output, ['Total de zonas nesta categoria: ' . count($zones)], ';');
    fputcsv($output, ['Total de zonas no sistema: ' . number_format($stats['hunting_zones']['total'])], ';');
    fputcsv($output, ['Zonas registadas: ' . number_format($stats['hunting_zones']['registered'])], ';');
    fputcsv($output, ['Zonas com trajetos: ' . number_format($stats['hunting_zones']['with_trajectories'])], ';');
    fputcsv($output, [''], ';'); // Empty line
    
    // Write headers
    if ($exportType === 'zones_with_trajectories') {
        fputcsv($output, ['Nº', 'Zona', 'Nome da Zona', 'Email Responsável', 'Tipo de Trajeto'], ';');
    } else {
        fputcsv($output, ['Nº', 'Zona', 'Nome da Zona', 'Email Responsável'], ';');
    }
    
    // Write data
    if (empty($zones)) {
        $emptyMessage = $exportType === 'zones_with_trajectories' ? 
            ['', '', 'Nenhuma zona encontrada nesta categoria', '', ''] :
            ['', '', 'Nenhuma zona encontrada nesta categoria', ''];
        fputcsv($output, $emptyMessage, ';');
    } else {
        $counter = 1;
        foreach ($zones as $zone) {
            $row = [
                $counter++,
                $zone['zona'] ?? 'N/A',
                $zone['nomeZona'] ?? 'N/A',
                $zone['email'] ?? 'N/A'
            ];
            
            if ($exportType === 'zones_with_trajectories') {
                $row[] = $zone['trajectoryType'] ?? 'N/A';
            }
            
            fputcsv($output, $row, ';');
        }
    }
    
    // Write footer
    fputcsv($output, [''], ';'); // Empty line
    fputcsv($output, ['ProROLA © ' . date('Y')], ';');
    fputcsv($output, ['Sistema 1.0.2'], ';');
    fputcsv($output, ['Dados exportados em: ' . date('d/m/Y H:i:s')], ';');
    
    fclose($output);
}

function getZonesWithTrajectories($stats) {
    // Get zones with trajectories from the stats data
    return $stats['hunting_zones']['registered_with_trajectories'] ?? [];
}
?> 