<?php
// Public Trajectory Map - No Authentication Required
// This page shows only trajectories from the ProROLA system

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get filter parameters
$dateRange = $_GET['dateRange'] ?? 'all';
$clearCache = $_GET['clearCache'] ?? false;
$skipZoneResolution = $_GET['skipZones'] ?? false;

// Firebase configuration
$projectId = 'prorola-a2f66';

// Load environment variables function
function getEnvVar($name) {
    return $_SERVER[$name] ?? getenv($name) ?? '';
}

/**
 * Simple Firebase class for trajectory data
 */
class TrajectoryFirebase {
    private $projectId;
    private $accessToken;
    private $cacheDir;
    private $cacheDuration = 1800; // 30 minutes in seconds

    public function __construct($projectId) {
        $this->projectId = $projectId;
        $this->accessToken = $this->getAdminAccessToken();
        $this->cacheDir = __DIR__ . '/cache';
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    private function getCacheKey($params = []) {
        return 'trajectories_' . md5(serialize($params));
    }

    private function getContactsCacheKey($params = []) {
        $serialized = serialize($params);
        $hash = md5($serialized);
        error_log("🔑 Cache key params: " . json_encode($params));
        error_log("🔑 Serialized params: " . $serialized);
        error_log("🔑 MD5 hash: " . $hash);
        return 'contacts_' . $hash;
    }

    private function getCacheFile($cacheKey) {
        return $this->cacheDir . '/' . $cacheKey . '.json';
    }

    private function getCacheData($cacheKey) {
        $cacheFile = $this->getCacheFile($cacheKey);
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (!$cacheData || !isset($cacheData['timestamp']) || !isset($cacheData['data'])) {
            return null;
        }
        
        // Check if cache is still valid
        if (time() - $cacheData['timestamp'] > $this->cacheDuration) {
            unlink($cacheFile); // Remove expired cache
            return null;
        }
        
        return $cacheData;
    }

    private function setCacheData($cacheKey, $data) {
        $cacheFile = $this->getCacheFile($cacheKey);
        $cacheData = [
            'timestamp' => time(),
            'data' => $data
        ];
        
        file_put_contents($cacheFile, json_encode($cacheData));
    }

    public function getCacheInfo() {
        $cacheKey = $this->getCacheKey(['all']); // Use same key as main cache
        $cacheFile = $this->getCacheFile($cacheKey);
        
        if (!file_exists($cacheFile)) {
            return [
                'exists' => false,
                'lastUpdated' => null,
                'nextUpdate' => null,
                'timeToNext' => null
            ];
        }
        
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (!$cacheData || !isset($cacheData['timestamp'])) {
            return [
                'exists' => false,
                'lastUpdated' => null,
                'nextUpdate' => null,
                'timeToNext' => null
            ];
        }
        
        $lastUpdated = $cacheData['timestamp'];
        $nextUpdate = $lastUpdated + $this->cacheDuration;
        $timeToNext = $nextUpdate - time();
        
        return [
            'exists' => true,
            'lastUpdated' => $lastUpdated,
            'nextUpdate' => $nextUpdate,
            'timeToNext' => max(0, $timeToNext),
            'isExpired' => $timeToNext <= 0
        ];
    }

    private function getAdminAccessToken() {
        try {
            // Get service account credentials from environment variables
            $serviceAccount = [
                'type' => getEnvVar('FIREBASE_TYPE'),
                'project_id' => getEnvVar('FIREBASE_PROJECT_ID'),
                'private_key_id' => getEnvVar('FIREBASE_PRIVATE_KEY_ID'),
                'private_key' => str_replace('\\n', "\n", getEnvVar('FIREBASE_PRIVATE_KEY')),
                'client_email' => getEnvVar('FIREBASE_CLIENT_EMAIL'),
                'client_id' => getEnvVar('FIREBASE_CLIENT_ID'),
                'auth_uri' => getEnvVar('FIREBASE_AUTH_URI'),
                'token_uri' => getEnvVar('FIREBASE_TOKEN_URI'),
                'auth_provider_x509_cert_url' => getEnvVar('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
                'client_x509_cert_url' => getEnvVar('FIREBASE_CLIENT_X509_CERT_URL')
            ];
            
            if (empty($serviceAccount['private_key']) || empty($serviceAccount['client_email'])) {
                throw new Exception('Firebase service account credentials not found');
            }
            
            // Create JWT
            $now = time();
            $payload = [
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/cloud-platform',
                'aud' => 'https://oauth2.googleapis.com/token',
                'exp' => $now + 3600,
                'iat' => $now
            ];
            
            $jwt = $this->createJWT($payload, $serviceAccount['private_key']);
            
            // Exchange JWT for access token
            $tokenData = [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($tokenData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode !== 200) {
                throw new Exception("Failed to get access token. HTTP Code: $httpCode");
            }
            
            $tokenResponse = json_decode($response, true);
            if (!isset($tokenResponse['access_token'])) {
                throw new Exception('Access token not found in response');
            }
            
            return $tokenResponse['access_token'];
            
        } catch (Exception $e) {
            error_log("Firebase auth error: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function createJWT($payload, $privateKey) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
        $payload = json_encode($payload);
        
        $base64Header = $this->base64UrlEncode($header);
        $base64Payload = $this->base64UrlEncode($payload);
        
        $signature = '';
        openssl_sign($base64Header . '.' . $base64Payload, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        $base64Signature = $this->base64UrlEncode($signature);
        
        return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
    }
    
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    public function getTrajectories($bounds = null, $dateRange = 'all', $types = null, $limit = 500)
    {
        try {
            // Create cache key for all trajectories (we cache the full dataset)
            $cacheKey = $this->getCacheKey(['all']);
            
            // Try to get cached data first
            $cachedData = $this->getCacheData($cacheKey);
            
            if ($cachedData !== null) {
                // Use cached trajectories and apply filters
                $allTrajectories = $cachedData['data']['trajectories'];
                $filteredTrajectories = $this->filterTrajectories($allTrajectories, $bounds, $dateRange, $types, $limit);
                return ['trajectories' => $filteredTrajectories, 'stats' => $this->calculateStats($filteredTrajectories)];
            }
            
            // Load fresh data from Firestore
            $trajectories = [];
            $uniqueZones = []; // Track unique zones to avoid duplicates
            
            // Get trajectories from both collections
            $collections = ['zonas', 'gestorMobile_trajetos'];

            foreach ($collections as $collection) {
                $collectionData = $this->getCollectionData($collection, $limit);

                
                foreach ($collectionData as $doc) {
                    // Process zonas collection (contains trajetos)
                    if ($collection === 'zonas') {
                        $parsed = $this->parseDocument($doc);
                        
                        // Check if zona has coordinates (some might not have trajectory data)
                        $coordinates = [];
                        if (isset($parsed['coordinates']) && is_array($parsed['coordinates']) && count($parsed['coordinates']) > 0) {
                            $coordinates = $parsed['coordinates'];
                        }
                        
                        // Only include if it has coordinate data and zone not already processed
                        $zoneId = $parsed['zoneId'] ?? uniqid();
                        if (count($coordinates) > 0 && !isset($uniqueZones[$zoneId])) {
                            $uniqueZones[$zoneId] = true; // Mark zone as processed
                            
                            // Debug: Log all available fields for this trajectory
                            error_log("Manual trajectory fields: " . implode(', ', array_keys($parsed)));
                            
                            // Check if this is a test trajectory - try multiple possible field names
                            $isTestTrajectory = false;
                            $testEmail = '<EMAIL>';
                            $possibleCreatorFields = ['createdBy', 'createdByEmail', 'userId', 'userEmail', 'email', 'creator', 'author'];
                            
                            foreach ($possibleCreatorFields as $field) {
                                if (isset($parsed[$field]) && $parsed[$field] === $testEmail) {
                                    $isTestTrajectory = true;
                                    error_log("Test trajectory detected via field '$field': " . $parsed[$field]);
                                    break;
                                }
                            }
                            
                            // Also check if trajectory name contains test indicators
                            $trajectoryName = $parsed['name'] ?? '';
                            if (stripos($trajectoryName, 'teste') !== false || 
                                stripos($trajectoryName, 'test') !== false ||
                                stripos($trajectoryName, 'zca teste') !== false) {
                                $isTestTrajectory = true;
                                error_log("Test trajectory detected via name: " . $trajectoryName);
                            }
                            
                            if (!$isTestTrajectory) {
                                error_log("No test indicators found for trajectory: " . $trajectoryName);
                            }
                            
                            $trajectory = [
                                'id' => $zoneId,
                                'name' => $parsed['name'] ?? 'Zona de Caça',
                                'type' => 'Manual', // Zonas are manual trajectories
                                'coordinates' => $coordinates,
                                'created_at' => $parsed['createdAt'] ?? $parsed['created_at'] ?? null,
                                'distance' => $parsed['distance'] ?? null,
                                'source' => 'zona',
                                'isTest' => $isTestTrajectory,
                                'testEmail' => $isTestTrajectory ? $testEmail : null
                            ];
                            
                            // Apply type filter
                            if ($types !== null && !empty($types) && !in_array($trajectory['type'], $types)) {
                                continue;
                            }
                            
                            // Apply date filter
                            if ($this->matchesDateRange($trajectory['created_at'], $dateRange)) {
                                // Apply bounds filter if provided
                                if (!$bounds || $this->trajectoryInBounds($trajectory['coordinates'], $bounds)) {
                                    $trajectories[] = $trajectory;
                                }
                            }
                        }
                    }
                    
                    // Process gestorMobile_trajetos collection (GPS trajectories from mobile app)
                    if ($collection === 'gestorMobile_trajetos') {
                        $parsed = $this->parseDocument($doc);

                        
                        // Check if mobile trajectory has path coordinates
                        $coordinates = [];
                        
                        // Try multiple possible coordinate field names
                        $coordFields = ['pathCoordinates', 'coordinates', 'path', 'route', 'points'];
                        foreach ($coordFields as $field) {
                            if (isset($parsed[$field]) && is_array($parsed[$field]) && count($parsed[$field]) > 0) {
                                $coordinates = $parsed[$field];

                                break;
                            }
                        }
                        
                        // Only include if it has coordinate data and zone not already processed
                        $zoneId = $parsed['zoneId'] ?? $parsed['sessionId'] ?? uniqid();
                        if (count($coordinates) > 0 && !isset($uniqueZones[$zoneId])) {
                            $uniqueZones[$zoneId] = true; // Mark zone as processed
                            
                            // Debug: Log all available fields for this GPS trajectory
                            error_log("GPS trajectory fields: " . implode(', ', array_keys($parsed)));
                            
                            // Check if this is a test trajectory - try multiple possible field names
                            $isTestTrajectory = false;
                            $testEmail = '<EMAIL>';
                            $possibleCreatorFields = ['createdBy', 'createdByEmail', 'userId', 'userEmail', 'email', 'creator', 'author'];
                            
                            foreach ($possibleCreatorFields as $field) {
                                if (isset($parsed[$field]) && $parsed[$field] === $testEmail) {
                                    $isTestTrajectory = true;
                                    error_log("GPS Test trajectory detected via field '$field': " . $parsed[$field]);
                                    break;
                                }
                            }
                            
                            // Also check if trajectory name contains test indicators
                            $trajectoryName = $parsed['zoneName'] ?? $parsed['name'] ?? '';
                            if (stripos($trajectoryName, 'teste') !== false || 
                                stripos($trajectoryName, 'test') !== false ||
                                stripos($trajectoryName, 'zca teste') !== false) {
                                $isTestTrajectory = true;
                                error_log("GPS Test trajectory detected via name: " . $trajectoryName);
                            }
                            
                            if (!$isTestTrajectory) {
                                error_log("No test indicators found for GPS trajectory: " . $trajectoryName);
                            }
                            
                            // Normalize coordinate format (convert latitude/longitude to lat/lng)
                            $normalizedCoords = [];
                            foreach ($coordinates as $coord) {
                                if (is_array($coord)) {
                                    $lat = $coord['lat'] ?? $coord['latitude'] ?? null;
                                    $lng = $coord['lng'] ?? $coord['longitude'] ?? null;
                                    
                                    if ($lat !== null && $lng !== null) {
                                        $normalizedCoords[] = [
                                            'lat' => (float)$lat,
                                            'lng' => (float)$lng
                                        ];
                                    }
                                }
                            }
                            
                            if (count($normalizedCoords) > 0) {
                                $trajectory = [
                                    'id' => $parsed['sessionId'] ?? uniqid(),
                                    'name' => $parsed['zoneName'] ?? 'Trajeto Mobile',
                                    'type' => 'GPS', // Mobile trajectories are GPS-based
                                    'coordinates' => $normalizedCoords,
                                    'created_at' => $parsed['createdAt'] ?? $parsed['created_at'] ?? null,
                                    'distance' => $parsed['totalDistance'] ?? null,
                                    'source' => 'mobile',
                                    'isTest' => $isTestTrajectory,
                                    'testEmail' => $isTestTrajectory ? $testEmail : null
                                ];
                                

                            
                                // Apply type filter
                                if ($types !== null && !empty($types) && !in_array($trajectory['type'], $types)) {
                                    continue;
                                }
                                
                                // Apply date filter
                                if ($this->matchesDateRange($trajectory['created_at'], $dateRange)) {
                                    // Apply bounds filter if provided
                                    if (!$bounds || $this->trajectoryInBounds($trajectory['coordinates'], $bounds)) {
                                        $trajectories[] = $trajectory;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // Cache the full dataset for future requests
            $this->setCacheData($cacheKey, ['trajectories' => $trajectories]);
            
            // Apply filters to the trajectory data
            $filteredTrajectories = $this->filterTrajectories($trajectories, $bounds, $dateRange, $types, $limit);
            
            return ['trajectories' => $filteredTrajectories, 'stats' => $this->calculateStats($filteredTrajectories)];
            
        } catch (Exception $e) {
            error_log("Error getting trajectories: " . $e->getMessage());
            return ['trajectories' => [], 'stats' => $this->calculateStats([])];
        }
    }

    private function filterTrajectories($trajectories, $bounds = null, $dateRange = 'all', $types = null, $limit = 500)
    {
        $filtered = [];
        
        foreach ($trajectories as $trajectory) {
            // Apply type filter
            if ($types !== null && !empty($types) && !in_array($trajectory['type'], $types)) {
                continue;
            }
            
            // Apply date filter
            if (!$this->matchesDateRange($trajectory['created_at'] ?? null, $dateRange)) {
                continue;
            }
            
            // Apply bounds filter
            if ($bounds && !$this->trajectoryInBounds($trajectory['coordinates'] ?? [], $bounds)) {
                continue;
            }
            
            $filtered[] = $trajectory;
            
            // Apply limit
            if ($limit && count($filtered) >= $limit) {
                break;
            }
        }
        
        return $filtered;
    }

    private function getCollectionData($collection, $limit = null)
    {
        try {
            $documents = [];
            $pageToken = null;
            $pageSize = min($limit ?? 1000, 1000);
            $totalFetched = 0;
            
            do {
                $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
                $queryParams = [];
                
                if ($pageSize) {
                    $queryParams['pageSize'] = $pageSize;
                }
                if ($pageToken) {
                    $queryParams['pageToken'] = $pageToken;
                }
                if (!empty($queryParams)) {
                    $url .= '?' . http_build_query($queryParams);
                }
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->accessToken,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode !== 200) {
                    throw new Exception("HTTP Error $httpCode for collection $collection");
                }
                
                $data = json_decode($response, true);
                
                if (isset($data['documents'])) {
                    foreach ($data['documents'] as $doc) {
                        $documents[] = $doc;
                        $totalFetched++;
                        
                        if ($limit && $totalFetched >= $limit) {
                            break 2; // Break out of both loops
                        }
                    }
                }
                
                $pageToken = $data['nextPageToken'] ?? null;
                
            } while ($pageToken && (!$limit || $totalFetched < $limit));
            
            return $documents;
            
        } catch (Exception $e) {
            error_log("Error fetching collection $collection: " . $e->getMessage());
            return [];
        }
    }

    private function parseDocument($doc)
    {
        $parsed = [];
        if (isset($doc['fields'])) {
            foreach ($doc['fields'] as $key => $value) {
                $parsed[$key] = $this->convertFromFirestoreValue($value);
            }
        }
        
        // Extract document ID from the document name
        if (isset($doc['name'])) {
            // Document name format: projects/{projectId}/databases/(default)/documents/{collection}/{docId}
            $nameParts = explode('/', $doc['name']);
            $parsed['id'] = end($nameParts);
        }
        
        return $parsed;
    }
    
    private function convertFromFirestoreValue($field) {
        if (isset($field['stringValue'])) {
            return $field['stringValue'];
        } elseif (isset($field['integerValue'])) {
            return (int)$field['integerValue'];
        } elseif (isset($field['doubleValue'])) {
            return (float)$field['doubleValue'];
        } elseif (isset($field['booleanValue'])) {
            return (bool)$field['booleanValue'];
        } elseif (isset($field['timestampValue'])) {
            return $field['timestampValue'];
        } elseif (isset($field['arrayValue']['values'])) {
            $array = [];
            foreach ($field['arrayValue']['values'] as $value) {
                $array[] = $this->convertFromFirestoreValue($value);
            }
            return $array;
        } elseif (isset($field['mapValue']['fields'])) {
            $map = [];
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $map[$key] = $this->convertFromFirestoreValue($value);
            }
            return $map;
        } elseif (isset($field['nullValue'])) {
            return null;
        }
        return null;
    }
    
    private function matchesDateRange($dateString, $dateRange) {
        if ($dateRange === 'all' || !$dateString) {
            return true;
        }
        
        try {
            $date = new DateTime($dateString);
            $now = new DateTime();
            
            switch ($dateRange) {
                case 'today':
                    return $date->format('Y-m-d') === $now->format('Y-m-d');
                case 'week':
                    $weekAgo = $now->sub(new DateInterval('P7D'));
                    return $date >= $weekAgo;
                case 'month':
                    $monthAgo = $now->sub(new DateInterval('P1M'));
                    return $date >= $monthAgo;
                default:
                    return true;
            }
        } catch (Exception $e) {
            return true;
        }
    }
    
    private function trajectoryInBounds($coordinates, $bounds) {
        if (!$bounds || !is_array($coordinates) || empty($coordinates)) {
            return true;
        }
        
        // bounds format: ['ne' => ['lat' => X, 'lng' => Y], 'sw' => ['lat' => X, 'lng' => Y]]
        $north = $bounds['ne']['lat'];
        $south = $bounds['sw']['lat'];
        $east = $bounds['ne']['lng'];
        $west = $bounds['sw']['lng'];
        
        foreach ($coordinates as $coord) {
            if (isset($coord['lat']) && isset($coord['lng'])) {
                $lat = (float)$coord['lat'];
                $lng = (float)$coord['lng'];
                
                if ($lat >= $south && $lat <= $north && $lng >= $west && $lng <= $east) {
                    return true;
                }
            }
        }
        
        return false;
    }

    private function lookupUsers(array $uids) {
        if (empty($uids)) {
            return [];
        }

        // Ensure UIDs are unique and filter out null/empty values
        $uids = array_unique(array_filter($uids, function($uid) {
            return !empty($uid) && is_string($uid);
        }));
        
        if (empty($uids)) {
            return [];
        }

        try {
            // Use Firebase Admin SDK with service account token
            $accessToken = $this->getAdminAccessToken();
            
            $url = "https://identitytoolkit.googleapis.com/v1/accounts:lookup";
            $data = ['localId' => array_values($uids)]; // Ensure array is indexed

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                error_log("Firebase user lookup failed. HTTP Code: $httpCode. Response: $response");
                return [];
            }

            $result = json_decode($response, true);
            $userMap = [];

            if (isset($result['users'])) {
                foreach ($result['users'] as $user) {
                    if (isset($user['localId'])) {
                        $userMap[$user['localId']] = $user['displayName'] ?? $user['email'] ?? $user['localId'];
                    }
                }
                error_log("Successfully looked up " . count($userMap) . " users out of " . count($uids) . " requested");
            } else {
                error_log("No users found in Firebase lookup response");
            }

            return $userMap;

        } catch (Exception $e) {
            error_log("Error looking up Firebase users: " . $e->getMessage());
            return [];
        }
    }
    
    private function resolveZoneNames(&$contacts) {
        if (empty($contacts)) {
            return;
        }
        
        try {
            // Limit zone resolution to prevent performance issues
            $maxContactsForZoneResolution = 1000;
            if (count($contacts) > $maxContactsForZoneResolution) {
                error_log("Skipping zone name resolution for " . count($contacts) . " contacts (limit: $maxContactsForZoneResolution)");
                return;
            }
            
            // Set a time limit for zone resolution
            $startTime = microtime(true);
            
            // Collect all trajectory IDs and zone IDs
            $trajectoryIds = [];
            $zoneIds = [];
            
            foreach ($contacts as $contact) {
                // Direct zone ID (for mobile contacts)
                if (!empty($contact['zoneId'])) {
                    $zoneIds[] = $contact['zoneId'];
                }
                // Trajectory ID (for web contacts)
                if (!empty($contact['trajectoryId'])) {
                    $trajectoryIds[] = $contact['trajectoryId'];
                }
            }
            
            // Get zone names for direct zone IDs
            $zoneNames = [];
            if (!empty($zoneIds)) {
                $zoneNames = $this->getZoneNames(array_unique($zoneIds));
            }
            
            // Get trajectory documents to find their zone IDs
            $trajectoryZones = [];
            if (!empty($trajectoryIds)) {
                $trajectoryZones = $this->getTrajectoryZones(array_unique($trajectoryIds));
                
                // Get zone names for trajectory zones
                $trajectoryZoneIds = array_values($trajectoryZones);
                if (!empty($trajectoryZoneIds)) {
                    $additionalZoneNames = $this->getZoneNames(array_unique($trajectoryZoneIds));
                    $zoneNames = array_merge($zoneNames, $additionalZoneNames);
                }
            }
            
            // Apply zone names to contacts
            $resolvedCount = 0;
            foreach ($contacts as &$contact) {
                if (!empty($contact['zoneId']) && isset($zoneNames[$contact['zoneId']])) {
                    // Direct zone ID
                    $contact['zoneName'] = $zoneNames[$contact['zoneId']];
                    $resolvedCount++;
                } elseif (!empty($contact['trajectoryId']) && isset($trajectoryZones[$contact['trajectoryId']])) {
                    // Via trajectory
                    $zoneId = $trajectoryZones[$contact['trajectoryId']];
                    if (isset($zoneNames[$zoneId])) {
                        $contact['zoneName'] = $zoneNames[$zoneId];
                        $contact['zoneId'] = $zoneId; // Also set the zone ID
                        $resolvedCount++;
                    }
                }
            }
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            error_log("Resolved zone names for $resolvedCount out of " . count($contacts) . " contacts in {$duration}ms");
            
        } catch (Exception $e) {
            error_log("Error resolving zone names (non-critical): " . $e->getMessage());
            // Don't throw - this is optional functionality
        }
    }
    
    private function getZoneNames($zoneIds) {
        if (empty($zoneIds)) {
            return [];
        }
        
        try {
            $zoneNames = [];
            
            // Use cache for zone names
            $cacheKey = 'zone_names_' . md5(serialize($zoneIds));
            $cachedZoneNames = $this->getCacheData($cacheKey);
            
            if ($cachedZoneNames !== null) {
                return $cachedZoneNames['data'];
            }
            
            // OPTIMIZATION: Get all zone documents in batch instead of individual calls
            $allZones = $this->getCollectionData('zonasCaca', 1000);
            
            foreach ($allZones as $doc) {
                $zoneDoc = $this->parseDocument($doc);
                $docId = $zoneDoc['id'] ?? null;
                
                if ($docId && in_array($docId, $zoneIds) && isset($zoneDoc['nomeZona'])) {
                    $zoneNames[$docId] = $zoneDoc['nomeZona'];
                }
            }
            
            // Cache zone names for 1 hour
            $this->setCacheData($cacheKey, $zoneNames);
            
            return $zoneNames;
            
        } catch (Exception $e) {
            error_log("Error getting zone names: " . $e->getMessage());
            return [];
        }
    }
    
    private function getTrajectoryZones($trajectoryIds) {
        if (empty($trajectoryIds)) {
            return [];
        }
        
        try {
            $trajectoryZones = [];
            
            // Use cache for trajectory zones
            $cacheKey = 'trajectory_zones_' . md5(serialize($trajectoryIds));
            $cachedTrajectoryZones = $this->getCacheData($cacheKey);
            
            if ($cachedTrajectoryZones !== null) {
                return $cachedTrajectoryZones['data'];
            }
            
            // OPTIMIZATION: Get all trajectory documents in batch instead of individual calls
            $allTrajectories = $this->getCollectionData('zonas', 2000);
            
            foreach ($allTrajectories as $doc) {
                $trajectoryDoc = $this->parseDocument($doc);
                $docId = $trajectoryDoc['id'] ?? null;
                
                if ($docId && in_array($docId, $trajectoryIds) && isset($trajectoryDoc['zoneId'])) {
                    $trajectoryZones[$docId] = $trajectoryDoc['zoneId'];
                }
            }
            
            // Cache trajectory zones for 1 hour
            $this->setCacheData($cacheKey, $trajectoryZones);
            
            return $trajectoryZones;
            
        } catch (Exception $e) {
            error_log("Error getting trajectory zones: " . $e->getMessage());
            return [];
        }
    }
    
    private function resolveTrajectoryNames(&$contacts) {
        if (empty($contacts)) {
            return;
        }
        
        try {
            // Collect all trajectory IDs from both trajectoryId (web) and sessionId (mobile)
            $trajectoryIds = [];
            foreach ($contacts as $contact) {
                // Web contacts use trajectoryId
                if (!empty($contact['trajectoryId'])) {
                    $trajectoryIds[] = $contact['trajectoryId'];
                }
                // Mobile contacts use sessionId as trajectory reference
                if (!empty($contact['sessionId'])) {
                    $trajectoryIds[] = $contact['sessionId'];
                }
            }
            
            if (empty($trajectoryIds)) {
                return;
            }
            
            // Get trajectory names from both 'zonas' (web) and 'gestorMobile_trajetos' (mobile) collections
            $trajectoryNames = $this->getTrajectoryNames(array_unique($trajectoryIds));
            
            // Apply trajectory names to contacts
            $resolvedCount = 0;
            foreach ($contacts as &$contact) {
                // Check trajectoryId first (web contacts)
                if (!empty($contact['trajectoryId']) && isset($trajectoryNames[$contact['trajectoryId']])) {
                    $contact['trajectoryName'] = $trajectoryNames[$contact['trajectoryId']];
                    $resolvedCount++;
                }
                // Check sessionId for mobile contacts
                elseif (!empty($contact['sessionId']) && isset($trajectoryNames[$contact['sessionId']])) {
                    $contact['trajectoryName'] = $trajectoryNames[$contact['sessionId']];
                    $resolvedCount++;
                }
            }
            
            error_log("Resolved trajectory names for $resolvedCount out of " . count($contacts) . " contacts");
            
        } catch (Exception $e) {
            error_log("Error resolving trajectory names (non-critical): " . $e->getMessage());
            // Don't throw - this is optional functionality
        }
    }
    
    private function getTrajectoryNames($trajectoryIds) {
        if (empty($trajectoryIds)) {
            return [];
        }
        
        try {
            $trajectoryNames = [];
            
            // Use cache for trajectory names
            $cacheKey = 'trajectory_names_' . md5(serialize($trajectoryIds));
            $cachedTrajectoryNames = $this->getCacheData($cacheKey);
            
            if ($cachedTrajectoryNames !== null) {
                return $cachedTrajectoryNames['data'];
            }
            
            // OPTIMIZATION: Get all trajectory documents in batch from both collections
            
            // 1. Get web trajectories from 'zonas' collection
            $allWebTrajectories = $this->getCollectionData('zonas', 2000);
            
            foreach ($allWebTrajectories as $doc) {
                $trajectoryDoc = $this->parseDocument($doc);
                $docId = $trajectoryDoc['id'] ?? null;
                
                if ($docId && in_array($docId, $trajectoryIds)) {
                    // Look for trajectory name in various possible fields
                    $trajectoryName = $trajectoryDoc['nomeZona'] ?? 
                                    $trajectoryDoc['zoneName'] ?? 
                                    $trajectoryDoc['name'] ?? 
                                    $trajectoryDoc['title'] ?? 
                                    'Trajeto ' . substr($docId, 0, 8);
                    
                    $trajectoryNames[$docId] = $trajectoryName;
                }
            }
            
            // 2. Get mobile trajectories from 'gestorMobile_trajetos' collection
            $allMobileTrajectories = $this->getCollectionData('gestorMobile_trajetos', 2000);
            
            foreach ($allMobileTrajectories as $doc) {
                $trajectoryDoc = $this->parseDocument($doc);
                $sessionId = $trajectoryDoc['sessionId'] ?? $trajectoryDoc['id'] ?? null;
                
                if ($sessionId && in_array($sessionId, $trajectoryIds)) {
                    // Look for trajectory name in mobile trajectory fields
                    $trajectoryName = $trajectoryDoc['zoneName'] ?? 
                                    $trajectoryDoc['nomeZona'] ?? 
                                    $trajectoryDoc['name'] ?? 
                                    $trajectoryDoc['title'] ?? 
                                    'Trajeto Mobile ' . substr($sessionId, 0, 8);
                    
                    $trajectoryNames[$sessionId] = $trajectoryName;
                }
            }
            
            // Cache trajectory names for 1 hour
            $this->setCacheData($cacheKey, $trajectoryNames);
            
            return $trajectoryNames;
            
        } catch (Exception $e) {
            error_log("Error getting trajectory names: " . $e->getMessage());
            return [];
        }
    }


    // NEW: Contacts functionality - extends existing system without breaking it
    public function getContacts($bounds = null, $dateRange = 'all', $circumstanceFilter = null, $limit = 10000, $clearCache = false, $skipZoneResolution = false)
    {
        try {
            // Create cache key for all contacts (we cache the full dataset)
            $cacheKey = $this->getContactsCacheKey(['all']);
            
            // Clear cache if requested
            if ($clearCache) {
                $cacheFile = $this->getCacheFile($cacheKey);
                if (file_exists($cacheFile)) {
                    unlink($cacheFile);
                    error_log("Cache cleared for contacts");
                }
            }
            
            // TEMPORARY: Clear cache to regenerate with trajectory names
            // This can be removed after the cache is regenerated once
            $cacheFile = $this->getCacheFile($cacheKey);
            if (file_exists($cacheFile)) {
                unlink($cacheFile);
                error_log("🧹 Temporarily cleared cache to regenerate with trajectory names");
            }
            
            // Try to get cached data first
            $cacheFile = $this->getCacheFile($cacheKey);
            error_log("🔑 Cache key generated: " . $cacheKey);
            error_log("📁 Cache file path: " . $cacheFile);
            error_log("📂 Cache directory: " . $this->cacheDir);
            error_log("📋 Cache directory exists: " . (is_dir($this->cacheDir) ? 'YES' : 'NO'));
            
            // List all files in cache directory
            if (is_dir($this->cacheDir)) {
                $files = scandir($this->cacheDir);
                error_log("📄 Files in cache directory: " . implode(', ', array_filter($files, function($f) { return $f !== '.' && $f !== '..'; })));
            }
            
            error_log("🔍 Cache file exists: " . (file_exists($cacheFile) ? 'YES' : 'NO'));
            
            if (file_exists($cacheFile)) {
                $fileAge = time() - filemtime($cacheFile);
                error_log("⏰ Cache file age: " . $fileAge . " seconds (max: " . $this->cacheDuration . ")");
            }
            
            $cachedData = $this->getCacheData($cacheKey);
            
            if ($cachedData !== null) {
                // Use cached contacts - they already have resolved user names and zone names
                $allContacts = $cachedData['data']['contacts'];
                
                error_log("Using cached contacts: " . count($allContacts) . " contacts loaded from cache");
                
                // Debug: Check if cached contacts have resolved names or UIDs
                $sampleContact = $allContacts[0] ?? null;
                if ($sampleContact) {
                    error_log("Sample cached contact createdBy: " . ($sampleContact['createdBy'] ?? 'null'));
                    error_log("Sample cached contact has resolved name: " . (isset($sampleContact['createdBy']) && !preg_match('/^[a-zA-Z0-9]{28}$/', $sampleContact['createdBy']) ? 'YES' : 'NO'));
                }
                
                // Apply filters to the cached contact data
                $filteredContacts = $this->filterContacts($allContacts, $bounds, $dateRange, $circumstanceFilter, $limit);
                return ['contacts' => $filteredContacts, 'stats' => $this->calculateContactStats($filteredContacts)];
            }
            
            error_log("No cache found, loading fresh data from Firestore...");
            
            // Load fresh data from Firestore
            $contacts = [];
            
            // Get contacts from all collections (same as stats page)
            $collections = ['contacts', 'contactEvents', 'gestorMobile_contacts'];
            
            foreach ($collections as $collection) {
                $collectionData = $this->getCollectionData($collection, $limit);
                
                foreach ($collectionData as $doc) {
                    $parsed = $this->parseDocument($doc);
                    
                    // Check if contact has coordinates (handle different collection structures)
                    $coordinates = null;
                    if (isset($parsed['coordinates']) && is_array($parsed['coordinates'])) {
                        $coordinates = $parsed['coordinates'];
                    } elseif (isset($parsed['location']) && is_array($parsed['location'])) {
                        $coordinates = $parsed['location'];
                    } elseif (isset($parsed['contactLocation']) && is_array($parsed['contactLocation'])) {
                        // contactEvents collection uses contactLocation field
                        $coordinates = [
                            'lat' => $parsed['contactLocation']['latitude'] ?? null,
                            'lng' => $parsed['contactLocation']['longitude'] ?? null
                        ];
                    } elseif (isset($parsed['observerLocation']) && is_array($parsed['observerLocation'])) {
                        // fallback to observer location for contactEvents
                        $coordinates = [
                            'lat' => $parsed['observerLocation']['latitude'] ?? null,
                            'lng' => $parsed['observerLocation']['longitude'] ?? null
                        ];
                    }
                    
                    // Only include if it has coordinate data
                    if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
                        // Check if this is a test contact - same filtering as trajectories
                        $isTestContact = false;
                        $testEmail = '<EMAIL>';
                        $possibleCreatorFields = ['createdBy', 'createdByEmail', 'userId', 'userEmail', 'email', 'creator', 'author'];
                        
                        foreach ($possibleCreatorFields as $field) {
                            if (isset($parsed[$field]) && $parsed[$field] === $testEmail) {
                                $isTestContact = true;
                                break;
                            }
                        }
                        
                        // Skip test contacts (same as trajectories)
                        if ($isTestContact) {
                            continue;
                        }
                        
                        // Handle different field names across collections
                        $circumstance = $parsed['circumstance'] ?? 'Não especificado';
                        
                        if ($collection === 'contactEvents') {
                            // contactEvents uses 'circumstances' (plural) with different structure
                            if (isset($parsed['circumstances']) && is_array($parsed['circumstances'])) {
                                $circumstances = $parsed['circumstances'];
                                // Convert circumstances object to readable string
                                $circumstanceLabels = [];
                                if (!empty($circumstances['rolaEmVoo'])) $circumstanceLabels[] = 'Rola em voo';
                                if (!empty($circumstances['rolaAdultaCantando'])) $circumstanceLabels[] = 'Rola adulta cantando';
                                if (!empty($circumstances['adultoPousado'])) $circumstanceLabels[] = 'Adulto pousado';
                                if (!empty($circumstances['adultoEmDisplay'])) $circumstanceLabels[] = 'Adulto em display';
                                if (!empty($circumstances['adultoAIncubar'])) $circumstanceLabels[] = 'Adulto a incubar';
                                if (!empty($circumstances['ovos'])) $circumstanceLabels[] = 'Ovos';
                                if (!empty($circumstances['crias'])) $circumstanceLabels[] = 'Crias';
                                if (!empty($circumstances['juvenile'])) $circumstanceLabels[] = 'Juvenil';
                                if (!empty($circumstances['nichoOcupado'])) $circumstanceLabels[] = 'Nicho ocupado';
                                if (!empty($circumstances['ninhoVazio'])) $circumstanceLabels[] = 'Ninho vazio';
                                if (!empty($circumstances['outraQual']) && !empty($circumstances['outraQualText'])) {
                                    $circumstanceLabels[] = $circumstances['outraQualText'];
                                }
                                $circumstance = !empty($circumstanceLabels) ? implode(', ', $circumstanceLabels) : 'Não especificado';
                            }
                        } elseif ($collection === 'gestorMobile_contacts') {
                            // gestorMobile_contacts also uses 'circumstances' with same structure as contactEvents
                            if (isset($parsed['circumstances']) && is_array($parsed['circumstances'])) {
                                $circumstances = $parsed['circumstances'];
                                // Convert circumstances object to readable string
                                $circumstanceLabels = [];
                                if (!empty($circumstances['rolaEmVoo'])) $circumstanceLabels[] = 'Rola em voo';
                                if (!empty($circumstances['rolaAdultaCantando'])) $circumstanceLabels[] = 'Rola adulta cantando';
                                if (!empty($circumstances['adultoPousado'])) $circumstanceLabels[] = 'Adulto pousado';
                                if (!empty($circumstances['adultoEmDisplay'])) $circumstanceLabels[] = 'Adulto em display';
                                if (!empty($circumstances['adultoAIncubar'])) $circumstanceLabels[] = 'Adulto a incubar';
                                if (!empty($circumstances['ovos'])) $circumstanceLabels[] = 'Ovos';
                                if (!empty($circumstances['crias'])) $circumstanceLabels[] = 'Crias';
                                if (!empty($circumstances['juvenile'])) $circumstanceLabels[] = 'Juvenil';
                                if (!empty($circumstances['nichoOcupado'])) $circumstanceLabels[] = 'Nicho ocupado';
                                if (!empty($circumstances['ninhoVazio'])) $circumstanceLabels[] = 'Ninho vazio';
                                if (!empty($circumstances['outraQual']) && !empty($circumstances['outraQualText'])) {
                                    $circumstanceLabels[] = $circumstances['outraQualText'];
                                }
                                $circumstance = !empty($circumstanceLabels) ? implode(', ', $circumstanceLabels) : 'Não especificado';
                            }
                        }

                        // Validate coordinates are within reasonable bounds for Portugal
                        $lat = (float)$coordinates['lat'];
                        $lng = (float)$coordinates['lng'];
                        
                        // Skip contacts with invalid coordinates (outside Portugal/Europe region)
                        if ($lat < 30 || $lat > 50 || $lng < -15 || $lng > 5) {
                            continue;
                        }
                        
                        $contact = [
                            'id' => $parsed['id'] ?? uniqid(),
                            'coordinates' => [
                                'lat' => $lat,
                                'lng' => $lng
                            ],
                            'circumstance' => $circumstance,
                            'location' => $parsed['location'] ?? null,
                            'time' => $parsed['time'] ?? $parsed['timestamp'] ?? null,
                            'created_at' => $parsed['createdAt'] ?? $parsed['created_at'] ?? $parsed['timestamp'] ?? null,
                            'trajectoryId' => $parsed['trajectoryId'] ?? null,
                            'sessionId' => $parsed['sessionId'] ?? null,
                            'source' => $collection,
                            'isTest' => false,
                            'createdBy' => $parsed['createdBy'] ?? $parsed['userName'] ?? $parsed['userId'] ?? null,
                            'originalCreatedBy' => $parsed['createdBy'] ?? $parsed['userName'] ?? $parsed['userId'] ?? null, // Keep original for debugging
                            'zoneId' => $parsed['zoneId'] ?? null, // Direct zone ID for mobile contacts
                            'zoneName' => null, // Will be populated later
                            'trajectoryName' => null // Will be populated later
                        ];
                        
                        $contacts[] = $contact;
                    }
                }
            }
            
            // Resolve user IDs to names
            $uids = array_column($contacts, 'createdBy');
            $uids = array_filter($uids, function($uid) {
                return !empty($uid) && is_string($uid);
            });
            
            if (!empty($uids)) {
                $userNames = $this->lookupUsers($uids);
                error_log("Attempting to resolve " . count($uids) . " user IDs: " . implode(', ', array_slice($uids, 0, 5)) . (count($uids) > 5 ? '...' : ''));
                error_log("Successfully resolved " . count($userNames) . " user names");

                foreach ($contacts as &$contact) {
                    if (isset($contact['createdBy']) && isset($userNames[$contact['createdBy']])) {
                        $contact['createdBy'] = $userNames[$contact['createdBy']];
                    }
                }
            }
            
            // Resolve zone names for contacts
            if (!$skipZoneResolution) {
                $this->resolveZoneNames($contacts);
            }
            
            // Resolve trajectory names for contacts
            $this->resolveTrajectoryNames($contacts);
            
            // Cache the FULLY PROCESSED dataset for future requests
            // This includes resolved user names and zone names
            $this->setCacheData($cacheKey, ['contacts' => $contacts]);
            error_log("Cached " . count($contacts) . " fully processed contacts");
            
            // Apply filters to the contact data
            $filteredContacts = $this->filterContacts($contacts, $bounds, $dateRange, $circumstanceFilter, $limit);
            
            return ['contacts' => $filteredContacts, 'stats' => $this->calculateContactStats($filteredContacts)];
            
        } catch (Exception $e) {
            error_log("Error getting contacts: " . $e->getMessage());
            return [
                'contacts' => [], 
                'stats' => $this->calculateContactStats([]),
                'debug' => [
                    'error' => $e->getMessage(),
                    'cache_key' => $cacheKey ?? 'not_set',
                    'cache_file' => isset($cacheFile) ? $cacheFile : 'not_set',
                    'cache_exists' => isset($cacheFile) ? file_exists($cacheFile) : false
                ]
            ];
        }
    }

    private function filterContacts($contacts, $bounds = null, $dateRange = 'all', $circumstanceFilter = null, $limit = 10000)
    {
        $filtered = [];
        
        foreach ($contacts as $contact) {
            // Apply circumstance filter
            if ($circumstanceFilter && $circumstanceFilter !== 'all' && 
                (!isset($contact['circumstance']) || $contact['circumstance'] !== $circumstanceFilter)) {
                continue;
            }
            
            // Apply date filter
            if (!$this->matchesDateRange($contact['created_at'] ?? null, $dateRange)) {
                continue;
            }
            
            // Apply bounds filter
            if ($bounds && !$this->contactInBounds($contact['coordinates'] ?? null, $bounds)) {
                continue;
            }
            
            $filtered[] = $contact;
            
            // Apply limit
            if ($limit && count($filtered) >= $limit) {
                break;
            }
        }
        
        return $filtered;
    }

    private function contactInBounds($coordinates, $bounds) {
        if (!$bounds || !$coordinates || !isset($coordinates['lat']) || !isset($coordinates['lng'])) {
            return true;
        }
        
        // bounds format: ['ne' => ['lat' => X, 'lng' => Y], 'sw' => ['lat' => X, 'lng' => Y]]
        $north = $bounds['ne']['lat'];
        $south = $bounds['sw']['lat'];
        $east = $bounds['ne']['lng'];
        $west = $bounds['sw']['lng'];
        
        $lat = (float)$coordinates['lat'];
        $lng = (float)$coordinates['lng'];
        
        return $lat >= $south && $lat <= $north && $lng >= $west && $lng <= $east;
    }

    private function calculateContactStats($contacts) {
        $stats = [
            'total_contacts' => count($contacts),
            'by_circumstance' => [],
            'by_source' => ['contacts' => 0, 'contactEvents' => 0, 'gestorMobile_contacts' => 0]
        ];
        
        foreach ($contacts as $contact) {
            // Count by circumstance
            $circumstance = $contact['circumstance'] ?? 'Não especificado';
            if (!isset($stats['by_circumstance'][$circumstance])) {
                $stats['by_circumstance'][$circumstance] = 0;
            }
            $stats['by_circumstance'][$circumstance]++;
            
            // Count by source
            $source = $contact['source'] ?? 'contacts';
            if (isset($stats['by_source'][$source])) {
                $stats['by_source'][$source]++;
            }
        }
        
        return $stats;
    }

    public function getTotalContactsCount() {
        try {
            $collections = ['contacts', 'contactEvents', 'gestorMobile_contacts'];
            $totalCount = 0;
            $testEmail = '<EMAIL>';
            
            foreach ($collections as $collection) {
                $count = 0;
                $pageToken = null;
                
                do {
                    $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
                    if ($pageToken) {
                        $url .= "?pageToken=" . urlencode($pageToken);
                    }
                    
                    $ch = curl_init($url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Authorization: Bearer ' . $this->getAdminAccessToken()
                    ]);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                    
                    $response = curl_exec($ch);
                    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($status !== 200) {
                        break;
                    }
                    
                    $result = json_decode($response, true);
                    if (isset($result['documents'])) {
                        // Filter out test contacts
                        foreach ($result['documents'] as $doc) {
                            $parsed = $this->parseDocument($doc);
                            $isTestContact = false;
                            $possibleCreatorFields = ['createdBy', 'createdByEmail', 'userId', 'userEmail', 'email', 'creator', 'author'];
                            
                            foreach ($possibleCreatorFields as $field) {
                                if (isset($parsed[$field]) && $parsed[$field] === $testEmail) {
                                    $isTestContact = true;
                                    break;
                                }
                            }
                            
                            if (!$isTestContact) {
                                $count++;
                            }
                        }
                    }
                    
                    $pageToken = $result['nextPageToken'] ?? null;
                    
                } while ($pageToken);
                
                $totalCount += $count;
            }
            
            return $totalCount;
            
        } catch (Exception $e) {
            error_log("Error getting total contacts count: " . $e->getMessage());
            return 0;
        }
    }
    
    private function calculateStats($trajectories) {
        $gpsCount = 0;
        $manualCount = 0;
        
        foreach ($trajectories as $trajectory) {
            if ($trajectory['type'] === 'GPS') {
                $gpsCount++;
            } else {
                $manualCount++;
            }
        }
        
        return [
            'gps_trajectories' => $gpsCount,
            'manual_trajectories' => $manualCount,
            'total_trajectories' => count($trajectories),
            'viewport_trajectories' => count($trajectories)
        ];
    }
}

// Main API logic
if (isset($_GET['action']) && $_GET['action'] === 'get_cache_info') {
    header('Content-Type: application/json');
    try {
        $firebase = new TrajectoryFirebase(getEnvVar('FIREBASE_PROJECT_ID'));
        $cacheInfo = $firebase->getCacheInfo();
        
        echo json_encode(['success' => true, 'cache' => $cacheInfo]);
    } catch (Exception $e) {
        header("HTTP/1.1 500 Internal Server Error");
        error_log("Cache Info API Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'An internal server error occurred.',
            'details' => $e->getMessage()
        ]);
    }
    exit;
}

if (isset($_GET['action']) && $_GET['action'] === 'get_trajectories') {
    header('Content-Type: application/json');
    try {
        $firebase = new TrajectoryFirebase(getEnvVar('FIREBASE_PROJECT_ID'));

        $boundsStr = $_GET['bounds'] ?? null;
        $bounds = null;
        if ($boundsStr) {
            list($neLat, $swLat, $neLng, $swLng) = explode(',', $boundsStr);
            $bounds = [
                'ne' => ['lat' => (float)$neLat, 'lng' => (float)$neLng],
                'sw' => ['lat' => (float)$swLat, 'lng' => (float)$swLng]
            ];
        }

        $dateRange = $_GET['dateRange'] ?? 'all';
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 500;
        $types = isset($_GET['types']) && !empty($_GET['types']) ? explode(',', rawurldecode($_GET['types'])) : null;

        $trajectoriesData = $firebase->getTrajectories($bounds, $dateRange, $types, $limit);

        echo json_encode(['success' => true, 'trajectories' => $trajectoriesData['trajectories'], 'stats' => $trajectoriesData['stats']]);
    } catch (Exception $e) {
        header("HTTP/1.1 500 Internal Server Error");
        error_log("API Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'An internal server error occurred.',
            'details' => $e->getMessage()
        ]);
    }
    exit;
}

// NEW: Contacts API endpoint - extends existing system
if (isset($_GET['action']) && $_GET['action'] === 'get_contacts') {
    header('Content-Type: application/json');
    try {
        $firebase = new TrajectoryFirebase(getEnvVar('FIREBASE_PROJECT_ID'));

        $boundsStr = $_GET['bounds'] ?? null;
        $bounds = null;
        if ($boundsStr) {
            list($neLat, $swLat, $neLng, $swLng) = explode(',', $boundsStr);
            $bounds = [
                'ne' => ['lat' => (float)$neLat, 'lng' => (float)$neLng],
                'sw' => ['lat' => (float)$swLat, 'lng' => (float)$swLng]
            ];
        }

        $dateRange = $_GET['dateRange'] ?? 'all';
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10000;
        $circumstanceFilter = $_GET['circumstance'] ?? null;

        $contactsData = $firebase->getContacts($bounds, $dateRange, $circumstanceFilter, $limit, $clearCache, $skipZoneResolution);
        
        // Get total count for accurate database statistics
        $totalContactsCount = $firebase->getTotalContactsCount();
        $contactsData['stats']['database_total_contacts'] = $totalContactsCount;

        echo json_encode(['success' => true, 'contacts' => $contactsData['contacts'], 'stats' => $contactsData['stats']]);
    } catch (Exception $e) {
        header("HTTP/1.1 500 Internal Server Error");
        error_log("Contacts API Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'An internal server error occurred.',
            'details' => $e->getMessage()
        ]);
    }
    exit;
}

// NEW: Get total contacts count API endpoint
if (isset($_GET['action']) && $_GET['action'] === 'get_total_contacts_count') {
    header('Content-Type: application/json');
    try {
        $firebase = new TrajectoryFirebase(getEnvVar('FIREBASE_PROJECT_ID'));
        $totalCount = $firebase->getTotalContactsCount();
        
        echo json_encode(['success' => true, 'total_contacts' => $totalCount]);
    } catch (Exception $e) {
        header("HTTP/1.1 500 Internal Server Error");
        error_log("Total Contacts Count API Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'An internal server error occurred.',
            'details' => $e->getMessage()
        ]);
    }
    exit;
}

// Check for cache refresh request
if (isset($_GET['refresh']) && $_GET['refresh'] === 'true') {
    $cacheDir = __DIR__ . '/cache';
    $files = glob($cacheDir . '/*');
    foreach($files as $file){
        if(is_file($file)) {
            unlink($file);
        }
    }
    // Redirect to the same page without the refresh parameter
    header('Location: mapa_trajetos.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa de Trajetos - ProROLA</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #0a7ea4;
            --primary-dark: #086b8a;
            --success-color: #28a745;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden; /* Prevent body scrollbars */
        }
        
        .header {
            flex-shrink: 0; /* Prevent header from shrinking */
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 100%;
        }
        
        .header-left {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .title-section {
            flex-shrink: 0;
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .header .subtitle {
            margin: 0;
            opacity: 0.9;
            font-size: 0.75rem;
        }
        
        .header-right {
            flex-shrink: 0;
            margin-left: 2rem;
            display: flex;
            align-items: center;
        }
        
        .header-cache-info {
            margin-right: 20px;
            padding: 5px 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            font-size: 0.75rem;
        }

        .header-cache-status {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .cache-info-row {
            display: flex;
            align-items: center;
            gap: 5px;
            color: rgba(255, 255, 255, 0.9);
            justify-content: flex-start;
        }

        .cache-info-row i {
            width: 12px;
            font-size: 10px;
        }
        
        .search-container {
            position: relative;
            min-width: 300px;
        }
        
        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .search-input {
            width: 100%;
            padding: 0.5rem 0.75rem 0.5rem 2.5rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .search-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
            z-index: 2;
        }
        
        .clear-search {
            position: absolute;
            right: 0.5rem;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: all 0.2s ease;
            z-index: 2;
        }
        
        .clear-search:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            margin-top: 0.5rem;
        }
        
        .search-result-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .search-result-item:hover {
            background-color: rgba(10, 126, 164, 0.1);
        }
        
        .search-result-name {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }
        
        .search-result-type {
            font-size: 0.8rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .search-no-results {
            padding: 1rem;
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .map-container {
            flex-grow: 1; /* Allow map to fill remaining space */
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .unified-control-panel {
            position: absolute;
            bottom: 35px;
            left: 15px;
            z-index: 10;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            min-width: 220px;
            max-width: 250px;
            font-size: 0.85rem;
        }
        
        .panel-section {
            margin-bottom: 12px;
        }
        
        .panel-section:last-child {
            margin-bottom: 0;
        }
        
        .panel-section-title {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.8rem;
            border-bottom: 1px solid rgba(10, 126, 164, 0.2);
            padding-bottom: 4px;
        }
        
        .panel-section-title i {
            margin-right: 6px;
            font-size: 0.75rem;
        }
        
        .loading-overlay {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(10, 126, 164, 0.95);
            color: white;
            padding: 20px 30px;
            border-radius: 12px;
            font-size: 1rem;
            z-index: 2000;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: none;
            text-align: center;
            min-width: 200px;
        }
        
        .loading-spinner {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-direction: column;
        }
        
        .loading-spinner .spinner-border {
            width: 2rem;
            height: 2rem;
            border-width: 3px;
            color: white;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-overlay {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
            to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 0.95rem;
        }
        
        .stat-item.compact {
            margin-bottom: 4px;
            font-size: 0.8rem;
        }
        
        .stat-value {
            font-weight: bold;
            color: var(--primary-color);
            font-size: 1.1rem;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-weight: 500;
            font-size: 0.8rem;
        }
        
        .legend-item:last-child {
            margin-bottom: 0;
        }

        .legend-line {
            width: 20px;
            height: 3px;
            margin-right: 8px;
            border-radius: 2px;
        }

        .legend-gps {
            background-color: var(--success-color);
        }

        .legend-manual {
            background-color: var(--primary-color);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(10, 126, 164, 0.3);
        }
        
        .stats-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1rem;
        }
        
        .stats-header i {
            margin-right: 8px;
        }
        
        .stats-header.compact {
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .controls-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .controls-header i {
            margin-right: 8px;
        }
        
        .controls-header.compact {
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        /* Trajectory label styling */
        .trajectory-label {
            background-color: rgba(255, 255, 255, 0.9);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            white-space: nowrap;
        }

        .legend-item-filter {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            padding: 4px 8px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .legend-item-filter:hover {
            background-color: rgba(0,0,0,0.05);
        }

        .legend-item-filter .form-check-input {
            margin-right: 10px;
            cursor: pointer;
        }

        .legend-item-filter .form-check-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.8rem;
        }

        /* Trajectory marker icons for stats panel */
        .trajectory-marker-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            color: white;
            font-weight: bold;
            font-size: 0.75rem;
            text-align: center;
            line-height: 1;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .trajectory-marker-icon.gps-marker {
            background-color: var(--success-color);
        }

        .trajectory-marker-icon.manual-marker {
            background-color: var(--primary-color);
        }

        /* Cache status styling */
        .cache-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0;
            margin-top: 0.5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .search-loading {
            padding: 1rem;
            text-align: center;
            color: #666;
        }

        .search-loading .loading-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        /* Test Trajectory Styling */
        .test-trajectory-warning {
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            animation: testBlink 2s infinite;
        }
        
        @keyframes testBlink {
            0%, 50% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }
        
        .test-trajectory-label {
            background-color: rgba(255, 107, 107, 0.95) !important;
            border: 2px dashed #ff4757 !important;
            color: white !important;
            font-weight: bold;
        }
        
        .test-trajectory-label::before {
                                            content: "[TESTE] ";
            font-size: 0.8em;
        }

        /* Custom Dropdown Styling */
        .custom-select-container {
            position: relative;
        }

        .custom-select-trigger {
            position: relative;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: .25rem;
            padding: .25rem .5rem;
            font-size: .875rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        }

        .custom-select-trigger:hover, .custom-select-container.open .custom-select-trigger {
            border-color: var(--primary-color);
        }

        .custom-select-trigger .arrow-icon {
            margin-left: 8px;
            transition: transform .2s ease;
        }

        .custom-select-container.open .custom-select-trigger .arrow-icon {
            transform: rotate(180deg);
        }
        
        .custom-options {
            position: absolute;
            bottom: 100%; /* Position above the trigger */
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-height: 250px;
            overflow-y: auto;
            z-index: 20;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: opacity .2s ease, transform .2s ease, visibility .2s;
            margin-bottom: 5px; /* Space between trigger and options */
        }

        .custom-select-container.open .custom-options {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .custom-option {
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color .2s ease, color .2s ease;
        }

        .custom-option:hover {
            background-color: rgba(10, 126, 164, 0.1);
        }

        .custom-option.selected {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="title-section">
                    <h1><i class="fas fa-route"></i> Mapa de Trajetos ProROLA</h1>
                    <p class="subtitle" style="padding-left:27px;">Trajetos registados nas zonas de caça</p>
                </div>
                <div class="header-cache-info">
                    <div id="headerCacheStatus" class="header-cache-status">
                        <div class="cache-info-row">
                            <i class="fas fa-clock"></i>
                            <span id="lastUpdateText">Carregando...</span>
                        </div>
                        <div class="cache-info-row">
                            <i class="fas fa-sync-alt"></i>
                            <span>Próximo: <span id="nextUpdateText">Próxima atualização em...</span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="zoneSearch" class="search-input" placeholder="Pesquisar trajeto...">
                        <button id="clearSearch" class="clear-search" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="searchResults" class="search-results"></div>
                </div>
                <button id="refreshCacheBtn" class="btn btn-sm btn-outline-light ms-3" title="Forçar Atualização do Cache">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </div>
    
    <div class="map-container">
        <div id="map"></div>
            
            <!-- Unified Control Panel -->
            <div class="unified-control-panel">
                <!-- Controls Section -->
                <div class="panel-section">
                    <div class="panel-section-title">
                        <i class="fas fa-sliders-h"></i>
                        <span>Controlos</span>
                    </div>
                    <div class="mb-0">
                        <label for="dateRange" class="form-label">Período:</label>
                        <select id="dateRange" class="form-select form-select-sm">
                            <option value="all" <?php echo $dateRange === 'all' ? 'selected' : ''; ?>>Todos</option>
                            <option value="today" <?php echo $dateRange === 'today' ? 'selected' : ''; ?>>Hoje</option>
                            <option value="week" <?php echo $dateRange === 'week' ? 'selected' : ''; ?>>Esta Semana</option>
                            <option value="month" <?php echo $dateRange === 'month' ? 'selected' : ''; ?>>Este Mês</option>
                        </select>
                    </div>
                </div>
                
                <!-- Legend & Filter Section -->
                <div class="panel-section">
                    <div class="panel-section-title">
                        <i class="fas fa-filter"></i>
                        <span>Filtros</span>
                    </div>
                    <div class="legend-item-filter">
                        <input class="form-check-input" type="checkbox" value="GPS" id="filter-gps" checked>
                        <label class="form-check-label" for="filter-gps">
                            <div class="legend-line legend-gps"></div>
                            <span>Trajeto GPS</span>
                        </label>
                    </div>
                    <div class="legend-item-filter">
                        <input class="form-check-input" type="checkbox" value="Manual" id="filter-manual" checked>
                        <label class="form-check-label" for="filter-manual">
                             <div class="legend-line legend-manual"></div>
                            <span>Trajeto Manual</span>
                        </label>
                    </div>
                    
                    <!-- NEW: Contacts Toggle -->
                    <div class="legend-item-filter">
                        <input class="form-check-input" type="checkbox" value="contacts" id="filter-contacts">
                        <label class="form-check-label" for="filter-contacts">
                            <img id="contacts-filter-icon" src="" alt="Ícone de Contactos" style="width: 20px; height: 20px; margin-right: 8px;" />
                            <span>Contactos</span>
                        </label>
                    </div>
                </div>
                
                <!-- NEW: Contacts Filter Section (hidden by default) -->
                <div class="panel-section" id="contacts-filter-section" style="display: none;">
                    <div class="mb-0">
                        <label for="circumstanceFilter" class="form-label">Circunstância:</label>
                        <div class="custom-select-container" id="customCircumstanceFilter">
                            <div class="custom-select-trigger">
                                <span>Todas</span>
                                <i class="fas fa-chevron-down arrow-icon"></i>
                            </div>
                            <div class="custom-options">
                                <!-- Options will be populated by JavaScript -->
                            </div>
                        </div>
                        <select id="circumstanceFilter" class="form-select form-select-sm" style="display: none;">
                            <option value="all">Todas</option>
                        </select>
                    </div>
                </div>
                
                <!-- Database Stats Section -->
                <div class="panel-section">
                    <div class="panel-section-title">
                        <i class="fas fa-database"></i>
                        <span>Base de Dados</span>
                    </div>
                    <div class="stat-item compact">
                        <span><span class="trajectory-marker-icon gps-marker me-1">T</span> GPS:</span>
                        <span class="stat-value" id="stat-gps-trajectories">0</span>
                    </div>
                    <div class="stat-item compact">
                        <span><span class="trajectory-marker-icon manual-marker me-1">T</span> Manuais:</span>
                        <span class="stat-value" id="stat-manual-trajectories">0</span>
                    </div>
                    <div class="stat-item compact">
                        <span><i class="fas fa-route"></i> Total:</span>
                        <span class="stat-value" id="stat-total-trajectories">0</span>
                    </div>
                    
                    <!-- NEW: Contacts Stats (always visible for database statistics) -->
                    <div id="contacts-stats">
                        <div class="stat-item compact" style="border-top: 1px solid rgba(0,0,0,0.1); padding-top: 8px; margin-top: 8px;">
                            <span><i class="fas fa-map-pin" style="color: #dc3545;"></i> Contactos:</span>
                            <span class="stat-value" id="stat-total-contacts">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- Visible Stats Section -->
                <div class="panel-section">
                    <div class="panel-section-title">
                        <i class="fas fa-eye"></i>
                        <span>Visíveis no Mapa</span>
                    </div>
                    <div class="stat-item compact">
                        <span><i class="fas fa-map-marker-alt"></i> Trajetos:</span>
                        <span class="stat-value" id="stat-visible">0</span>
                    </div>
                    
                    <!-- NEW: Visible Contacts Stats (hidden by default) -->
                    <div id="visible-contacts-stats" style="display: none;">
                        <div class="stat-item compact">
                            <span><i class="fas fa-map-pin" style="color: #dc3545;"></i> Contactos:</span>
                            <span class="stat-value" id="stat-visible-contacts">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div id="loadingOverlay" class="loading-overlay">
                <div class="loading-spinner">
                    <div class="spinner-border" role="status"></div>
                    <span id="loading-status">Carregando...</span>
                </div>
            </div>
        </div>
    
    <script>
        // Global variables
        let map;
        let currentPolylines = [];
        let currentMarkers = [];
        let currentLabels = [];
        let currentBounds = null;
        let currentZoom = 7;
        let loadingTimeout;
        let dataCache = new Map();
        let totalDatabaseStats = null; // Store total database stats
        let contactMarkerIconUrl = null; // To store the generated icon URL
        // Removed contactMarkerClusterer - using simple density-based optimization instead

        // Global cache system
        let globalCache = {
            data: null,
            lastUpdated: null,
            isLoading: false
        };
        
        // NEW: Translations for contact details
        const translations = {
            circumstance: {
                'adultSinging': 'Adulto a cantar',
                'adultPerched': 'Adulto pousado',
                'Voo': 'Voo',
                'Pousado': 'Pousado',
                'Morto': 'Morto',
                'Ferido': 'Ferido',
                'Ninho': 'Ninho',
                'emptyNest': 'Ninho vazio',
                'adultDisplay': 'Adulto em exibição',
                'flying': 'A voar',
                'occupiedNest': 'Ninho ocupado'
            },
            location: {
                'tree': 'Árvore',
                'waterPoint': 'Ponto de água',
                'shrub': 'Arbusto',
                'clearing': 'Clareira'
            }
        };

        function translate(value, type) {
            if (translations[type] && translations[type][value]) {
                return translations[type][value];
            }
            return value; // Return original value if no translation found
        }

        // Format time for display - handles both simple time format and ISO timestamps
        function formatTime(timeValue) {
            if (!timeValue) return '';
            
            // If it's already in simple format (like "08:35"), return as is
            if (typeof timeValue === 'string' && timeValue.match(/^\d{2}:\d{2}$/)) {
                return timeValue;
            }
            
            // If it's an ISO timestamp, extract just the time portion
            if (typeof timeValue === 'string' && timeValue.includes('T')) {
                try {
                    const date = new Date(timeValue);
                    return date.toLocaleTimeString('pt-PT', { 
                        hour: '2-digit', 
                        minute: '2-digit',
                        hour12: false 
                    });
                } catch (error) {
                    console.warn('Error parsing time:', timeValue, error);
                    return timeValue; // Return original if parsing fails
                }
            }
            
            // Return original value if we can't format it
            return timeValue;
        }

        // NEW: Contacts functionality
        let currentContactMarkers = [];
        let currentClusterMarkers = []; // NEW: Track cluster markers separately
        let contactsCache = {
            data: null,
            lastUpdated: null,
            isLoading: false,
            loadingPromise: null
        };
        let contactsEnabled = false;

        function updateCircumstanceFilter(contacts) {
            const container = document.getElementById('customCircumstanceFilter');
            const trigger = container.querySelector('.custom-select-trigger span');
            const optionsContainer = container.querySelector('.custom-options');
            const nativeSelect = document.getElementById('circumstanceFilter');

            const selectedValue = nativeSelect.value;
            
            const circumstances = new Set(contacts.map(c => c.circumstance).filter(Boolean));
            
            // Clear existing options
            optionsContainer.innerHTML = '';
            nativeSelect.innerHTML = '';

            // Add "All" option
            const allOption = document.createElement('div');
            allOption.className = 'custom-option';
            allOption.dataset.value = 'all';
            allOption.textContent = 'Todas';
            optionsContainer.appendChild(allOption);
            nativeSelect.add(new Option('Todas', 'all'));

            circumstances.forEach(circ => {
                const option = document.createElement('div');
                option.className = 'custom-option';
                option.dataset.value = circ;
                option.textContent = translate(circ, 'circumstance');
                optionsContainer.appendChild(option);
                nativeSelect.add(new Option(translate(circ, 'circumstance'), circ));
            });

            // Restore selection or default to "All"
            nativeSelect.value = selectedValue;
            const currentOption = Array.from(optionsContainer.children).find(opt => opt.dataset.value === nativeSelect.value);
            if (currentOption) {
                trigger.textContent = currentOption.textContent;
                Array.from(optionsContainer.children).forEach(opt => opt.classList.remove('selected'));
                currentOption.classList.add('selected');
            }
        }

        function setupCustomSelect() {
            const container = document.getElementById('customCircumstanceFilter');
            if (!container) return;

            const trigger = container.querySelector('.custom-select-trigger');
            const optionsContainer = container.querySelector('.custom-options');
            const nativeSelect = document.getElementById('circumstanceFilter');

            trigger.addEventListener('click', () => {
                container.classList.toggle('open');
            });

            optionsContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('custom-option')) {
                    const selectedValue = e.target.dataset.value;
                    const selectedText = e.target.textContent;

                    // Update UI and native select
                    trigger.querySelector('span').textContent = selectedText;
                    nativeSelect.value = selectedValue;
                    
                    // Trigger change event for map filtering
                    nativeSelect.dispatchEvent(new Event('change'));
                    
                    // Update selected class
                    Array.from(optionsContainer.children).forEach(opt => opt.classList.remove('selected'));
                    e.target.classList.add('selected');
                    
                    container.classList.remove('open');
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!container.contains(e.target)) {
                    container.classList.remove('open');
                }
            });
        }

        /**
         * Creates a custom marker icon using Canvas API for reliability.
         * Draws a circle with a border and an image inside.
         * @param {function(string)} callback - The callback function to receive the data URL.
         */
        function createContactIcon(callback) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const size = 32; // Marker size
            canvas.width = size;
            canvas.height = size;

            const doveIcon = new Image();
            // Path must be relative to the HTML file (mapa_trajetos.php)
            doveIcon.src = 'icons/dove-icon.png';
            doveIcon.crossOrigin = 'Anonymous'; // Handle potential CORS issues

            doveIcon.onload = () => {
                // Draw green circle with white border
                ctx.beginPath();
                ctx.arc(size / 2, size / 2, (size / 2) - 1, 0, 2 * Math.PI, false);
                ctx.fillStyle = '#28a745'; // Green fill
                ctx.fill();
                ctx.lineWidth = 2; // White border width
                ctx.strokeStyle = '#ffffff';
                ctx.stroke();

                // Draw the dove icon in the center
                const iconSize = 18; // Size of the dove icon
                ctx.drawImage(doveIcon, (size - iconSize) / 2, (size - iconSize) / 2, iconSize, iconSize);

                // Get the final image as a Data URL and pass to callback
                callback(canvas.toDataURL('image/png'));
            };

            doveIcon.onerror = () => {
                console.error("Failed to load the dove icon image from 'icons/dove-icon.png'. Please check the path and file permissions.");
                // Fallback to a simple green circle if the image fails to load
                ctx.beginPath();
                ctx.arc(size / 2, size / 2, (size / 2) - 1, 0, 2 * Math.PI, false);
                ctx.fillStyle = '#28a745';
                ctx.fill();
                ctx.lineWidth = 2;
                ctx.strokeStyle = '#ffffff';
                ctx.stroke();
                callback(canvas.toDataURL('image/png'));
            };
        }

        // Initialize map
        function initMap() {
            // First, create the custom icon. The rest of the initialization will proceed
            // in the callback, ensuring the icon is ready before we try to use it.
            createContactIcon(generatedIconUrl => {
                contactMarkerIconUrl = generatedIconUrl; // Store the generated icon URL globally

                // NEW: Set the icon in the filter panel
                const contactsFilterIcon = document.getElementById('contacts-filter-icon');
                if (contactsFilterIcon) {
                    contactsFilterIcon.src = contactMarkerIconUrl;
                }

                // Now that the icon is ready, proceed with map initialization
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 7,
                    center: { lat: 39.5, lng: -8.0 }, // Center of Portugal
                    mapTypeId: 'terrain',
                    gestureHandling: 'greedy',
                    zoomControl: true,
                    mapTypeControl: true,
                    scaleControl: true,
                    streetViewControl: false,
                    rotateControl: false,
                    fullscreenControl: true,
                    disableDefaultUI: false,
                    clickableIcons: false,
                    styles: [
                        { featureType: 'water', elementType: 'geometry', stylers: [{ color: '#a2daf2' }] },
                        { featureType: 'landscape.natural', elementType: 'geometry', stylers: [{ color: '#f5f5f2' }] },
                        { featureType: 'poi', elementType: 'all', stylers: [{ visibility: 'off' }] },
                        // ... other styles
                    ]
                });
                
                // Map event listeners
                map.addListener('bounds_changed', debounce(onBoundsChanged, 500));
                map.addListener('zoom_changed', onZoomChanged);
                
                // Initial data load
                loadMapData();
                // NEW: Setup custom dropdown logic
                setupCustomSelect();
            });
        }
        
        function getVisibleTrajectoryTypes() {
            const types = [];
            if (document.getElementById('filter-gps').checked) types.push('GPS');
            if (document.getElementById('filter-manual').checked) types.push('Manual');
            return types;
        }
        
        // Load map data based on current viewport
        function loadMapData() {
            const overallStartTime = performance.now();
            console.log('🚀 loadMapData() started');
            
            const bounds = map.getBounds();
            if (!bounds) {
                console.log('⏳ Map bounds not ready, retrying in 100ms');
                setTimeout(loadMapData, 100);
                return;
            }
            
            currentBounds = bounds;
            currentZoom = map.getZoom();
            console.log('📍 Map bounds and zoom level set:', { zoom: currentZoom });
            
            // Load trajectories (always)
            const trajectoryStartTime = performance.now();
            const trajectoryPromise = loadAllTrajectories()
                .then(allTrajectories => {
                    const trajectoryLoadTime = performance.now() - trajectoryStartTime;
                    console.log(`📍 loadAllTrajectories() completed in ${trajectoryLoadTime.toFixed(2)}ms`);
                    
                    if (!allTrajectories || allTrajectories.length === 0) {
                        console.warn('⚠️ No trajectories loaded from cache');
                        return { trajectories: [], stats: { gps_trajectories: 0, manual_trajectories: 0, total_trajectories: 0 } };
                    }
                    
                    // Filter trajectories based on current viewport and filters
                    const filterStartTime = performance.now();
                    const filteredTrajectories = filterTrajectories(allTrajectories, bounds);
                    const filterTime = performance.now() - filterStartTime;
                    console.log(`🔍 Trajectory filtering took ${filterTime.toFixed(2)}ms (${allTrajectories.length} → ${filteredTrajectories.length})`);
                    
                    return {
                        trajectories: filteredTrajectories,
                        stats: calculateStatsFromTrajectories(allTrajectories)
                    };
                })
                .catch(error => {
                    const errorTime = performance.now() - trajectoryStartTime;
                    console.error(`❌ Error loading trajectory data after ${errorTime.toFixed(2)}ms:`, error);
                    return { trajectories: [], stats: { gps_trajectories: 0, manual_trajectories: 0, total_trajectories: 0 } };
                });
            
            // Show appropriate loading message based on what's being loaded
            const visibleTrajectoryTypes = getVisibleTrajectoryTypes();
            const trajectoryFiltersEnabled = visibleTrajectoryTypes.length > 0;
            
            if (contactsEnabled && trajectoryFiltersEnabled) {
                showLoading('Carregando trajetos e contactos...');
            } else if (contactsEnabled && !trajectoryFiltersEnabled) {
                showLoading('Carregando contactos...');
            } else if (!contactsEnabled && trajectoryFiltersEnabled) {
                showLoading('Carregando trajetos...');
            } else {
                showLoading('Carregando dados...');
            }
            
            // Load contacts if enabled (with level-of-detail based on zoom)
            const contactsStartTime = performance.now();
            const contactsPromise = contactsEnabled ? 
                loadAllContacts()
                    .then(allContacts => {
                        const contactsLoadTime = performance.now() - contactsStartTime;
                        console.log(`📍 loadAllContacts() completed in ${contactsLoadTime.toFixed(2)}ms`);
                        
                        if (!allContacts || allContacts.length === 0) {
                            console.warn('⚠️ No contacts loaded from cache');
                            return { contacts: [], contactStats: { total_contacts: 0, by_circumstance: {} } };
                        }
                        
                        // NEW: Dynamically update the circumstance filter
                        const updateFilterStartTime = performance.now();
                        updateCircumstanceFilter(allContacts);
                        const updateFilterTime = performance.now() - updateFilterStartTime;
                        console.log(`🔄 updateCircumstanceFilter() took ${updateFilterTime.toFixed(2)}ms`);

                        // Filter contacts based on current viewport and filters
                        const contactFilterStartTime = performance.now();
                        let filteredContacts = filterContacts(allContacts, bounds);
                        const contactFilterTime = performance.now() - contactFilterStartTime;
                        console.log(`🔍 Contact filtering took ${contactFilterTime.toFixed(2)}ms (${allContacts.length} → ${filteredContacts.length})`);
                        
                        // Debug: Log contact coordinates to understand geographic distribution
                        if (filteredContacts.length > 0) {
                            const sampleContacts = filteredContacts.slice(0, 5);
                            console.log('📍 Sample contact coordinates:', sampleContacts.map(c => ({
                                lat: c.coordinates?.lat,
                                lng: c.coordinates?.lng,
                                source: c.source
                            })));
                        }
                        
                        // No artificial limits - clustering handles performance
                        // Show all contacts that are actually in the viewport and match filters
                        console.log(`✅ Showing ${filteredContacts.length} contacts in viewport (clustering will optimize display)`);
                        
                        // Note: Performance is handled by the clustering algorithm in optimizeContactMarkers()
                        // which groups nearby contacts into clusters based on zoom level
                        
                        return {
                            contacts: filteredContacts,
                            contactStats: { total_contacts: allContacts.length, by_circumstance: {} }
                        };
                    })
                    .catch(error => {
                        const errorTime = performance.now() - contactsStartTime;
                        console.error(`❌ Error loading contact data after ${errorTime.toFixed(2)}ms:`, error);
                        return { contacts: [], contactStats: { total_contacts: 0, by_circumstance: {} } };
                    }) :
                Promise.resolve({ contacts: [], contactStats: { total_contacts: 0, by_circumstance: {} } });
            
            // Wait for both promises to complete
            const promiseAllStartTime = performance.now();
            Promise.all([trajectoryPromise, contactsPromise])
                .then(([trajectoryData, contactData]) => {
                    const promiseAllTime = performance.now() - promiseAllStartTime;
                    console.log(`🔄 Promise.all completed in ${promiseAllTime.toFixed(2)}ms`);
                    
                    // Create response structure compatible with existing code
                    const responseData = {
                        success: true,
                        trajectories: trajectoryData.trajectories,
                        contacts: contactData.contacts,
                        stats: trajectoryData.stats,
                        contactStats: contactData.contactStats
                    };
                    
                    const processStartTime = performance.now();
                    processMapData(responseData);
                    const processTime = performance.now() - processStartTime;
                    console.log(`🗺️ processMapData() took ${processTime.toFixed(2)}ms`);
                    
                    const overallTime = performance.now() - overallStartTime;
                    console.log(`✅ loadMapData() completed in ${overallTime.toFixed(2)}ms total`);
                    
                    hideLoading();
                })
                .catch(error => {
                    const errorTime = performance.now() - overallStartTime;
                    console.error(`❌ Error loading map data after ${errorTime.toFixed(2)}ms:`, error);
                    showLoading('Erro ao carregar dados: ' + error.message);
                    setTimeout(hideLoading, 3000);
                });
        }

        // Filter trajectories based on bounds, date range, and type filters
        function filterTrajectories(trajectories, bounds) {
            const dateRange = document.getElementById('dateRange').value;
            const visibleTypes = getVisibleTrajectoryTypes();
            
            // If no trajectory types are selected, return empty array
            if (visibleTypes.length === 0) {
                return [];
            }
            
            return trajectories.filter(trajectory => {
                // Type filter
                if (!visibleTypes.includes(trajectory.type)) {
                    return false;
                }
                
                // Date filter
                if (!matchesDateRange(trajectory.created_at, dateRange)) {
                    return false;
                }
                
                // Bounds filter
                if (!trajectoryInBounds(trajectory.coordinates, bounds)) {
                    return false;
                }
                
                return true;
            }).slice(0, 500); // Limit to 500 for performance
        }

        // Helper functions for filtering
        function matchesDateRange(dateString, dateRange) {
            if (dateRange === 'all' || !dateString) {
                return true;
            }
            
            try {
                const date = new Date(dateString);
                const now = new Date();
                
                switch (dateRange) {
                    case 'today':
                        return date.toDateString() === now.toDateString();
                    case 'week':
                        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        return date >= weekAgo;
                    case 'month':
                        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                        return date >= monthAgo;
                    default:
                        return true;
                }
            } catch (e) {
                return true;
            }
        }

        function trajectoryInBounds(coordinates, bounds) {
            if (!bounds || !coordinates || coordinates.length === 0) {
                return true;
            }
            
            const north = bounds.getNorthEast().lat();
            const south = bounds.getSouthWest().lat();
            const east = bounds.getNorthEast().lng();
            const west = bounds.getSouthWest().lng();
            
            // Check if any coordinate is within bounds
            return coordinates.some(coord => {
                return coord.lat >= south && coord.lat <= north &&
                       coord.lng >= west && coord.lng <= east;
            });
        }

        function calculateStatsFromTrajectories(trajectories) {
            const gpsCount = trajectories.filter(t => t.type === 'GPS').length;
            const manualCount = trajectories.filter(t => t.type === 'Manual').length;
            
            return {
                gps_trajectories: gpsCount,
                manual_trajectories: manualCount,
                total_trajectories: trajectories.length
            };
        }
        
        // Process and display map data
        function processMapData(data) {
            const processStartTime = performance.now();
            console.log('🗺️ processMapData() started');
            
            // Clear existing elements
            const clearStartTime = performance.now();
            clearMapElements();
            const clearTime = performance.now() - clearStartTime;
            console.log(`🧹 clearMapElements() took ${clearTime.toFixed(2)}ms`);
            
            const markers = [];
            const polylines = [];
            const labels = [];
            let actualVisibleContactCount = 0; // Initialize visible contact count
            
            // Add trajectories only
            data.trajectories.forEach(trajectory => {
                if (trajectory.coordinates && trajectory.coordinates.length > 0) {
                    const isTest = trajectory.isTest === true;
                    
                    // Skip test trajectories when zoomed out (only show when zoom >= 13)
                    if (isTest && currentZoom < 13) {
                        return;
                    }
                    // Create trajectory polyline
                    const path = trajectory.coordinates.map(coord => ({
                        lat: parseFloat(coord.lat),
                        lng: parseFloat(coord.lng)
                    }));
                    
                    const isGPS = trajectory.type === 'GPS';
                    
                    // Define colors and styles based on trajectory type and test status
                    let strokeColor, shadowColor, strokePattern;
                    if (isTest) {
                        strokeColor = '#ff6b6b'; // Red for test trajectories
                        shadowColor = '#ff4757'; // Darker red for shadow
                        strokePattern = [10, 5]; // Dashed pattern for test
                    } else {
                        strokeColor = isGPS ? '#28a745' : '#0a7ea4';
                        shadowColor = '#000000';
                        strokePattern = null; // Solid line for normal trajectories
                    }
                    
                    // Create shadow polyline (darker, slightly offset)
                    const shadowPolyline = new google.maps.Polyline({
                        path: path,
                        geodesic: true,
                        strokeColor: shadowColor,
                        strokeOpacity: isTest ? 0.5 : 0.3,
                        strokeWeight: isTest ? 8 : 6,
                        map: map,
                        zIndex: 1
                    });
                    
                    // Create main polyline (on top of shadow)
                    const polylineOptions = {
                        path: path,
                        geodesic: true,
                        strokeColor: strokeColor,
                        strokeOpacity: isTest ? 1.0 : 0.9,
                        strokeWeight: isTest ? 6 : 4,
                        map: map,
                        zIndex: 2
                    };
                    
                    // Add dashed pattern for test trajectories
                    if (isTest && strokePattern) {
                        polylineOptions.icons = [{
                            icon: {
                                path: 'M 0,-1 0,1',
                                strokeOpacity: 1,
                                strokeWeight: 6,
                                strokeColor: strokeColor,
                                scale: 1
                            },
                            offset: '0',
                            repeat: '15px'
                        }];
                        polylineOptions.strokeOpacity = 0; // Hide the base line for dashed effect
                    }
                    
                    const polyline = new google.maps.Polyline(polylineOptions);
                    
                    polylines.push(shadowPolyline);
                    polylines.push(polyline);
                    
                    // Use stored distance if available, otherwise calculate
                    let distanceKm;
                    if (trajectory.distance && typeof trajectory.distance === 'number') {
                        // Use stored distance (already in km from preprocessing)
                        distanceKm = trajectory.distance;
                    } else {
                        // Fallback: calculate from path coordinates
                        distanceKm = calculateTrajectoryDistance(path);
                    }
                    
                    
                    // Debug info window content
                    if (distanceKm === 0) {
                        console.warn('Distance calculation returned 0 for trajectory:', trajectory.name);
                    }
                    
                    // Add info window for trajectory details
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 12px; min-width: 250px;">
                                ` + (isTest ? `
                                    <div style="background: linear-gradient(45deg, #ff6b6b, #ffa500); color: white; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center; font-weight: bold; font-size: 0.9rem; text-transform: uppercase; letter-spacing: 1px;">
                                        [!] TRAJETO DE TESTE [!]
                                    </div>
                                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 8px; border-radius: 4px; margin-bottom: 12px; font-size: 0.85rem; color: #856404;">
                                        <strong>Atenção:</strong> Este trajeto foi criado pela equipa de desenvolvimento para fins de teste e depuração.
                                    </div>
                                ` : '') + `
                                <h6 style="margin-bottom: 12px; color: ` + strokeColor + `; font-size: 1.1rem;">
                                    <i class="fas fa-route"></i> ` + (trajectory.name || 'Trajeto') + 
                                    (isTest ? ' <span style="font-size: 0.7rem; background: #ff6b6b; color: white; padding: 2px 6px; border-radius: 3px;">TESTE</span>' : '') + `
                                </h6>
                                <div style="margin-bottom: 8px;">
                                    <strong>Tipo:</strong> 
                                    <span style="color: ` + strokeColor + `;">
                                        <i class="fas fa-` + (isGPS ? 'satellite' : 'hand-pointer') + `"></i>
                                        ` + (trajectory.type || 'Manual') + (isTest ? ' (Teste)' : '') + `
                                    </span>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Distância:</strong> <span style="color: ` + strokeColor + `; font-weight: 600;">` + distanceKm + ` km</span>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Pontos:</strong> ` + path.length + `
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Origem:</strong> ` + (trajectory.source === 'mobile' ? 'Aplicação Mobile' : 'Zona de Caça') + `
                                </div>
                                ` + (trajectory.created_at ? '<div style="margin-bottom: 0; font-size: 0.85em; color: #666;"><strong>Criado:</strong> ' + new Date(trajectory.created_at).toLocaleDateString('pt-PT') + '</div>' : '') + `
                            </div>
                        `
                    });
                    
                    // Add click listener to polyline
                    polyline.addListener('click', function(event) {
                        infoWindow.setPosition(event.latLng);
                        infoWindow.open(map);
                    });
                    
                    // Add trajectory name label when zoomed in close enough
                    if (currentZoom >= 13 && trajectory.name) {
                        const labelPosition = path[Math.floor(path.length / 2)]; // Middle of trajectory
                        
                        // Use stored distance if available, otherwise calculate  
                        let labelDistanceKm;
                        if (trajectory.distance && typeof trajectory.distance === 'number') {
                            // Use stored distance (already in km from preprocessing)
                            labelDistanceKm = trajectory.distance;
                        } else {
                            // Fallback: calculate from path coordinates
                            labelDistanceKm = calculateTrajectoryDistance(path);
                        }
                        
                        // Create enhanced label text with type and distance
                        const typeLabel = trajectory.type === 'GPS' ? 'GPS' : 'Manual';
                        const testPrefix = isTest ? '[TESTE] ' : '';
                        const labelText = `${testPrefix}${trajectory.name} - ${typeLabel} (${labelDistanceKm.toFixed(2)}km)`;
                        
                        const label = new google.maps.Marker({
                            position: labelPosition,
                            map: map,
                            icon: {
                                url: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns='http://www.w3.org/2000/svg' width='1' height='1' viewBox='0 0 1 1'>
                                        <rect width='1' height='1' fill='transparent'/>
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(1, 1),
                                anchor: new google.maps.Point(0, 0),
                                labelOrigin: new google.maps.Point(0, 0)
                            },
                            label: {
                                text: labelText,
                                color: strokeColor,
                                fontSize: '12px',
                                fontWeight: 'bold',
                                fontFamily: 'Arial, sans-serif',
                                className: isTest ? 'trajectory-label test-trajectory-label' : 'trajectory-label'
                            },
                            title: labelText
                        });
                        labels.push(label);
                    }
                    
                    // Add markers based on zoom level (skip markers for test trajectories)
                    if (currentZoom >= 11 && !isTest) {
                        // Detailed view: show start and end markers
                        const startMarker = new google.maps.Marker({
                            position: path[0],
                            map: map,
                            icon: {
                                url: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns='http://www.w3.org/2000/svg' width='26' height='26' viewBox='0 0 26 26'>
                                        <circle cx='13' cy='13' r='10' fill='rgba(0,0,0,0.3)' transform='translate(2,2)'/>
                                        <circle cx='13' cy='13' r='11' fill='` + (isTest ? '#ff6b6b' : '#28a745') + `' stroke='white' stroke-width='2'/>
                                        ` + (isTest ? 
                                            `<text x='13' y='17' text-anchor='middle' font-family='Arial' font-size='10' font-weight='bold' fill='white'>!</text>` :
                                            `<path d='M10 8 L18 13 L10 18 Z' fill='white'/>`
                                        ) + `
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(26, 26),
                                anchor: new google.maps.Point(13, 13)
                            },
                            title: (isTest ? '[TESTE] ' : '') + 'Início: ' + (trajectory.name || 'Trajeto') + ' (' + (trajectory.type || 'Manual') + ')'
                        });
                        markers.push(startMarker);
                        
                        // Add click listener to start marker
                        startMarker.addListener('click', function() {
                            infoWindow.setPosition(startMarker.getPosition());
                            infoWindow.open(map);
                        });
                        
                        // Add end marker if trajectory has multiple points
                        if (path.length > 1) {
                            const endMarker = new google.maps.Marker({
                                position: path[path.length - 1],
                                map: map,
                                icon: {
                                    url: 'data:image/svg+xml;base64,' + btoa(`
                                        <svg xmlns='http://www.w3.org/2000/svg' width='26' height='26' viewBox='0 0 26 26'>
                                            <circle cx='13' cy='13' r='10' fill='rgba(0,0,0,0.3)' transform='translate(2,2)'/>
                                            <circle cx='13' cy='13' r='11' fill='` + (isTest ? '#ff6b6b' : '#dc3545') + `' stroke='white' stroke-width='2'/>
                                            ` + (isTest ? 
                                                `<text x='13' y='17' text-anchor='middle' font-family='Arial' font-size='10' font-weight='bold' fill='white'>!</text>` :
                                                `<rect x='9' y='9' width='8' height='8' fill='white'/>`
                                            ) + `
                                        </svg>
                                    `),
                                    scaledSize: new google.maps.Size(26, 26),
                                    anchor: new google.maps.Point(13, 13)
                                },
                                title: (isTest ? '[TESTE] ' : '') + 'Fim: ' + (trajectory.name || 'Trajeto')
                            });
                            markers.push(endMarker);
                            
                            // Add click listener to end marker
                            endMarker.addListener('click', function() {
                                infoWindow.setPosition(endMarker.getPosition());
                                infoWindow.open(map);
                            });
                        }
                    } else if (!isTest) {
                        // Overview: show single trajectory marker with "T" (skip for test trajectories)
                        const trajectoryColor = isTest ? '#ff6b6b' : (isGPS ? '#28a745' : '#0a7ea4');
                        const trajectoryMarker = new google.maps.Marker({
                            position: path[Math.floor(path.length / 2)], // Middle of trajectory
                            map: map,
                            icon: {
                                url: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'>
                                        <circle cx='12' cy='12' r='9' fill='rgba(0,0,0,0.3)' transform='translate(2,2)'/>
                                        <circle cx='12' cy='12' r='10' fill='` + trajectoryColor + `' stroke='white' stroke-width='2'/>
                                        <text x='12' y='16' text-anchor='middle' font-family='Arial' font-size='12' font-weight='bold' fill='white'>` + (isTest ? '!' : 'T') + `</text>
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(24, 24),
                                anchor: new google.maps.Point(12, 12)
                            },
                            title: (isTest ? '[TESTE] ' : '') + (trajectory.name || 'Trajeto') + ' (' + (trajectory.type || 'Manual') + ')'
                        });
                        markers.push(trajectoryMarker);
                        
                        // Add click listener to trajectory marker
                        trajectoryMarker.addListener('click', function() {
                            infoWindow.setPosition(trajectoryMarker.getPosition());
                            infoWindow.open(map);
                        });
                    }
                }
            });
            
            // NEW: Add contact markers if contacts are enabled and available
            if (contactsEnabled && data.contacts && data.contacts.length > 0) {
                // Create contact markers (but don't add to map yet)
                const contactMarkers = [];
                
                data.contacts.forEach(contact => {
                    if (contact.coordinates && contact.coordinates.lat && contact.coordinates.lng) {
                        // Create contact marker with dove icon on green circle background
                        const contactMarker = new google.maps.Marker({
                            position: {
                                lat: parseFloat(contact.coordinates.lat),
                                lng: parseFloat(contact.coordinates.lng)
                            },
                            icon: {
                                url: contactMarkerIconUrl, // Use the pre-generated canvas icon
                                scaledSize: new google.maps.Size(24, 24), // Smaller for clustering
                                anchor: new google.maps.Point(12, 12)
                            },
                            title: `Contacto: ${contact.circumstance || 'Não especificado'}`
                        });
                        
                        // Add info window for contact
                        const contactInfoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 12px; min-width: 200px;">
                                    <h6 style="margin-bottom: 12px; color: #0a7ea4; font-size: 1.1rem;">
                                        <img src="${contactMarkerIconUrl}" style="width: 20px; height: 20px; vertical-align: text-bottom; margin-right: 5px;"/> Contacto
                                    </h6>
                                    <div style="margin-bottom: 8px;">
                                        <strong>Circunstância:</strong> 
                                        <span style="color: #0a7ea4; font-weight: 600;">
                                            ${translate(contact.circumstance, 'circumstance') || 'Não especificado'}
                                        </span>
                                    </div>
                                    ${contact.time ? `<div style="margin-bottom: 8px;"><strong>Hora:</strong> ${formatTime(contact.time)}</div>` : ''}
                                    ${contact.location ? `<div style="margin-bottom: 8px;"><strong>Local:</strong> ${translate(contact.location, 'location')}</div>` : ''}
                                    ${contact.zoneName ? `<div style="margin-bottom: 8px;"><strong>Zona de Caça:</strong> <span style="color: #0a7ea4; font-weight: 600;">${contact.zoneName}</span></div>` : ''}
                                    ${contact.trajectoryName ? `<div style="margin-bottom: 8px;"><strong>Trajeto:</strong> <span style="color: #0a7ea4; font-weight: 600;">${contact.trajectoryName}</span></div>` : ''}
                                    ${contact.createdBy ? `<div style="margin-bottom: 8px;"><strong>Registado por:</strong> ${contact.createdBy}</div>` : ''}
                                    ${contact.originalCreatedBy && contact.originalCreatedBy !== contact.createdBy ? `<div style="margin-bottom: 8px;"><strong>ID Original:</strong> <span style="font-family: monospace; font-size: 0.85em; color: #999;">${contact.originalCreatedBy}</span></div>` : ''}
                                    <div style="margin-bottom: 8px;">
                                        <strong>Coordenadas:</strong> ${contact.coordinates.lat.toFixed(6)}, ${contact.coordinates.lng.toFixed(6)}
                                    </div>
                                    <div style="margin-bottom: 8px;">
                                        <strong>Origem:</strong> ${contact.source === 'gestorMobile_contacts' ? 'Aplicação Mobile' : 'Sistema Web'}
                                    </div>
                                    ${contact.id ? `<div style="margin-bottom: 8px;"><strong>ID do Documento:</strong> <span style="font-family: monospace; font-size: 0.9em; color: #666;">${contact.id}</span></div>` : ''}
                                    ${contact.created_at ? '<div style="margin-bottom: 0; font-size: 0.85em; color: #666;"><strong>Criado:</strong> ' + new Date(contact.created_at).toLocaleDateString('pt-PT') + '</div>' : ''}
                                </div>
                            `
                        });
                        
                        // Add click listener to contact marker
                        contactMarker.addListener('click', function() {
                            contactInfoWindow.open(map, contactMarker);
                        });
                        
                        contactMarkers.push(contactMarker);
                        currentContactMarkers.push(contactMarker);
                    }
                });
                
                // Add contact markers with simple density-based optimization
                if (contactMarkers.length > 0) {
                    try {
                        // Simple density-based approach to reduce visual clutter
                        const zoom = map.getZoom();
                        const clusterResult = optimizeContactMarkers(contactMarkers, zoom);
                        
                        console.log(`Showing ${clusterResult.markers.length} cluster markers representing ${clusterResult.totalContacts} contacts (clustered from ${contactMarkers.length}) at zoom level ${zoom}`);
                        
                        // Add optimized markers to map
                        clusterResult.markers.forEach(marker => {
                            marker.setMap(map);
                        });
                        
                        // Store all markers for cleanup (including hidden ones)
                        currentContactMarkers = contactMarkers;
                        currentClusterMarkers = clusterResult.markers; // NEW: Store cluster markers
                        
                        // Track actual total contacts represented by visible clusters (not just cluster count)
                        actualVisibleContactCount = clusterResult.totalContacts;
                        
                    } catch (error) {
                        console.error('Error creating contact markers:', error);
                        // Fallback: add all markers individually
                        contactMarkers.forEach(marker => {
                            marker.setMap(map);
                        });
                        currentContactMarkers = contactMarkers;
                        currentClusterMarkers = contactMarkers; // NEW: Store individual markers as clusters
                        actualVisibleContactCount = contactMarkers.length;
                    }
                } else {
                    // No contact markers to show
                    actualVisibleContactCount = 0;
                    currentClusterMarkers = []; // NEW: Clear cluster markers when no contacts
                }
            }
            
            currentMarkers = markers;
            currentPolylines = polylines;
            currentLabels = labels;
            
            // Update stats (pass actual visible clustered count)
            const statsStartTime = performance.now();
            updateStats(data.stats, data.trajectories.length, actualVisibleContactCount, data.contactStats);
            const statsTime = performance.now() - statsStartTime;
            console.log(`📊 updateStats() took ${statsTime.toFixed(2)}ms`);
            
            // Update search data with all trajectories for search functionality
            if (data.trajectories && data.trajectories.length > 0) {
                // Use the same distance logic as map display for consistency
                allTrajectories = data.trajectories;
            }
            
            const processTime = performance.now() - processStartTime;
            console.log(`✅ processMapData() completed in ${processTime.toFixed(2)}ms`);
        }
        
        // Update statistics display
        function updateStats(stats, visibleTrajectories, visibleContacts, contactStats) {
            // Store total database stats on first load (these never change)
            if (!totalDatabaseStats) {
                totalDatabaseStats = {
                    gps_trajectories: stats.gps_trajectories || 0,
                    manual_trajectories: stats.manual_trajectories || 0,
                    total_trajectories: stats.total_trajectories || 0
                };
            }
            
            // Update Base de Dados section with TOTAL database counts (fixed)
            document.getElementById('stat-gps-trajectories').textContent = totalDatabaseStats.gps_trajectories;
            document.getElementById('stat-manual-trajectories').textContent = totalDatabaseStats.manual_trajectories;
            document.getElementById('stat-total-trajectories').textContent = totalDatabaseStats.total_trajectories;
            
            // Update Visíveis no Mapa section with current visible count (changes with zoom)
            document.getElementById('stat-visible').textContent = visibleTrajectories || 0;
            
            // NEW: Update contacts stats
            if (contactStats && contactStats.database_total_contacts) {
                // Always update database total count (from getTotalContactsCount)
                document.getElementById('stat-total-contacts').textContent = contactStats.database_total_contacts;
            }
            
            // Update visible contacts count only if contacts are enabled
            if (contactsEnabled) {
                document.getElementById('stat-visible-contacts').textContent = visibleContacts || 0;
                console.log(`Updating visible contacts count: ${visibleContacts || 0}`);
            } else {
                // If contacts are disabled, show 0 visible contacts
                document.getElementById('stat-visible-contacts').textContent = 0;
            }
        }
        
        // Event handlers with debouncing for performance
        const debouncedProcessMapData = debounce(processMapDataFromCache, 300);
        
        function onBoundsChanged() {
            // Only filter and display cached data, don't reload from server
            debouncedProcessMapData();
        }
        
        function onZoomChanged() {
            currentZoom = map.getZoom();
            // Only filter and display cached data, don't reload from server
            debouncedProcessMapData();
        }
        
        // NEW: Process map data from cache without reloading from server
        function processMapDataFromCache() {
            const bounds = map.getBounds();
            if (!bounds) {
                console.log('⏳ Map bounds not ready, skipping cache processing');
                return;
            }
            
            currentBounds = bounds;
            currentZoom = map.getZoom();
            
            // Show loading indicator
            const visibleTrajectoryTypes = getVisibleTrajectoryTypes();
            const trajectoryFiltersEnabled = visibleTrajectoryTypes.length > 0;
            
            // Check if any filters are enabled
            if (!contactsEnabled && !trajectoryFiltersEnabled) {
                // No filters enabled - clear everything
                console.log('🧹 No filters enabled, clearing map');
                clearMapElements();
                updateStats({ gps_trajectories: 0, manual_trajectories: 0, total_trajectories: 0 }, 0, 0, { total_contacts: 0 });
                return;
            }
            
            // Show appropriate loading message
            if (contactsEnabled && trajectoryFiltersEnabled) {
                showLoading('Filtrando trajetos e contactos...');
            } else if (contactsEnabled && !trajectoryFiltersEnabled) {
                showLoading('Filtrando contactos...');
            } else if (!contactsEnabled && trajectoryFiltersEnabled) {
                showLoading('Filtrando trajetos...');
            } else {
                showLoading('Filtrando dados...');
            }
            
            // Use cached data to filter and display
            const trajectoryData = globalCache.data || [];
            const contactData = contactsCache.data || [];
            
            // Debug cache data availability
            console.log(`📊 Cache data available: ${trajectoryData.length} trajectories, ${contactData.length} contacts`);
            
            // Load trajectory data if not in cache (only if trajectory filters are enabled)
            if (trajectoryFiltersEnabled && trajectoryData.length === 0) {
                console.log('⚠️ No trajectory data in cache, loading trajectories...');
                loadAllTrajectories().then(loadedTrajectories => {
                    if (loadedTrajectories.length > 0) {
                        // Process again with loaded trajectories
                        setTimeout(() => processMapDataFromCache(), 100);
                    } else {
                        hideLoading();
                    }
                });
                return; // Exit early to avoid processing empty data
            }
            
            // Filter trajectories based on current viewport and filters
            const filteredTrajectories = trajectoryFiltersEnabled ? filterTrajectories(trajectoryData, bounds) : [];
            
            // Filter contacts if enabled
            let filteredContacts = [];
            if (contactsEnabled) {
                if (contactData.length > 0) {
                    updateCircumstanceFilter(contactData);
                    filteredContacts = filterContacts(contactData, bounds);
                } else {
                    console.log('⚠️ No contact data in cache, loading contacts...');
                    // Don't hide loading message - let loadAllContacts() handle it
                    // Load contacts if not in cache
                    loadAllContacts().then(loadedContacts => {
                        if (loadedContacts.length > 0) {
                            // Process again with loaded contacts
                            setTimeout(() => processMapDataFromCache(), 100);
                        } else {
                            hideLoading();
                        }
                    });
                    // Return early to avoid processing empty contact data
                    return;
                }
            }
            
            // Create response structure compatible with existing code
            const responseData = {
                success: true,
                trajectories: filteredTrajectories,
                contacts: filteredContacts,
                stats: trajectoryFiltersEnabled ? calculateStatsFromTrajectories(trajectoryData) : { gps_trajectories: 0, manual_trajectories: 0, total_trajectories: 0 },
                contactStats: calculateContactStats(filteredContacts)
            };
            
            // Process the filtered data
            processMapData(responseData);
            
            console.log(`🚀 Processed cached data: ${filteredTrajectories.length} trajectories, ${filteredContacts.length} contacts`);
            
            // Hide loading indicator
            hideLoading();
        }
        
        // Calculate contact stats (client-side version)
        function calculateContactStats(contacts) {
            const stats = {
                total_contacts: contacts.length,
                by_circumstance: {},
                by_source: { contacts: 0, contactEvents: 0, gestorMobile_contacts: 0 }
            };
            
            contacts.forEach(contact => {
                // Count by circumstance
                const circumstance = contact.circumstance || 'Não especificado';
                if (!stats.by_circumstance[circumstance]) {
                    stats.by_circumstance[circumstance] = 0;
                }
                stats.by_circumstance[circumstance]++;
                
                // Count by source
                const source = contact.source || 'contacts';
                if (stats.by_source[source] !== undefined) {
                    stats.by_source[source]++;
                }
            });
            
            return stats;
        }
        
        // Clear all map elements
        function clearMapElements() {
            // Clear markers
            currentMarkers.forEach(marker => {
                marker.setMap(null);
            });
            currentMarkers = [];
            
            // Clear polylines
            currentPolylines.forEach(polyline => {
                polyline.setMap(null);
            });
            currentPolylines = [];
            
            // Clear labels
            currentLabels.forEach(label => {
                label.setMap(null);
            });
            currentLabels = [];
            
            // NEW: Clear contact markers (both individual and cluster markers)
            currentContactMarkers.forEach(marker => {
                try {
                    marker.setMap(null);
                } catch (error) {
                    console.warn('Error clearing contact marker:', error);
                }
            });
            currentContactMarkers = [];
            
            // NEW: Clear cluster markers (these are the ones actually visible on the map)
            currentClusterMarkers.forEach(marker => {
                try {
                    marker.setMap(null);
                } catch (error) {
                    console.warn('Error clearing cluster marker:', error);
                }
            });
            currentClusterMarkers = [];
        }
        
        // Loading indicator functions
        let loadingTimer;
        function showLoading(message) {
            // Always show loading immediately during filter operations
            clearTimeout(loadingTimer);
            
            const loadingElement = document.getElementById('loading-status');
            const overlayElement = document.getElementById('loadingOverlay');
            
            if (loadingElement && overlayElement) {
                loadingElement.textContent = message || 'Carregando...';
                overlayElement.style.display = 'block';
            } else {
                console.warn('Loading elements not found:', { loadingElement, overlayElement });
            }
        }
        
        function hideLoading() {
            clearTimeout(loadingTimer);
            const overlayElement = document.getElementById('loadingOverlay');
            if (overlayElement) {
                overlayElement.style.display = 'none';
            }
        }
        
        // Utility functions
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Calculate trajectory distance in kilometers
        function calculateTrajectoryDistance(path) {
            if (!path || path.length < 2) return 0;
            
            // Check if geometry library is available
            if (typeof google === 'undefined' || typeof google.maps.geometry === 'undefined') {
                console.warn('Google Maps Geometry library not loaded, using approximation');
                return calculateDistanceApproximation(path);
            }
            
            let totalDistance = 0;
            for (let i = 0; i < path.length - 1; i++) {
                const distance = google.maps.geometry.spherical.computeDistanceBetween(
                    new google.maps.LatLng(path[i].lat, path[i].lng),
                    new google.maps.LatLng(path[i + 1].lat, path[i + 1].lng)
                );
                totalDistance += distance;
            }
            
            // Convert from meters to kilometers and round to 2 decimal places
            return Math.round((totalDistance / 1000) * 100) / 100;
        }
        
        // Fallback distance calculation using Haversine formula
        function calculateDistanceApproximation(path) {
            if (!path || path.length < 2) return 0;
            
            let totalDistance = 0;
            for (let i = 0; i < path.length - 1; i++) {
                const R = 6371; // Earth's radius in km
                const dLat = (path[i + 1].lat - path[i].lat) * Math.PI / 180;
                const dLng = (path[i + 1].lng - path[i].lng) * Math.PI / 180;
                const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                         Math.cos(path[i].lat * Math.PI / 180) * Math.cos(path[i + 1].lat * Math.PI / 180) *
                         Math.sin(dLng/2) * Math.sin(dLng/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                const distance = R * c;
                totalDistance += distance;
            }
            
            return Math.round(totalDistance * 100) / 100;
        }
        
        // Global cache management
        // Get server-side cache info (with client-side caching)
        let cacheInfoCache = {
            data: null,
            lastUpdated: null
        };
        
        function getCacheInfo() {
            // Cache for 30 seconds to reduce frequent server calls
            const cacheExpiry = 30 * 1000; // 30 seconds
            const now = Date.now();
            
            // Use cached info if available and not expired
            if (cacheInfoCache.data && cacheInfoCache.lastUpdated &&
                (now - cacheInfoCache.lastUpdated) < cacheExpiry) {
                return Promise.resolve(cacheInfoCache.data);
            }
            
            return fetch('?action=get_cache_info')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Update cache
                        cacheInfoCache.data = data.cache;
                        cacheInfoCache.lastUpdated = now;
                        return data.cache;
                    }
                    throw new Error(data.error || 'Failed to get cache info');
                })
                .catch(error => {
                    console.error('Cache info error:', error);
                    // Return cached info if available, even if stale
                    if (cacheInfoCache.data) {
                        return cacheInfoCache.data;
                    }
                    return {
                        exists: false,
                        lastUpdated: null,
                        nextUpdate: null,
                        timeToNext: null,
                        isExpired: true
                    };
                });
        }

        function loadAllTrajectories(forceRefresh = false) {
            // Return cached data if we have it and not forcing refresh
            if (!forceRefresh && globalCache.data && globalCache.lastUpdated) {
                return Promise.resolve(globalCache.data);
            }
            
            // Return existing promise if already loading
            if (globalCache.isLoading) {
                return new Promise((resolve) => {
                    const checkLoading = () => {
                        if (!globalCache.isLoading) {
                            resolve(globalCache.data);
                        } else {
                            setTimeout(checkLoading, 100);
                        }
                    };
                    checkLoading();
                });
            }
            
            // Load data from server (server handles caching)
            globalCache.isLoading = true;
            showCacheLoading();
            
            return fetch('?action=get_trajectories&limit=10000&dateRange=all&types=GPS,Manual')
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.error || 'Erro ao carregar dados');
                    }
                    
                    // Calculate distances for all trajectories
                    const trajectoriesWithDistance = data.trajectories.map(trajectory => {
                        // Use stored distance if available (more accurate for GPS trajectories)
                        if (trajectory.distance && typeof trajectory.distance === 'number') {
                            // Distance is stored in meters, convert to km
                            return { ...trajectory, distance: trajectory.distance / 1000 };
                        } else if (trajectory.coordinates && trajectory.coordinates.length > 0) {
                            // Fallback: calculate from coordinates
                            const distance = calculateTrajectoryDistance(trajectory.coordinates);
                            return { ...trajectory, distance: distance };
                        }
                        return trajectory;
                    });
                    
                    // Update local cache for this session
                    globalCache.data = trajectoriesWithDistance;
                    globalCache.lastUpdated = Date.now();
                    globalCache.isLoading = false;
                    
                    updateCacheStatus();
                    console.log(`Trajectories loaded: ${trajectoriesWithDistance.length} from server cache`);
                    
                    return trajectoriesWithDistance;
                })
                .catch(error => {
                    console.error('Error loading trajectories:', error);
                    globalCache.isLoading = false;
                    updateCacheStatus();
                    
                    // Return cached data if available, even if stale
                    if (globalCache.data) {
                        console.log('Using stale local data due to error');
                        return globalCache.data;
                    }
                    
                    return [];
                });
        }

        function showCacheLoading() {
            showLoading('Atualizando cache de trajetos...');
        }

        // NEW: Load total contacts count for database statistics (with caching)
        let totalContactsCache = {
            count: null,
            lastUpdated: null
        };
        
        function loadTotalContactsCount() {
            // Cache for 10 minutes
            const cacheExpiry = 10 * 60 * 1000; // 10 minutes
            const now = Date.now();
            
            // Use cached count if available and not expired
            if (totalContactsCache.count !== null && totalContactsCache.lastUpdated &&
                (now - totalContactsCache.lastUpdated) < cacheExpiry) {
                document.getElementById('stat-total-contacts').textContent = totalContactsCache.count;
                console.log(`📊 Using cached total contacts count: ${totalContactsCache.count}`);
                return Promise.resolve(totalContactsCache.count);
            }
            
            // Load from server
            return fetch('?action=get_total_contacts_count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update cache
                        totalContactsCache.count = data.total_contacts || 0;
                        totalContactsCache.lastUpdated = now;
                        
                        // Update the database statistics even if contacts are not enabled
                        document.getElementById('stat-total-contacts').textContent = totalContactsCache.count;
                        console.log(`📊 Total contacts count loaded: ${totalContactsCache.count}`);
                        return totalContactsCache.count;
                    }
                    throw new Error('Failed to load total contacts count');
                })
                .catch(error => {
                    console.error('Error loading total contacts count:', error);
                    // Use cached count if available, even if stale
                    if (totalContactsCache.count !== null) {
                        document.getElementById('stat-total-contacts').textContent = totalContactsCache.count;
                        return totalContactsCache.count;
                    }
                    return 0;
                });
        }

                // NEW: Contacts loading functionality with improved caching
        function loadAllContacts(forceRefresh = false) {
            const startTime = performance.now();
            console.log('🔄 loadAllContacts() called, forceRefresh:', forceRefresh);
            
            // Return cached data if we have it and not forcing refresh (cache for 5 minutes)
            const cacheExpiry = 5 * 60 * 1000; // 5 minutes
            const now = Date.now();

            if (!forceRefresh && contactsCache.data && contactsCache.lastUpdated &&
                (now - contactsCache.lastUpdated) < cacheExpiry) {
                const cacheAge = Math.round((now - contactsCache.lastUpdated) / 1000);
                const timeTaken = performance.now() - startTime;
                console.log(`⚡ Using cached contact data (${cacheAge}s old, ${contactsCache.data.length} contacts) - took ${timeTaken.toFixed(2)}ms`);
                return Promise.resolve(contactsCache.data);
            }

            // Return existing promise if already loading
            if (contactsCache.isLoading && contactsCache.loadingPromise) {
                console.log('⏳ Already loading contacts, waiting for existing request...');
                return contactsCache.loadingPromise.then(() => {
                    const timeTaken = performance.now() - startTime;
                    console.log(`✅ Contacts loading completed, returning ${contactsCache.data?.length || 0} contacts - took ${timeTaken.toFixed(2)}ms`);
                    return contactsCache.data || [];
                });
            }

            // Load data from server (server handles caching)
            console.log('🌐 Loading contacts from server...');
            contactsCache.isLoading = true;
            const fetchStartTime = performance.now();
            
            // Show loading message only when actually loading from server
            showLoading('Carregando contactos...');

            // Store the loading promise so concurrent requests can wait for it
            contactsCache.loadingPromise = fetch('?action=get_contacts&limit=10000&dateRange=all')
                .then(response => {
                    const fetchTime = performance.now() - fetchStartTime;
                    console.log(`📡 Fetch request completed in ${fetchTime.toFixed(2)}ms, status: ${response.status}`);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    const parseTime = performance.now() - fetchStartTime;
                    console.log(`📊 JSON parsing completed in ${parseTime.toFixed(2)}ms`);
                    
                    if (!data.success) {
                        throw new Error(data.error || 'Erro ao carregar contactos');
                    }

                    // Update local cache for this session
                    contactsCache.data = data.contacts;
                    contactsCache.lastUpdated = now;
                    contactsCache.isLoading = false;
                    contactsCache.loadingPromise = null;

                    const totalTime = performance.now() - startTime;
                    console.log(`✅ Contacts loaded: ${data.contacts.length} from server - total time: ${totalTime.toFixed(2)}ms`);
                    
                    // Log server-side cache info if available
                    if (data.stats) {
                        console.log('📈 Server stats:', data.stats);
                    }

                    // Hide loading message when done
                    hideLoading();
                    
                    return data.contacts;
                })
                .catch(error => {
                    const errorTime = performance.now() - startTime;
                    console.error(`❌ Error loading contacts after ${errorTime.toFixed(2)}ms:`, error);
                    contactsCache.isLoading = false;
                    contactsCache.loadingPromise = null;

                    // Hide loading message on error
                    hideLoading();

                    // Return cached data if available, even if stale
                    if (contactsCache.data) {
                        console.log('🔄 Using stale contact data due to error');
                        return contactsCache.data;
                    }

                    console.warn('⚠️ No cached contact data available, returning empty array');
                    return [];
                });
            
            // Return the loading promise
            return contactsCache.loadingPromise;
        }

        function filterContacts(contacts, bounds) {
            const dateRange = document.getElementById('dateRange').value;
            const circumstanceFilter = document.getElementById('circumstanceFilter').value;
            
            return contacts.filter(contact => {
                // Circumstance filter
                if (circumstanceFilter && circumstanceFilter !== 'all' && 
                    contact.circumstance !== circumstanceFilter) {
                    return false;
                }
                
                // Date filter
                if (!matchesDateRange(contact.created_at, dateRange)) {
                    return false;
                }
                
                // Bounds filter
                if (!contactInBounds(contact.coordinates, bounds)) {
                    return false;
                }
                
                return true;
            }); // No artificial limit - show actual visible contacts
        }

        function contactInBounds(coordinates, bounds) {
            if (!coordinates || !coordinates.lat || !coordinates.lng) {
                return false; // Reject contacts without valid coordinates
            }
            
            // Basic sanity check: Portugal is roughly between 36-42°N and 9.5-6°W
            const lat = parseFloat(coordinates.lat);
            const lng = parseFloat(coordinates.lng);
            
            // Reject obviously invalid coordinates (like Africa coordinates)
            if (lat < 30 || lat > 50 || lng < -15 || lng > 5) {
                console.warn('Rejecting contact with invalid coordinates:', { lat, lng });
                return false;
            }
            
            if (!bounds) {
                return true; // If no bounds specified, accept valid coordinates
            }
            
            const north = bounds.getNorthEast().lat();
            const south = bounds.getSouthWest().lat();
            const east = bounds.getNorthEast().lng();
            const west = bounds.getSouthWest().lng();
            
            return lat >= south && lat <= north && lng >= west && lng <= east;
        }

        // Advanced clustering with visual cluster markers like the image
        function optimizeContactMarkers(markers, zoom) {
            if (!markers || markers.length === 0) return { markers: [], totalContacts: 0 };
            
            // At high zoom levels, show all markers
            if (zoom >= 15) {
                return { markers: markers, totalContacts: markers.length };
            }
            
            // Calculate grid size based on zoom level
            let gridSize = 0.01; // Default grid size in degrees
            if (zoom < 6) {
                gridSize = 0.3; // Very large grid for country level
            } else if (zoom < 8) {
                gridSize = 0.15; // Large grid for country level
            } else if (zoom < 10) {
                gridSize = 0.08; // Medium grid for regional level
            } else if (zoom < 12) {
                gridSize = 0.04; // Small grid for city level
            } else if (zoom < 14) {
                gridSize = 0.02; // Very small grid for detailed level
            } else {
                gridSize = 0.01; // Tiny grid for very detailed level
            }
            
            // Group markers by grid cells
            const grid = new Map();
            
            markers.forEach(marker => {
                const pos = marker.getPosition();
                const lat = pos.lat();
                const lng = pos.lng();
                
                // Calculate grid cell
                const gridLat = Math.floor(lat / gridSize) * gridSize;
                const gridLng = Math.floor(lng / gridSize) * gridSize;
                const gridKey = `${gridLat},${gridLng}`;
                
                if (!grid.has(gridKey)) {
                    grid.set(gridKey, []);
                }
                grid.get(gridKey).push(marker);
            });
            
            // Create cluster markers and track total contacts
            const clusterMarkers = [];
            let totalContactsInClusters = 0;
            
            grid.forEach((cellMarkers, gridKey) => {
                if (cellMarkers.length === 1) {
                    // Single marker in cell - show it as is
                    clusterMarkers.push(cellMarkers[0]);
                    totalContactsInClusters += 1; // Single contact
                } else if (cellMarkers.length > 1) {
                    // Multiple markers in cell - create cluster marker
                    const firstMarker = cellMarkers[0];
                    const pos = firstMarker.getPosition();
                    const count = cellMarkers.length;
                    
                    // Create cluster marker with count
                    const clusterMarker = new google.maps.Marker({
                        position: pos,
                        icon: createClusterIcon(count),
                        title: `${count} contactos nesta área`,
                        zIndex: 1000 + count // Higher count = higher z-index
                    });
                    
                    // Add click handler to show info about clustered contacts
                    clusterMarker.addListener('click', function() {
                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 10px;">
                                    <h6 style="margin: 0 0 10px 0; color: #0a7ea4;">
                                        <i class="fas fa-dove" style="margin-right: 5px;"></i>
                                        Cluster de Contactos
                                    </h6>
                                    <p style="margin: 0; font-size: 14px;">
                                        <strong>${count} contactos</strong> nesta área<br>
                                        <small style="color: #666;">Aproxime o zoom para ver individualmente</small>
                                    </p>
                                </div>
                            `
                        });
                        infoWindow.open(map, clusterMarker);
                    });
                    
                    clusterMarkers.push(clusterMarker);
                    totalContactsInClusters += count; // Add all contacts in this cluster
                }
            });
            
            return { markers: clusterMarkers, totalContacts: totalContactsInClusters };
        }
        
        // Create cluster icon similar to the image but with green theme
        function createClusterIcon(count) {
            const size = Math.min(60, Math.max(30, 25 + Math.log(count) * 5)); // Dynamic size
            
            // Choose color based on count
            let backgroundColor, textColor;
            if (count < 10) {
                backgroundColor = '#28a745'; // Green
                textColor = 'white';
            } else if (count < 50) {
                backgroundColor = '#20c997'; // Teal
                textColor = 'white';
            } else if (count < 100) {
                backgroundColor = '#17a2b8'; // Info blue
                textColor = 'white';
            } else {
                backgroundColor = '#0a7ea4'; // Primary blue
                textColor = 'white';
            }
            
            return {
                url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
                        <circle cx="${size/2}" cy="${size/2}" r="${size/2-2}" 
                                fill="${backgroundColor}" 
                                stroke="white" 
                                stroke-width="2"
                                opacity="0.9"/>
                        <text x="${size/2}" y="${size/2}" 
                              text-anchor="middle" 
                              dominant-baseline="central" 
                              fill="${textColor}" 
                              font-family="Arial, sans-serif" 
                              font-size="${Math.max(10, size/4)}" 
                              font-weight="bold">${count}</text>
                    </svg>
                `)}`,
                scaledSize: new google.maps.Size(size, size),
                anchor: new google.maps.Point(size/2, size/2)
            };
        }

        function updateCacheStatus() {
            getCacheInfo().then(cacheInfo => {
                if (!cacheInfo.exists) {
                    // No cache exists yet
                    const lastUpdateElement = document.getElementById('lastUpdateText');
                    const nextUpdateElement = document.getElementById('nextUpdateText');
                    
                    if (lastUpdateElement) {
                        lastUpdateElement.textContent = 'Carregando...';
                    }
                    
                    if (nextUpdateElement) {
                        nextUpdateElement.textContent = 'Preparando cache...';
                    }
                    return;
                }
                
                const now = Math.floor(Date.now() / 1000); // Convert to seconds
                const timeSinceUpdate = now - cacheInfo.lastUpdated;
                const minutesAgo = Math.floor(timeSinceUpdate / 60);
                
                let statusText;
                if (minutesAgo < 1) {
                    statusText = 'Atualizado agora';
                } else if (minutesAgo < 60) {
                    statusText = `Atualizado há ${minutesAgo} min`;
                } else {
                    const hoursAgo = Math.floor(minutesAgo / 60);
                    statusText = `Atualizado há ${hoursAgo}h`;
                }
                
                let nextUpdateText;
                if (cacheInfo.timeToNext <= 0) {
                    nextUpdateText = 'Disponível agora';
                } else {
                    const minutesToNext = Math.floor(cacheInfo.timeToNext / 60);
                    if (minutesToNext < 60) {
                        nextUpdateText = `${minutesToNext} min`;
                    } else {
                        const hoursToNext = Math.floor(minutesToNext / 60);
                        const remainingMinutes = minutesToNext % 60;
                        nextUpdateText = `${hoursToNext}h ${remainingMinutes}min`;
                    }
                }
                
                // Update header cache info
                const lastUpdateElement = document.getElementById('lastUpdateText');
                const nextUpdateElement = document.getElementById('nextUpdateText');
                
                if (lastUpdateElement) {
                    lastUpdateElement.textContent = statusText;
                }
                
                if (nextUpdateElement) {
                    nextUpdateElement.textContent = nextUpdateText;
                }
                
                // Update status in the control panel
                let statusElement = document.getElementById('cache-status');
                if (!statusElement) {
                    // Create status element if it doesn't exist
                    const controlsSection = document.querySelector('.panel-section');
                    if (controlsSection) {
                        statusElement = document.createElement('div');
                        statusElement.id = 'cache-status';
                        statusElement.className = 'cache-status';
                        controlsSection.appendChild(statusElement);
                    }
                }
                
                if (statusElement) {
                    const isStale = cacheInfo.isExpired;
                    statusElement.innerHTML = `
                        <i class="fas fa-clock" style="color: ${isStale ? '#ffc107' : '#28a745'};"></i>
                        <span style="font-size: 0.7rem; color: ${isStale ? '#ffc107' : '#666'};">${statusText}</span>
                    `;
                }
            }).catch(error => {
                console.error('Error updating cache status:', error);
            });
        }

        // Search functionality
        let searchTimeout;

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('zoneSearch');
            const clearButton = document.getElementById('clearSearch');
            const searchResults = document.getElementById('searchResults');

            // Search input event listener
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                
                if (query.length === 0) {
                    hideSearchResults();
                    clearButton.style.display = 'none';
                    return;
                }
                
                clearButton.style.display = 'block';
                
                // Debounce search
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });

            // Clear search button
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                hideSearchResults();
                clearButton.style.display = 'none';
                searchInput.focus();
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-container')) {
                    hideSearchResults();
                }
            });

            // Show search results when focusing on input (if there's a query)
            searchInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    performSearch(this.value.trim());
                }
            });
        }

        // Perform search through trajectories
        function performSearch(query) {
            const searchResults = document.getElementById('searchResults');
            const queryLower = query.toLowerCase();
            
            // Show loading state
            searchResults.innerHTML = `
                <div class="search-loading">
                    <div class="loading-spinner">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                        <span>Pesquisando...</span>
                    </div>
                </div>
            `;
            searchResults.style.display = 'block';
            
            // Load global trajectories and perform search
            loadAllTrajectories().then(trajectories => {
                // Filter trajectories based on search query
                const matchingTrajectories = trajectories.filter(trajectory => {
                    return trajectory.name && trajectory.name.toLowerCase().includes(queryLower);
                });

                displaySearchResults(matchingTrajectories, query);
            }).catch(error => {
                console.error('Search error:', error);
                searchResults.innerHTML = `
                    <div class="search-no-results">
                        Erro ao pesquisar: ${error.message}
                    </div>
                `;
            });
        }

        // Display search results
        function displaySearchResults(trajectories, query) {
            const searchResults = document.getElementById('searchResults');
            
            if (trajectories.length === 0) {
                searchResults.innerHTML = `
                    <div class="search-no-results">
                        Nenhuma trajeto encontrado para "${query}"
                    </div>
                `;
            } else {
                searchResults.innerHTML = trajectories.slice(0, 10).map(trajectory => {
                    const typeIcon = trajectory.type === 'GPS' ? 
                        '<span class="trajectory-marker-icon gps-marker">T</span>' : 
                        '<span class="trajectory-marker-icon manual-marker">T</span>';
                    
                    return `
                        <div class="search-result-item" data-trajectory-id="${trajectory.id}">
                            <div>
                                <div class="search-result-name">${trajectory.name}</div>
                                <div class="search-result-type">
                                    ${typeIcon}
                                    <span>${trajectory.type}</span>
                                    ${trajectory.distance ? `<span>• ${trajectory.distance.toFixed(2)}km</span>` : ''}
                                </div>
                            </div>
                            <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                        </div>
                    `;
                }).join('');

                // Add click listeners to search results
                searchResults.querySelectorAll('.search-result-item').forEach(item => {
                    item.addEventListener('click', function() {
                        const trajectoryId = this.dataset.trajectoryId;
                        const trajectory = trajectories.find(t => t.id === trajectoryId);
                        if (trajectory) {
                            zoomToTrajectory(trajectory);
                            hideSearchResults();
                            document.getElementById('zoneSearch').blur();
                        }
                    });
                });
            }
            
            searchResults.style.display = 'block';
        }

        // Hide search results
        function hideSearchResults() {
            document.getElementById('searchResults').style.display = 'none';
        }

        // Simple zoom out then zoom in animation to trajectory
        function zoomToTrajectory(trajectory) {
            if (!trajectory.coordinates || trajectory.coordinates.length === 0) {
                return;
            }

            // Calculate target bounds for the trajectory
            const bounds = new google.maps.LatLngBounds();
            trajectory.coordinates.forEach(coord => {
                bounds.extend(new google.maps.LatLng(coord.lat, coord.lng));
            });

            // Add padding to the bounds
            const ne = bounds.getNorthEast();
            const sw = bounds.getSouthWest();
            const latPadding = (ne.lat() - sw.lat()) * 0.2;
            const lngPadding = (ne.lng() - sw.lng()) * 0.2;
            
            const targetBounds = new google.maps.LatLngBounds(
                new google.maps.LatLng(sw.lat() - latPadding, sw.lng() - lngPadding),
                new google.maps.LatLng(ne.lat() + latPadding, ne.lng() + lngPadding)
            );

            const currentZoom = map.getZoom();
            const targetCenter = targetBounds.getCenter();
            const overviewZoom = 7; // Zoom out level
            
            // Always do the same animation: zoom out -> zoom in
            // Step 1: Smooth zoom out to overview level
            smoothZoomTo(overviewZoom, () => {
                // Step 2: Fit bounds to trajectory (this will pan and zoom in one smooth motion)
                
                // Step 2: Fit bounds to trajectory (this will pan and zoom in one smooth motion)
                map.fitBounds(targetBounds, {
                    top: 60,
                    right: 60,
                    bottom: 60,
                    left: 320
                });
                
                // Highlight after animation completes
                setTimeout(() => {
                    highlightTrajectory(trajectory);
                }, 1000);
            });
        }

        // Smooth zoom animation with easing
        function smoothZoomTo(targetZoom, callback) {
            const startZoom = map.getZoom();
            const zoomDiff = targetZoom - startZoom;
            
            if (Math.abs(zoomDiff) < 0.1) {
                if (callback) callback();
                return;
            }
            
            const duration = Math.min(Math.abs(zoomDiff) * 200, 1200); // Max 1.2 seconds
            const startTime = Date.now();
            
            function animate() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function for smooth animation
                const easeInOutQuart = (t) => {
                    return t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2;
                };
                
                const easedProgress = easeInOutQuart(progress);
                const currentZoom = startZoom + (zoomDiff * easedProgress);
                
                map.setZoom(currentZoom);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    map.setZoom(targetZoom);
                    if (callback) {
                        setTimeout(callback, 100);
                    }
                }
            }
            
            requestAnimationFrame(animate);
        }
        
        // Calculate appropriate zoom level for bounds
        function calculateZoomForBounds(bounds) {
            const WORLD_DIM = { height: 256, width: 256 };
            const ZOOM_MAX = 18;
            
            function latRad(lat) {
                const sin = Math.sin(lat * Math.PI / 180);
                const radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
                return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
            }
            
            function zoom(mapPx, worldPx, fraction) {
                return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
            }
            
            const ne = bounds.getNorthEast();
            const sw = bounds.getSouthWest();
            
            const latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;
            const lngDiff = ne.lng() - sw.lng();
            const lngFraction = ((lngDiff < 0) ? (lngDiff + 360) : lngDiff) / 360;
            
            const mapDiv = map.getDiv();
            const latZoom = zoom(mapDiv.offsetHeight - 120, WORLD_DIM.height, latFraction); // Account for padding
            const lngZoom = zoom(mapDiv.offsetWidth - 380, WORLD_DIM.width, lngFraction); // Account for control panel
            
            return Math.min(latZoom, lngZoom, ZOOM_MAX);
        }

        // Calculate zoom level that would fit bounds
        function getBoundsZoomLevel(bounds, mapDiv) {
            const WORLD_DIM = { height: 256, width: 256 };
            const ZOOM_MAX = 21;

            function latRad(lat) {
                const sin = Math.sin(lat * Math.PI / 180);
                const radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
                return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
            }

            function zoom(mapPx, worldPx, fraction) {
                return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
            }

            const ne = bounds.getNorthEast();
            const sw = bounds.getSouthWest();

            const latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;
            const lngDiff = ne.lng() - sw.lng();
            const lngFraction = ((lngDiff < 0) ? (lngDiff + 360) : lngDiff) / 360;

            const latZoom = zoom(mapDiv.offsetHeight, WORLD_DIM.height, latFraction);
            const lngZoom = zoom(mapDiv.offsetWidth, WORLD_DIM.width, lngFraction);

            return Math.min(latZoom, lngZoom, ZOOM_MAX);
        }

        // Highlight trajectory function with pulsing animation
        function highlightTrajectory(trajectory) {
            // Find and briefly highlight the trajectory
            const matchingPolyline = currentPolylines.find(polyline => {
                const path = polyline.getPath();
                return path && path.getLength() === trajectory.coordinates.length;
            });
            
            if (matchingPolyline) {
                const originalWeight = matchingPolyline.get('strokeWeight') || 4;
                const originalOpacity = matchingPolyline.get('strokeOpacity') || 0.9;
                const originalColor = matchingPolyline.get('strokeColor') || '#FF0000';
                
                // Create pulsing highlight effect
                let pulseCount = 0;
                const maxPulses = 3;
                
                function pulse() {
                    if (pulseCount >= maxPulses) {
                        // Restore original style
                        matchingPolyline.setOptions({ 
                            strokeWeight: originalWeight,
                            strokeOpacity: originalOpacity,
                            strokeColor: originalColor,
                            zIndex: 1
                        });
                        return;
                    }
                    
                    // Pulse to highlight
                    matchingPolyline.setOptions({ 
                        strokeWeight: originalWeight + 6,
                        strokeOpacity: 1,
                        strokeColor: '#FFD700', // Gold color for highlight
                        zIndex: 1000
                    });
                    
                    setTimeout(() => {
                        // Pulse back to normal
                        matchingPolyline.setOptions({ 
                            strokeWeight: originalWeight + 2,
                            strokeOpacity: 0.8,
                            strokeColor: originalColor,
                            zIndex: 1000
                        });
                        
                        pulseCount++;
                        setTimeout(pulse, 400);
                    }, 400);
                }
                
                pulse();
            }
        }

        // Start periodic header cache status updates
        function startHeaderCacheUpdates() {
            // Update immediately
            updateCacheStatus();
            
            // Update every 2 minutes (reduced from 30 seconds)
            setInterval(() => {
                updateCacheStatus();
            }, 2 * 60 * 1000);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('dateRange').addEventListener('change', function() {
                processMapDataFromCache(); // Use cached data with new filters
            });

            document.getElementById('filter-gps').addEventListener('change', function() {
                processMapDataFromCache(); // Use cached data with new filters
            });

            document.getElementById('filter-manual').addEventListener('change', function() {
                processMapDataFromCache(); // Use cached data with new filters
            });

            // NEW: Contacts event listeners
            document.getElementById('filter-contacts').addEventListener('change', function() {
                contactsEnabled = this.checked;
                
                // Show/hide contacts filter section and visible contacts stats
                const contactsFilterSection = document.getElementById('contacts-filter-section');
                const visibleContactsStats = document.getElementById('visible-contacts-stats');
                
                if (contactsEnabled) {
                    contactsFilterSection.style.display = 'block';
                    visibleContactsStats.style.display = 'block';
                } else {
                    contactsFilterSection.style.display = 'none';
                    visibleContactsStats.style.display = 'none';
                }
                
                // Note: contacts-stats (database total) always remains visible
                
                processMapDataFromCache(); // Use cached data with new filters
            });

            document.getElementById('circumstanceFilter').addEventListener('change', function() {
                if (contactsEnabled) {
                    processMapDataFromCache(); // Use cached data with new filters
                }
            });

                    // Initialize search functionality
        initializeSearch();

        // Load initial cache and set up auto-refresh
        loadAllTrajectories();
        
        // Load total contacts count for database statistics
        loadTotalContactsCount();
            loadAllContacts(); // Pre-cache contacts on page load
            
            // Start header cache status updates
            startHeaderCacheUpdates();
            
            // Auto-refresh cache every 15 minutes to check for server cache updates (reduced frequency)
            setInterval(() => {
                console.log('Checking for cache updates...');
                loadAllTrajectories(false); // Check server cache
            }, 15 * 60 * 1000);

            // Add event listeners to controls
            document.getElementById('refreshCacheBtn').addEventListener('click', () => {
                window.location.href = 'mapa_trajetos.php?refresh=true';
            });
        });
        
        // Load Google Maps API
        window.onload = function() {
            const script = document.createElement('script');
            script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCWTWKSZHVqnTCzDMNw7rI3_laAownyYys&libraries=geometry&callback=initMap';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        };
    </script>
</body>
</html>
