<?php
// Debug script to show incomplete/draft trajectories without coordinates
// This helps understand the discrepancy between total trajectories and zones with trajectories

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Password protection (same as main stats)
session_start();
$correct_password = 'prorola2025';

if (!isset($_POST['password']) && !isset($_SESSION['debug_authenticated'])) {
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Debug - Trajetos Incompletos</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background: #f8f9fa; padding: 2rem; }
            .debug-card { background: white; border-radius: 8px; padding: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="debug-card">
                        <h3 class="text-center mb-4">🔍 Debug - Trajetos Incompletos</h3>
                        <form method="POST">
                            <div class="mb-3">
                                <input type="password" class="form-control" name="password" placeholder="Palavra-passe" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Entrar</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Check password
if (isset($_POST['password'])) {
    if ($_POST['password'] === $correct_password) {
        $_SESSION['debug_authenticated'] = true;
    } else {
        echo "<div class='alert alert-danger'>Palavra-passe incorreta</div>";
        exit;
    }
}

// Load environment variables function
function getEnvVar($name) {
    return $_SERVER[$name] ?? getenv($name) ?? '';
}

// Simple Firebase class (minimal version)
class DebugFirebase {
    private $projectId;
    private $accessToken;
    
    public function __construct($projectId) {
        $this->projectId = $projectId;
        $this->accessToken = $this->getAdminAccessToken();
    }
    
    private function getAdminAccessToken() {
        try {
            $serviceAccount = [
                'type' => getEnvVar('FIREBASE_TYPE'),
                'project_id' => getEnvVar('FIREBASE_PROJECT_ID'),
                'private_key_id' => getEnvVar('FIREBASE_PRIVATE_KEY_ID'),
                'private_key' => str_replace('\\n', "\n", getEnvVar('FIREBASE_PRIVATE_KEY')),
                'client_email' => getEnvVar('FIREBASE_CLIENT_EMAIL'),
                'client_id' => getEnvVar('FIREBASE_CLIENT_ID'),
                'auth_uri' => getEnvVar('FIREBASE_AUTH_URI'),
                'token_uri' => getEnvVar('FIREBASE_TOKEN_URI'),
            ];
            
            if (empty($serviceAccount['private_key']) || empty($serviceAccount['client_email'])) {
                throw new Exception('Firebase credentials not found');
            }
            
            $now = time();
            $payload = [
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/datastore',
                'aud' => 'https://oauth2.googleapis.com/token',
                'exp' => $now + 3600,
                'iat' => $now
            ];
            
            $jwt = $this->createJWT($payload, $serviceAccount['private_key']);
            
            $tokenData = [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($tokenData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode !== 200) {
                throw new Exception("Failed to get access token. HTTP Code: $httpCode");
            }
            
            $tokenResponse = json_decode($response, true);
            return $tokenResponse['access_token'] ?? null;
            
        } catch (Exception $e) {
            throw new Exception("Firebase auth error: " . $e->getMessage());
        }
    }
    
    private function createJWT($payload, $privateKey) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
        $payload = json_encode($payload);
        
        $base64Header = $this->base64UrlEncode($header);
        $base64Payload = $this->base64UrlEncode($payload);
        
        $signature = '';
        openssl_sign($base64Header . '.' . $base64Payload, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        $base64Signature = $this->base64UrlEncode($signature);
        
        return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
    }
    
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    public function getCollectionData($collection) {
        $allDocuments = [];
        $pageToken = null;
        $pageSize = 1000; // Process in chunks
        
        do {
            // Build URL with pagination
            $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
            $queryParams = [];
            
            if ($pageSize) {
                $queryParams['pageSize'] = $pageSize;
            }
            if ($pageToken) {
                $queryParams['pageToken'] = $pageToken;
            }
            if (!empty($queryParams)) {
                $url .= '?' . http_build_query($queryParams);
            }
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode !== 200) {
                throw new Exception("Failed to fetch collection: $collection (HTTP $httpCode)");
            }
            
            $data = json_decode($response, true);
            
            // Extract pagination token for next page
            $pageToken = $data['nextPageToken'] ?? null;
            
            // Process documents from this page
            if (isset($data['documents'])) {
                foreach ($data['documents'] as $doc) {
                    $allDocuments[] = $this->parseDocument($doc);
                }
            }
            
        } while ($pageToken); // Continue until no more pages
        
        return $allDocuments;
    }
    
    private function parseDocument($doc) {
        $parsed = [];
        if (isset($doc['fields'])) {
            foreach ($doc['fields'] as $key => $field) {
                $parsed[$key] = $this->convertFromFirestoreValue($field);
            }
        }
        return $parsed;
    }
    
    private function convertFromFirestoreValue($field) {
        if (isset($field['stringValue'])) return $field['stringValue'];
        if (isset($field['integerValue'])) return (int)$field['integerValue'];
        if (isset($field['doubleValue'])) return (float)$field['doubleValue'];
        if (isset($field['booleanValue'])) return $field['booleanValue'];
        if (isset($field['timestampValue'])) return $field['timestampValue'];
        if (isset($field['arrayValue']['values'])) {
            $array = [];
            foreach ($field['arrayValue']['values'] as $value) {
                $array[] = $this->convertFromFirestoreValue($value);
            }
            return $array;
        }
        if (isset($field['mapValue']['fields'])) {
            $map = [];
            foreach ($field['mapValue']['fields'] as $key => $value) {
                $map[$key] = $this->convertFromFirestoreValue($value);
            }
            return $map;
        }
        return null;
    }
}

// Firebase configuration
$projectId = getEnvVar('FIREBASE_PROJECT_ID') ?: 'prorola-a2f66';

try {
    $firebase = new DebugFirebase($projectId);
    
    // Get manual trajectories from 'zonas' collection
    $manualTrajectories = $firebase->getCollectionData('zonas');
    $totalManualRetrieved = count($manualTrajectories);
    error_log("DEBUG: Retrieved $totalManualRetrieved manual trajectories from 'zonas' collection");
    
    // Get GPS trajectories from 'gestorMobile_trajetos' collection
    $gpsTrajectories = $firebase->getCollectionData('gestorMobile_trajetos');
    $totalGpsRetrieved = count($gpsTrajectories);
    error_log("DEBUG: Retrieved $totalGpsRetrieved GPS trajectories from 'gestorMobile_trajetos' collection");
    
    // Analyze manual trajectories
    $manualWithCoordinates = [];
    $manualWithoutCoordinates = [];
    
    foreach ($manualTrajectories as $traj) {
        if (isset($traj['coordinates']) && is_array($traj['coordinates']) && !empty($traj['coordinates'])) {
            $manualWithCoordinates[] = $traj;
        } else {
            $manualWithoutCoordinates[] = $traj;
        }
    }
    
    // Analyze GPS trajectories
    $gpsWithCoordinates = [];
    $gpsWithoutCoordinates = [];
    
    foreach ($gpsTrajectories as $traj) {
        // GPS trajectories are considered "complete" if they have a zoneId (matching main stats API logic)
        // The main stats API doesn't validate coordinates for GPS trajectories
        if (isset($traj['zoneId']) && !empty($traj['zoneId'])) {
            $gpsWithCoordinates[] = $traj; // "Complete" = has zoneId
        } else {
            $gpsWithoutCoordinates[] = $traj; // "Incomplete" = no zoneId
        }
    }
    
    // DEBUG: Log first few GPS trajectories to understand structure
    error_log("DEBUG: GPS Trajectory Structure Analysis:");
    for ($i = 0; $i < min(3, count($gpsTrajectories)); $i++) {
        error_log("GPS Trajectory $i keys: " . implode(', ', array_keys($gpsTrajectories[$i])));
        if (isset($gpsTrajectories[$i]['coordinates'])) {
            error_log("GPS Trajectory $i coordinates type: " . gettype($gpsTrajectories[$i]['coordinates']));
            if (is_array($gpsTrajectories[$i]['coordinates'])) {
                error_log("GPS Trajectory $i coordinates count: " . count($gpsTrajectories[$i]['coordinates']));
            }
        }
        if (isset($gpsTrajectories[$i]['trajeto'])) {
            error_log("GPS Trajectory $i trajeto type: " . gettype($gpsTrajectories[$i]['trajeto']));
            if (is_array($gpsTrajectories[$i]['trajeto'])) {
                error_log("GPS Trajectory $i trajeto count: " . count($gpsTrajectories[$i]['trajeto']));
            }
        }
    }
    
    // Calculate totals
    $totalTrajectories = $totalManualRetrieved + $totalGpsRetrieved;
    $totalWithCoordinates = count($manualWithCoordinates) + count($gpsWithCoordinates);
    $totalWithoutCoordinates = count($manualWithoutCoordinates) + count($gpsWithoutCoordinates);
    
    // Analyze zones with multiple trajectories (matching main stats API logic)
    $zoneIds = [];
    $zoneTrajectoryCount = [];
    
    // Count trajectories per zone from manual trajectories (with coordinates)
    foreach ($manualWithCoordinates as $traj) {
        if (isset($traj['zoneId'])) {
            $zoneId = $traj['zoneId'];
            $zoneIds[$zoneId] = true;
            $zoneTrajectoryCount[$zoneId] = ($zoneTrajectoryCount[$zoneId] ?? 0) + 1;
        }
    }
    
    // Count trajectories per zone from GPS trajectories (with zoneId)
    foreach ($gpsWithCoordinates as $traj) {
        if (isset($traj['zoneId']) && !empty($traj['zoneId'])) {
            $zoneId = $traj['zoneId'];
            $zoneIds[$zoneId] = true;
            $zoneTrajectoryCount[$zoneId] = ($zoneTrajectoryCount[$zoneId] ?? 0) + 1;
        }
    }
    
    $uniqueZonesWithTrajectories = count($zoneIds);
    $zonesWithMultipleTrajectories = array_filter($zoneTrajectoryCount, function($count) { return $count > 1; });
    
    error_log("DEBUG: Unique zones with trajectories: $uniqueZonesWithTrajectories");
    error_log("DEBUG: Zones with multiple trajectories: " . count($zonesWithMultipleTrajectories));
    
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Debug - Trajetos Incompletos</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body { background: #f8f9fa; padding: 2rem; }
            .debug-card { background: white; border-radius: 8px; padding: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; }
            .trajectory-item { border: 1px solid #dee2e6; border-radius: 6px; padding: 1rem; margin-bottom: 1rem; }
            .trajectory-item.incomplete { border-color: #dc3545; background: #fff5f5; }
            .trajectory-item.complete { border-color: #198754; background: #f0fff4; }
            .badge-incomplete { background: #dc3545; }
            .badge-complete { background: #198754; }
            .coord-count { font-family: monospace; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="debug-card">
                <h2><i class="fas fa-bug"></i> Debug - Análise Completa de Trajetos</h2>
                <p class="text-muted">Análise detalhada de todos os trajetos: manuais (coleção 'zonas') e GPS (coleção 'gestorMobile_trajetos')</p>
                
                <?php if ($totalTrajectories >= 100): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Paginação Ativa:</strong> Foram recuperados <?php echo $totalTrajectories; ?> trajetos usando paginação para garantir que todos os dados sejam analisados.
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?php echo $totalTrajectories; ?></h3>
                                <p class="card-text">Total de Trajetos</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo $totalWithCoordinates; ?></h3>
                                <p class="card-text">Com Coordenadas</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-danger"><?php echo $totalWithoutCoordinates; ?></h3>
                                <p class="card-text">Sem Coordenadas</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Breakdown by type -->
                <div class="debug-card">
                    <h3><i class="fas fa-chart-pie"></i> Distribuição por Tipo</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-map"></i> Trajetos Manuais (Zonas)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4 class="text-primary"><?php echo $totalManualRetrieved; ?></h4>
                                            <small>Total</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-success"><?php echo count($manualWithCoordinates); ?></h4>
                                            <small>Completos</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-danger"><?php echo count($manualWithoutCoordinates); ?></h4>
                                            <small>Incompletos</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-mobile-alt"></i> Trajetos GPS (Móvel)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4 class="text-primary"><?php echo $totalGpsRetrieved; ?></h4>
                                            <small>Total</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-success"><?php echo count($gpsWithCoordinates); ?></h4>
                                            <small>Completos</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-danger"><?php echo count($gpsWithoutCoordinates); ?></h4>
                                            <small>Incompletos</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if (count($manualWithoutCoordinates) > 0): ?>
            <div class="debug-card">
                <h3><i class="fas fa-exclamation-triangle text-danger"></i> Trajetos Incompletos (Sem Coordenadas)</h3>
                <p class="text-muted">Estes trajetos existem na base de dados mas não têm coordenadas válidas:</p>
                
                <?php foreach ($manualWithoutCoordinates as $index => $traj): ?>
                <div class="trajectory-item incomplete">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>
                                <span class="badge badge-incomplete">Incompleto</span>
                                <?php echo htmlspecialchars($traj['name'] ?? 'Nome não definido'); ?>
                            </h5>
                            <p><strong>Zone ID:</strong> <?php echo htmlspecialchars($traj['zoneId'] ?? 'N/A'); ?></p>
                            <p><strong>Descrição:</strong> <?php echo htmlspecialchars($traj['description'] ?? 'N/A'); ?></p>
                            <p><strong>Status:</strong> <?php echo htmlspecialchars($traj['status'] ?? 'N/A'); ?></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>Criado em:</strong> <?php echo htmlspecialchars($traj['createdAt'] ?? 'N/A'); ?></p>
                            <p><strong>Criado por:</strong> <?php echo htmlspecialchars($traj['createdByEmail'] ?? $traj['createdBy'] ?? 'N/A'); ?></p>
                            <p><strong>Coordenadas:</strong> 
                                <span class="coord-count text-danger">
                                    <?php 
                                    $coords = $traj['coordinates'] ?? null;
                                    if (is_array($coords)) {
                                        echo count($coords) . ' pontos';
                                    } else {
                                        echo 'Nenhuma';
                                    }
                                    ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            
            <div class="debug-card">
                <h3><i class="fas fa-check-circle text-success"></i> Trajetos Completos (Com Coordenadas)</h3>
                <p class="text-muted">Mostrando apenas os primeiros 5 trajetos completos:</p>
                
                <?php foreach (array_slice($manualWithCoordinates, 0, 5) as $index => $traj): ?>
                <div class="trajectory-item complete">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>
                                <span class="badge badge-complete">Completo</span>
                                <?php echo htmlspecialchars($traj['name'] ?? 'Nome não definido'); ?>
                            </h5>
                            <p><strong>Zone ID:</strong> <?php echo htmlspecialchars($traj['zoneId'] ?? 'N/A'); ?></p>
                            <p><strong>Distância:</strong> <?php echo htmlspecialchars($traj['distance'] ?? 'N/A'); ?></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>Criado em:</strong> <?php echo htmlspecialchars($traj['createdAt'] ?? 'N/A'); ?></p>
                            <p><strong>Coordenadas:</strong> 
                                <span class="coord-count text-success">
                                    <?php echo count($traj['coordinates'] ?? []); ?> pontos
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <?php if (count($manualWithCoordinates) > 5): ?>
                <p class="text-muted">... e mais <?php echo count($manualWithCoordinates) - 5; ?> trajetos completos.</p>
                <?php endif; ?>
            </div>
            
            <?php if (count($gpsWithoutCoordinates) > 0): ?>
            <div class="debug-card">
                <h3><i class="fas fa-exclamation-triangle text-danger"></i> Trajetos GPS Incompletos (Sem zoneId)</h3>
                <p class="text-muted">Estes trajetos GPS existem na base de dados mas não têm zoneId válido:</p>
                
                <?php foreach ($gpsWithoutCoordinates as $index => $traj): ?>
                <div class="trajectory-item incomplete">
                    <div class="row">
                        <div class="col-md-8">
                            <span class="badge badge-danger">Incompleto</span>
                            <strong>Trajeto GPS <?php echo isset($traj['id']) ? $traj['id'] : 'ID não definido'; ?></strong>
                            <?php if (isset($traj['createdAt'])): ?>
                            - <?php echo date('d/m/Y H:i', strtotime($traj['createdAt'])); ?>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-right">
                            <small class="text-muted">
                                <?php if (isset($traj['createdAt'])): ?>
                                Criado em: <?php echo date('Y-m-d\TH:i:s+P', strtotime($traj['createdAt'])); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <small><strong>Utilizador:</strong> <?php echo isset($traj['userId']) ? $traj['userId'] : 'N/A'; ?></small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-danger"><strong>Zona ID:</strong> <?php echo isset($traj['zoneId']) && !empty($traj['zoneId']) ? $traj['zoneId'] : 'AUSENTE (razão da incompletude)'; ?></small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            
            <div class="debug-card">
                <h3><i class="fas fa-info-circle"></i> Explicação</h3>
                <div class="alert alert-info">
                    <h5>Porque existe esta diferença?</h5>
                    <ul>
                        <li><strong>Total de Trajetos (<?php echo $totalTrajectories; ?>):</strong> Conta todos os documentos na coleção 'zonas' e 'gestorMobile_trajetos'</li>
                        <li><strong>Trajetos Completos (<?php echo $totalWithCoordinates; ?>):</strong> 
                            <ul>
                                <li>Trajetos manuais: devem ter coordenadas válidas</li>
                                <li>Trajetos GPS: devem ter zoneId válido (não verificam coordenadas)</li>
                            </ul>
                        </li>
                        <li><strong>Diferença (<?php echo $totalWithoutCoordinates; ?>):</strong> Trajetos incompletos/rascunhos</li>
                    </ul>
                    <p class="mb-0">
                        <strong>Nota:</strong> Os trajetos GPS são contados de forma diferente - são considerados "completos" 
                        se tiverem um zoneId válido, independentemente das coordenadas. Isto corresponde à lógica 
                        utilizada nas estatísticas principais.
                    </p>
                </div>
            </div>
            
            <!-- GPS Structure Debug -->
            <div class="debug-card">
                <h3><i class="fas fa-search"></i> Debug: Estrutura dos Trajetos GPS</h3>
                <p class="text-muted">Análise da estrutura dos primeiros trajetos GPS para entender como armazenam coordenadas:</p>
                
                <?php for ($i = 0; $i < min(3, count($gpsTrajectories)); $i++): ?>
                <div class="card mb-3">
                    <div class="card-header">
                        <strong>Trajeto GPS #<?php echo $i + 1; ?></strong>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Campos Disponíveis:</h6>
                                <code><?php echo implode(', ', array_keys($gpsTrajectories[$i])); ?></code>
                            </div>
                            <div class="col-md-6">
                                <h6>Análise de Coordenadas:</h6>
                                <?php if (isset($gpsTrajectories[$i]['coordinates'])): ?>
                                    <div><strong>Campo 'coordinates':</strong> 
                                        <span class="badge badge-info"><?php echo gettype($gpsTrajectories[$i]['coordinates']); ?></span>
                                        <?php if (is_array($gpsTrajectories[$i]['coordinates'])): ?>
                                            <span class="badge badge-secondary"><?php echo count($gpsTrajectories[$i]['coordinates']); ?> elementos</span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div><strong>Campo 'coordinates':</strong> <span class="badge badge-warning">Não existe</span></div>
                                <?php endif; ?>
                                
                                <?php if (isset($gpsTrajectories[$i]['trajeto'])): ?>
                                    <div><strong>Campo 'trajeto':</strong> 
                                        <span class="badge badge-info"><?php echo gettype($gpsTrajectories[$i]['trajeto']); ?></span>
                                        <?php if (is_array($gpsTrajectories[$i]['trajeto'])): ?>
                                            <span class="badge badge-secondary"><?php echo count($gpsTrajectories[$i]['trajeto']); ?> elementos</span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div><strong>Campo 'trajeto':</strong> <span class="badge badge-warning">Não existe</span></div>
                                <?php endif; ?>
                                
                                <?php 
                                // Check for other possible coordinate fields
                                $possibleCoordFields = ['route', 'path', 'points', 'locations', 'gpsPoints', 'trackPoints'];
                                foreach ($possibleCoordFields as $field):
                                    if (isset($gpsTrajectories[$i][$field])):
                                ?>
                                    <div><strong>Campo '<?php echo $field; ?>':</strong> 
                                        <span class="badge badge-success"><?php echo gettype($gpsTrajectories[$i][$field]); ?></span>
                                        <?php if (is_array($gpsTrajectories[$i][$field])): ?>
                                            <span class="badge badge-secondary"><?php echo count($gpsTrajectories[$i][$field]); ?> elementos</span>
                                        <?php endif; ?>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>
                        
                        <?php if (isset($gpsTrajectories[$i]['coordinates']) && is_array($gpsTrajectories[$i]['coordinates']) && !empty($gpsTrajectories[$i]['coordinates'])): ?>
                        <div class="row mt-2">
                            <div class="col-12">
                                <h6>Exemplo de Coordenada:</h6>
                                <code><?php echo json_encode($gpsTrajectories[$i]['coordinates'][0] ?? 'Vazio', JSON_PRETTY_PRINT); ?></code>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endfor; ?>
            </div>
            
            <!-- Zone Analysis -->
            <div class="debug-card">
                <h3><i class="fas fa-map-marked-alt"></i> Análise de Zonas vs Trajetos</h3>
                <p class="text-muted">Explicação da diferença entre número de trajetos e zonas com trajetos:</p>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?php echo $totalWithCoordinates; ?></h3>
                                <p class="card-text">Total de Trajetos Completos</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo $uniqueZonesWithTrajectories; ?></h3>
                                <p class="card-text">Zonas Únicas com Trajetos</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info"><?php echo $totalWithCoordinates - $uniqueZonesWithTrajectories; ?></h3>
                                <p class="card-text">Trajetos "Extra" (Múltiplos por Zona)</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (count($zonesWithMultipleTrajectories) > 0): ?>
                <div class="mt-4">
                    <h5><i class="fas fa-layer-group"></i> Zonas com Múltiplos Trajetos</h5>
                    <p class="text-muted">Estas zonas têm mais de um trajeto cada:</p>
                    
                    <div class="row">
                        <?php foreach ($zonesWithMultipleTrajectories as $zoneId => $count): ?>
                        <div class="col-md-6 mb-2">
                            <div class="alert alert-info mb-2">
                                <strong>Zona:</strong> <?php echo $zoneId; ?> 
                                <span class="badge badge-primary ml-2"><?php echo $count; ?> trajetos</span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="fas fa-lightbulb"></i> Explicação da Discrepância</h6>
                    <p class="mb-2">
                        <strong>Estatísticas de Trajetos:</strong> <?php echo $totalWithCoordinates; ?> trajetos 
                        (conta todos os documentos de trajeto)
                    </p>
                    <p class="mb-2">
                        <strong>Estatísticas de Zonas:</strong> <?php echo $uniqueZonesWithTrajectories; ?> zonas com trajetos 
                        (conta apenas zonas únicas que têm pelo menos 1 trajeto)
                    </p>
                    <p class="mb-0">
                        <strong>Diferença:</strong> <?php echo $totalWithCoordinates - $uniqueZonesWithTrajectories; ?> trajetos 
                        representam zonas que têm múltiplos trajetos ao longo do tempo.
                    </p>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    
} catch (Exception $e) {
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Debug - Erro</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body style="background: #f8f9fa; padding: 2rem;">
        <div class="container">
            <div class="alert alert-danger">
                <h4>Erro ao carregar dados</h4>
                <p><?php echo htmlspecialchars($e->getMessage()); ?></p>
                <p class="mb-0"><small>Certifique-se de que as credenciais do Firebase estão configuradas corretamente.</small></p>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
