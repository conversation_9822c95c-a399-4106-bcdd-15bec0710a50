<?php
// Public Statistics Dashboard - Password Protected
// This page shows aggregated statistics without sensitive information

// Set timezone to Portugal
date_default_timezone_set('Europe/Lisbon');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Password protection
session_start();
$correct_password = 'prorola2025';
$cookie_name = 'prorola_stats_auth';
$cookie_duration = 30 * 24 * 60 * 60; // 30 days

// Check if password is submitted
if (isset($_POST['password'])) {
    if ($_POST['password'] === $correct_password) {
        // Set session and cookie
        $_SESSION['authenticated'] = true;
        setcookie($cookie_name, hash('sha256', $correct_password), time() + $cookie_duration, '/', '', false, true);
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    } else {
        $error_message = 'Palavra-passe incorreta. Tente novamente.';
    }
}

// Check authentication (session or cookie)
$is_authenticated = false;
if (isset($_SESSION['authenticated']) && $_SESSION['authenticated']) {
    $is_authenticated = true;
} elseif (isset($_COOKIE[$cookie_name]) && $_COOKIE[$cookie_name] === hash('sha256', $correct_password)) {
    $_SESSION['authenticated'] = true;
    $is_authenticated = true;
}

// Show login form if not authenticated
if (!$is_authenticated) {
    ?>
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ProROLA - Acesso às Estatísticas</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            :root {
                --primary-color: #0a7ea4;
                --primary-dark: #086b8a;
            }
            
            body {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            
            .login-card {
                background: white;
                border-radius: 16px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                padding: 2.5rem;
                width: 100%;
                max-width: 400px;
                text-align: center;
            }
            
            .login-header {
                margin-bottom: 2rem;
            }
            
            .login-header h1 {
                color: var(--primary-color);
                font-size: 1.8rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            }
            
            .login-header p {
                color: #6b7280;
                font-size: 0.95rem;
            }
            
            .form-control {
                border-radius: 8px;
                border: 2px solid #e5e7eb;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
            }
            
            .form-control:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25);
            }
            
            .btn-primary {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
                border: none;
                border-radius: 8px;
                padding: 0.75rem 2rem;
                font-weight: 600;
                font-size: 1rem;
                transition: all 0.3s ease;
            }
            
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(10, 126, 164, 0.3);
            }
            
            .alert-danger {
                border-radius: 8px;
                border: none;
                background: #fef2f2;
                color: #dc2626;
                font-size: 0.9rem;
            }
            
            .login-icon {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
                color: white;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1.5rem;
                font-size: 1.5rem;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="login-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <div class="login-header">
                <h1>ProROLA</h1>
                <p>Acesso às Estatísticas Públicas</p>
            </div>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="password" class="form-label visually-hidden">Palavra-passe</label>
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="Introduza a palavra-passe" required autofocus>
                </div>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Entrar
                </button>
            </form>
            
            <div class="mt-4">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Acesso restrito às estatísticas do sistema ProROLA
                </small>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

try {

// Firebase configuration
define('FIREBASE_PROJECT_ID', 'prorola-a2f66');
define('FIREBASE_API_KEY', 'AIzaSyAIOTTQiP22chvCkD8q4EwTBnXVNAIa5is');

// Cache configuration
$cacheFile = __DIR__ . '/stats_cache.json';
$cacheExpiry = 60 * 60; // 1 hour in seconds

// Use cache by default (no test parameters)
$useCache = true;
$forceRefresh = false;

/**
 * Simple Firebase class for public statistics
 */
class PublicFirebase {
    private $projectId;
    private $adminToken;
    
    public function __construct($projectId) {
        $this->projectId = $projectId;
        $this->adminToken = $this->getAdminAccessToken();
    }
    
    private function getAdminAccessToken() {
        // Get Firebase service account credentials from environment variables
        $credentials = [
            'type' => $_ENV['FIREBASE_TYPE'] ?? getenv('FIREBASE_TYPE'),
            'project_id' => $_ENV['FIREBASE_PROJECT_ID'] ?? getenv('FIREBASE_PROJECT_ID'),
            'private_key_id' => $_ENV['FIREBASE_PRIVATE_KEY_ID'] ?? getenv('FIREBASE_PRIVATE_KEY_ID'),
            'private_key' => str_replace('\\n', "\n", $_ENV['FIREBASE_PRIVATE_KEY'] ?? getenv('FIREBASE_PRIVATE_KEY')),
            'client_email' => $_ENV['FIREBASE_CLIENT_EMAIL'] ?? getenv('FIREBASE_CLIENT_EMAIL'),
            'client_id' => $_ENV['FIREBASE_CLIENT_ID'] ?? getenv('FIREBASE_CLIENT_ID'),
            'auth_uri' => $_ENV['FIREBASE_AUTH_URI'] ?? getenv('FIREBASE_AUTH_URI'),
            'token_uri' => $_ENV['FIREBASE_TOKEN_URI'] ?? getenv('FIREBASE_TOKEN_URI'),
            'auth_provider_x509_cert_url' => $_ENV['FIREBASE_AUTH_PROVIDER_X509_CERT_URL'] ?? getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
            'client_x509_cert_url' => $_ENV['FIREBASE_CLIENT_X509_CERT_URL'] ?? getenv('FIREBASE_CLIENT_X509_CERT_URL')
        ];
        
        // Check if all required credentials are available
        if (empty($credentials['private_key']) || empty($credentials['client_email'])) {
            error_log("Firebase credentials not available in environment variables");
            return null;
        }
        
        try {
            $now = time();
            $payload = [
                'iss' => $credentials['client_email'],
                'sub' => $credentials['client_email'],
                'aud' => 'https://oauth2.googleapis.com/token',
                'iat' => $now,
                'exp' => $now + 3600,
                'scope' => 'https://www.googleapis.com/auth/cloud-platform'
            ];
            
            $jwt = $this->createJWT($payload, $credentials['private_key']);
            
            $ch = curl_init('https://oauth2.googleapis.com/token');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ]));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);
            
            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($status === 200) {
                $result = json_decode($response, true);
                return $result['access_token'] ?? null;
            }
            
            error_log("Failed to get admin access token: HTTP $status - $response");
            return null;
            
        } catch (Exception $e) {
            error_log("Exception getting admin access token: " . $e->getMessage());
            return null;
        }
    }
    
    private function createJWT($payload, $privateKey) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
        $payload = json_encode($payload);
        
        $base64Header = $this->base64UrlEncode($header);
        $base64Payload = $this->base64UrlEncode($payload);
        
        $signature = '';
        openssl_sign($base64Header . '.' . $base64Payload, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        $base64Signature = $this->base64UrlEncode($signature);
        
        return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
    }
    
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    public function getCollectionCount($collection) {
        if (!$this->adminToken) {
            return 0;
        }
        
        try {
            $count = 0;
            $pageToken = null;
            
            do {
                $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
                if ($pageToken) {
                    $url .= "?pageToken=" . urlencode($pageToken);
                }
                
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->adminToken
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                
                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($status !== 200) {
                    break;
                }
                
                $result = json_decode($response, true);
                if (isset($result['documents'])) {
                    $count += count($result['documents']);
                }
                
                $pageToken = $result['nextPageToken'] ?? null;
                
            } while ($pageToken);
            
            return $count;
            
        } catch (Exception $e) {
            error_log("Error getting collection count for $collection: " . $e->getMessage());
            return 0;
        }
    }
    
    public function getCollectionData($collection, $limit = null) {
        if (!$this->adminToken) {
            return [];
        }
        
        try {
            $documents = [];
            $pageToken = null;
            $fetched = 0;
            
            do {
                $url = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents/{$collection}";
                if ($pageToken) {
                    $url .= "?pageToken=" . urlencode($pageToken);
                }
                
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->adminToken
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                
                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($status !== 200) {
                    error_log("HTTP error $status for collection $collection");
                    break;
                }
                
                $result = json_decode($response, true);
                if (isset($result['documents'])) {
                    foreach ($result['documents'] as $doc) {
                        if ($limit && $fetched >= $limit) break 2;
                        $documents[] = $this->parseDocument($doc);
                        $fetched++;
                    }
                }
                
                $pageToken = $result['nextPageToken'] ?? null;
                
            } while ($pageToken && (!$limit || $fetched < $limit));
            
            error_log("Fetched $fetched documents from $collection collection");
            return $documents;
            
        } catch (Exception $e) {
            error_log("Error getting collection data for $collection: " . $e->getMessage());
            return [];
        }
    }
    
    private function parseDocument($doc) {
        if (!isset($doc['fields'])) {
            return [];
        }
        
        $result = [];
        foreach ($doc['fields'] as $key => $field) {
            $result[$key] = $this->convertFromFirestoreValue($field);
        }
        
        // Add document ID
        if (isset($doc['name'])) {
            $parts = explode('/', $doc['name']);
            $result['id'] = end($parts);
        }
        
        return $result;
    }
    
    private function convertFromFirestoreValue($field) {
        if (isset($field['stringValue'])) {
            return $field['stringValue'];
        } elseif (isset($field['integerValue'])) {
            return (int)$field['integerValue'];
        } elseif (isset($field['doubleValue'])) {
            return (float)$field['doubleValue'];
        } elseif (isset($field['booleanValue'])) {
            return $field['booleanValue'];
        } elseif (isset($field['timestampValue'])) {
            return $field['timestampValue'];
        } elseif (isset($field['nullValue'])) {
            return null;
        } elseif (isset($field['arrayValue'])) {
            $result = [];
            if (isset($field['arrayValue']['values'])) {
                foreach ($field['arrayValue']['values'] as $value) {
                    $result[] = $this->convertFromFirestoreValue($value);
                }
            }
            return $result;
        } elseif (isset($field['mapValue'])) {
            $result = [];
            if (isset($field['mapValue']['fields'])) {
                foreach ($field['mapValue']['fields'] as $key => $value) {
                    $result[$key] = $this->convertFromFirestoreValue($value);
                }
            }
            return $result;
        }
        
        return null;
    }
}

// Initialize Firebase
$firebase = new PublicFirebase(FIREBASE_PROJECT_ID);

// Cache functions
function isCacheValid($cacheFile, $cacheExpiry) {
    if (!file_exists($cacheFile)) {
        return false;
    }
    
    $cacheTime = filemtime($cacheFile);
    
    // Check if we're still within the same hour as the cache was created
    $cacheHour = date('H', $cacheTime);
    $currentHour = date('H');
    $cacheDate = date('Y-m-d', $cacheTime);
    $currentDate = date('Y-m-d');
    
    // Cache is valid if it's the same hour of the same day
    return ($cacheDate === $currentDate && $cacheHour === $currentHour);
}

function loadCache($cacheFile) {
    if (!file_exists($cacheFile)) {
        return null;
    }
    
    $cacheData = file_get_contents($cacheFile);
    return json_decode($cacheData, true);
}

function saveCache($cacheFile, $data) {
    $cacheData = [
        'timestamp' => time(),
        'data' => $data
    ];
    
    file_put_contents($cacheFile, json_encode($cacheData, JSON_PRETTY_PRINT));
}

// Initialize empty stats structure for template
$stats = [
    'users' => [
        'total' => 0,
        'technicians' => 0,
        'colaboradores' => 0,
        'hunt_managers' => 0,
        'administrators' => 0
    ],
    'trajectories' => [
        'total' => 0,
        'manual' => 0,
        'gps' => 0,
        'total_distance' => 0,
        'distance_distribution' => []
    ],
    'contacts' => [
        'total' => 0,
        'manual' => 0,
        'mobile' => 0,
        'by_circumstance' => [],
        'by_location' => []
    ],
    'hunting_zones' => [
        'total' => 0,
        'registered' => 0,
        'with_trajectories' => 0,
        'total_quota' => 0
    ],
    'reports' => [
        'total' => 0,
        'with_contacts' => 0,
        'total_report_contacts' => 0,
        'avg_contacts_per_report' => 0,
        'by_role' => [],
        'by_location' => []
    ]
];

// Cache status for display (will be updated via AJAX)
$cacheUsed = false;








?>

<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProROLA - Estatísticas</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #0a7ea4;
            --primary-light: #1e90a4;
            --primary-dark: #086b8a;
            --secondary-color: #f8f9fa;
            --text-color: #374151;
            --border-color: #e5e7eb;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            min-height: 100vh;
        }
        
        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0891b2 50%, #06b6d4 100%);
            color: white;
            padding: 1.5rem 0;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(10, 126, 164, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(10px);
        }
        
        .header-section .container {
            position: relative;
            z-index: 2;
        }
        
        .header-logo-container {
            background: white;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        
        .header-logo-container:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
        }
        
        .header-logo {
            height: 75px;
            width: auto;
            object-fit: contain;
        }
        
        .header-section h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: -0.02em;
        }
        
        .header-section .subtitle {
            font-size: 1.2rem;
            opacity: 0.95;
            font-weight: 400;
            margin-bottom: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .last-updated {
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .header-section .btn-light {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: var(--primary-color);
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .header-section .btn-light:hover {
            background: white;
            color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid var(--border-color);
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .stats-card h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6b7280;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            height: 300px;
        }
        
        .chart-title {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-align: center;
        }
        
        .chart-wrapper {
            height: 250px;
            position: relative;
        }
        
        .export-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }
        
        .export-btn {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.2s ease;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .export-btn:hover {
            background: var(--primary-dark);
            color: white;
            text-decoration: none;
        }
        
        .footer {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 2rem;
        }
        
        /* Print styles */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            body { 
                background: white !important;
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif !important;
                line-height: 1.4 !important;
            }
            
            .header-section,
            .footer {
                display: none !important;
            }
            
            .container {
                max-width: none !important;
                margin: 0 !important;
                padding: 15px !important;
            }
            
            .stats-card {
                background: white !important;
                border: 2px solid #0a7ea4 !important;
                border-radius: 8px !important;
                box-shadow: none !important;
                margin-bottom: 25px !important;
                padding: 20px !important;
                page-break-inside: avoid;
            }
            
            .stats-card h3 {
                color: #0a7ea4 !important;
                font-size: 20px !important;
                margin-bottom: 15px !important;
                border-bottom: 1px solid #0a7ea4 !important;
                padding-bottom: 8px !important;
            }
            
            .stats-card h3 i {
                margin-right: 8px !important;
            }
            
            .metric-grid {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
                gap: 15px !important;
                margin-top: 15px !important;
            }
            
            .metric-item {
                text-align: center !important;
                padding: 12px !important;
                border: 1px solid #e5e7eb !important;
                border-radius: 6px !important;
                background: #f8f9fa !important;
            }
            
            .metric-value {
                color: #0a7ea4 !important;
                font-size: 24px !important;
                font-weight: bold !important;
                display: block !important;
                margin-bottom: 5px !important;
            }
            
            .metric-label {
                color: #374151 !important;
                font-size: 12px !important;
                font-weight: 500 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
            }
            
            .chart-section {
                display: block !important;
                margin-top: 20px !important;
                padding: 15px !important;
                border: 1px solid #e5e7eb !important;
                border-radius: 6px !important;
                background: #f8f9fa !important;
            }
            
            .chart-wrapper,
            .chart-wrapper-inline {
                display: none !important;
            }
            
            .chart-stats {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
                gap: 10px !important;
                margin-top: 10px !important;
            }
            
            .chart-stat-item {
                text-align: center !important;
                padding: 8px !important;
                background: white !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
            }
            
            .chart-stat-label {
                font-size: 11px !important;
                color: #666 !important;
                margin-bottom: 3px !important;
            }
            
            .chart-stat-value {
                font-size: 16px !important;
                font-weight: bold !important;
                color: #0a7ea4 !important;
            }
            
            .chart-section-title {
                color: #0a7ea4 !important;
                font-size: 16px !important;
                font-weight: 600 !important;
                margin-bottom: 8px !important;
            }
            
            .chart-details {
                color: #666 !important;
                font-size: 11px !important;
                margin-bottom: 10px !important;
            }
            
            .print-header {
                display: block !important;
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 3px solid #0a7ea4;
                padding-bottom: 20px;
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
                padding: 20px !important;
                border-radius: 8px !important;
            }
            
            .print-header h1 {
                color: #0a7ea4 !important;
                font-size: 28px !important;
                margin: 0 !important;
                font-weight: bold !important;
            }
            
            .print-header .subtitle {
                color: #374151 !important;
                font-size: 14px !important;
                margin: 5px 0 !important;
            }
            
            .text-muted {
                color: #666 !important;
            }
            
            .row {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 15px !important;
                margin-top: 15px !important;
            }
            
            .col-md-6 {
                width: 100% !important;
            }
        }
        
        .print-header {
            display: none;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .metric-item {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-color);
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.85rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .chart-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.25rem;
            border: 1px solid #e9ecef;
            margin-top: 1rem;
        }
        
        .chart-header {
            margin-bottom: 1rem;
        }
        
        .chart-section-title {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
        }
        
        .chart-details {
            margin-bottom: 0.75rem;
        }
        
        .chart-wrapper-inline {
            height: 200px;
            position: relative;
            margin-bottom: 1rem;
        }
        
        .chart-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            padding-top: 0.75rem;
            border-top: 1px solid #e9ecef;
        }
        
        .chart-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 100px;
        }
        
        .chart-stat-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }
        
        .chart-stat-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        /* Chart stat highlight */
        .chart-stat-highlight {
            background: rgba(10, 126, 164, 0.1);
            border-radius: 6px;
            padding: 0.5rem;
            border: 1px solid rgba(10, 126, 164, 0.2);
        }
        
        .last-updated {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: 12px;
            font-size: 0.95rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .last-updated:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .last-updated i {
            margin-right: 0.5rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .header-section {
                padding: 1.5rem 0;
            }
            
            .header-logo-container {
                width: 60px;
                height: 60px;
            }
            
            .header-logo {
                height: 40px;
            }
            
            .header-section h1 {
                font-size: 2.2rem;
            }
            
            .header-section .subtitle {
                font-size: 1rem;
            }
            
            .last-updated {
                font-size: 0.85rem;
                padding: 0.5rem 1rem;
                margin-top: 1rem;
            }
            
            .stats-number {
                font-size: 2rem;
            }
            
            .metric-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
        
        @media (max-width: 576px) {
            .header-section {
                padding: 1.25rem 0;
            }
            
            .header-section h1 {
                font-size: 1.8rem;
            }
            
            .header-section .subtitle {
                font-size: 0.9rem;
            }
            
            .d-flex.align-items-center {
                flex-direction: column;
                text-align: center;
            }
            
            .header-logo-container {
                margin-bottom: 1rem !important;
                margin-right: 0 !important;
            }
            
            .last-updated {
                width: 100%;
                margin-top: 1.5rem;
            }
        }
        
        /* Loading Spinner Styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a7ea4 0%, #1e90a4 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        .loading-text {
            color: #ffffff;
            font-size: 1.2rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .loading-subtext {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            text-align: center;
        }
        
        .loading-logo {
            width: 100px;
            height: 100px;
            margin-bottom: 30px;
            background: white;
            border-radius: 50%;
            padding: 1px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Zone Lists - Modern Professional Style */
        .zone-list-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            height: 100%;
            overflow: hidden;
        }
        
        .zone-list-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }
        
        .zone-list-header {
            background: linear-gradient(135deg, #0a7ea4 0%, #086b8a 100%);
            padding: 1rem 1.25rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .zone-list-title {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
        }
        
        .zone-list-header .export-btn {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            font-size: 0.875rem;
            margin-left: auto;
            flex-shrink: 0;
        }
        
        .zone-list-header .export-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .zone-list-header .export-btn i {
            opacity: 0.9;
            margin-right: 0.375rem;
        }
        
        .zone-list-title i {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .zone-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 700;
            min-width: 2rem;
            text-align: center;
        }
        
        .zone-list-container {
            max-height: 350px;
            overflow-y: auto;
            overflow-x: hidden;
            background: white;
        }
        
        .zone-list-container::-webkit-scrollbar {
            width: 6px;
        }
        
        .zone-list-container::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .zone-list-container::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .zone-list-container::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        .zone-list-loading {
            padding: 3rem;
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .zone-list-empty {
            padding: 3rem;
            text-align: center;
            color: #64748b;
            font-style: italic;
            font-size: 0.9rem;
        }
        
        .zone-list-item {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .zone-list-item:last-child {
            border-bottom: none;
        }
        
        .zone-list-item:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding-left: 1.5rem;
        }
        
        .zone-list-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: #0a7ea4;
            transform: scaleY(0);
            transition: transform 0.2s ease;
        }
        
        .zone-list-item:hover::before {
            transform: scaleY(1);
        }
        
        .zone-number {
            font-weight: 700;
            color: #0a7ea4;
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }
        
        .zone-name {
            font-weight: 500;
            color: #1e293b;
            margin: 0.25rem 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .zone-email {
            color: #64748b;
            font-size: 0.8rem;
            font-family: 'Courier New', monospace;
            background: #f8fafc;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
            margin-top: 0.25rem;
            word-break: break-all;
        }
        
        .zone-trajectory-type {
            color: #0a7ea4;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            background: #e6f3f7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }
        
        .zone-trajectory-type i {
            color: #059669;
            margin-right: 0.25rem;
        }
        
        .zone-export-section {
            padding: 0.75rem 1.25rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }
        
        .export-btn-new {
            background: linear-gradient(135deg, #0a7ea4 0%, #086b8a 100%);
            color: white;
            border: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            font-size: 0.875rem;
        }
        
        .export-btn-new:hover {
            background: linear-gradient(135deg, #086b8a 0%, #065a73 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3);
        }
        
        /* Responsive design for zone lists */
        @media (max-width: 768px) {
            .zone-list-card {
                margin-bottom: 1rem;
            }
            
            .zone-list-container {
                max-height: 300px;
            }
            
            .zone-list-item {
                padding: 0.75rem 1rem;
            }
            
            .zone-list-title {
                font-size: 0.9rem;
            }
            
            .zone-count {
                font-size: 0.8rem;
                padding: 0.2rem 0.6rem;
            }
        }

        /* Hide main content initially */
        .main-content {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }
        
        .main-content.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <img src="prorola-logo.svg" alt="ProROLA" class="loading-logo">
        <div class="loading-spinner"></div>
        <div class="loading-text">A carregar estatísticas...</div>
        <div class="loading-subtext">Por favor aguarde enquanto os dados são processados</div>
    </div>

    <!-- Main Content -->
    <div id="mainContent" class="main-content">
        <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="header-logo-container me-4">
                            <img src="prorola-logo.svg" alt="ProROLA" class="header-logo">
                        </div>
                        <div>
                            <h1 class="mb-1">ProROLA</h1>
                            <p class="subtitle mb-0">Estatísticas</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex flex-column align-items-end gap-2">
                        <!-- Export buttons (hidden for now) -->
                        <div class="d-flex gap-2" style="display: none !important;">
                            <button onclick="printStatistics()" class="btn btn-light btn-sm"
                                    data-bs-toggle="tooltip" data-bs-placement="bottom" 
                                    title="Imprimir estatísticas">
                                <i class="fas fa-print me-1"></i>Imprimir
                            </button>
                            <button onclick="generatePDF()" class="btn btn-light btn-sm" 
                               data-bs-toggle="tooltip" data-bs-placement="bottom" 
                               title="Gerar e descarregar PDF das estatísticas">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                            <a href="export.php?type=excel" class="btn btn-light btn-sm"
                               data-bs-toggle="tooltip" data-bs-placement="bottom" 
                               title="Descarregar estatísticas em formato Excel">
                                <i class="fas fa-file-excel me-1"></i>Excel
                            </a>
                        </div>
                        <!-- Update info -->
                        <div class="last-updated">
                            <i class="fas fa-clock me-1"></i>
                            <?php 
                            if (file_exists($cacheFile)) {
                                $lastUpdate = filemtime($cacheFile);
                                
                                // Calculate the top of the next hour
                                $nextUpdate = strtotime(date('Y-m-d H:00:00', strtotime('+1 hour')));

                                echo 'Atualizado: ' . date('d/m/Y H:i', $lastUpdate);
                                echo '<br><small class="text-light opacity-75">';
                                echo '<i class="fas fa-sync-alt me-1"></i>';
                                echo 'Próxima atualização: ' . date('H:00', $nextUpdate);
                                echo '</small>';
                            } else {
                                echo 'Atualizado: ' . date('d/m/Y H:i');
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Print Header (only visible when printing) -->
        <div class="print-header">
            <h1>ProROLA - Estatísticas Públicas</h1>
            <div class="subtitle">Sistema de Monitorização de Rola Brava</div>
            <div class="subtitle">Impresso em: <span id="print-date"></span></div>
        </div>

        <!-- Hunting Zone Statistics -->
        <div class="stats-card">
            <h3><i class="fas fa-binoculars"></i>Estatísticas de Zonas de Caça</h3>
            <p class="text-muted mb-3" style="font-size: 0.9rem;">
                <i class="fas fa-info-circle me-1"></i>
                Zonas de caça registadas no sistema, incluindo quotas e atividade de trajetos
            </p>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="zoneTotal">0</div>
                    <div class="metric-label">Total de Zonas</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="zoneRegistered">0</div>
                    <div class="metric-label">Zonas Registadas</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="zoneWithTrajectories">0</div>
                    <div class="metric-label">Com Trajetos</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="zoneTotalQuota">0</div>
                    <div class="metric-label">Quota Total</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="zoneManagers">0</div>
                    <div class="metric-label">Gestores</div>
                </div>
            </div>
            
            <!-- Zone Lists -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="zone-list-card">
                        <div class="zone-list-header">
                            <h5 class="zone-list-title">
                                <i class="fas fa-check-circle me-2"></i>
                                Zonas com Trajetos
                                <span class="zone-count" id="registeredWithTrajectoriesCount">0</span>
                            </h5>
                        </div>
                        <div class="zone-export-section">
                            <a href="export_zones.php?type=zones_with_trajectories" class="btn btn-sm export-btn-new" 
                               data-bs-toggle="tooltip" data-bs-placement="top" 
                               title="Exportar lista de zonas com trajetos para Excel">
                                <i class="fas fa-download me-1"></i>Exportar Excel
                            </a>
                        </div>
                        <div class="zone-list-container" id="registeredWithTrajectories">
                            <div class="zone-list-loading">
                                <i class="fas fa-spinner fa-spin"></i> Carregando...
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="zone-list-card">
                        <div class="zone-list-header">
                            <h5 class="zone-list-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Zonas sem Trajetos
                                <span class="zone-count" id="registeredWithoutTrajectoriesCount">0</span>
                            </h5>
                        </div>
                        <div class="zone-export-section">
                            <a href="export_zones.php?type=zones_without_trajectories" class="btn btn-sm export-btn-new" 
                               data-bs-toggle="tooltip" data-bs-placement="top" 
                               title="Exportar lista de zonas sem trajetos para Excel">
                                <i class="fas fa-download me-1"></i>Exportar Excel
                            </a>
                        </div>
                        <div class="zone-list-container" id="registeredWithoutTrajectories">
                            <div class="zone-list-loading">
                                <i class="fas fa-spinner fa-spin"></i> Carregando...
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="zone-list-card">
                        <div class="zone-list-header">
                            <h5 class="zone-list-title">
                                <i class="fas fa-times-circle me-2"></i>
                                Zonas Não Registadas
                                <span class="zone-count" id="unregisteredZonesCount">0</span>
                            </h5>
                        </div>
                        <div class="zone-export-section">
                            <a href="export_zones.php?type=zones_unregistered" class="btn btn-sm export-btn-new" 
                               data-bs-toggle="tooltip" data-bs-placement="top" 
                               title="Exportar lista de zonas não registadas para Excel">
                                <i class="fas fa-download me-1"></i>Exportar Excel
                            </a>
                        </div>
                        <div class="zone-list-container" id="unregisteredZones">
                            <div class="zone-list-loading">
                                <i class="fas fa-spinner fa-spin"></i> Carregando...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Zone Status Charts -->
            <div class="stats-card mt-4">
                <h3><i class="fas fa-chart-pie"></i>Análise de Status das Zonas</h3>
                <p class="text-muted mb-4">
                    <i class="fas fa-info-circle me-1"></i>
                    Distribuição percentual do status das zonas registadas e não registadas
                </p>
                
                <div class="row">
                    <!-- Registered Zones Status Chart -->
                    <div class="col-md-6">
                        <div class="chart-section">
                            <div class="chart-header">
                                <h5 class="chart-section-title">
                                    <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Zonas Registadas
                                </h5>
                                <div class="chart-details">
                                    <small class="text-muted">
                                        Distribuição entre zonas com e sem trajetos
                                    </small>
                                </div>
                            </div>
                            <div class="chart-wrapper-inline">
                                <canvas id="registeredZonesChart"></canvas>
                            </div>
                            <div class="chart-stats">
                                <div class="chart-stat-item">
                                    <span class="chart-stat-label">Com Trajetos:</span>
                                    <span class="chart-stat-value" id="registeredWithTrajPercent">0%</span>
                                </div>
                                <div class="chart-stat-item">
                                    <span class="chart-stat-label">Sem Trajetos:</span>
                                    <span class="chart-stat-value" id="registeredWithoutTrajPercent">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Total Zones Status Chart -->
                    <div class="col-md-6">
                        <div class="chart-section">
                            <div class="chart-header">
                                <h5 class="chart-section-title">
                                    <i class="fas fa-map-marker-alt me-2" style="color: #0a7ea4;"></i>Todas as Zonas
                                </h5>
                                <div class="chart-details">
                                    <small class="text-muted">
                                        Distribuição geral por status de registo
                                    </small>
                                </div>
                            </div>
                            <div class="chart-wrapper-inline">
                                <canvas id="allZonesChart"></canvas>
                            </div>
                            <div class="chart-stats">
                                <div class="chart-stat-item">
                                    <span class="chart-stat-label">Registadas:</span>
                                    <span class="chart-stat-value" id="allZonesRegisteredPercent">0%</span>
                                </div>
                                <div class="chart-stat-item">
                                    <span class="chart-stat-label">Não Registadas:</span>
                                    <span class="chart-stat-value" id="allZonesUnregisteredPercent">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <!-- Zone Activity Chart (Expanded) -->
                <div class="col-12">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-pie me-2"></i>Atividade das Zonas
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Distribuição entre zonas com e sem trajetos
                                </small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-wrapper-inline">
                                    <canvas id="zoneActivityChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-stats">
                                    <div class="chart-stat-item">
                                        <i class="fas fa-check-circle text-success me-1"></i>
                                        <span class="chart-stat-label">Com Trajetos:</span>
                                        <span class="chart-stat-value" id="zoneWithTrajPercent">0%</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <i class="fas fa-times-circle text-muted me-1"></i>
                                        <span class="chart-stat-label">Sem Trajetos:</span>
                                        <span class="chart-stat-value" id="zoneWithoutTrajPercent">0%</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <i class="fas fa-clipboard-list me-1" style="color: #0a7ea4;"></i>
                                        <span class="chart-stat-label">Taxa de Registo:</span>
                                        <span class="chart-stat-value" id="zone-registration-rate">0%</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <i class="fas fa-chart-line me-1" style="color: #0a7ea4;"></i>
                                        <span class="chart-stat-label">Taxa Utilização:</span>
                                        <span class="chart-stat-value" id="zoneUtilizationRate">0%</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <i class="fas fa-map-marked-alt me-1" style="color: #0a7ea4;"></i>
                                        <span class="chart-stat-label">Zonas Ativas:</span>
                                        <span class="chart-stat-value" id="zoneActiveCount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Activity Comparison Chart -->
            <div class="chart-section mt-4">
                <div class="chart-header">
                    <h5 class="chart-section-title">
                        <i class="fas fa-chart-area me-2"></i>Comparação de Atividade
                    </h5>
                    <div class="chart-details">
                        <small class="text-muted">
                                                                Relação entre zonas registadas, trajetos e contactos de rola-brava
                        </small>
                    </div>
                </div>
                <div class="chart-wrapper-inline">
                    <canvas id="activityComparisonChart"></canvas>
                </div>
                <div class="chart-stats">
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Zonas/Trajetos:</span>
                        <span class="chart-stat-value" id="activityZoneTrajectoryRatio">0</span>
                    </div>
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Contactos/Trajeto:</span>
                        <span class="chart-stat-value" id="activityContactTrajectoryRatio">0</span>
                    </div>
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Eficiência:</span>
                        <span class="chart-stat-value" id="activityEfficiency">0%</span>
                    </div>
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Quota Média:</span>
                        <span class="chart-stat-value" id="zone-avg-quota">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trajectory Statistics -->
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="mb-0"><i class="fas fa-route"></i>Estatísticas de Trajetos</h3>
                <a href="mapa_trajetos.php" target="_blank" class="btn" style="background-color: #0a7ea4; color: white; border: none; padding: 0.5rem 1rem; font-weight: 500; border-radius: 6px; text-decoration: none; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#086b8a'; this.style.transform='translateY(-1px)'" onmouseout="this.style.backgroundColor='#0a7ea4'; this.style.transform='translateY(0)'">
                    <i class="fas fa-map-marked-alt me-2"></i>Ver Mapa de Trajetos
                </a>
            </div>
            <p class="text-muted mb-3" style="font-size: 0.9rem;">
                <i class="fas fa-info-circle me-1"></i>
                Trajetos criados pelos gestores, incluindo manuais (web) e GPS (móvel)
            </p>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="trajectoryTotal">0</div>
                    <div class="metric-label">Total de Trajetos</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="trajectoryManual">0</div>
                    <div class="metric-label">Trajetos Manuais</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="trajectoryGps">0</div>
                    <div class="metric-label">Trajetos GPS</div>
                </div>

                <div class="metric-item">
                    <div class="metric-value" id="trajectoryDistance">0.0</div>
                    <div class="metric-label">Distância Total (km)</div>
                </div>
            </div>
            
            <div class="row mt-4">
                <!-- Trajectory Types Chart -->
                <div class="col-md-6">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-pie me-2"></i>Tipos de Trajetos
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Distribuição entre trajetos manuais e GPS
                                </small>
                            </div>
                        </div>
                        <div class="chart-wrapper-inline">
                            <canvas id="trajectoryTypesChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Manuais:</span>
                                <span class="chart-stat-value" id="trajectoryManualPercent">0%</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">GPS:</span>
                                <span class="chart-stat-value" id="trajectoryGpsPercent">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Distance Distribution Chart -->
                <div class="col-md-6">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-bar me-2"></i>Distribuição de Distâncias
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Análise das distâncias percorridas por faixa de quilómetros
                                </small>
                            </div>
                        </div>
                        <div class="chart-wrapper-inline">
                            <canvas id="distanceDistributionChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Distância Média:</span>
                                <span class="chart-stat-value" id="trajectoryAvgDistance">0 km</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Distância Total:</span>
                                <span class="chart-stat-value" id="trajectoryTotalDistance">0.0 km</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Statistics -->
        <div class="stats-card">
            <h3><i class="fas fa-dove"></i>Estatísticas de Contactos</h3>
            <p class="text-muted mb-3" style="font-size: 0.9rem;">
                <i class="fas fa-info-circle me-1"></i>
                Contactos de Rolas Bravas registados durante os trajetos e monitorização
            </p>

            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="contactTotal">0</div>
                    <div class="metric-label">Total de Contactos</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="contactManual">0</div>
                    <div class="metric-label">Contactos Manuais</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="contactMobile">0</div>
                    <div class="metric-label">Contactos Móveis</div>
                </div>
            </div>
            
            <div class="row mt-4">
                <!-- Contact Circumstances Chart -->
                <div class="col-md-6">
                    <div class="chart-section">
                        <div class="chart-header">
                                                <h5 class="chart-section-title">
                        <i class="fas fa-chart-pie me-2"></i>Circunstâncias dos Contactos
                    </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Distribuição dos contactos por tipo de circunstância
                                </small>
                            </div>
                        </div>
                        <div class="chart-wrapper-inline">
                            <canvas id="circumstancesChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Mais Comum:</span>
                                <span class="chart-stat-value" id="circumstanceMostCommon">N/A</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Tipos Diferentes:</span>
                                <span class="chart-stat-value" id="circumstanceTypes">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Locations Chart -->
                <div class="col-md-6">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-doughnut me-2"></i>Locais de Avistamento
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Distribuição dos contactos por tipo de habitat
                                </small>
                            </div>
                        </div>
                        <div class="chart-wrapper-inline">
                            <canvas id="locationsChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Mais Comum:</span>
                                <span class="chart-stat-value" id="locationMostCommon">N/A</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Locais Diferentes:</span>
                                <span class="chart-stat-value" id="locationTypes">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Statistics -->
        <div class="stats-card">
            <h3><i class="fas fa-file-alt"></i>Estatísticas de Relatórios</h3>
            <p class="text-muted mb-3" style="font-size: 0.9rem;">
                <i class="fas fa-info-circle me-1"></i>
                Relatórios de monitorização submetidos pelos técnicos ProROLA
            </p>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="reportTotal">0</div>
                    <div class="metric-label">Total de Relatórios</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="reportWithContacts">0</div>
                    <div class="metric-label">Com Contactos</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="reportTotalContacts">0</div>
                    <div class="metric-label">Total de Contactos</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="reportAvgContacts">0</div>
                    <div class="metric-label">Média Contactos/Relatório</div>
                </div>
            </div>
            
            <div class="row mt-4">
                <!-- Monthly Reports Trend Chart -->
                <div class="col-md-6">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-line me-2"></i>Tendência Mensal
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Evolução dos relatórios submetidos ao longo do tempo
                                </small>
                            </div>
                        </div>
                        <div class="chart-wrapper-inline">
                            <canvas id="monthlyReportsChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Mês Mais Ativo:</span>
                                <span class="chart-stat-value" id="reportMostActiveMonth">N/A</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Média Mensal:</span>
                                <span class="chart-stat-value" id="reportMonthlyAverage">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Wildlife Occurrence Chart -->
                <div class="col-md-6">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-pie me-2"></i>Ocorrência de Rola-Brava
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Distribuição entre relatórios com e sem presença de rola-brava
                                </small>
                            </div>
                        </div>
                        <div class="chart-wrapper-inline">
                            <canvas id="reportQualityChart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Com Presença:</span>
                                <span class="chart-stat-value" id="reportWithContactsPercent">0%</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Sem Presença:</span>
                                <span class="chart-stat-value" id="reportWithoutContactsPercent">0%</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-label">Taxa de Ocorrência:</span>
                                <span class="chart-stat-value" id="reportOverallEfficiency">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Productivity Analysis (Expanded) -->
            <div class="row mt-4">
                <!-- Contact Productivity Chart (Full Width) -->
                <div class="col-12">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h5 class="chart-section-title">
                                <i class="fas fa-chart-area me-2"></i>Produtividade de Contactos
                            </h5>
                            <div class="chart-details">
                                <small class="text-muted">
                                    Análise detalhada da eficiência na detecção de rola-brava por técnico
                                </small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="chart-wrapper-inline">
                                    <canvas id="contactProductivityChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="chart-stats">
                                    <div class="chart-stat-item">
                                        <span class="chart-stat-label">Contactos/Técnico:</span>
                                        <span class="chart-stat-value" id="reportContactsPerTechnician">0</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <span class="chart-stat-label">Relatórios/Técnico:</span>
                                        <span class="chart-stat-value" id="reportReportsPerTechnician">0</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <span class="chart-stat-label">Taxa de Sucesso:</span>
                                        <span class="chart-stat-value" id="reportSuccessRate">0%</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <span class="chart-stat-label">Eficácia:</span>
                                        <span class="chart-stat-value" id="reportEfficiency">Baixa</span>
                                    </div>
                                    <div class="chart-stat-item">
                                        <span class="chart-stat-label">Média de Contactos:</span>
                                        <span class="chart-stat-value" id="reportAvgContacts">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="stats-card">
            <h3><i class="fas fa-users"></i>Estatísticas de Utilizadores</h3>
            <p class="text-muted mb-3" style="font-size: 0.9rem;">
                <i class="fas fa-info-circle me-1"></i>
                Utilizadores registados na plataforma ProROLA por tipo e estado de aprovação
            </p>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="userTotal">0</div>
                    <div class="metric-label">Total de Utilizadores</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="userTechnicians">0</div>
                    <div class="metric-label">Técnicos</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="userColaboradores">0</div>
                    <div class="metric-label">Colaboradores</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="userHuntManagers">0</div>
                    <div class="metric-label">Gestores de Zona</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="userAdministrators">0</div>
                    <div class="metric-label">Administradores</div>
                </div>
            </div>
            
            <!-- User Distribution Chart -->
            <div class="chart-section mt-4">
                <div class="chart-header">
                    <h5 class="chart-section-title">
                        <i class="fas fa-chart-bar me-2"></i>Distribuição por Tipo
                    </h5>
                    <div class="chart-details">
                        <small class="text-muted">
                            Número de utilizadores por categoria de acesso
                        </small>
                    </div>
                </div>
                <div class="chart-wrapper" style="height: 280px;">
                    <canvas id="userTypesChart"></canvas>
                </div>
                <div class="chart-stats">
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Técnicos:</span>
                        <span class="chart-stat-value" id="userTechPercent">0%</span>
                    </div>
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Colaboradores:</span>
                        <span class="chart-stat-value" id="userColabPercent">0%</span>
                    </div>
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Gestores:</span>
                        <span class="chart-stat-value" id="userHuntPercent">0%</span>
                    </div>
                    <div class="chart-stat-item">
                        <span class="chart-stat-label">Administradores:</span>
                        <span class="chart-stat-value" id="userAdminPercent">0%</span>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <p class="mb-0">
                <span>ProROLA © <?php echo date("Y"); ?> Todos os direitos reservados ICNF</span>
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Charts -->
    <script>
        // Chart.js configuration
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.color = '#374151';

        // OLD PHP CHARTS COMPLETELY REMOVED - USING AJAX CHARTS ONLY
        
        // Loading Management and AJAX Data Loading
        let statsData = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            loadStatisticsData();
        });
        
        function loadStatisticsData() {
            
            // First test if API is working
            fetch('api.php?test=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // API is working, load real data directly
                        loadRealStatisticsData();
                    } else {
                        throw new Error('API test failed');
                    }
                })
                .catch(error => {
                    console.error('API test error:', error);
                    showError('API não está acessível: ' + error.message);
                });
        }
        
        function loadRealStatisticsData() {
            
            let apiUrl = 'api.php';
            
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text(); // Get as text first to see raw response
                })
                .then(text => {
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            statsData = data.data;
                            
                            updatePageContent(statsData);
                            showMainContent();
                            // Initialize charts after main content is shown
                            setTimeout(() => {
                                initializeCharts(statsData);
                            }, 100);
                        } else {
                            console.error('API returned error:', data);
                            showError('Erro ao carregar dados: ' + (data.message || data.error || 'Erro desconhecido'));
                        }
                    } catch (parseError) {
                        console.error('JSON Parse Error:', parseError);
                        console.error('Raw response:', text);
                        showError('Erro ao processar resposta do servidor. Verifique o console para mais detalhes.');
                    }
                })
                .catch(error => {
                    console.error('Error loading statistics:', error);
                    showError('Erro de conexão: ' + error.message);
                });
        }
        
        function updatePageContent(stats) {
            // Update user statistics
            updateElement('userTotal', stats.users.total);
            updateElement('userTechnicians', stats.users.technicians);
            updateElement('userColaboradores', stats.users.colaboradores);
            updateElement('userHuntManagers', stats.users.hunt_managers);
            updateElement('userAdministrators', stats.users.administrators);
            
            // Update hunting zone statistics
            updateElement('zoneTotal', stats.hunting_zones.total);
            updateElement('zoneRegistered', stats.hunting_zones.registered);
            updateElement('zoneWithTrajectories', stats.hunting_zones.with_trajectories);
            updateElement('zoneTotalQuota', stats.hunting_zones.total_quota);
            updateElement('zoneManagers', stats.users.hunt_managers);
            
            // Update zone chart data for print
            const registrationRate = stats.hunting_zones.total > 0 ? ((stats.hunting_zones.registered / stats.hunting_zones.total) * 100).toFixed(1) : 0;
            const avgQuota = stats.hunting_zones.total > 0 ? (stats.hunting_zones.total_quota / stats.hunting_zones.total).toFixed(1) : 0;
            updateElement('zone-registration-rate', registrationRate + '%');
            updateElement('zone-avg-quota', avgQuota);
            
            // Update zone lists
            const registeredWithTrajectories = stats.hunting_zones.registered_with_trajectories || [];
            const registeredWithoutTrajectories = stats.hunting_zones.registered_without_trajectories || [];
            const unregisteredZones = stats.hunting_zones.unregistered || [];
            
            updateZoneList('registeredWithTrajectories', registeredWithTrajectories);
            updateZoneList('registeredWithoutTrajectories', registeredWithoutTrajectories);
            updateZoneList('unregisteredZones', unregisteredZones);
            
            // Update zone count badges
            updateElement('registeredWithTrajectoriesCount', registeredWithTrajectories.length);
            updateElement('registeredWithoutTrajectoriesCount', registeredWithoutTrajectories.length);
            updateElement('unregisteredZonesCount', unregisteredZones.length);
            
            // Calculate and update zone status percentages
            const registeredTotal = stats.hunting_zones.registered;
            const zonesWithTrajectories = stats.hunting_zones.with_trajectories;
            const registeredWithoutTraj = registeredWithoutTrajectories.length;
            const totalZones = stats.hunting_zones.total;
            const unregisteredTotal = unregisteredZones.length;
            
            // Registered zones breakdown
            const registeredWithTrajPercent = registeredTotal > 0 ? ((zonesWithTrajectories / registeredTotal) * 100).toFixed(1) : 0;
            const registeredWithoutTrajPercent = registeredTotal > 0 ? ((registeredWithoutTraj / registeredTotal) * 100).toFixed(1) : 0;
            
            // All zones breakdown
            const allZonesRegisteredPercent = totalZones > 0 ? ((registeredTotal / totalZones) * 100).toFixed(1) : 0;
            const allZonesUnregisteredPercent = totalZones > 0 ? ((unregisteredTotal / totalZones) * 100).toFixed(1) : 0;
            
            // Update percentage displays
            updateElement('registeredWithTrajPercent', registeredWithTrajPercent + '%');
            updateElement('registeredWithoutTrajPercent', registeredWithoutTrajPercent + '%');
            updateElement('allZonesRegisteredPercent', allZonesRegisteredPercent + '%');
            updateElement('allZonesUnregisteredPercent', allZonesUnregisteredPercent + '%');
            
            // Update zone status charts
            updateZoneStatusCharts(stats, registeredWithoutTraj, unregisteredTotal);
            
            // Update trajectory statistics
            updateElement('trajectoryTotal', stats.trajectories.total);
            updateElement('trajectoryManual', stats.trajectories.manual);
            updateElement('trajectoryGps', stats.trajectories.gps);
            updateElement('trajectoryDistance', stats.trajectories.total_distance);
            
            // Update contact statistics
            updateElement('contactTotal', stats.contacts.total);
            updateElement('contactManual', stats.contacts.manual);
            updateElement('contactMobile', stats.contacts.mobile);
            
            // Update report statistics
            updateElement('reportTotal', stats.reports.total);
            updateElement('reportWithContacts', stats.reports.with_contacts);
            updateElement('reportTotalContacts', stats.reports.total_report_contacts);
            updateElement('reportAvgContacts', stats.reports.avg_contacts_per_report);
            
            // Update contact chart statistics
            if (stats.contacts.by_circumstance && Object.keys(stats.contacts.by_circumstance).length > 0) {
                const circumstances = Object.entries(stats.contacts.by_circumstance)
                    .sort((a, b) => b[1] - a[1]); // Sort by count descending
                updateElement('circumstanceMostCommon', translateCircumstance(circumstances[0][0]));
                updateElement('circumstanceTypes', circumstances.length);
            }
            
            if (stats.contacts.by_location && Object.keys(stats.contacts.by_location).length > 0) {
                const locations = Object.entries(stats.contacts.by_location)
                    .sort((a, b) => b[1] - a[1]); // Sort by count descending
                updateElement('locationMostCommon', translateLocation(locations[0][0]));
                updateElement('locationTypes', locations.length);
            }
            
            // Update percentages
            updatePercentage('userTechPercent', stats.users.technicians, stats.users.total);
            updatePercentage('userColabPercent', stats.users.colaboradores, stats.users.total);
            updatePercentage('userHuntPercent', stats.users.hunt_managers, stats.users.total);
            updatePercentage('userAdminPercent', stats.users.administrators, stats.users.total);
            
            updatePercentage('zoneWithTrajPercent', stats.hunting_zones.with_trajectories, stats.hunting_zones.registered);
            updatePercentage('zoneWithoutTrajPercent', (stats.hunting_zones.registered - stats.hunting_zones.with_trajectories), stats.hunting_zones.registered);
            updatePercentage('zoneUtilizationRate', stats.hunting_zones.with_trajectories, stats.hunting_zones.registered);
            
            updatePercentage('trajectoryManualPercent', stats.trajectories.manual, stats.trajectories.total);
            updatePercentage('trajectoryGpsPercent', stats.trajectories.gps, stats.trajectories.total);
            
            // Update additional zone and trajectory statistics
            updateElement('zoneActiveCount', stats.hunting_zones.with_trajectories);
            
            // Update distance calculations
            const avgDistance = stats.trajectories.total > 0 ? (stats.trajectories.total_distance / stats.trajectories.total).toFixed(1) : 0;
            updateElement('trajectoryAvgDistance', avgDistance + ' km');
            updateElement('trajectoryTotalDistance', stats.trajectories.total_distance.toFixed(1) + ' km');
            
            // Update activity comparison ratios
            const zoneTrajectoryRatio = stats.hunting_zones.registered > 0 ? (stats.trajectories.total / stats.hunting_zones.registered).toFixed(1) : 0;
            const contactTrajectoryRatio = stats.trajectories.total > 0 ? (stats.contacts.total / stats.trajectories.total).toFixed(1) : 0;
            const efficiency = stats.hunting_zones.registered > 0 ? ((stats.contacts.total / stats.hunting_zones.registered) * 10).toFixed(1) : 0;
            
            updateElement('activityZoneTrajectoryRatio', zoneTrajectoryRatio);
            updateElement('activityContactTrajectoryRatio', contactTrajectoryRatio);
            updateElement('activityEfficiency', efficiency + '%');
            
            // Update report statistics
            if (stats.reports.monthly_reports && Object.keys(stats.reports.monthly_reports).length > 0) {
                const monthlyData = stats.reports.monthly_reports;
                const months = Object.keys(monthlyData);
                const values = Object.values(monthlyData);
                
                // Find most active month
                const maxValue = Math.max(...values);
                const maxMonth = months[values.indexOf(maxValue)];
                const formattedMonth = new Date(maxMonth + '-01').toLocaleDateString('pt-PT', { month: 'short', year: '2-digit' });
                updateElement('reportMostActiveMonth', formattedMonth);
                
                // Calculate monthly average
                const monthlyAverage = (values.reduce((a, b) => a + b, 0) / values.length).toFixed(1);
                updateElement('reportMonthlyAverage', monthlyAverage);
            }
            
            // Update report quality statistics
            const reportsWithContactsPercent = stats.reports.total > 0 ? ((stats.reports.with_contacts / stats.reports.total) * 100).toFixed(1) : 0;
            const reportsWithoutContactsPercent = stats.reports.total > 0 ? (((stats.reports.total - stats.reports.with_contacts) / stats.reports.total) * 100).toFixed(1) : 0;
            const overallEfficiency = reportsWithContactsPercent;
            
            updateElement('reportWithContactsPercent', reportsWithContactsPercent + '%');
            updateElement('reportWithoutContactsPercent', reportsWithoutContactsPercent + '%');
            updateElement('reportOverallEfficiency', overallEfficiency + '%');
            
            // Update report success rate and efficiency
            const successRate = stats.reports.total > 0 ? ((stats.reports.with_contacts / stats.reports.total) * 100).toFixed(1) : 0;
            updateElement('reportSuccessRate', successRate + '%');
            
            let efficiency_text = 'Baixa';
            if (successRate >= 80) efficiency_text = 'Excelente';
            else if (successRate >= 60) efficiency_text = 'Boa';
            else if (successRate >= 40) efficiency_text = 'Moderada';
            updateElement('reportEfficiency', efficiency_text);
            
            // Update productivity statistics
            const contactsPerTechnician = stats.users.technicians > 0 ? (stats.reports.total_report_contacts / stats.users.technicians).toFixed(1) : 0;
            const reportsPerTechnician = stats.users.technicians > 0 ? (stats.reports.total / stats.users.technicians).toFixed(1) : 0;
            updateElement('reportContactsPerTechnician', contactsPerTechnician);
            updateElement('reportReportsPerTechnician', reportsPerTechnician);
        }
        
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = typeof value === 'number' ? value.toLocaleString() : value;
            } else {
                console.warn(`Element with id '${id}' not found`);
            }
        }
        
        function updatePercentage(id, value, total) {
            const element = document.getElementById(id);
            if (element) {
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                element.textContent = percentage + '%';
            } else {
                console.warn(`Percentage element with id '${id}' not found`);
            }
        }
        
        function updateZoneList(containerId, zones) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.warn(`Zone list container with id '${containerId}' not found`);
                return;
            }
            
            if (zones.length === 0) {
                container.innerHTML = '<div class="zone-list-empty"><i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Nenhuma zona encontrada</div>';
                return;
            }
            
            let html = '';
            zones.forEach(zone => {
                html += `
                    <div class="zone-list-item">
                        <div class="zone-number">Zona ${zone.zona}</div>
                        <div class="zone-name">${zone.nomeZona}</div>
                        <div class="zone-email">${zone.email}</div>
                        ${zone.trajectoryType ? `<div class="zone-trajectory-type"><i class="fas fa-route me-1"></i>${zone.trajectoryType}</div>` : ''}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function updateZoneStatusCharts(stats, registeredWithoutTraj, unregisteredTotal) {
            const registeredTotal = stats.hunting_zones.registered;
            const zonesWithTrajectories = stats.hunting_zones.with_trajectories;
            const totalZones = stats.hunting_zones.total;
            
            // Registered Zones Chart (Com/Sem Trajetos)
            if (chartInstances.registeredZones) {
                chartInstances.registeredZones.destroy();
            }
            
            chartInstances.registeredZones = createChart('registeredZonesChart', {
                type: 'doughnut',
                data: {
                    labels: ['Com Trajetos', 'Sem Trajetos'],
                    datasets: [{
                        data: [zonesWithTrajectories, registeredWithoutTraj],
                        backgroundColor: ['#10b981', '#f59e0b'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
            
            // All Zones Chart (Registadas/Não Registadas)
            if (chartInstances.allZones) {
                chartInstances.allZones.destroy();
            }
            
            chartInstances.allZones = createChart('allZonesChart', {
                type: 'doughnut',
                data: {
                    labels: ['Registadas', 'Não Registadas'],
                    datasets: [{
                        data: [registeredTotal, unregisteredTotal],
                        backgroundColor: ['#0a7ea4', '#ef4444'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }
        
        function showMainContent() {
            const mainContent = document.getElementById('mainContent');
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            mainContent.classList.add('show');
            loadingOverlay.classList.add('fade-out');
            
            // Remove loading overlay after transition
            setTimeout(function() {
                loadingOverlay.style.display = 'none';
            }, 500);
        }
        
        function showError(message) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.innerHTML = `
                <img src="prorola-logo.svg" alt="ProROLA" class="loading-logo">
                <div style="color: #ff4444; font-size: 1.2rem; font-weight: 500; text-align: center; margin-bottom: 10px;">
                    <i class="fas fa-exclamation-triangle"></i> Erro
                </div>
                <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem; text-align: center; margin-bottom: 20px;">
                    ${message}
                </div>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button onclick="location.reload()" style="background: white; color: #0a7ea4; border: none; padding: 10px 20px; border-radius: 5px; font-weight: 500; cursor: pointer;">
                        Tentar Novamente
                    </button>
                </div>
            `;
        }
        
        function printStatistics() {
            // Set the print date
            const printDateElement = document.getElementById('print-date');
            if (printDateElement) {
                printDateElement.textContent = new Date().toLocaleDateString('pt-PT', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // Trigger print dialog
            window.print();
        }
        
        // Global chart storage
        let chartInstances = {};

        function destroyExistingCharts() {
            for (let chartId in chartInstances) {
                if (chartInstances[chartId]) {
                    chartInstances[chartId].destroy();
                    delete chartInstances[chartId];
                }
            }
        }

        // Helper function to safely create charts
        function createChart(canvasId, chartConfig) {
            try {
                const element = document.getElementById(canvasId);
                if (!element) {
                    console.warn(`Canvas element '${canvasId}' not found, skipping chart`);
                    return null;
                }
                const ctx = element.getContext('2d');
                if (!ctx) {
                    console.warn(`Could not get 2D context for '${canvasId}', skipping chart`);
                    return null;
                }
                return new Chart(ctx, chartConfig);
            } catch (error) {
                console.error(`Error creating chart '${canvasId}':`, error);
                return null;
            }
        }

        // Translation functions for database values
        function translateCircumstance(circumstance) {
            const translations = {
                'flying': 'Rola em voo',
                'adultsinging': 'Adulto cantando',
                'adultSinging': 'Adulto cantando',
                'adultperched': 'Adulto pousado',
                'adultPerched': 'Adulto pousado',
                'groupsof2individuals': 'Grupos de 2 indivíduos',
                'groupsOf2individuals': 'Grupos de 2 indivíduos',
                'groupsof3individuals': 'Grupos de 3 indivíduos',
                'groupsOf3individuals': 'Grupos de 3 indivíduos',
                'groupsof4individuals': 'Grupos de 4 indivíduos',
                'groupsOf4individuals': 'Grupos de 4 indivíduos',
                'groupsof5individuals': 'Grupos de 5 indivíduos',
                'groupsOf5individuals': 'Grupos de 5 indivíduos',
                'groupsof6individuals': 'Grupos de 6 indivíduos',
                'groupsOf6individuals': 'Grupos de 6 indivíduos',
                'groupsof7individuals': 'Grupos de 7 indivíduos',
                'groupsOf7individuals': 'Grupos de 7 indivíduos',
                'groupsof8individuals': 'Grupos de 8 indivíduos',
                'groupsOf8individuals': 'Grupos de 8 indivíduos',
                'groupsof9individuals': 'Grupos de 9 indivíduos',
                'groupsOf9individuals': 'Grupos de 9 indivíduos',
                'groupsof10individuals': 'Grupos de 10 indivíduos',
                'groupsOf10individuals': 'Grupos de 10 indivíduos'
            };
            return translations[circumstance] || circumstance;
        }

        function translateLocation(location) {
            const translations = {
                'tree': 'Árvore',
                'trees': 'Árvores',
                'shrub': 'Arbusto',
                'waterPoint': 'Ponto de água',
                'clearing': 'Clareira',
                'no ar': 'No ar',
                'Comedouro': 'Comedouro',
                'Restolho': 'Restolho',
                'Pinhal': 'Pinhal'
            };
            return translations[location] || location;
        }

        function initializeCharts(stats) {
            
            // Destroy existing charts first
            destroyExistingCharts();
            
            // User Types Horizontal Bar Chart
            chartInstances.userTypes = createChart('userTypesChart', {
                type: 'bar',
                data: {
                    labels: ['Gestores de Zona', 'Colaboradores', 'Técnicos', 'Administradores'],
                    datasets: [{
                        label: 'Número de Utilizadores',
                        data: [
                            stats.users.hunt_managers,
                            stats.users.colaboradores,
                            stats.users.technicians,
                            stats.users.administrators
                        ],
                        backgroundColor: [
                            '#0a7ea4',
                            '#059669', 
                            '#1e90a4',
                            '#086b8a'
                        ],
                        borderColor: [
                            '#0a7ea4',
                            '#059669',
                            '#1e90a4', 
                            '#086b8a'
                        ],
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                font: {
                                    size: 11
                                }
                            },
                            grid: {
                                color: 'rgba(10, 126, 164, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                },
                                color: '#374151'
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Zone Activity Chart
            chartInstances.zoneActivity = createChart('zoneActivityChart', {
                type: 'doughnut',
                data: {
                    labels: ['Com Trajetos', 'Sem Trajetos'],
                    datasets: [{
                        data: [
                            stats.hunting_zones.with_trajectories,
                            stats.hunting_zones.registered - stats.hunting_zones.with_trajectories
                        ],
                        backgroundColor: ['#0a7ea4', '#e5e7eb'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Create zone status charts
            updateZoneStatusCharts(stats, stats.hunting_zones.registered - stats.hunting_zones.with_trajectories, stats.hunting_zones.total - stats.hunting_zones.registered);

            // Activity Comparison Chart
            chartInstances.activityComparison = createChart('activityComparisonChart', {
                type: 'bar',
                data: {
                    labels: [
                        'Zonas Registadas',
                        'Zonas com Trajetos', 
                        'Trajetos Totais',
                        'Contactos de Rola-Brava',
                        'Relatórios Submetidos'
                    ],
                    datasets: [{
                        label: 'Quantidade',
                        data: [
                            stats.hunting_zones.registered || 0,
                            stats.hunting_zones.with_trajectories || 0,
                            stats.trajectories.total || 0,
                            stats.contacts.total || 0,
                            stats.reports.total || 0
                        ],
                        backgroundColor: [
                            '#0a7ea4',
                            '#059669',
                            '#1e90a4',
                            '#086b8a',
                            '#0891b2'
                        ],
                        borderRadius: 6,
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed.y}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 0,
                                minRotation: 0
                            }
                        }
                    }
                }
            });

            // Trajectory Types Chart
            chartInstances.trajectoryTypes = createChart('trajectoryTypesChart', {
                type: 'doughnut',
                data: {
                    labels: ['Trajetos Manuais', 'Trajetos GPS'],
                    datasets: [{
                        data: [stats.trajectories.manual, stats.trajectories.gps],
                        backgroundColor: ['#0a7ea4', '#059669'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Distance Distribution Chart
            chartInstances.distanceDistribution = createChart('distanceDistributionChart', {
                type: 'bar',
                data: {
                    labels: ['0-2 km', '2-5 km', '5-10 km', '10-20 km', '20+ km'],
                    datasets: [{
                        label: 'Número de Trajetos',
                        data: [
                            stats.trajectories.distance_distribution['0-2km'] || 0,
                            stats.trajectories.distance_distribution['2-5km'] || 0,
                            stats.trajectories.distance_distribution['5-10km'] || 0,
                            stats.trajectories.distance_distribution['10km+'] || 0,
                            0 // 20+ km placeholder
                        ],
                        backgroundColor: '#0a7ea4',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Contact Circumstances Chart
            if (stats.contacts.by_circumstance && Object.keys(stats.contacts.by_circumstance).length > 0) {
                const circumstanceData = Object.entries(stats.contacts.by_circumstance)
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 8); // Top 8 circumstances

                const totalCircumstances = circumstanceData.reduce((sum, item) => sum + item[1], 0);

                chartInstances.circumstances = createChart('circumstancesChart', {
                    type: 'bar',
                    data: {
                        labels: circumstanceData.map(item => translateCircumstance(item[0])),
                        datasets: [{
                            label: 'Contactos',
                            data: circumstanceData.map(item => item[1]),
                            backgroundColor: [
                                '#0a7ea4', '#059669', '#1e90a4', '#086b8a',
                                '#0891b2', '#0d9488', '#0284c7', '#0369a1'
                            ],
                            borderRadius: 4,
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.parsed.x;
                                        const percentage = ((value / totalCircumstances) * 100).toFixed(1);
                                        return `${value} contactos (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            },
                            y: {
                                ticks: {
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Contact Locations Chart
            if (stats.contacts.by_location && Object.keys(stats.contacts.by_location).length > 0) {
                const locationData = Object.entries(stats.contacts.by_location)
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 8); // Top 8 locations

                const totalLocations = locationData.reduce((sum, item) => sum + item[1], 0);

                chartInstances.locations = createChart('locationsChart', {
                    type: 'bar',
                    data: {
                        labels: locationData.map(item => translateLocation(item[0])),
                        datasets: [{
                            label: 'Contactos',
                            data: locationData.map(item => item[1]),
                            backgroundColor: [
                                '#0a7ea4', '#059669', '#1e90a4', '#086b8a',
                                '#0891b2', '#0d9488', '#0284c7', '#0369a1'
                            ],
                            borderRadius: 4,
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.parsed.x;
                                        const percentage = ((value / totalLocations) * 100).toFixed(1);
                                        return `${value} contactos (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            },
                            y: {
                                ticks: {
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Monthly Reports Chart
            if (stats.reports.monthly_reports && Object.keys(stats.reports.monthly_reports).length > 0) {
                const monthlyLabels = Object.keys(stats.reports.monthly_reports);
                const monthlyData = Object.values(stats.reports.monthly_reports);
                
                chartInstances.monthlyReports = createChart('monthlyReportsChart', {
                    type: 'line',
                    data: {
                        labels: monthlyLabels.map(month => {
                            const date = new Date(month + '-01');
                            return date.toLocaleDateString('pt-PT', { month: 'short', year: '2-digit' });
                        }),
                        datasets: [{
                            label: 'Relatórios',
                            data: monthlyData,
                            borderColor: '#0a7ea4',
                            backgroundColor: 'rgba(10, 126, 164, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#0a7ea4',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }

            // Report Quality Chart
            const totalReports = stats.reports.total;
            const reportsWithContacts = stats.reports.with_contacts;
            const reportsWithoutContacts = totalReports - reportsWithContacts;
            const qualityRate = totalReports > 0 ? (reportsWithContacts / totalReports) * 100 : 0;

            chartInstances.reportQuality = createChart('reportQualityChart', {
                type: 'doughnut',
                data: {
                    labels: ['Com Presença', 'Sem Presença'],
                    datasets: [{
                        data: [reportsWithContacts, reportsWithoutContacts],
                        backgroundColor: [
                            '#059669', // Green for reports with contacts
                            '#f3f4f6'  // Light gray for reports without contacts
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const percentage = totalReports > 0 ? ((value / totalReports) * 100).toFixed(1) : 0;
                                    return `${context.label}: ${value} relatórios (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });



            // Contact Productivity Chart
            chartInstances.contactProductivity = createChart('contactProductivityChart', {
                type: 'bar',
                data: {
                    labels: ['Contactos/Técnico', 'Relatórios/Técnico'],
                    datasets: [{
                        label: 'Produtividade',
                        data: [
                            stats.users.technicians > 0 ? Math.round(stats.contacts.total / stats.users.technicians) : 0,
                            stats.users.technicians > 0 ? Math.round(stats.reports.total / stats.users.technicians) : 0
                        ],
                        backgroundColor: ['#0a7ea4', '#059669'],
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('pt-PT').format(num);
        }
        
        function generatePDF() {
            if (!statsData) {
                alert('Dados ainda não carregados. Aguarde um momento e tente novamente.');
                return;
            }
            
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Colors
                const primaryColor = [10, 126, 164]; // #0a7ea4
                const secondaryColor = [5, 150, 105]; // #059669
                const textColor = [55, 65, 81]; // #374151
                const lightGray = [229, 231, 235]; // #e5e7eb
                
                // Helper functions
                function drawHeader() {
                    // Background gradient effect (simplified)
                    doc.setFillColor(240, 249, 255);
                    doc.rect(0, 0, 210, 45, 'F');
                    
                    // Main title
                    doc.setFont("helvetica", "bold");
                    doc.setFontSize(24);
                    doc.setTextColor(...primaryColor);
                    doc.text('ProROLA', 105, 20, { align: 'center' });
                    
                    doc.setFont("helvetica", "normal");
                    doc.setFontSize(16);
                    doc.setTextColor(...textColor);
                    doc.text('Estatísticas Públicas', 105, 28, { align: 'center' });
                    
                    doc.setFontSize(10);
                    doc.text('Sistema de Monitorização de Rola Brava', 105, 34, { align: 'center' });
                    doc.text('Gerado em: ' + new Date().toLocaleDateString('pt-PT', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    }), 105, 40, { align: 'center' });
                    
                    // Bottom border
                    doc.setDrawColor(...primaryColor);
                    doc.setLineWidth(1);
                    doc.line(20, 45, 190, 45);
                }
                
                function drawStatCard(title, metrics, yPos) {
                    const metricsCount = Object.keys(metrics).length;
                    const cardHeight = 25 + (Math.ceil(metricsCount / 2) * 8);
                    
                    // Card background
                    doc.setFillColor(248, 250, 252);
                    doc.setDrawColor(203, 213, 225);
                    doc.setLineWidth(0.5);
                    doc.roundedRect(15, yPos, 180, cardHeight, 2, 2, 'FD');
                    
                    // Title section
                    doc.setFillColor(...primaryColor);
                    doc.roundedRect(15, yPos, 180, 12, 2, 2, 'F');
                    doc.rect(15, yPos + 10, 180, 2, 'F'); // Remove bottom rounded corners
                    
                    // Title
                    doc.setFont("helvetica", "bold");
                    doc.setFontSize(11);
                    doc.setTextColor(255, 255, 255);
                    doc.text(title, 20, yPos + 8);
                    
                    // Metrics in grid
                    doc.setFont("helvetica", "normal");
                    doc.setFontSize(9);
                    doc.setTextColor(55, 65, 81);
                    
                    let currentY = yPos + 20;
                    let isLeft = true;
                    const leftCol = 20;
                    const rightCol = 105;
                    
                    for (const [key, value] of Object.entries(metrics)) {
                        const x = isLeft ? leftCol : rightCol;
                        
                        // Metric text
                        doc.setFont("helvetica", "normal");
                        doc.text(key + ':', x, currentY);
                        doc.setFont("helvetica", "bold");
                        doc.setTextColor(...primaryColor);
                        doc.text(value.toString(), x + 50, currentY);
                        doc.setTextColor(55, 65, 81);
                        
                        if (!isLeft) {
                            currentY += 8;
                        }
                        isLeft = !isLeft;
                    }
                    
                    return yPos + cardHeight + 8;
                }
                

                
                function drawChart(x, y, width, height, data, title) {
                    // Chart background
                    doc.setFillColor(255, 255, 255);
                    doc.setDrawColor(203, 213, 225);
                    doc.setLineWidth(0.5);
                    doc.rect(x, y, width, height, 'FD');
                    
                    // Title
                    doc.setFont("helvetica", "bold");
                    doc.setFontSize(9);
                    doc.setTextColor(...primaryColor);
                    doc.text(title, x + width/2, y - 4, { align: 'center' });
                    
                    // Simple bar chart
                    const maxValue = Math.max(...Object.values(data));
                    if (maxValue === 0) return; // Skip empty charts
                    
                    const barWidth = (width - 20) / Object.keys(data).length;
                    let currentX = x + 10;
                    
                    Object.entries(data).forEach(([label, value], index) => {
                        const barHeight = Math.max(2, (value / maxValue) * (height - 25));
                        const barY = y + height - 15 - barHeight;
                        
                        // Bar
                        const colors = [primaryColor, secondaryColor, [220, 38, 127], [245, 101, 101]];
                        doc.setFillColor(...colors[index % colors.length]);
                        doc.rect(currentX, barY, barWidth - 3, barHeight, 'F');
                        
                        // Value on top
                        doc.setFont("helvetica", "bold");
                        doc.setFontSize(7);
                        doc.setTextColor(55, 65, 81);
                        doc.text(formatNumber(value), currentX + (barWidth - 3)/2, barY - 1, { align: 'center' });
                        
                        // Label at bottom
                        doc.setFont("helvetica", "normal");
                        doc.setFontSize(6);
                        const shortLabel = label.length > 8 ? label.substring(0, 6) + '..' : label;
                        doc.text(shortLabel, currentX + (barWidth - 3)/2, y + height - 3, { align: 'center' });
                        
                        currentX += barWidth;
                    });
                }
                
                // Start PDF generation
                drawHeader();
                let yPos = 55;
                
                // Page 1: Zones and Trajectories
                yPos = drawStatCard('Estatísticas de Zonas de Caça', {
                    'Total de Zonas': formatNumber(statsData.hunting_zones.total),
                    'Zonas Registadas': formatNumber(statsData.hunting_zones.registered),
                    'Com Trajetos': formatNumber(statsData.hunting_zones.with_trajectories),
                    'Quota Total': formatNumber(statsData.hunting_zones.total_quota),
                    'Taxa de Registo': ((statsData.hunting_zones.registered / Math.max(statsData.hunting_zones.total, 1)) * 100).toFixed(1) + '%',
                    'Quota Média': (statsData.hunting_zones.total_quota / Math.max(statsData.hunting_zones.total, 1)).toFixed(1)
                }, yPos);
                
                // Zone activity chart
                drawChart(20, yPos, 170, 25, {
                    'Registadas': statsData.hunting_zones.registered,
                    'Com Trajetos': statsData.hunting_zones.with_trajectories,
                    'Sem Trajetos': statsData.hunting_zones.registered - statsData.hunting_zones.with_trajectories
                }, 'Atividade das Zonas');
                
                yPos += 35;
                
                // Zone status charts
                const registeredWithoutTraj = statsData.hunting_zones.registered - statsData.hunting_zones.with_trajectories;
                const unregisteredTotal = statsData.hunting_zones.total - statsData.hunting_zones.registered;
                
                drawChart(20, yPos, 80, 25, {
                    'Com Trajetos': statsData.hunting_zones.with_trajectories,
                    'Sem Trajetos': registeredWithoutTraj
                }, 'Zonas Registadas');
                
                drawChart(110, yPos, 80, 25, {
                    'Registadas': statsData.hunting_zones.registered,
                    'Não Registadas': unregisteredTotal
                }, 'Todas as Zonas');
                
                yPos += 35;
                
                yPos = drawStatCard('Estatísticas de Trajetos', {
                    'Total de Trajetos': formatNumber(statsData.trajectories.total),
                    'Trajetos Manuais': formatNumber(statsData.trajectories.manual),
                    'Trajetos GPS': formatNumber(statsData.trajectories.gps),
                    'Distância Total': statsData.trajectories.total_distance.toFixed(1) + ' km',
                    'Distância Média': (statsData.trajectories.total_distance / Math.max(statsData.trajectories.total, 1)).toFixed(1) + ' km',
                    'Percentagem Manual': ((statsData.trajectories.manual / Math.max(statsData.trajectories.total, 1)) * 100).toFixed(1) + '%'
                }, yPos);
                
                // Trajectory chart
                drawChart(20, yPos, 170, 25, {
                    'Manuais': statsData.trajectories.manual,
                    'GPS': statsData.trajectories.gps
                }, 'Tipos de Trajetos');
                
                yPos += 35;
                
                // New page for contacts and reports
                doc.addPage();
                drawHeader();
                yPos = 55;
                
                yPos = drawStatCard('Estatísticas de Contactos', {
                    'Total de Contactos': formatNumber(statsData.contacts.total),
                    'Contactos Manuais': formatNumber(statsData.contacts.manual),
                    'Contactos Móveis': formatNumber(statsData.contacts.mobile),
                    'Contactos por Trajeto': (statsData.contacts.total / Math.max(statsData.trajectories.total, 1)).toFixed(1),
                    'Percentagem Manual': ((statsData.contacts.manual / Math.max(statsData.contacts.total, 1)) * 100).toFixed(1) + '%',
                    'Percentagem Móvel': ((statsData.contacts.mobile / Math.max(statsData.contacts.total, 1)) * 100).toFixed(1) + '%'
                }, yPos);
                
                // Contact chart
                drawChart(20, yPos, 170, 25, {
                    'Manuais': statsData.contacts.manual,
                    'Móveis': statsData.contacts.mobile
                }, 'Distribuição de Contactos');
                
                yPos += 35;
                
                yPos = drawStatCard('Estatísticas de Relatórios', {
                    'Total de Relatórios': formatNumber(statsData.reports.total),
                    'Com Contactos': formatNumber(statsData.reports.with_contacts),
                    'Total de Contactos': formatNumber(statsData.reports.total_report_contacts),
                    'Média Contactos/Relatório': statsData.reports.avg_contacts_per_report.toFixed(1),
                    'Taxa de Conclusão': ((statsData.reports.with_contacts / Math.max(statsData.reports.total, 1)) * 100).toFixed(1) + '%',
                    'Sem Contactos': formatNumber(statsData.reports.total - statsData.reports.with_contacts)
                }, yPos);
                
                // Reports chart
                drawChart(20, yPos, 170, 25, {
                    'Com Contactos': statsData.reports.with_contacts,
                    'Sem Contactos': statsData.reports.total - statsData.reports.with_contacts
                }, 'Distribuição de Relatórios');
                
                yPos += 35;
                
                yPos = drawStatCard('Estatísticas de Utilizadores', {
                    'Total de Utilizadores': formatNumber(statsData.users.total),
                    'Técnicos': formatNumber(statsData.users.technicians),
                    'Colaboradores': formatNumber(statsData.users.colaboradores),
                    'Gestores de Zona': formatNumber(statsData.users.hunt_managers),
                    'Administradores': formatNumber(statsData.users.administrators),
                    'Percentagem Técnicos': ((statsData.users.technicians / Math.max(statsData.users.total, 1)) * 100).toFixed(1) + '%'
                }, yPos);
                
                // Users chart
                drawChart(20, yPos, 170, 25, {
                    'Técnicos': statsData.users.technicians,
                    'Colaboradores': statsData.users.colaboradores,
                    'Gestores': statsData.users.hunt_managers,
                    'Admins': statsData.users.administrators
                }, 'Distribuição de Utilizadores');
                
                // Footer
                doc.setFont("helvetica", "bold");
                doc.setFontSize(10);
                doc.setTextColor(...primaryColor);
                doc.text('ProROLA © ' + new Date().getFullYear(), 105, 280, { align: 'center' });
                doc.setFont("helvetica", "normal");
                doc.setFontSize(8);
                doc.setTextColor(...textColor);
                doc.text('Sistema de Monitorização de Rola Brava', 105, 285, { align: 'center' });
                
                // Save the PDF
                const filename = 'ProROLA_Estatisticas_' + new Date().toISOString().slice(0, 10).replace(/-/g, '-') + '_' + 
                                new Date().toTimeString().slice(0, 5).replace(':', '-') + '.pdf';
                doc.save(filename);
                
            } catch (error) {
                console.error('Erro ao gerar PDF:', error);
                alert('Erro ao gerar PDF. Por favor, tente novamente.');
            }
        }
    </script>
    </div> <!-- End Main Content -->
</body>
</html>
<?php
} catch (Exception $e) {
    error_log("Stats Page Error: " . $e->getMessage());
    header('Location: error/500.php');
    exit;
}
?> 